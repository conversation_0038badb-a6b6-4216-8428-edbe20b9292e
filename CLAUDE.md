# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Frontend Development
```bash
# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview

# Type check
npm run check

# Watch mode type checking
npm run check:watch
```

### Tauri Development
```bash
# Start Tauri development mode (opens desktop app)
npm run tauri dev

# Build Tauri application
npm run tauri build

# Tauri commands
npm run tauri
```

### Database Operations
- SQLite database is automatically created at: `/Users/<USER>/我的文档/sqlite/peckbyte.db`
- MongoDB configuration is managed through the app's settings interface

## Architecture Overview

This is a clinical research project management desktop application built with:
- **Frontend**: SvelteKit + TypeScript + Tailwind CSS
- **Backend**: Rust + Tauri
- **Databases**: SQLite (local) + MongoDB (cloud config)
- **AI Integration**: Langchain.js + OpenAI API

### Backend Architecture (Rust/Tauri)
The backend follows a layered architecture:

- **Commands Layer** (`src-tauri/src/commands/`): Tauri commands exposed to frontend
- **Services Layer** (`src-tauri/src/services/`): Business logic implementation  
- **Repositories Layer** (`src-tauri/src/repositories/`): Data access abstraction
- **Models Layer** (`src-tauri/src/models/`): Data structures and types

Key files:
- `src-tauri/src/lib.rs`: Main library with all Tauri command registrations
- `src-tauri/src/app.rs`: Application initialization and database setup
- `src-tauri/src/db.rs`: Database connection management
- `src-tauri/src/error.rs`: Unified error handling
- `src-tauri/src/response.rs`: Standard API response formats

### Frontend Architecture (SvelteKit)
- **Routes** (`src/routes/`): File-based routing with SvelteKit conventions
- **Components** (`src/lib/components/`): Reusable UI components organized by feature
- **Services** (`src/lib/services/`): Frontend service layer that calls Tauri commands
- **Stores** (`src/lib/stores/`): Svelte reactive state management
- **Utils** (`src/lib/utils/`): Shared utilities and type definitions

### Data Storage Strategy
- **SQLite**: Core business data (projects, staff, rules, criteria)
- **MongoDB**: System configuration and sensitive settings (API keys)
- **Browser Storage**: UI preferences via Svelte stores

## Key Development Patterns

### Frontend-Backend Communication
- Frontend calls backend via `@tauri-apps/api/core` `invoke()` function
- All backend functions are registered as Tauri commands in `lib.rs`
- Use `camelCase` in frontend payloads, `snake_case` in Rust structs
- Watch for `#[serde(flatten)]` in Rust - requires flattened JSON structure from frontend

### Error Handling
- Backend: Use `Result<T, AppError>` pattern with unified error types
- Frontend: Try-catch with detailed error logging in service layer
- All errors flow through standardized response format

### Database Access
- Use repository pattern for data access abstraction
- Support both SQLite and MongoDB through unified interfaces
- All database operations should be transactional where appropriate

### AI Integration
- Langchain.js integration for intelligent features
- OpenAI API for content generation and classification
- Configuration managed through secure settings system

## Important Conventions

### Rust Development
- Use `#[tauri::command]` macro for all frontend-callable functions
- Register all commands in `lib.rs` 
- Follow repository pattern for data access
- Use unified error handling via `AppError` type
- Add logging with `info!`, `debug!`, `warn!`, `error!` macros

### Frontend Development  
- Use TypeScript for type safety
- Follow SvelteKit file routing conventions
- Use Tailwind CSS for styling (avoid custom CSS)
- Organize components by feature in `lib/components/`
- Use service layer pattern for backend communication
- Handle loading states and errors consistently

### Component Development
- Use `{#key}` blocks to force re-renders when data structure stays same but content changes
- For complex objects use `JSON.stringify()` in key expressions
- Default filter states should be "all selected" not "none selected" for better UX

### Common Debugging
- Add `console.log` in frontend services to trace payloads
- Add `info!()` logs in Rust command entry points  
- Check browser dev tools for network request payloads
- Verify JSON structure matches Rust struct expectations, especially with `#[serde(flatten)]`