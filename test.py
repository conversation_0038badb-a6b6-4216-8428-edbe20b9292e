import sqlite3
import os

def generate_sqlite_doc(db_path, output_path):
    """
    Generates documentation for a SQLite database including table schemas and foreign keys.

    Args:
        db_path (str): The path to the SQLite database file.
        output_path (str): The full path including directory and filename for the output file.
    """
    doc_content = []

    # Add the introductory sentence
    doc_content.append(f"以下是本项目的sqlite数据库表单结构，数据库地址是：`{db_path}`\n\n")

    # Extract directory from output_path
    output_dir = os.path.dirname(output_path)

    # Create the directory if it doesn't exist
    if output_dir: # Only try to create if output_dir is not empty (i.e., not current directory)
        try:
            os.makedirs(output_dir, exist_ok=True)
            print(f"Ensured directory exists: `{output_dir}`")
        except OSError as e:
            doc_content.append(f"# Error: Failed to create output directory\n")
            doc_content.append(f"Could not create directory `{output_dir}`: {e}\n")
            print(f"Error: Failed to create output directory `{output_dir}`: {e}")
            # Write initial error message to the intended file path, if possible
            # This might fail if the directory creation failed due to permissions etc.
            try:
                with open(output_path, "w", encoding="utf-8") as f:
                    f.writelines(doc_content)
            except Exception as write_e:
                print(f"Further error writing initial error message to file: {write_e}")
            return # Exit if directory creation fails

    if not os.path.exists(db_path):
        doc_content.append(f"# Error: Database file not found\n")
        doc_content.append(f"The specified database path does not exist: `{db_path}`\n")
        print(f"Error: Database file not found at {db_path}")
        # Even if error, write the error message to the file
        with open(output_path, "w", encoding="utf-8") as f:
            f.writelines(doc_content)
        return

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        doc_content.append(f"# Database Documentation\n\n")
        doc_content.append(f"Documentation for database: `{os.path.basename(db_path)}`\n\n")

        # Get all table names
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()

        if not tables:
            doc_content.append("No tables found in the database.\n")
        else:
            doc_content.append("## Tables\n\n")
            for i, table in enumerate(tables):
                table_name = table[0]
                doc_content.append(f"### Table: `{table_name}`\n\n")

                # Get table info (columns)
                doc_content.append("#### Columns\n\n")
                doc_content.append("| Column Name | Data Type | NOT NULL | Primary Key | Default Value |\n")
                doc_content.append("|-------------|-----------|----------|-------------|---------------|\n")
                cursor.execute(f"PRAGMA table_info(`{table_name}`);")
                columns = cursor.fetchall()
                for col in columns:
                    cid, name, type, notnull, dflt_value, pk = col
                    doc_content.append(f"| {name} | {type} | {'YES' if notnull else 'NO'} | {'YES' if pk else 'NO'} | {dflt_value if dflt_value is not None else 'NULL'} |\n")
                doc_content.append("\n")

                # Get foreign key info
                cursor.execute(f"PRAGMA foreign_key_list(`{table_name}`);")
                foreign_keys = cursor.fetchall()

                if foreign_keys:
                    doc_content.append("#### Foreign Keys (Relationships)\n\n")
                    doc_content.append("| ID | Sequence | To Table | From Column | To Column | On Update | On Delete | Match |\n")
                    doc_content.append("|----|----------|----------|-------------|-----------|-----------|-----------|-------|\n")
                    for fk in foreign_keys:
                        id, seq, table_ref, from_col, to_col, on_update, on_delete, match = fk
                        doc_content.append(f"| {id} | {seq} | `{table_ref}` | `{from_col}` | `{to_col}` | {on_update} | {on_delete} | {match} |\n")
                    doc_content.append("\n")
                else:
                    doc_content.append("This table has no foreign key constraints.\n\n")

                # Add a horizontal rule between tables for clarity
                if i < len(tables) - 1:
                    doc_content.append("---\n\n")

        # Write the documentation to a file
        with open(output_path, "w", encoding="utf-8") as f:
            f.writelines(doc_content)

        print(f"Documentation generated successfully at `{output_path}`")

    except sqlite3.Error as e:
        # Add database error info to the doc content as well
        doc_content.append(f"# Error: Database Error\n")
        doc_content.append(f"An error occurred while accessing the database: {e}\n")
        print(f"Database error: {e}")
        # Write the error message and existing content to the file
        try:
            with open(output_path, "w", encoding="utf-8") as f:
                f.writelines(doc_content)
        except Exception as write_e:
             print(f"Further error writing database error message to file: {write_e}")


    finally:
        if 'conn' in locals() and conn:
            conn.close()

# --- Configuration ---
DB_PATH = "/Users/<USER>/我的文档/sqlite/peckbyte.db"
# Updated output path to the specified directory and file extension
OUTPUT_DOC_PATH = "peckbyte_database_doc.mdc"

# --- Run the script ---
generate_sqlite_doc(DB_PATH, OUTPUT_DOC_PATH)
