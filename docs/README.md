---
noteId: "46cdcde03e9411f0b4ec13362c7e567c"
tags: []

---

# GCPM 系统文档

欢迎使用 GCPM (Good Clinical Practice Management) 系统文档。本文档集合提供了系统的完整API文档和使用指南。

## 📚 文档目录

### API 文档
- **[API 系统概览](./gcpm-api-summary.md)** - 系统功能模块概述和核心特性介绍
- **[完整 API 文档](./gcpm-api-documentation.md)** - 详细的接口文档，包含所有端点和数据模型
- **[API 快速参考](./gcpm-api-quick-reference.md)** - 常用接口速查和开发指南

### 架构文档
- **[前端架构分析](./frontend-architecture-analysis.md)** - 前端系统架构分析

### 数据迁移文档
- **[数据迁移注意事项](./数据迁移工作注意事项.md)** - 完整的数据迁移指南
- **[数据迁移注意事项(精简版)](./数据迁移工作注意事项-精简.md)** - 精简版迁移指南

## 🚀 快速开始

### 1. 了解系统
首先阅读 [API 系统概览](./gcpm-api-summary.md) 了解 GCPM 系统的核心功能和模块。

### 2. 开发准备
查看 [API 快速参考](./gcpm-api-quick-reference.md) 获取常用接口和开发技巧。

### 3. 详细开发
参考 [完整 API 文档](./gcpm-api-documentation.md) 进行具体的接口开发。

## 📊 系统概况

- **API 版本**: OpenAPI 3.0.0
- **接口总数**: 156 个
- **功能模块**: 24 个
- **核心功能**: 临床试验管理、患者管理、访视管理、研究项目管理

## 🔧 主要功能模块

| 模块 | 接口数量 | 主要功能 |
|------|----------|----------|
| 访视管理 | 22 | 访视预约、记录管理、历史查询 |
| 研究项目管理 | 32 | 项目信息、成员管理、统计分析 |
| 患者管理 | 15 | 患者CRUD、分页查询、数据同步 |
| CRC工作面板 | 10 | 访视预约、质量控制事件 |
| 临床设备管理 | 7 | 设备注册、事件记录、绑定管理 |
| 团队管理 | 7 | 成员管理、部门管理、状态管理 |
| 慢病管理系统集成 | 7 | 患者同步、项目同步、疾病管理 |
| 导诊工作面板 | 5 | 访视查询、创建、未完成访视 |
| 引用数据管理 | 9 | 基础数据、状态定义、事件类型 |
| 用户认证与授权 | 9 | 登录注册、CRC白名单管理 |

## 🛠️ 技术特性

### API 设计
- ✅ RESTful 架构设计
- ✅ 统一的响应格式
- ✅ 完善的错误处理
- ✅ 分页查询支持

### 数据集成
- ✅ 金数据平台集成
- ✅ 慢病管理系统同步
- ✅ 实时和批量数据同步

### 安全性
- ✅ Token 身份验证
- ✅ 细粒度权限控制
- ✅ 数据加密保护

### 可扩展性
- ✅ 模块化设计
- ✅ 标准化接口
- ✅ 版本控制机制

## 📖 使用指南

### 开发者
1. 阅读 [API 快速参考](./gcpm-api-quick-reference.md) 了解常用接口
2. 查看 [完整 API 文档](./gcpm-api-documentation.md) 获取详细信息
3. 参考示例代码进行开发

### 系统管理员
1. 阅读 [数据迁移注意事项](./数据迁移工作注意事项.md) 了解迁移流程
2. 查看 [前端架构分析](./frontend-architecture-analysis.md) 了解系统架构

### 项目经理
1. 阅读 [API 系统概览](./gcpm-api-summary.md) 了解系统能力
2. 查看功能模块列表规划项目需求

## 🔗 相关链接

- **OpenAPI 规范文件**: `../openapi/gcpm-openapi-spec.json`
- **前端应用**: `../apps/gcpm-frontend/`
- **系统源码**: 项目根目录

## 📝 文档维护

### 自动生成
API 文档通过分析 OpenAPI 规范文件自动生成，确保文档与代码同步。

### 更新流程
1. 修改 OpenAPI 规范文件
2. 运行文档生成脚本: `python3 analyze_openapi.py`
3. 检查生成的文档
4. 提交更新

### 贡献指南
- 发现文档问题请提交 Issue
- 改进建议欢迎提交 Pull Request
- 新增功能请同步更新文档

## 📞 支持与反馈

如有问题或建议，请通过以下方式联系：

- 📧 技术支持邮箱
- 💬 项目讨论群
- 🐛 GitHub Issues

---

**最后更新**: 2024年12月
**文档版本**: v1.0.0
**API 版本**: OpenAPI 3.0.0
