---
noteId: "46ce6a203e9411f0b4ec13362c7e567c"
tags: []

---

# GCPM API 文档

本文档基于 OpenAPI 3.0.0 规范自动生成，详细描述了 GCPM (Good Clinical Practice Management) 系统的 API 接口。

## 目录

- [API 概览](#api-概览)
- [接口分类](#接口分类)
- [数据模型](#数据模型)
- [详细接口文档](#详细接口文档)

## API 概览

- **OpenAPI 版本**: 3.0.0
- **基础路径**: /
- **接口总数**: 156
- **分类总数**: 24

## 接口分类

- **App** (1 个接口)
- **CRCRegister** (2 个接口)
- **CRCWhitelist** (5 个接口)
- **CRC工作面板** (10 个接口)
- **Goldata** (5 个接口)
- **MBGLSystemUsers** (7 个接口)
- **PatientCqrs** (5 个接口)
- **Schedule** (2 个接口)
- **User** (2 个接口)
- **auth** (2 个接口)
- **临床事件** (4 个接口)
- **临床设备** (7 个接口)
- **团队管理** (7 个接口)
- **导诊工作面板** (5 个接口)
- **引用数据** (9 个接口)
- **患者管理** (10 个接口)
- **慢病管理系统患者** (3 个接口)
- **慢病管理系统疾病** (1 个接口)
- **慢病管理系统项目** (3 个接口)
- **推荐联系人管理** (9 个接口)
- **文档管理** (2 个接口)
- **研究项目列表** (32 个接口)
- **访视** (22 个接口)
- **项目成员用户列表** (1 个接口)

## 数据模型

系统定义了 134 个数据模型，用于API请求和响应的数据结构。

### AddMemberRequestDto

---

### AddMemberResponseDto

---

### AddTeamMemberDto

---

### CRCRegisterRequestDto

**属性**:

- `username` (string) (必需): 用户名
- `password` (string) (必需): 密码
- `phone` (string) (必需): 手机号
- `email` (string) (必需): 邮箱
- `verificationCode` (string) (必需): 验证码

---

### CRCRegisterSendVerificationCodeRequestDto

**属性**:

- `phone` (string) (必需): 手机号
- `username` (string) (必需): 姓名
- `email` (string) (必需): 邮箱

---

### CRCUpdateRequestDto

**属性**:

- `name` (string) (必需): 姓名
- `phone` (string) (必需): 电话号码
- `email` (string) (必需): 邮箱

---

### CRCWhitelistCreateRequestDto

**属性**:

- `name` (string) (必需): 姓名
- `phone` (string) (必需): 电话号码
- `email` (string) (必需): 邮箱

---

### CRCWhitelistQueryResponseDto

**属性**:

- `id` (string) (必需): ID
- `name` (string) (必需): 姓名
- `phone` (string) (必需): 电话号码
- `email` (string) (必需): 邮箱

---

### CancelReserveVisitDto

---

### ClinicalDataVisitReserveCreateRequestDto

**属性**:

- `visitTypeID` (number) (必需): 访视类型ID
- `visitDate` (string) (必需): 访视预约日期
- `mbglUserID` (number) (必需): 访视预约患者ID（来自慢病管理系统）
- `mbglProjectID` (number) (必需): 访视预约项目 ID（来自慢病管理系统）
- `crcName` (string) (必需): 访视预约 CRC 名称

---

### ClinicalDataVisitReserveEntity

---

### CrcCreateReserveVisitDto

**属性**:

- `mbglUserID` (string) (必需): 慢病管理系统中的患者 Id
- `mbglProjectID` (string) (必需): 慢病管理系统中的项目 Id
- `visitTypeID` (string) (必需): 访视类型 Id
- `visitTypeOtherInfo` (string) (必需): 访视类型补充信息
- `visitDate` (string) (必需): 访视日期
- `visitName` (string) (必需): 访视名称
- `lungfunctionovertimeList` (array) (必需): 是否需要肺功能加班
- `patientFastingNeeds` (string) (必需): 患者是否需要空腹
- `patientBloodTestNeeds` (string) (必需): 患者是否需要采血
- `patientStayInCenter` (string) (必需): 患者是否在中心留宿
- `nurseOvertimeForBloodTest` (array) (必需): 护士采血加班
- `specialExamination` (array) (必需): 特殊检查
- `dispenseDrugNeeds` (string) (必需): 是否需要发放药物
- `drugSavingLocation` (string) (必需): 药物保存位置
- `needCenterVisitAssistance` (array) (必需): 需要中心提供访视协办事务
- `remark` (string) (必需): 备注
- `specialExaminationOtherInfo` (string) (必需): 特殊检查补充信息
- `drugSavingLocationOtherInfo` (string) (必需): 中心提供访视协办事务补充信息
- `createBy` (string) (必需): 创建者
- `createByUUID` (string) (必需): 创建者UUID
- `mainVisitContent` (array) (必需): 主要访视内容
- `isUnplannedVisit` (boolean) (必需): 是否为计划外访视

---

### CrcReserveVisitResponseDto

**属性**:

- `id` (string) (必需): 访视预约 Id
- `mbglProjectId` (number) (必需): 慢病管理系统中的项目信息
- `mbglPatientId` (number) (必需): 慢病管理系统中的患者 Id
- `visitDate` (string) (必需): 访视日期
- `visitName` (string) (必需): 访视名称
- `lungfunctionovertimeList` (array) (必需): 肺功能加班
- `dispenseDrugNeeds` (boolean) (必需): 是否需要药物
- `drugSavingLocation` (string) (必需): 药物保存位置
- `drugSavingLocationOtherInfo` (string) (必需): 药物保存位置补充信息
- `patientBloodTestNeeds` (boolean) (必需): 是否需要采血
- `needCentrifuge` (boolean) (必需): 是否需要离心机
- `needECG` (boolean) (必需): 是否需要心电图
- `needFreezer` (boolean) (必需): 是否需要冰箱
- `patientFastingNeeds` (boolean) (必需): 是否需要空腹检查
- `nurseOvertimeForBloodTest` (array) (必需): 护士采血加班
- `patientStayInCenter` (boolean) (必需): 是否在中心留宿
- `specialExamination` (array) (必需): 特殊检查
- `needCenterVisitAssistance` (array) (必需): 中心提供访视协办事务
- `remark` (string) (必需): 备注
- `specialExaminationOtherInfo` (string) (必需): 特殊检查补充信息
- `needCenterVisitAssistanceOtherInfo` (string) (必需): 中心提供访视协办事务补充信息
- `cancelReason` (string) (必需): 取消原因
- `mainVisitContent` (array) (必需): 主要访视内容
- `mainVisitContentOtherInfo` (string) (必需): 主要访视内容补充信息
- `isUnplannedVisit` (boolean) (必需): 是否为计划外访视

---

### CreateAssociationRecordDto

**属性**:

- `referrerId` (string) (必需): 推荐人 ID
- `patientMBGLId` (number) (必需): 慢病管理患者 ID
- `patientGCPMId` (string) (可选): GCPM 患者 UUID

---

### CreateCRCWhitelistRequestDto

**属性**:

- `name` (string) (必需): 姓名
- `phone` (string) (必需): 电话号码
- `email` (string) (必需): 邮箱

---

### CreateClinicalDeviceBindingRequestDto

**属性**:

- `clinicalProjectId` (string) (必需): 临床项目ID
- `deviceId` (number) (必需): 设备ID
- `createBy` (string) (必需): 创建人
- `createdAt` (string) (必需): 创建时间
- `updatedAt` (string) (必需): 更新时间

---

### CreateClinicalDeviceEventDto

---

### CreateClinicalDeviceEventRequestDto

**属性**:

- `createdAt` (string) (必需): 创建时间
- `updatedAt` (string) (必需): 更新时间
- `eventDate` (string) (必需): 事件日期
- `deviceId` (number) (必需): 设备ID
- `managementEventType` (string) (必需): 管理事件类型
- `minTemperature` (number) (必需): 最小温度
- `maxTemperature` (number) (必需): 最大温度
- `clinicalProjectId` (string) (必需): 临床项目ID
- `crcName` (string) (必需): CRC名称
- `drugExpirationDate` (string) (必需): 药物过期日期
- `createBy` (string) (必需): 创建人

---

### CreateClinicalDeviceRequestDto

**属性**:

- `serialNumber` (object) (必需): 金数据序号
- `id` (number) (必需): 临床设备ID
- `deviceType` (string) (必需): 设备类型
- `refrigeratorType` (string) (必需): 冰箱类型
- `provider` (string) (必需): 设备提供方
- `deviceLocation` (string) (必需): 设备放置位置
- `deviceLabel` (string) (可选): 设备标签
- `createBy` (string) (可选): 创建人

---

### CreateClinicalEventDto

---

### CreateDocumentRequestDto

**属性**:

- `files` (array) (必需)
- `documentType` (string) (必需): 文档类型
- `createBy` (string) (必需): 创建者

---

### CreateGoldatumDto

---

### CreateMbglSystemUsersDto

---

### CreatePatientProfileRequestDto

**属性**:

- `gender` (number) (必需): 患者性别
- `birthday` (string) (必需): 患者生日
- `address` (string) (必需): 患者地址
- `idNumber` (string) (必需): 患者身份证号

---

### CreatePatientRequestDto

**属性**:

- `name` (string) (必需): 患者姓名
- `phone` (string) (必需): 患者电话
- `profile` (unknown) (必需): 患者档案
- `diseases` (array) (必需): 患者疾病
- `mbglUserID` (string) (必需): 患者MBGL用户ID

---

### CreatePatientResponseDto

**属性**:

- `patientID` (string) (必需): 患者ID

---

### CreateProjectFilingDto

---

### CreateProjectRequestDto

**属性**:

- `projectFullname` (string) (必需): 项目全称
- `projectCode` (string) (必需): 项目代码
- `launchDate` (string) (必需): 项目启动日期
- `sponsorId` (string) (必需): 项目申办方ID
- `objectTypeId` (string) (必需): 项目对象类型ID
- `diseaseIds` (array) (必需): 项目疾病ID
- `phaseId` (string) (必需): 项目阶段ID
- `statusId` (string) (必需): 项目状态ID

---

### CreateProjectResponseDto

**属性**:

- `projectID` (string) (必需): 项目ID

---

### CreateQualityControlAEEventRequestDto

**属性**:

- `mbglPatientID` (string) (必需): 慢病管理系统中的患者 Id
- `mbglProjectID` (string) (必需): 慢病管理系统中的项目 Id
- `judgementDoctorId` (string) (必需): 判定医生 Id
- `detail` (string) (必需): 事件详情
- `date` (string) (必需): 事件日期
- `drugRelated` (string) (必需): 药物相关性

---

### CreateQualityControlPDEventRequestDto

**属性**:

- `mbglProjectID` (string) (必需): 慢病管理系统中的项目 Id
- `detail` (string) (必需): 事件详情
- `date` (string) (必需): 事件日期
- `issueType` (string) (必需): 问题类型

---

### CreateQualityControlSAEEventRequestDto

**属性**:

- `mbglPatientID` (string) (必需): 慢病管理系统中的患者 Id
- `mbglProjectID` (string) (必需): 慢病管理系统中的项目 Id
- `judgementDoctorId` (string) (必需): 判定医生 Id
- `detail` (string) (必需): 事件详情
- `date` (string) (必需): 事件日期
- `drugRelated` (string) (必需): 药物相关性

---

### CreateReferrerRequestDto

**属性**:

- `name` (string) (必需): 推荐人的姓名
- `phone` (string) (必需): 推荐人的电话

---

### CreateReferrerResponseDto

**属性**:

- `referrerId` (string) (必需): 返回创建的推荐人ID

---

### CreateReserveVisitRequestDto

**属性**:

- `mbglUserID` (string) (必需): 慢病管理系统中的患者 Id
- `mbglProjectID` (string) (必需): 慢病管理系统中的项目 Id
- `visitDate` (string) (必需): 访视日期
- `visitName` (string) (必需): 访视名称
- `lungfunctionovertimeList` (array) (必需): 是否需要肺功能加班
- `patientFastingNeeds` (string) (必需): 患者是否需要空腹
- `patientBloodTestNeeds` (string) (必需): 患者是否需要采血
- `needECG` (string) (必需): 是否需要心电图
- `needCentrifuge` (string) (必需): 是否需要离心机
- `needFreezer` (string) (必需): 是否需要标本冰箱
- `patientStayInCenter` (string) (必需): 是否在中心留宿
- `nurseOvertimeForBloodTest` (array) (必需): 护士采血加班
- `specialExamination` (array) (必需): 特殊检查
- `dispenseDrugNeeds` (string) (必需): 是否需要发放药物
- `drugSavingLocation` (string) (必需): 药物保存位置
- `needCenterVisitAssistance` (array) (必需): 需要中心提供访视协办事务
- `remark` (string) (必需): 备注
- `specialExaminationOtherInfo` (string) (必需): 特殊检查补充信息
- `needCenterVisitAssistanceOtherInfo` (string) (必需): 中心提供访视协办事务补充信息
- `drugSavingLocationOtherInfo` (string) (必需): 药物保存位置补充信息
- `mainVisitContent` (array) (必需): 主要访视内容
- `mainVisitContentOtherInfo` (string) (必需): 主要访视内容补充信息
- `isUnplannedVisit` (boolean) (必需): 是否为计划外访视

---

### CreateVisitCompleteRecordRequestDto

---

### CreateVisitReserveRequestDto

**属性**:

- `serialNumber` (number) (必需): 预约ID
- `visit_date` (string) (必需): 预约日期
- `user_id` (string) (必需): 用户ID
- `user_name` (string) (必需): 用户名
- `phone` (string) (必需): 电话号码
- `project_serial_number` (number) (必需): 金数据中的项目序号
- `project_id` (string) (必需): 项目ID
- `project_shortname` (string) (必需): 项目简称
- `crcname` (string) (必需): CRC名称
- `visit_name` (string) (必需): 访视名称
- `visit_type` (string) (必需): 访视类型
- `lungfunctionovertime_list` (string) (必需): 肺功能加班列表
- `dispense_drug_needs` (number) (必需): 是否需要发药
- `drug_saving_type` (string) (必需): 药物保存类型
- `drug_saving_location` (string) (必需): 药物保存位置
- `goldata_created_at` (string) (必需): 创建时间
- `goldata_updated_at` (string) (必需): 更新时间
- `info_platform` (string) (必需): 信息平台
- `info_filling_duration` (number) (必需): 信息填写时长
- `patient_fasting_needs` (number) (必需): 患者是否需要空腹
- `patient_blood_test_needs` (number) (必需): 患者是否需要采血
- `nurse_overtime_for_blood_test` (array) (必需): 护士采血加班
- `special_examination` (array) (必需): 需要特殊检查请勾选

---

### CursorPaginatedGetAssociationRecordResponseDto

**属性**:

- `items` (array) (必需): 当前页数据列表
- `nextCursor` (string) (可选): 下一页的游标。如果为 null，表示没有更多数据。
- `previousCursor` (string) (可选): 用于获取上一页数据的游标 (来自下一页响应的 previousCursor)
- `sortBy` (string) (可选): 排序字段 (通常是游标字段)
- `sortOrder` (string) (可选): 排序顺序

---

### CursorPaginatedGetNoReferrerMbglPatientsResponseDto

**属性**:

- `items` (array) (必需): 当前页数据列表
- `nextCursor` (string) (可选): 下一页的游标。如果为 null，表示没有更多数据。
- `previousCursor` (string) (可选): 用于获取上一页数据的游标 (来自下一页响应的 previousCursor)
- `sortBy` (string) (可选): 排序字段 (通常是游标字段)
- `sortOrder` (string) (可选): 排序顺序

---

### CursorPaginatedGetReferrersResponseDto

**属性**:

- `items` (array) (必需): 当前页数据列表
- `nextCursor` (string) (可选): 下一页的游标。如果为 null，表示没有更多数据。
- `previousCursor` (string) (可选): 用于获取上一页数据的游标 (来自下一页响应的 previousCursor)
- `sortBy` (string) (可选): 排序字段 (通常是游标字段)
- `sortOrder` (string) (可选): 排序顺序

---

### DepartmentStaff

**属性**:

- `name` (string) (必需)
- `phone` (string) (必需)

---

### DiseaseResponseDto

**属性**:

- `id` (number) (必需): 疾病ID
- `name` (string) (必需): 疾病名称

---

### FinishVisitCreateDto

**属性**:

- `基础访视` (object) (必需)

---

### FinishVisitCreateEvents

**属性**:

- `知情` (object) (可选)
- `随机` (object) (可选)
- `筛选` (object) (可选)
- `出组` (object) (可选)

---

### FinishVisitMbglCheckDto

**属性**:

- `sysCaseTempleNames` (array) (必需)
- `mbglUserId` (number) (必需)
- `jsjProjectId` (number) (必需)
- `mbglProjectId` (number) (必需)

---

### GetAllResearchProjectResponseDto

**属性**:

- `id` (string) (必需): 研究项目ID
- `name` (string) (必需): 研究项目名称
- `sponsor` (unknown) (必需): 研究项目申办方
- `code` (string) (必需): 研究项目代码
- `objectType` (unknown) (必需): 研究项目对象类型
- `projectPhase` (unknown) (必需): 研究项目阶段
- `status` (unknown) (必需): 研究项目状态
- `diseases` (array) (必需): 研究项目疾病
- `members` (array) (必需): 研究项目成员
- `pi` (unknown) (必需): 研究项目PI
- `launchDate` (string) (必需): 研究项目启动日期
- `endDate` (string) (必需): 研究项目结束日期
- `createdAt` (string) (必需): 研究项目创建时间
- `updatedAt` (string) (必需): 研究项目更新时间

---

### GetAssociationRecordResponseDto

**属性**:

- `id` (string) (必需): 关联记录 ID
- `referrer` (unknown) (必需): 推荐人
- `patient` (unknown) (必需): 患者
- `createdAt` (string) (必需): 创建时间
- `updatedAt` (string) (必需): 更新时间

---

### GetCrcWhitelistResponseDto

**属性**:

- `id` (string) (必需): ID
- `name` (string) (必需): 姓名
- `phone` (string) (必需): 电话号码
- `email` (string) (必需): 邮箱

---

### GetDiseaseResponseDto

**属性**:

- `id` (number) (必需): 疾病ID
- `name` (string) (必需): 疾病名称

---

### GetNoReferrerMbglPatientsResponseDto

**属性**:

- `mbglId` (string) (必需): 慢病管理患者 ID
- `patientName` (string) (必需): 患者姓名
- `patientPhone` (string) (必需): 患者手机号
- `createdAt` (string) (必需): 创建时间
- `updatedAt` (string) (必需): 更新时间

---

### GetObjectTypeResponseDto

**属性**:

- `id` (string) (必需): 研究对象类型ID
- `name` (string) (必需): 研究对象类型名称

---

### GetPatientResponseDto

**属性**:

- `id` (string) (必需): 患者ID
- `mbglUserID` (string) (必需): 患者MBGL用户ID
- `name` (string) (必需): 患者姓名
- `phone` (string) (必需): 患者电话
- `profile` (unknown) (必需): 患者档案
- `diseases` (array) (必需): 患者疾病

---

### GetPatientWithPaginationResponseDto

**属性**:

- `patients` (array) (必需): 患者列表
- `nextCursor` (string) (必需): 下一页游标

---

### GetProjectDetailsResponseDto

**属性**:

- `id` (string) (必需): 研究项目ID
- `name` (string) (必需): 研究项目名称
- `fullName` (string) (必需): 研究项目全称
- `sponsor` (unknown) (必需): 研究项目申办方
- `code` (string) (必需): 研究项目代码
- `objectType` (unknown) (必需): 研究项目对象类型
- `projectPhase` (unknown) (必需): 研究项目阶段
- `status` (unknown) (必需): 研究项目状态
- `diseases` (array) (必需): 研究项目疾病
- `members` (array) (必需): 研究项目成员
- `launchDate` (string) (必需): 研究项目启动日期
- `endDate` (string) (必需): 研究项目结束日期
- `createdAt` (string) (必需): 研究项目创建时间
- `updatedAt` (string) (必需): 研究项目更新时间
- `isSyncedToMbgl` (boolean) (必需): 项目是否同步到慢病管理系统

---

### GetProjectMemberResponseDto

**属性**:

- `id` (string) (必需): 成员ID
- `user` (unknown) (必需): 成员用户
- `roles` (array) (必需): 成员角色
- `isBlinded` (boolean) (必需): 是否盲态

---

### GetProjectPhaseResponseDto

**属性**:

- `id` (string) (必需): 项目阶段ID
- `name` (string) (必需): 项目阶段名称

---

### GetProjectRoleTypeResponseDto

**属性**:

- `id` (string) (必需): 项目角色类型ID
- `name` (string) (必需): 项目角色类型名称

---

### GetProjectStatusResponseDto

**属性**:

- `id` (string) (必需): 项目状态ID
- `name` (string) (必需): 项目状态名称

---

### GetReferrersResponseDto

**属性**:

- `id` (string) (必需): 推荐人ID
- `name` (string) (必需): 推荐人名称
- `phone` (string) (必需): 推荐人手机号
- `createdAt` (string) (必需): 创建时间
- `updatedAt` (string) (必需): 更新时间

---

### GetSponsorResponseDto

**属性**:

- `id` (string) (必需): 申办方ID
- `name` (string) (必需): 申办方名称

---

### GetUserDetailResponseDto

**属性**:

- `id` (string) (必需): 用户ID
- `name` (string) (必需): 用户名称
- `phone` (string) (必需): 用户手机号
- `email` (string) (必需): 用户邮箱
- `userTypeId` (string) (必需): 用户类型ID
- `displayName` (string) (必需): 用户显示名称
- `userProfile` (unknown) (必需): 用户资料

---

### GetUserProfileResponseDto

**属性**:

- `id` (string) (必需): 用户资料ID
- `firstName` (string) (必需): 姓
- `lastName` (string) (必需): 名
- `phone` (string) (必需): 电话号码

---

### GuidanceCreateReserveVisitRequestDto

**属性**:

- `mbglUserID` (string) (必需): 慢病管理系统中的患者 Id
- `mbglProjectID` (string) (必需): 慢病管理系统中的项目 Id
- `visitDate` (string) (必需): 访视日期
- `mainVisitContent` (array) (必需): 主要访视内容
- `mainVisitContentOtherInfo` (string) (必需): 主要访视内容补充信息
- `isUnplannedVisit` (boolean) (必需): 是否为计划外访视
- `crcName` (string) (必需): CRC 名称

---

### GuidanceMbglPatientResponseDto

**属性**:

- `id` (number) (必需)
- `name` (string) (必需)
- `age` (number) (必需)
- `sex` (string) (必需)
- `phone` (string) (必需)

---

### GuidanceQueryReserveVisitAdditionalInfoDto

**属性**:

- `reserveVisitId` (string) (必需): 访视预约 Id
- `informedDoctor` (string) (必需): 知情医生姓名

---

### GuidanceQueryReserveVisitResponseDto

**属性**:

- `reserveVisits` (array) (必需): 访视预约列表
- `patients` (array) (必需): 患者列表
- `projects` (array) (必需): 项目列表
- `additionalInfo` (array) (必需): 附加信息（例如知情医生姓名等）

---

### GuidanceReserveVisitResponseDto

**属性**:

- `id` (string) (必需): 访视预约 Id
- `mbglProjectId` (number) (必需): 慢病管理系统中的项目信息
- `mbglPatientId` (number) (必需): 慢病管理系统中的患者 Id
- `visitDate` (string) (必需): 访视日期
- `visitName` (string) (必需): 访视名称
- `lungfunctionovertimeList` (array) (必需): 肺功能加班
- `dispenseDrugNeeds` (boolean) (必需): 是否需要药物
- `drugSavingLocation` (string) (必需): 药物保存位置
- `drugSavingLocationOtherInfo` (string) (必需): 药物保存位置补充信息
- `patientBloodTestNeeds` (boolean) (必需): 是否需要采血
- `needCentrifuge` (boolean) (必需): 是否需要离心机
- `needECG` (boolean) (必需): 是否需要心电图
- `needFreezer` (boolean) (必需): 是否需要冰箱
- `patientFastingNeeds` (boolean) (必需): 是否需要空腹检查
- `patientStayInCenter` (boolean) (必需): 患者是否在中心留宿
- `nurseOvertimeForBloodTest` (array) (必需): 护士采血加班
- `specialExamination` (array) (必需): 特殊检查
- `needCenterVisitAssistance` (array) (必需): 中心提供访视协办事务
- `remark` (string) (必需): 备注
- `specialExaminationOtherInfo` (string) (必需): 特殊检查补充信息
- `needCenterVisitAssistanceOtherInfo` (string) (必需): 中心提供访视协办事务补充信息
- `cancelReason` (string) (必需): 取消原因
- `TempFinishVisitSyncResult` (unknown) (必需): 访视同步结果
- `crcName` (string) (必需)
- `mainVisitContent` (array) (必需): 主要访视内容
- `mainVisitContentOtherInfo` (string) (必需): 主访视内容补充信息
- `isUnplannedVisit` (boolean) (必需): 是否为未计划访视

---

### GuidanceUpdateReserveVisitRequestDto

**属性**:

- `mbglUserID` (string) (必需): 慢病管理系统中的患者 Id
- `mbglProjectID` (string) (必需): 慢病管理系统中的项目 Id
- `visitDate` (string) (必需): 访视日期
- `mainVisitContent` (array) (必需): 主要访视内容
- `mainVisitContentOtherInfo` (string) (必需): 主要访视内容补充信息
- `isUnplannedVisit` (boolean) (必需): 是否为计划外访视
- `crcName` (string) (必需): CRC 名称

---

### IsVisitCompleteDto

**属性**:

- `isVisitComplete` (boolean) (必需)

---

### LoginRequestDto

**属性**:

- `username` (string) (必需): 用户名
- `password` (string) (必需): 密码

---

### LoginResponseDto

---

### MbglDiseaseQueryResponseDto

**属性**:

- `name` (string) (必需): 疾病名称
- `id` (number) (必需): 疾病ID

---

### MbglPatientCreateRequestDto

**属性**:

- `name` (string) (必需): 患者姓名
- `phone` (string) (必需): 患者电话
- `gender` (number) (必需): 患者性别
- `birthday` (string) (必需): 患者生日

---

### MbglPatientQueryResponseDto

**属性**:

- `userId` (number) (必需): 患者ID
- `userName` (string) (必需): 患者姓名
- `phone` (string) (必需): 患者电话
- `gender` (number) (必需): 患者性别

---

### MbglPatientResponseDto

**属性**:

- `id` (number) (必需)
- `name` (string) (必需)
- `gender` (string) (必需)
- `phone` (string) (必需)

---

### MbglProjectQueryResponseDto

**属性**:

- `id` (number) (必需): 项目ID
- `name` (string) (必需): 项目名称

---

### MbglProjectResponseDto

**属性**:

- `id` (number) (必需)
- `name` (string) (必需)

---

### MbglSysRecommendPatientResponseDto

**属性**:

- `userId` (number) (必需): 患者ID
- `userName` (string) (必需): 患者姓名
- `phone` (string) (必需): 患者电话
- `diseases` (array) (必需): 疾病
- `gender` (string) (必需): 患者性别
- `age` (number) (必需): 患者年龄

---

### ObjectTypeResponseDto

**属性**:

- `id` (string) (必需): 研究对象类型ID
- `name` (string) (必需): 研究对象类型名称

---

### PatientCreateRequestDto

**属性**:

- `name` (string) (必需): 患者姓名
- `phone` (string) (必需): 患者电话
- `gender` (number) (必需): 患者性别
- `birthday` (string) (必需): 患者生日
- `address` (string) (必需): 患者地址
- `idNumber` (string) (可选): 患者身份证号
- `diseases` (array) (必需): 患者疾病

---

### PatientCreateResponseDto

**属性**:

- `id` (number) (必需): 患者ID
- `name` (string) (必需): 患者姓名
- `phone` (string) (必需): 患者电话
- `gender` (number) (必需): 患者性别
- `birthday` (string) (必需): 患者生日
- `address` (string) (必需): 患者地址
- `idNumber` (string) (必需): 患者身份证号
- `mbglUserID` (string) (可选): 患者在慢病管理系统中的 ID

---

### PatientDiseaseResponseDto

**属性**:

- `id` (string) (必需): 疾病ID
- `name` (string) (必需): 疾病名称
- `code` (string) (必需): 疾病代码
- `remarks` (string) (必需): 疾病备注

---

### PatientDto

**属性**:

- `mbglId` (number) (必需): 慢病管理患者 ID
- `gcpmId` (string) (必需): GCPM 患者 UUID
- `name` (string) (必需): 患者姓名
- `phone` (string) (必需): 患者电话

---

### PatientPaginationResponseDto

**属性**:

- `data` (array) (必需): 患者列表
- `nextCursor` (string) (必需): 下一页游标

---

### PatientProfileResponseDto

**属性**:

- `gender` (number) (必需): 患者性别
- `birthday` (string) (必需): 患者生日
- `address` (string) (必需): 患者地址
- `idNumber` (string) (必需): 患者身份证号

---

### PatientQueryResponseDto

**属性**:

- `id` (number) (必需): 患者ID
- `name` (string) (必需): 患者姓名
- `phone` (string) (必需): 患者电话
- `gender` (number) (必需): 患者性别
- `birthday` (string) (必需): 患者生日
- `address` (string) (必需): 患者地址
- `idNumber` (string) (必需): 患者身份证号
- `currentResearchStatusID` (number) (必需): 当前研究状态ID
- `isActive` (boolean) (必需): 是否激活
- `mbglUserID` (number) (必需): 慢病管理系统患者ID
- `diseases` (array) (必需): 疾病列表
- `createdAt` (string) (必需)
- `updatedAt` (string) (必需)
- `deleted` (boolean) (必需)
- `deletedAt` (string) (必需)

---

### PatientUpdateRequestDto

**属性**:

- `name` (string) (必需): 患者姓名
- `phone` (string) (必需): 患者电话
- `gender` (number) (必需): 患者性别
- `birthday` (string) (必需): 患者生日
- `address` (string) (必需): 患者地址
- `idNumber` (string) (必需): 患者身份证号
- `diseases` (array) (必需): 患者疾病

---

### PatientUpdateResponseDto

**属性**:

- `id` (number) (必需): 患者ID
- `name` (string) (必需): 患者姓名
- `phone` (string) (必需): 患者电话
- `gender` (number) (必需): 患者性别
- `birthday` (string) (必需): 患者生日
- `address` (string) (必需): 患者地址
- `idNumber` (string) (必需): 患者身份证号
- `currentResearchStatusID` (number) (必需): 当前研究状态ID
- `isActive` (boolean) (必需): 是否激活
- `mbglUserID` (number) (必需): 慢病管理系统患者ID

---

### ProjectMemberRoleResponseDto

**属性**:

- `id` (string) (必需): 角色ID
- `name` (string) (必需): 角色名称
- `code` (string) (必需): 角色代码

---

### ProjectMemberUserProfileResponseDto

**属性**:

- `id` (string) (必需): 用户档案ID
- `phone` (string) (必需): 用户档案手机号
- `lastName` (string) (必需): 用户档案姓氏
- `firstName` (string) (必需): 用户档案名字

---

### ProjectMemberUserResponseDto

**属性**:

- `id` (string) (必需): 用户ID
- `username` (string) (必需): 用户名称
- `email` (string) (必需): 用户邮箱
- `profile` (unknown) (必需): 用户档案

---

### ProjectPhaseResponseDto

**属性**:

- `id` (string) (必需): 项目阶段ID
- `name` (string) (必需): 项目阶段名称

---

### ProjectQueryResponseDto

**属性**:

- `name` (string) (必需): 项目名称
- `id` (number) (必需): 项目ID

---

### QueryQualityControlAEEventDto

**属性**:

- `id` (string) (必需)
- `mbglProjectId` (number) (必需)
- `mbglPatientId` (number) (必需)
- `judgementDoctorId` (string) (必需)
- `date` (string) (必需)
- `detail` (string) (必需)
- `drugRelated` (string) (必需)
- `createBy` (string) (必需)

---

### QueryQualityControlEventDto

**属性**:

- `aeEvents` (array) (必需)
- `saeEvents` (array) (必需)
- `pdEvents` (array) (必需)
- `patients` (array) (必需)
- `projects` (array) (必需)
- `judgmentDoctors` (array) (必需)

---

### QueryQualityControlPDEventDto

**属性**:

- `id` (string) (必需)
- `mbglProjectId` (number) (必需)
- `date` (string) (必需)
- `detail` (string) (必需)
- `issueType` (string) (必需)
- `createBy` (string) (必需)

---

### QueryQualityControlSAEEventDto

**属性**:

- `id` (string) (必需)
- `mbglProjectId` (number) (必需)
- `mbglPatientId` (number) (必需)
- `judgementDoctorId` (string) (必需)
- `date` (string) (必需)
- `detail` (string) (必需)
- `drugRelated` (string) (必需)
- `createBy` (string) (必需)

---

### ReferralDto

**属性**:

- `id` (string) (必需): 推荐人 ID
- `name` (string) (必需): 推荐人姓名
- `phone` (string) (必需): 推荐人电话

---

### RegisterRequestDto

---

### RegisterResponseDto

---

### ResearchProjectEditBasicInfoDto

---

### ReserveCheckMBGLSyncResultDto

**属性**:

- `result` (string) (必需)

---

### ReserveVisitPaginationResponseDto

**属性**:

- `data` (array) (必需): 访视预约列表
- `patients` (array) (必需): 患者列表
- `projects` (array) (必需): 项目列表
- `nextCursor` (string) (必需): 下一页游标

---

### ReserveVisitPatientStateDto

**属性**:

- `patientId` (number) (必需): 患者ID
- `mbglProjectId` (number) (必需): 慢病管理系统项目ID
- `jsjProjectId` (number) (必需): 金数据项目序号

---

### SponsorResponseDto

**属性**:

- `id` (string) (必需): 申办方ID
- `name` (string) (必需): 申办方名称

---

### StatusResponseDto

**属性**:

- `id` (string) (必需): 项目状态ID
- `name` (string) (必需): 项目状态名称

---

### Subject

**属性**:

- `researchProjectUUID` (string) (必需): 项目UUID
- `id` (number) (必需): 受试者id
- `patientUUID` (string) (必需): 受试者uuid
- `tempMBGLUserID` (string) (必需): 慢病管理系统用户id
- `tempJSJProjectID` (number) (必需): 金数据序号
- `tempMBGLProjectID` (number) (必需): 慢病管理系统项目id
- `researchProjectID` (number) (必需): 研究项目id
- `stateID` (number) (必需): 受试者状态id
- `patientID` (number) (必需): 患者id
- `screeningNumber` (string) (必需): 受试号（筛选号）
- `createdAt` (string) (必需)
- `updatedAt` (string) (必需)

---

### SubjectEvent

**属性**:

- `id` (number) (必需): 事件id
- `subjectID` (number) (必需): 受试者id
- `eventTypeID` (number) (必需): 事件类型id
- `eventTime` (string) (必需): 事件时间
- `content` (string) (必需): 事件内容
- `remark` (string) (必需): 备注
- `createdAt` (string) (必需)
- `updatedAt` (string) (必需)
- `parentEventID` (number) (必需): 父事件id

---

### SubjectEventQueryDto

**属性**:

- `id` (number) (必需): 事件id
- `subjectID` (number) (必需): 受试者id
- `eventTypeID` (number) (必需): 事件类型id
- `eventTime` (string) (必需): 事件时间
- `remark` (string) (必需): 备注
- `parentEventID` (number) (必需): 父事件id
- `content` (object) (必需): 事件内容

---

### SubjectEventTypeResponseDto

**属性**:

- `id` (number) (必需): 事件类型id
- `code` (string) (必需): 事件类型代码
- `priority` (number) (必需): 事件优先级
- `createdAt` (string) (必需)
- `updatedAt` (string) (必需)

---

### SubjectState

**属性**:

- `id` (number) (必需): 受试者状态id
- `code` (string) (必需): 受试者状态代码
- `createdAt` (string) (必需)
- `updatedAt` (string) (必需)

---

### TempFinishVisitSyncResultDto

**属性**:

- `id` (number) (必需): 访视同步结果 Id
- `reserveId` (number) (必需): 访视预约 Id
- `mbglDataId` (number) (必需): 慢病管理系统中的数据 Id
- `needSync` (boolean) (必需): 是否需要同步

---

### TempGetUserQueryResponseDto

**属性**:

- `id` (string) (必需): 用户 ID
- `name` (string) (必需): 用户名
- `phone` (string) (必需): 用户电话

---

### TempJSJVisitTypeResponseDto

**属性**:

- `id` (number) (必需)
- `visitTypeCode` (string) (必需)

---

### UpdateAssociationRecordDto

**属性**:

- `referrerId` (string) (必需): 推荐人 ID
- `patientMBGLId` (number) (必需): 慢病管理患者 ID
- `patientGCPMId` (string) (可选): GCPM 患者 UUID

---

### UpdateAssociationRecordResponseDto

**属性**:

- `associationRecordId` (string) (必需): 关联记录 ID

---

### UpdateCRCWhitelistRequestDto

**属性**:

- `name` (string) (必需): 姓名
- `phone` (string) (必需): 电话号码
- `email` (string) (必需): 邮箱

---

### UpdateGoldatumDto

---

### UpdateMbglSystemUsersDto

---

### UpdatePatientProfileRequestDto

**属性**:

- `gender` (number) (必需): 患者性别
- `birthday` (string) (必需): 患者生日
- `address` (string) (必需): 患者地址
- `idNumber` (string) (必需): 患者身份证号

---

### UpdatePatientRequestDto

**属性**:

- `name` (string) (必需): 患者姓名
- `phone` (string) (必需): 患者电话
- `profile` (unknown) (必需): 患者档案
- `diseases` (array) (必需): 患者疾病

---

### UpdatePatientResponseDto

**属性**:

- `patientID` (string) (必需): 患者ID

---

### UpdateProjectMemberRequestDto

---

### UpdateProjectMemberResponseDto

---

### UpdateReferrerRequestDto

**属性**:

- `name` (string) (必需): 推荐人的姓名
- `phone` (string) (必需): 推荐人的电话

---

### UpdateReferrerResponseDto

**属性**:

- `referrerId` (string) (必需): 返回更新的推荐人ID

---

### UpdateResearchProjectRequestDto

**属性**:

- `projectFullname` (string) (必需): 项目全称
- `projectCode` (string) (必需): 项目代码
- `launchDate` (string) (必需): 项目启动日期
- `sponsorId` (string) (必需): 项目申办方ID
- `objectTypeId` (string) (必需): 项目对象类型ID
- `diseaseIds` (array) (必需): 项目疾病ID
- `phaseId` (string) (必需): 项目阶段ID
- `statusId` (string) (必需): 项目状态ID

---

### UpdateResearchProjectResponseDto

**属性**:

- `id` (string) (必需): 项目ID

---

### UpdateReserveVisitRequestDto

**属性**:

- `mbglUserID` (string) (必需): 慢病管理系统中的患者 Id
- `mbglProjectID` (string) (必需): 慢病管理系统中的项目 Id
- `visitDate` (string) (必需): 访视日期
- `visitName` (string) (必需): 访视名称
- `lungfunctionovertimeList` (array) (必需): 是否需要肺功能加班
- `patientFastingNeeds` (string) (必需): 患者是否需要空腹
- `patientBloodTestNeeds` (string) (必需): 患者是否需要采血
- `needECG` (string) (必需): 是否需要心电图
- `needCentrifuge` (string) (必需): 是否需要离心机
- `needFreezer` (string) (必需): 是否需要标本冰箱
- `patientStayInCenter` (string) (必需): 是否在中心留宿
- `nurseOvertimeForBloodTest` (array) (必需): 护士采血加班
- `specialExamination` (array) (必需): 特殊检查
- `dispenseDrugNeeds` (string) (必需): 是否需要发放药物
- `drugSavingLocation` (string) (必需): 药物保存位置
- `needCenterVisitAssistance` (array) (必需): 需要中心提供访视协办事务
- `remark` (string) (必需): 备注
- `specialExaminationOtherInfo` (string) (必需): 特殊检查补充信息
- `needCenterVisitAssistanceOtherInfo` (string) (必需): 中心提供访视协办事务补充信息
- `drugSavingLocationOtherInfo` (string) (必需): 药物保存位置补充信息
- `mainVisitContent` (array) (必需): 主要访视内容
- `mainVisitContentOtherInfo` (string) (必需): 主要访视内容补充信息
- `isUnplannedVisit` (boolean) (必需): 是否为计划外访视

---

### UpdateTeamMemberInfoDto

---

### UpdateVisitDto

---

### UpdateVisitReserveDto

**属性**:

- `mbglProjectID` (number) (必需): 项目ID
- `mbglUserID` (number) (必需): 患者ID
- `visitTypeID` (number) (必需): 访视类型ID
- `visitDate` (string) (必需): 访视预约的日期
- `crcName` (string) (必需): CRC 姓名

---

### VisitCompleteRecords

**属性**:

- `id` (number) (必需)
- `user_id` (number) (必需)
- `created_at` (string) (必需)
- `updated_at` (string) (必需)
- `reserve_id` (number) (必需)
- `record_infos` (object) (必需)

---

### VisitReserveResponseDto

**属性**:

- `id` (number) (必需): 预约ID
- `visit_date` (string) (必需): 访视日期
- `user_id` (string) (必需): 用户ID
- `user_name` (string) (必需): 用户姓名
- `phone` (string) (必需): 电话号码
- `project_id` (string) (可选): 项目ID
- `project_shortname` (string) (可选): 项目简称
- `crcname` (string) (可选): CRC姓名
- `visit_name` (string) (可选): 访视名称
- `visit_type` (string) (可选): 访视类型
- `lungfunctionovertime_list` (array) (必需): 肺功能加班列表
- `patient_fasting_needs` (number) (必需): 患者是否需要空腹
- `patient_blood_test_needs` (number) (必需): 患者是否需要采血
- `needECG` (number) (必需): 是否需要心电图
- `needCentrifuge` (number) (必需): 是否需要离心机
- `needFreezer` (number) (必需): 是否需要标本冰箱
- `nurse_overtime_for_blood_test` (array) (必需): 护士采血加班
- `special_examination` (array) (必需): 特殊检查
- `dispense_drug_needs` (number) (可选): 是否需要发放药物
- `drug_saving_type` (string) (可选): 药物保存类型
- `drug_saving_location` (string) (可选): 药物保存位置
- `encrypted_reserve_infos` (string) (可选): 加密的预约信息（十六进制字符串）
- `goldata_created_at` (string) (必需): Goldata创建时间
- `goldata_updated_at` (string) (必需): Goldata更新时间
- `info_platform` (string) (可选): 信息平台
- `info_filling_duration` (number) (可选): 填写持续时间（秒）
- `finished_status` (number) (必需): 完成状态
- `created_at` (string) (必需): 创建时间
- `updated_at` (string) (必需): 更新时间
- `project_serial_number` (number) (必需): 项目序列号
- `visitTypeID` (number) (必需): 访视类型ID
- `mbglProjectID` (number) (必需): MBGL项目ID
- `serialNumber` (number) (必需): 序列号
- `mbglUserID` (number) (必需): MBGL用户ID
- `need_center_visit_assistance_info` (array) (必需): 中心提供访视协办事务补充信息
- `remark` (string) (必需): 备注
- `cancelReason` (string) (必需): 取消原因
- `createBy` (string) (必需): 创建者
- `createByUUID` (string) (必需): 创建者UUID
- `patient_stay_in_center` (number) (必需): 患者是否在中心留宿
- `mainVisitContent` (string) (必需): 主要访视内容
- `isUnplannedVisit` (boolean) (必需): 是否为计划外访视

---

## 详细接口文档

### App

#### GET /

**操作ID**: `AppController_getHello`

**响应**:

- `200`: 

---

### CRCRegister

#### POST /user/crc/register/verification-code

**摘要**: 获取验证码

**操作ID**: `CRCRegisterController_getVerificationCode`

**请求体**:

- 必需
- Content-Type: `application/json`
- Schema: `CRCRegisterSendVerificationCodeRequestDto`

**响应**:

- `default`: 

---

#### POST /user/crc/register

**摘要**: 注册

**操作ID**: `CRCRegisterController_register`

**请求体**:

- 必需
- Content-Type: `application/json`
- Schema: `CRCRegisterRequestDto`

**响应**:

- `201`: 

---

### CRCWhitelist

#### GET /user/crc/whitelist

**摘要**: 获取白名单用户列表

**操作ID**: `CRCWhitelistController_getWhitelistUserList`

**响应**:

- `200`: 白名单用户列表

---

#### POST /user/crc/whitelist

**摘要**: 添加 CRC 到白名单

**操作ID**: `CRCWhitelistController_createWhitelistUser`

**请求体**:

- 必需
- Content-Type: `application/json`
- Schema: `CRCWhitelistCreateRequestDto`

**响应**:

- `200`: 添加 CRC 到白名单成功

---

#### DELETE /user/crc/whitelist/{id}

**摘要**: 从白名单中删除 CRC

**操作ID**: `CRCWhitelistController_deleteWhitelistUser`

**参数**:

- `id` (string, 必需, path): CRC 的 ID

**响应**:

- `200`: 从白名单中删除 CRC 成功

---

#### PATCH /user/crc/whitelist/{id}

**摘要**: 更新白名单中的 CRC 信息

**操作ID**: `CRCWhitelistController_updateWhitelistUser`

**参数**:

- `id` (string, 必需, path): CRC 的 ID

**请求体**:

- 必需
- Content-Type: `application/json`
- Schema: `CRCUpdateRequestDto`

**响应**:

- `200`: 更新白名单中的 CRC 信息成功

---

#### GET /user/crc/whitelist/{id}

**摘要**: 获取白名单中的 CRC 信息

**操作ID**: `CRCWhitelistController_getWhitelistUserDetail`

**参数**:

- `id` (string, 必需, path): CRC 的 ID

**响应**:

- `200`: 获取白名单中的 CRC 信息成功
  - Content-Type: `application/json`, Schema: `CRCWhitelistQueryResponseDto`

---

### CRC工作面板

#### POST /crc/reserve-visit

**操作ID**: `CrcController_createReserveVisit`

**请求体**:

- 必需
- Content-Type: `application/json`
- Schema: `CreateReserveVisitRequestDto`

**响应**:

- `201`: 访视预约创建成功
- `400`: 请求参数无效
- `404`: 用户不存在

---

#### GET /crc/reserve-visit

**操作ID**: `CrcController_getReserveVisit`

**参数**:

- `id` (string, 必需, query): 访视预约ID

**响应**:

- `200`: 
  - Content-Type: `application/json`, Schema: `CrcReserveVisitResponseDto`
- `403`: 无权访问该访视预约
- `404`: 访视预约或用户不存在

---

#### PATCH /crc/reserve-visit/{id}

**操作ID**: `CrcController_updateReserveVisit`

**参数**:

- `id` (string, 必需, path)

**请求体**:

- 必需
- Content-Type: `application/json`
- Schema: `UpdateReserveVisitRequestDto`

**响应**:

- `200`: 访视预约更新成功
- `400`: 请求参数无效
- `403`: 无权访问该访视预约
- `404`: 访视预约或用户不存在

---

#### POST /crc/reserve-visit/{id}/cancel

**操作ID**: `CrcController_cancelReserveVisit`

**参数**:

- `id` (string, 必需, path)

**请求体**:

- 必需
- Content-Type: `application/json`
- Schema: `CancelReserveVisitDto`

**响应**:

- `200`: 访视预约取消成功
- `403`: 无权取消该访视预约
- `404`: 访视预约或用户不存在

---

#### GET /crc/reserve-visit/pagination

**操作ID**: `CrcController_getReserveVisitsWithPagination`

**参数**:

- `limit` (number, 可选, query): 每页条数
- `cursor` (string, 可选, query): 游标(访视预约ID)
- `visitName` (string, 可选, query): 访视名称(可选过滤)
- `visitDate` (string, 可选, query): 访视日期(可选过滤)

**响应**:

- `200`: 
  - Content-Type: `application/json`, Schema: `ReserveVisitPaginationResponseDto`
- `404`: 用户不存在

---

#### POST /crc/quality-control/ae

**操作ID**: `CrcController_createQualityControlAEEvent`

**请求体**:

- 必需
- Content-Type: `application/json`
- Schema: `CreateQualityControlAEEventRequestDto`

**响应**:

- `201`: 质量控制 AE 事件创建成功
- `400`: 请求参数无效
- `404`: 用户不存在

---

#### POST /crc/quality-control/sae

**操作ID**: `CrcController_createQualityControlSAEEvent`

**请求体**:

- 必需
- Content-Type: `application/json`
- Schema: `CreateQualityControlSAEEventRequestDto`

**响应**:

- `201`: 质量控制 SAE 事件创建成功
- `400`: 请求参数无效
- `404`: 用户不存在

---

#### POST /crc/quality-control/pd

**操作ID**: `CrcController_createQualityControlPDEvent`

**请求体**:

- 必需
- Content-Type: `application/json`
- Schema: `CreateQualityControlPDEventRequestDto`

**响应**:

- `201`: 质量控制 PD 事件创建成功
- `400`: 请求参数无效
- `404`: 用户不存在

---

#### GET /crc/quality-control/events

**操作ID**: `CrcController_getQualityControlEvents`

**响应**:

- `200`: 
  - Content-Type: `application/json`, Schema: `QueryQualityControlEventDto`

---

#### GET /crc/main-visit-content

**操作ID**: `CrcController_getMainVisitContent`

**响应**:

- `200`: 

---

### Goldata

#### POST /goldata

**操作ID**: `GoldataController_create`

**请求体**:

- 必需
- Content-Type: `application/json`
- Schema: `CreateGoldatumDto`

**响应**:

- `201`: 

---

#### GET /goldata

**操作ID**: `GoldataController_findAll`

**响应**:

- `200`: 

---

#### GET /goldata/{id}

**操作ID**: `GoldataController_findOne`

**参数**:

- `id` (string, 必需, path)

**响应**:

- `200`: 

---

#### PATCH /goldata/{id}

**操作ID**: `GoldataController_update`

**参数**:

- `id` (string, 必需, path)

**请求体**:

- 必需
- Content-Type: `application/json`
- Schema: `UpdateGoldatumDto`

**响应**:

- `200`: 

---

#### DELETE /goldata/{id}

**操作ID**: `GoldataController_remove`

**参数**:

- `id` (string, 必需, path)

**响应**:

- `200`: 

---

### MBGLSystemUsers

#### POST /mbgl-users

**操作ID**: `MbglSystemUsersController_create`

**请求体**:

- 必需
- Content-Type: `application/json`
- Schema: `CreateMbglSystemUsersDto`

**响应**:

- `201`: 

---

#### GET /mbgl-users

**操作ID**: `MbglSystemUsersController_findAll`

**响应**:

- `200`: 

---

#### GET /mbgl-users/{id}

**操作ID**: `MbglSystemUsersController_findOne`

**参数**:

- `id` (string, 必需, path)

**响应**:

- `200`: 

---

#### PATCH /mbgl-users/{id}

**操作ID**: `MbglSystemUsersController_update`

**参数**:

- `id` (string, 必需, path)

**请求体**:

- 必需
- Content-Type: `application/json`
- Schema: `UpdateMbglSystemUsersDto`

**响应**:

- `200`: 

---

#### DELETE /mbgl-users/{id}

**操作ID**: `MbglSystemUsersController_remove`

**参数**:

- `id` (string, 必需, path)

**响应**:

- `200`: 

---

#### GET /mbgl-users/name/{name}/phone/{phone}

**操作ID**: `MbglSystemUsersController_findOneWithNameAndPhone`

**参数**:

- `name` (string, 必需, path)
- `phone` (string, 必需, path)

**响应**:

- `200`: 

---

#### GET /mbgl-users/{id}/visit-complete-records

**操作ID**: `MbglSystemUsersController_findVisitCompleteRecordsWithUserId`

**参数**:

- `id` (string, 必需, path)

**响应**:

- `200`: 

---

### PatientCqrs

#### POST /patient

**摘要**: 创建患者

**操作ID**: `PatientCqrsController_createPatient`

**请求体**:

- 必需
- Content-Type: `application/json`
- Schema: `CreatePatientRequestDto`

**响应**:

- `200`: 创建患者成功
  - Content-Type: `application/json`, Schema: `CreatePatientResponseDto`

---

#### GET /patient

**摘要**: 查询患者列表

**操作ID**: `PatientCqrsController_getPatients`

**参数**:

- `limit` (number, 必需, query)
- `cursor` (string, 可选, query)
- `name` (string, 可选, query)
- `phone` (string, 可选, query)
- `gender` (number, 可选, query)
- `birthday` (array, 可选, query): 生日过滤条件，格式为 ['gte', 'YYYY-MM-DD', 'lte', 'YYYY-MM-DD']
- `address` (string, 可选, query)
- `idNumber` (string, 可选, query)

**响应**:

- `200`: 查询患者列表成功
  - Content-Type: `application/json`, Schema: `GetPatientWithPaginationResponseDto`

---

#### PUT /patient/{id}

**摘要**: 更新患者

**操作ID**: `PatientCqrsController_updatePatient`

**参数**:

- `id` (string, 必需, path): 患者ID

**请求体**:

- 必需
- Content-Type: `application/json`
- Schema: `UpdatePatientRequestDto`

**响应**:

- `200`: 更新患者成功
  - Content-Type: `application/json`, Schema: `UpdatePatientResponseDto`

---

#### GET /patient/{id}

**摘要**: 查询患者详情

**操作ID**: `PatientCqrsController_getPatientById`

**参数**:

- `id` (string, 必需, path): 患者ID

**响应**:

- `200`: 查询患者详情成功
  - Content-Type: `application/json`, Schema: `GetPatientResponseDto`

---

#### GET /patient/{patientID}/sync-to-mbgl

**摘要**: 同步患者到慢病管理系统

**操作ID**: `PatientCqrsController_syncPatientToMbgl`

**参数**:

- `patientID` (string, 必需, path): 患者 ID

**响应**:

- `200`: 同步患者到慢病管理系统成功
- `409`: 患者有 mbglUserID，但慢病管理系统中没有找到此患者

---

### Schedule

#### POST /schedule/user-sync-to-goldata

**操作ID**: `ScheduleController_syncUsers`

**参数**:

- `start` (string, 必需, query)
- `end` (string, 必需, query)

**响应**:

- `201`: 

---

#### GET /schedule/visit-record

**操作ID**: `ScheduleController_visitRecord`

**参数**:

- `start_date` (string, 必需, query)
- `end_date` (string, 必需, query)

**响应**:

- `200`: 

---

### User

#### GET /user

**摘要**: 获取所有 CRC 用户

**操作ID**: `UserController_getUsers`

**响应**:

- `200`: 

---

#### GET /user/judgment-doctors

**摘要**: 获取所有判定医生

**操作ID**: `UserController_getJudgmentDoctors`

**响应**:

- `200`: 

---

### auth

#### POST /auth/login

**摘要**: 登录

**操作ID**: `AuthController_login`

**请求体**:

- 必需
- Content-Type: `application/json`
- Schema: `LoginRequestDto`

**响应**:

- `200`: 登录成功
  - Content-Type: `application/json`, Schema: `LoginResponseDto`
- `401`: 身份验证失败(用户名或密码错误)
- `404`: 未找到用户

---

#### POST /auth/register

**摘要**: 注册用户

**操作ID**: `AuthController_register`

**请求体**:

- 必需
- Content-Type: `application/json`
- Schema: `RegisterRequestDto`

**响应**:

- `201`: 注册成功
  - Content-Type: `application/json`, Schema: `RegisterResponseDto`
- `409`: 用户已存在

---

### 临床事件

#### POST /clinical-events/from-goldata

**操作ID**: `ClinicalEventsController_fromGoldata`

**请求体**:

- 必需
- Content-Type: `application/json`
- Schema: `CreateClinicalEventDto`

**响应**:

- `201`: 

---

#### GET /clinical-events/patient/name/{name}

**操作ID**: `ClinicalEventsController_getClinicalEventsWithUserName`

**参数**:

- `name` (string, 必需, path)

**响应**:

- `200`: 

---

#### GET /clinical-events/patient/id/{id}

**操作ID**: `ClinicalEventsController_getClinicalEventWithUserId`

**参数**:

- `id` (string, 必需, path)
- `project_code` (string, 必需, query)
- `order` (string, 必需, query)

**响应**:

- `200`: 

---

#### POST /clinical-events/fix-no-user-id

**操作ID**: `ClinicalEventsController_fixNoUserId`

**响应**:

- `201`: 

---

### 临床设备

#### GET /clinical-device

**摘要**: 获取所有临床设备

**操作ID**: `ClinicalDeviceController_getClinicalDevices`

**参数**:

- `skip` (number, 可选, query)
- `take` (number, 可选, query)
- `orderBy` (string, 可选, query)

**响应**:

- `200`: 

---

#### GET /clinical-device/{id}

**摘要**: 根据ID获取临床设备

**操作ID**: `ClinicalDeviceController_getClinicalDeviceById`

**参数**:

- `id` (number, 必需, path)

**响应**:

- `200`: 

---

#### PUT /clinical-device/{id}

**摘要**: 更新临床设备信息

**操作ID**: `ClinicalDeviceController_updateClinicalDevice`

**参数**:

- `id` (number, 必需, path)

**响应**:

- `200`: 

---

#### POST /clinical-device/event

**摘要**: 创建临床设备事件

**操作ID**: `ClinicalDeviceController_createClinicalDeviceEvent`

**请求体**:

- 必需
- Content-Type: `application/json`
- Schema: `CreateClinicalDeviceEventDto`

**响应**:

- `200`: 创建临床设备事件成功

---

#### POST /clinical-device/event/from-goldata

**摘要**: 从金数据添加临床设备事件

**操作ID**: `ClinicalDeviceController_createClinicalDeviceEventFromGoldata`

**请求体**:

- 必需
- Content-Type: `application/json`
- Schema: `CreateClinicalDeviceEventRequestDto`

**响应**:

- `200`: 从金数据添加临床设备事件成功
- `400`: 从金数据添加临床设备事件失败

---

#### POST /clinical-device/binding/from-goldata

**摘要**: 从金数据添加临床设备绑定

**操作ID**: `ClinicalDeviceController_createClinicalDeviceBindingFromGoldata`

**请求体**:

- 必需
- Content-Type: `application/json`
- Schema: `CreateClinicalDeviceBindingRequestDto`

**响应**:

- `200`: 从金数据添加临床设备绑定成功
- `400`: 从金数据添加临床设备绑定失败

---

#### POST /clinical-device/from-goldata

**摘要**: 从金数据注册临床设备

**操作ID**: `ClinicalDeviceController_createClinicalDeviceFromGoldata`

**请求体**:

- 必需
- Content-Type: `application/json`
- Schema: `CreateClinicalDeviceRequestDto`

**响应**:

- `200`: 从金数据注册临床设备成功
- `400`: 从金数据注册临床设备失败

---

### 团队管理

#### DELETE /team-management/{userID}

**摘要**: 删除团队成员

**操作ID**: `TeamManagementController_removeTeamMember`

**参数**:

- `userID` (string, 必需, path)

**响应**:

- `200`: 

---

#### PUT /team-management/{userID}

**摘要**: 更新成员信息

**操作ID**: `TeamManagementController_updateTeamMemberInfo`

**参数**:

- `userID` (string, 必需, path)

**请求体**:

- 必需
- Content-Type: `application/json`
- Schema: `UpdateTeamMemberInfoDto`

**响应**:

- `200`: 

---

#### GET /team-management/all-user-profile

**摘要**: 获取所有用户基本信息

**操作ID**: `TeamManagementController_getAllUserProfile`

**响应**:

- `200`: 

---

#### GET /team-management/all-department

**摘要**: 获取所有部门

**操作ID**: `TeamManagementController_getAllDepartment`

**响应**:

- `200`: 

---

#### GET /team-management/all-status

**摘要**: 获取所有在职状态

**操作ID**: `TeamManagementController_getAllStatus`

**响应**:

- `200`: 

---

#### GET /team-management/all-department-member

**摘要**: 获取所有部门成员

**操作ID**: `TeamManagementController_getAllDepartmentMember`

**响应**:

- `200`: 

---

#### POST /team-management/add-member

**摘要**: 添加团队成员

**操作ID**: `TeamManagementController_addTeamMember`

**请求体**:

- 必需
- Content-Type: `application/json`
- Schema: `AddTeamMemberDto`

**响应**:

- `201`: 

---

### 导诊工作面板

#### GET /guidance/reserve-visit/date/{date}

**操作ID**: `GuidanceController_getReserveVisitsByDate`

**参数**:

- `date` (string, 必需, path): 访视预约日期

**响应**:

- `200`: 
  - Content-Type: `application/json`, Schema: `GuidanceQueryReserveVisitResponseDto`
- `404`: 用户不存在

---

#### GET /guidance/reserve-visit

**操作ID**: `GuidanceController_getReserveVisitsById`

**参数**:

- `id` (string, 必需, query): 访视预约 id

**响应**:

- `200`: 
  - Content-Type: `application/json`, Schema: `GuidanceQueryReserveVisitResponseDto`
- `404`: 用户不存在

---

#### POST /guidance/reserve-visit

**操作ID**: `GuidanceController_createReserveVisit`

**请求体**:

- 必需
- Content-Type: `application/json`
- Schema: `GuidanceCreateReserveVisitRequestDto`

**响应**:

- `200`: 
  - Content-Type: `application/json`, Schema: `GuidanceQueryReserveVisitResponseDto`
- `404`: 用户不存在

---

#### GET /guidance/reserve-visit/unfinished

**操作ID**: `GuidanceController_getUnfinishedReserveVisits`

**参数**:

- `date` (string, 必需, query)

**响应**:

- `200`: 
  - Content-Type: `application/json`, Schema: `GuidanceQueryReserveVisitResponseDto`
- `404`: 用户不存在

---

#### PATCH /guidance/reserve-visit/{id}

**操作ID**: `GuidanceController_updateReserveVisit`

**参数**:

- `id` (string, 必需, path)

**请求体**:

- 必需
- Content-Type: `application/json`
- Schema: `GuidanceUpdateReserveVisitRequestDto`

**响应**:

- `200`: 
  - Content-Type: `application/json`, Schema: `GuidanceQueryReserveVisitResponseDto`
- `404`: 用户不存在

---

### 引用数据

#### GET /reference-data/subject-states

**摘要**: 获取所有受试者状态

**操作ID**: `ReferenceDataController_getAllSubjectStates`

**响应**:

- `default`: 

---

#### GET /reference-data/subject-event-types

**摘要**: 获取所有受试者事件类型

**操作ID**: `ReferenceDataController_getAllSubjectEventTypes`

**响应**:

- `default`: 

---

#### GET /reference-data/visit-types

**摘要**: 获取所有访视类型

**操作ID**: `ReferenceDataController_getAllVisitTypes`

**响应**:

- `default`: 

---

#### GET /reference-data/diseases

**摘要**: 获取所有疾病

**操作ID**: `ReferenceDataController_getAllDiseases`

**响应**:

- `default`: 

---

#### GET /reference-data/research-project-phases

**摘要**: 获取所有研究分期

**操作ID**: `ReferenceDataController_getAllResearchProjectPhases`

**响应**:

- `default`: 

---

#### GET /reference-data/research-project-statuses

**摘要**: 获取所有研究状态

**操作ID**: `ReferenceDataController_getAllResearchProjectStatuses`

**响应**:

- `default`: 

---

#### GET /reference-data/sponsors

**摘要**: 获取所有申办方

**操作ID**: `ReferenceDataController_getAllSponsors`

**响应**:

- `default`: 

---

#### GET /reference-data/object-type

**摘要**: 获取所有研究对象类型

**操作ID**: `ReferenceDataController_getAllObjectType`

**响应**:

- `default`: 

---

#### GET /reference-data/project-role-types

**摘要**: 获取所有项目角色类型

**操作ID**: `ReferenceDataController_getAllProjectRoleTypes`

**响应**:

- `default`: 

---

### 患者管理

#### POST /patients

**摘要**: 创建患者

**操作ID**: `PatientsController_create`

**请求体**:

- 必需
- Content-Type: `application/json`
- Schema: `PatientCreateRequestDto`

**响应**:

- `409`: 同名和同电话的患者在GCPM中已经存在
- `default`: 
  - Content-Type: `application/json`, Schema: `PatientCreateResponseDto`

---

#### GET /patients

**摘要**: 查找患者

**操作ID**: `PatientsController_query`

**参数**:

- `name` (string, 可选, query)
- `phone` (string, 可选, query)

**响应**:

- `default`: 

---

#### POST /patients/patients/{patientId}/mbgl-sync

**摘要**: 同步 GCPM 患者到慢病管理系统中

**操作ID**: `PatientsController_syncToMbgl`

**参数**:

- `patientId` (string, 必需, path): 患者ID

**响应**:

- `default`: 
  - Content-Type: `application/json`, Schema: `MbglPatientCreateRequestDto`

---

#### GET /patients/pagination

**摘要**: 分页查询患者

**操作ID**: `PatientsController_queryWithPagination`

**参数**:

- `limit` (number, 可选, query): 每页条数
- `cursor` (string, 可选, query): 游标(患者ID)
- `name` (string, 可选, query): 患者姓名(可选过滤)
- `phone` (string, 可选, query): 患者电话(可选过滤)

**响应**:

- `default`: 
  - Content-Type: `application/json`, Schema: `PatientPaginationResponseDto`

---

#### GET /patients/{id}

**摘要**: 根据ID获取患者详情

**操作ID**: `PatientsController_getById`

**参数**:

- `id` (number, 必需, path): 患者ID

**响应**:

- `default`: 
  - Content-Type: `application/json`, Schema: `PatientQueryResponseDto`

---

#### PATCH /patients/{id}

**摘要**: 更新患者信息

**操作ID**: `PatientsController_update`

**参数**:

- `id` (number, 必需, path): 患者ID

**请求体**:

- 必需
- Content-Type: `application/json`
- Schema: `PatientUpdateRequestDto`

**响应**:

- `default`: 
  - Content-Type: `application/json`, Schema: `PatientUpdateResponseDto`

---

#### DELETE /patients/{id}

**摘要**: 删除患者(软删除)

**操作ID**: `PatientsController_softDelete`

**参数**:

- `id` (number, 必需, path): 患者ID

**响应**:

- `default`: 
  - Content-Type: `application/json`, Schema: `PatientUpdateResponseDto`

---

#### POST /patients/{id}/restore

**摘要**: 恢复已删除的患者

**操作ID**: `PatientsController_restore`

**参数**:

- `id` (number, 必需, path): 患者ID

**响应**:

- `default`: 
  - Content-Type: `application/json`, Schema: `PatientUpdateResponseDto`

---

#### GET /patients/deleted

**摘要**: 查询已删除的患者

**操作ID**: `PatientsController_queryDeletedWithPagination`

**参数**:

- `limit` (number, 可选, query): 每页条数
- `cursor` (string, 可选, query): 游标(患者ID)
- `name` (string, 可选, query): 患者姓名(可选过滤)
- `phone` (string, 可选, query): 患者电话(可选过滤)

**响应**:

- `default`: 
  - Content-Type: `application/json`, Schema: `PatientPaginationResponseDto`

---

#### GET /patients/{id}/diseases

**摘要**: 获取患者疾病列表

**操作ID**: `PatientsController_getPatientDiseases`

**参数**:

- `id` (number, 必需, path): 患者ID

**响应**:

- `default`: 

---

### 慢病管理系统患者

#### GET /mbgl/patient

**摘要**: 查询慢病管理系统中的患者

**操作ID**: `MbglPatientController_query`

**参数**:

- `name` (string, 可选, query)
- `phone` (string, 可选, query)

**响应**:

- `default`: 

---

#### GET /mbgl/patient/{patientId}

**摘要**: 根据 Id 返回某个患者的详细信息

**操作ID**: `MbglPatientController_getPatientById`

**参数**:

- `patientId` (string, 必需, path): 患者ID

**响应**:

- `default`: 
  - Content-Type: `application/json`, Schema: `MbglPatientQueryResponseDto`

---

#### GET /mbgl/patient/report/total

**摘要**: 获取某个时间段内新增的患者

**操作ID**: `MbglPatientController_queryTotalPatients`

**参数**:

- `startDate` (string, 必需, query): 开始日期
- `endDate` (string, 必需, query): 结束日期

**响应**:

- `default`: 

---

### 慢病管理系统疾病

#### GET /mbgl/disease/{id}

**摘要**: 根据疾病ID获取疾病信息

**操作ID**: `MbglDiseaseController_getDiseaseById`

**参数**:

- `id` (number, 必需, path): 疾病ID

**响应**:

- `default`: 
  - Content-Type: `application/json`, Schema: `MbglDiseaseQueryResponseDto`

---

### 慢病管理系统项目

#### GET /mbgl/project

**摘要**: 查询慢病管理系统中的项目

**操作ID**: `MbglProjectController_query`

**响应**:

- `default`: 

---

#### GET /mbgl/project/{id}/patient-state/{patientId}

**摘要**: 获取项目中某个患者当前的受试状态

**操作ID**: `MbglProjectController_getPatientStatus`

**参数**:

- `id` (number, 必需, path)
- `patientId` (number, 必需, path)

**响应**:

- `default`: 

---

#### GET /mbgl/project/{id}

**摘要**: 获取慢病管理系统中的项目信息

**操作ID**: `MbglProjectController_getProjectInfo`

**参数**:

- `id` (number, 必需, path)

**响应**:

- `default`: 
  - Content-Type: `application/json`, Schema: `MbglProjectQueryResponseDto`

---

### 推荐联系人管理

#### POST /referral-management/referrer

**摘要**: 添加推荐人

**操作ID**: `ReferralManagementController_addReferral`

**请求体**:

- 必需
- Content-Type: `application/json`
- Schema: `CreateReferrerRequestDto`

**响应**:

- `201`: 推荐人添加成功
  - Content-Type: `application/json`, Schema: `CreateReferrerResponseDto`
- `409`: 推荐人手机号已存在

---

#### GET /referral-management/referrer

**摘要**: 分页查询推荐人

**操作ID**: `ReferralManagementController_getReferrers`

**参数**:

- `limit` (number, 可选, query): 请求数量限制
- `after` (string, 可选, query): 下一页数据的游标
- `before` (string, 可选, query): 上一页数据的游标
- `sortBy` (string, 可选, query): 排序字段
- `sortOrder` (string, 可选, query): 排序顺序
- `referrerName` (string, 可选, query): 按推荐人姓名模糊搜索
- `referrerPhone` (string, 可选, query): 按推荐人手机号精确或模糊搜索

**响应**:

- `200`: 推荐人查询成功
  - Content-Type: `application/json`, Schema: `CursorPaginatedGetReferrersResponseDto`

---

#### PUT /referral-management/referrer/{id}

**摘要**: 更新推荐人

**操作ID**: `ReferralManagementController_updateReferral`

**参数**:

- `id` (string, 必需, path)

**请求体**:

- 必需
- Content-Type: `application/json`
- Schema: `UpdateReferrerRequestDto`

**响应**:

- `200`: 推荐人更新成功
  - Content-Type: `application/json`, Schema: `UpdateReferrerResponseDto`
- `404`: 推荐人不存在
- `409`: 推荐人手机号已存在

---

#### GET /referral-management/referrer/{id}

**摘要**: 查询推荐人

**操作ID**: `ReferralManagementController_getReferrerById`

**参数**:

- `id` (string, 必需, path)

**响应**:

- `200`: 推荐人查询成功
  - Content-Type: `application/json`, Schema: `GetReferrersResponseDto`

---

#### POST /referral-management/association

**摘要**: 创建推荐人与患者的关联

**操作ID**: `ReferralManagementController_createAssociation`

**请求体**:

- 必需
- Content-Type: `application/json`
- Schema: `CreateAssociationRecordDto`

**响应**:

- `201`: 

---

#### GET /referral-management/association

**摘要**: 分页查询推荐人与患者的关联记录(基于游标)

**操作ID**: `ReferralManagementController_getAssociationRecords`

**参数**:

- `limit` (number, 可选, query): 请求数量限制
- `after` (string, 可选, query): 下一页数据的游标
- `before` (string, 可选, query): 上一页数据的游标
- `sortBy` (string, 可选, query): 排序字段
- `sortOrder` (string, 可选, query): 排序顺序
- `patientName` (string, 可选, query): 按患者姓名模糊搜索
- `patientPhone` (string, 可选, query): 按患者手机号精确或模糊搜索
- `referrerName` (string, 可选, query): 按推荐人姓名模糊搜索
- `referrerPhone` (string, 可选, query): 按推荐人手机号精确或模糊搜索

**响应**:

- `200`: 推荐人与患者的关联记录查询成功
  - Content-Type: `application/json`, Schema: `CursorPaginatedGetAssociationRecordResponseDto`

---

#### PUT /referral-management/association/{id}

**摘要**: 更新推荐人与患者的关联

**操作ID**: `ReferralManagementController_updateAssociation`

**参数**:

- `id` (string, 必需, path): 关联记录ID

**请求体**:

- 必需
- Content-Type: `application/json`
- Schema: `UpdateAssociationRecordDto`

**响应**:

- `200`: 推荐人与患者的关联记录更新成功
  - Content-Type: `application/json`, Schema: `UpdateAssociationRecordResponseDto`
- `404`: 关联记录不存在

---

#### GET /referral-management/association/{id}

**摘要**: 查询推荐人与患者的关联记录

**操作ID**: `ReferralManagementController_getAssociationRecordById`

**参数**:

- `id` (string, 必需, path)

**响应**:

- `200`: 推荐人与患者的关联记录查询成功
  - Content-Type: `application/json`, Schema: `GetAssociationRecordResponseDto`

---

#### GET /referral-management/no-referrer-mbgl-patients

**摘要**: 查询没有推荐人的慢病管理患者

**操作ID**: `ReferralManagementController_getNoReferrerMbglPatients`

**参数**:

- `limit` (number, 可选, query): 请求数量限制
- `after` (string, 可选, query): 慢病管理患者id,下一页数据的游标
- `before` (string, 可选, query): 慢病管理患者id,上一页数据的游标
- `sortBy` (string, 可选, query): 排序字段
- `sortOrder` (string, 可选, query): 排序顺序
- `patientName` (string, 可选, query): 按患者姓名模糊搜索
- `patientPhone` (string, 可选, query): 按患者手机号精确或模糊搜索

**响应**:

- `200`: 没有推荐人的慢病管理患者查询成功
  - Content-Type: `application/json`, Schema: `CursorPaginatedGetNoReferrerMbglPatientsResponseDto`

---

### 文档管理

#### POST /docs/create

**操作ID**: `DocumentController_createDocument`

**请求体**:

- 必需
- Content-Type: `multipart/form-data`
- Schema: `CreateDocumentRequestDto`

**响应**:

- `201`: 

---

#### GET /docs/{id}

**操作ID**: `DocumentController_getDocument`

**参数**:

- `id` (string, 必需, path)

**响应**:

- `200`: 

---

### 研究项目列表

#### GET /research-projects/all-projects

**摘要**: 获取所有研究项目

**操作ID**: `ResearchProjectsController_getAllResearchProjects`

**响应**:

- `200`: 

---

#### GET /research-projects/project-by-id

**摘要**: 获取对应 ID 的项目

**操作ID**: `ResearchProjectsController_getProjectByID`

**参数**:

- `project_id` (string, 必需, query)

**响应**:

- `200`: 

---

#### GET /research-projects/all-project-statuses

**摘要**: 获取所有项目状态

**操作ID**: `ResearchProjectsController_getAllResearchProjectStatuses`

**响应**:

- `200`: 

---

#### GET /research-projects/all-project-phases

**摘要**: 获取所有项目研究期数

**操作ID**: `ResearchProjectsController_getAllResearchProjectPhases`

**参数**:

- `order_by` (string, 必需, query)

**响应**:

- `200`: 

---

#### GET /research-projects/all-research-sponsors

**摘要**: 获取所有研究申办方

**操作ID**: `ResearchProjectsController_getAllResearchSponsors`

**响应**:

- `200`: 

---

#### GET /research-projects/all-projects-members

**摘要**: 获取所有项目人员

**操作ID**: `ResearchProjectsController_getAllProjectsMembers`

**响应**:

- `200`: 

---

#### GET /research-projects/project-members

**摘要**: 获取对应项目下的所有人员

**操作ID**: `ResearchProjectsController_getProjectMembers`

**参数**:

- `project_id` (string, 必需, query)

**响应**:

- `200`: 

---

#### GET /research-projects/all-project-role-types

**摘要**: 获取所有项目成员类型

**操作ID**: `ResearchProjectsController_getAllProjectRoleTypes`

**响应**:

- `200`: 

---

#### GET /research-projects/project-diseases

**摘要**: 获取所有项目疾病对应表

**操作ID**: `ResearchProjectsController_getProjectDiseases`

**响应**:

- `200`: 

---

#### GET /research-projects/all-diseases

**摘要**: 获取所有研究疾病

**操作ID**: `ResearchProjectsController_getAllDiseases`

**响应**:

- `200`: 

---

#### GET /research-projects/status-with-id

**摘要**: 通过 statusID 获取对应的 ResearchProjectStatus

**操作ID**: `ResearchProjectsController_getResearchProjectStatusWithID`

**参数**:

- `status_id` (string, 必需, query)

**响应**:

- `200`: 

---

#### POST /research-projects/create-project-filing

**摘要**: 创建项目备案表单

**操作ID**: `ResearchProjectsController_createProjectFiling`

**请求体**:

- 必需
- Content-Type: `application/json`
- Schema: `CreateProjectFilingDto`

**响应**:

- `201`: 

---

#### GET /research-projects/all-research-object-types

**摘要**: 获取所有研究对象类型

**操作ID**: `ResearchProjectsController_getAllResearchObjectTypes`

**响应**:

- `200`: 

---

#### GET /research-projects/research-project

**摘要**: 获取一个研究项目

**操作ID**: `ResearchProjectsController_getResearchProject`

**参数**:

- `project_id` (string, 必需, query)

**响应**:

- `200`: 

---

#### GET /research-projects/{projectID}/event-statistics

**摘要**: 获取研究项目临床事件统计数据

**操作ID**: `ResearchProjectsController_getClinicalEventStatistics`

**参数**:

- `projectID` (string, 必需, path)

**响应**:

- `200`: 

---

#### POST /research-projects/{projectID}/update-project-basic-info

**摘要**: 编辑项目基本信息

**操作ID**: `ResearchProjectsController_updateProjectBasicInfo`

**参数**:

- `projectID` (string, 必需, path)

**请求体**:

- 必需
- Content-Type: `application/json`
- Schema: `ResearchProjectEditBasicInfoDto`

**响应**:

- `201`: 

---

#### GET /research-projects/temp-jsj-projects

**摘要**: 获取金数据项目列表

**操作ID**: `ResearchProjectsController_getTempJSJProjects`

**响应**:

- `200`: 

---

#### GET /research-projects/mbgl-projects

**摘要**: 获取慢病管理系统的项目列表

**操作ID**: `ResearchProjectsController_getMbglProjects`

**响应**:

- `200`: 

---

#### POST /research-projects/{projectID}/sync-to-mbgl

**摘要**: 同步研究项目到慢病管理系统中

**操作ID**: `ResearchProjectsController_syncResearchProjectsToMbgl`

**参数**:

- `projectID` (string, 必需, path)

**响应**:

- `201`: 

---

#### GET /research-projects/subjects/{id}/subjects

**摘要**: 获取研究项目下的所有受试者

**操作ID**: `SubjectController_getSubjectsByProjectId`

**参数**:

- `id` (string, 必需, path)

**响应**:

- `default`: 

---

#### GET /research-projects/subjects/{id}/subject/{subjectId}/events

**摘要**: 获取一位受试者的所有事件

**操作ID**: `SubjectController_getSubjectEvents`

**参数**:

- `id` (string, 必需, path)
- `subjectId` (string, 必需, path)

**响应**:

- `default`: 

---

#### GET /research-projects/subjects/{id}/patient

**摘要**: 通过受试者 Id 获取患者信息

**操作ID**: `SubjectController_getPatientBySubjectId`

**参数**:

- `id` (number, 必需, path)

**响应**:

- `default`: 
  - Content-Type: `application/json`, Schema: `GetPatientResponseDto`

---

#### GET /research-projects/subjects/{id}/project

**摘要**: 通过受试者 Id 获取研究项目信息

**操作ID**: `SubjectController_getProjectBySubjectId`

**参数**:

- `id` (number, 必需, path)

**响应**:

- `default`: 
  - Content-Type: `application/json`, Schema: `ProjectQueryResponseDto`

---

#### GET /research-projects/subjects/analyzer/subject-event

**摘要**: 获取受试者事件总量

**操作ID**: `SubjectController_getSubjectEventTotal`

**参数**:

- `startDate` (string, 必需, query)
- `endDate` (string, 必需, query)
- `subjectEventTypeId` (number, 可选, query)

**响应**:

- `200`: 获取受试者事件总量成功

---

#### POST /ddd-research-projects

**摘要**: 创建研究项目

**操作ID**: `DDDResearchProjectsController_createResearchProject`

**请求体**:

- 必需
- Content-Type: `application/json`
- Schema: `CreateProjectRequestDto`

**响应**:

- `201`: 
  - Content-Type: `application/json`, Schema: `CreateProjectResponseDto`
- `409`: 
    存在项目冲突, 检查以下信息:

    - 存在相同项目代码的研究项目
    

---

#### GET /ddd-research-projects

**摘要**: 获取所有研究项目

**操作ID**: `DDDResearchProjectsController_getAllResearchProjects`

**响应**:

- `200`: 

---

#### GET /ddd-research-projects/{id}

**摘要**: 获取对应项目

**操作ID**: `DDDResearchProjectsController_getProjectByID`

**参数**:

- `id` (string, 必需, path): 项目 ID

**响应**:

- `200`: 
  - Content-Type: `application/json`, Schema: `GetProjectDetailsResponseDto`
- `404`: 项目不存在

---

#### PATCH /ddd-research-projects/{id}

**摘要**: 更新研究项目

**操作ID**: `DDDResearchProjectsController_updateResearchProject`

**参数**:

- `id` (string, 必需, path)

**请求体**:

- 必需
- Content-Type: `application/json`
- Schema: `UpdateResearchProjectRequestDto`

**响应**:

- `200`: 
  - Content-Type: `application/json`, Schema: `UpdateResearchProjectResponseDto`

---

#### POST /ddd-research-projects/{projectID}/members

**摘要**: 添加项目成员

**操作ID**: `DDDResearchProjectsController_addProjectMember`

**参数**:

- `projectID` (string, 必需, path)

**请求体**:

- 必需
- Content-Type: `application/json`
- Schema: `AddMemberRequestDto`

**响应**:

- `200`: 
  - Content-Type: `application/json`, Schema: `AddMemberResponseDto`

---

#### PUT /ddd-research-projects/{projectID}/members

**摘要**: 更新项目成员

**操作ID**: `DDDResearchProjectsController_updateProjectMember`

**参数**:

- `projectID` (string, 必需, path)

**请求体**:

- 必需
- Content-Type: `application/json`
- Schema: `UpdateProjectMemberRequestDto`

**响应**:

- `200`: 
  - Content-Type: `application/json`, Schema: `UpdateProjectMemberResponseDto`

---

#### DELETE /ddd-research-projects/{projectID}/members/{memberID}

**摘要**: 移除项目成员

**操作ID**: `DDDResearchProjectsController_removeProjectMember`

**响应**:

- `200`: 

---

#### GET /ddd-research-projects/{projectID}/sync-to-mbgl

**摘要**: 同步项目到慢病管理系统

**操作ID**: `DDDResearchProjectsController_syncProjectToMbgl`

**参数**:

- `projectID` (string, 必需, path): 项目 ID

**响应**:

- `200`: 

---

### 访视

#### POST /visits/reserve/from-goldata

**摘要**: 从Goldata创建访视预约

**操作ID**: `VisitsController_createVisitReserve`

**请求体**:

- 必需
- Content-Type: `application/json`
- Schema: `CreateVisitReserveRequestDto`

**响应**:

- `201`: 

---

#### POST /visits/complete-info-record/from-goldata

**摘要**: 从Goldata创建完整信息记录

**操作ID**: `VisitsController_createVisitCompleteRecord`

**请求体**:

- 必需
- Content-Type: `application/json`
- Schema: `CreateVisitCompleteRecordRequestDto`

**响应**:

- `201`: 信息记录创建成功
- `400`: 无效数据

---

#### POST /visits/complete-info-record/from-gcpm-frontend

**摘要**: 从 gcpm-frontend 创建结束访视记录

**操作ID**: `VisitsController_createVisitCompleteRecordFromGCPMFrontend`

**请求体**:

- 必需
- Content-Type: `application/json`
- Schema: `FinishVisitCreateDto`

**响应**:

- `201`: 结束访视记录创建成功
- `400`: 无效数据

---

#### GET /visits/complete-info-record/history

**摘要**: 获取历史结束访视记录

**操作ID**: `VisitsController_getHistoryVisitCompleteRecords`

**参数**:

- `encrypted_reserve_infos` (string, 必需, query)
- `user_id` (string, 必需, query)

**响应**:

- `200`: 成功获取所有历史结束访视记录

---

#### GET /visits/complete-info-record/current-history

**摘要**: 获取最新的历史结束访视记录

**操作ID**: `VisitsController_getCurrentHistoryVisitCompleteRecord`

**参数**:

- `encrypted_reserve_infos` (string, 必需, query)
- `user_id` (string, 必需, query)

**响应**:

- `200`: 成功获取最新历史结束访视记录
  - Content-Type: `application/json`, Schema: `VisitCompleteRecords`

---

#### GET /visits

**摘要**: 获取所有访视

**操作ID**: `VisitsController_findAll`

**响应**:

- `200`: 成功获取所有访视

---

#### GET /visits/{id}

**摘要**: 通过ID获取访视

**操作ID**: `VisitsController_findOne`

**参数**:

- `id` (string, 必需, path)

**响应**:

- `200`: 成功获取访视
  - Content-Type: `application/json`, Schema: `VisitReserveResponseDto`

---

#### PATCH /visits/{id}

**摘要**: 更新访视

**操作ID**: `VisitsController_update`

**参数**:

- `id` (string, 必需, path)

**请求体**:

- 必需
- Content-Type: `application/json`
- Schema: `UpdateVisitDto`

**响应**:

- `200`: 访视更新成功

---

#### DELETE /visits/{id}

**摘要**: 删除访视

**操作ID**: `VisitsController_remove`

**参数**:

- `id` (string, 必需, path)

**响应**:

- `200`: 访视删除成功

---

#### GET /visits/reserve/date/{date}

**摘要**: 通过日期获取访视预约

**操作ID**: `VisitsController_findByReserveDate`

**参数**:

- `date` (string, 必需, path): 日期格式: YYYY-MM-DD

**响应**:

- `200`: 成功获取访视预约

---

#### GET /visits/reserve/department-staff

**摘要**: 获取科室人员信息表

**操作ID**: `VisitsController_getDepartmentStaff`

**响应**:

- `200`: 成功获取科室人员信息表

---

#### GET /visits/reserve/is-visit-complete

**摘要**: 查询一条访视预约记录是否已经登记过结束

**操作ID**: `VisitsController_isVisitComplete`

**参数**:

- `reserveID` (string, 必需, query)

**响应**:

- `200`: 成功获取访视预约记录是否已经登记过结束
  - Content-Type: `application/json`, Schema: `IsVisitCompleteDto`

---

#### POST /visits/reserve

**摘要**: 创建访视预约

**操作ID**: `VisitsController_createVisitReserveFromClinicalData`

**请求体**:

- 必需
- Content-Type: `application/json`
- Schema: `ClinicalDataVisitReserveCreateRequestDto`

**响应**:

- `201`: 

---

#### POST /visits/complete-info-record/mbgl-sync

**摘要**: 同步访视登记到慢病管理系统

**操作ID**: `VisitsController_syncFinishVisitToMBGL`

**请求体**:

- 必需
- Content-Type: `application/json`
- Schema: `FinishVisitCreateDto`

**响应**:

- `201`: 

---

#### POST /visits/complete-info-record/mbgl-check

**摘要**: 检查访视登记表单是否是合法的慢病管理系统表单

**操作ID**: `VisitsController_checkVisitFormValid`

**请求体**:

- 必需
- Content-Type: `application/json`
- Schema: `FinishVisitMbglCheckDto`

**响应**:

- `200`: 访视登记表单是合法的慢病管理系统表单（如果成功恒为 true，失败会抛出异常）

---

#### POST /visits/reserve/patient-state

**摘要**: 获取患者状态

**操作ID**: `VisitsController_getPatientState`

**请求体**:

- 必需
- Content-Type: `application/json`
- Schema: `ReserveVisitPatientStateDto`

**响应**:

- `default`: 

---

#### PATCH /visits/reserve/{id}

**摘要**: 更新访视预约

**操作ID**: `VisitsController_updateVisitReserve`

**参数**:

- `id` (string, 必需, path): 访视预约ID

**请求体**:

- 必需
- Content-Type: `application/json`
- Schema: `UpdateVisitReserveDto`

**响应**:

- `200`: 访视预约更新成功

---

#### GET /visits/reserve/{id}

**摘要**: 获取一个访视预约的详细信息

**操作ID**: `VisitsController_getVisitReserveDetail`

**参数**:

- `id` (number, 必需, path): 访视预约ID

**响应**:

- `default`: 
  - Content-Type: `application/json`, Schema: `VisitReserveResponseDto`

---

#### GET /visits/reserve/{id}/check-mbgl-sync

**摘要**: 检查访视预约是否登记过，以及是否登记/同步成功

**操作ID**: `VisitsController_checkVisitReserveSyncToMBGL`

**参数**:

- `id` (number, 必需, path): 访视预约ID

**响应**:

- `200`: 检查访视预约是否登记过，以及是否登记/同步成功
  - Content-Type: `application/json`, Schema: `ReserveCheckMBGLSyncResultDto`

---

#### GET /visits/reserve/{id}/resync-mbgl

**摘要**: 重新同步访视预约到慢病管理系统

**操作ID**: `VisitsController_resyncVisitReserveToMBGL`

**参数**:

- `id` (number, 必需, path): 访视预约ID

**响应**:

- `200`: 重新同步访视预约到慢病管理系统

---

#### POST /visits/reserve/crc

**摘要**: CRC访视预约的创建

**操作ID**: `VisitsController_crcCreateReserveVisit`

**请求体**:

- 必需
- Content-Type: `application/json`
- Schema: `CrcCreateReserveVisitDto`

**响应**:

- `201`: 

---

#### GET /visits/reserve/analyzer/total

**摘要**: 获取访视预约总量

**操作ID**: `VisitsController_getVisitTotal`

**参数**:

- `startDate` (string, 必需, query)
- `endDate` (string, 必需, query)

**响应**:

- `200`: 获取访视预约总量成功

---

### 项目成员用户列表

#### GET /ddd-research-projects-temp-user

**摘要**: 获取项目成员用户列表

**操作ID**: `DDDResearchProjectsTempUserController_getUser`

**参数**:

- `username` (string, 必需, query): 用户名

**响应**:

- `200`: 

---

