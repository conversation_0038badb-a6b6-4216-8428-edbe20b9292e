---
noteId: "e78d94303e9511f0b4ec13362c7e567c"
tags: []

---

# GCPM API Postman Collection Guide

## Overview

This comprehensive Postman collection provides complete API testing capabilities for the GCPM (Good Clinical Practice Management) system. The collection includes all available endpoints with proper authentication, request examples, and automated test scripts.

## Files Included

1. **GCPM_API_Postman_Collection.json** - Main Postman collection file
2. **GCPM_API_Environment.postman_environment.json** - Environment variables file
3. **GCPM_API_Postman_Guide.md** - This guide

## Quick Start

### 1. Import Collection and Environment

1. Open Postman
2. Click "Import" button
3. Import both files:
   - `GCPM_API_Postman_Collection.json`
   - `GCPM_API_Environment.postman_environment.json`

### 2. Configure Environment

1. Select "GCPM API Environment" from the environment dropdown
2. Update the following variables:
   - `base_url`: Your GCPM API server URL (default: http://localhost:11451)
   - `username`: Your login username
   - `password`: Your login password

### 3. Authenticate

1. Navigate to "Authentication" → "Login"
2. Update the request body with your credentials
3. Send the request
4. The `auth_token` will be automatically set for subsequent requests

## Collection Structure

### 1. Authentication
- **Login**: Authenticate and obtain access token
- **Register**: Create new user account

### 2. Patient Management
- **Create Patient**: Add new patient records
- **Get Patients with Pagination**: Retrieve patient lists with filtering
- **Get Patient by ID**: Fetch detailed patient information

### 3. Visit Management
- **Create Visit Reserve from Goldata**: Schedule visits from external platform
- **Get Visits by Date**: Retrieve visits for specific dates
- **Get All Visits**: Fetch all visit records
- **Get Visit by ID**: Get detailed visit information

### 4. Research Projects
- **Get All Research Projects**: List all research projects
- **Create Research Project**: Add new research projects
- **Get Project by ID**: Retrieve project details

### 5. CRC Work Panel
- **Create Reserve Visit**: Schedule visits through CRC interface
- **Get Reserve Visit with Pagination**: List CRC visit reservations
- **Create Quality Control AE Event**: Record adverse events

### 6. Clinical Device Management
- **Get All Clinical Devices**: List clinical devices
- **Create Clinical Device Event**: Record device events

### 7. Reference Data
- **Get All Diseases**: Retrieve disease lookup data
- **Get All Sponsors**: Fetch sponsor information
- **Get All Project Phases**: List project phases

## Environment Variables

### Core Configuration
- `base_url`: API server base URL
- `auth_token`: Authentication token (auto-set)
- `username`: Login username
- `password`: Login password

### Test Data IDs
- `patient_id`: Sample patient ID (auto-set after creation)
- `project_id`: Sample project ID (auto-set after creation)
- `visit_id`: Sample visit ID
- `device_id`: Sample device ID
- `mbgl_user_id`: MBGL system user ID
- `mbgl_project_id`: MBGL system project ID

### Common Parameters
- `current_date`: Current date for testing (YYYY-MM-DD format)
- `pagination_limit`: Default pagination limit
- `pagination_cursor`: Pagination cursor
- `crc_name`: Sample CRC name

## Authentication

The collection uses Bearer token authentication. After successful login:

1. The `auth_token` is automatically extracted and stored
2. All subsequent requests include the token in the Authorization header
3. Token is valid for 30 days (configurable on server)

## Test Scripts

Each request includes automated test scripts that verify:

- **Response Status**: Checks for expected HTTP status codes
- **Response Structure**: Validates JSON response format
- **Data Integrity**: Ensures required fields are present
- **Performance**: Monitors response times
- **Auto-Variable Setting**: Extracts and stores IDs for subsequent requests

## Common Request Patterns

### Pagination
Most list endpoints support pagination with these parameters:
- `limit`: Number of records per page (default: 10)
- `cursor`: Pagination cursor for next page
- `name`: Optional name filter
- `phone`: Optional phone filter

### Date Formats
- Use ISO 8601 format: `YYYY-MM-DD`
- Example: `2024-01-15`

### Error Handling
The collection includes comprehensive error handling:
- 400: Bad Request (invalid parameters)
- 401: Unauthorized (authentication required)
- 403: Forbidden (insufficient permissions)
- 404: Not Found (resource doesn't exist)
- 409: Conflict (resource already exists)

## Sample Workflows

### 1. Complete Patient Management Workflow
1. Login → Get auth token
2. Create Patient → Get patient ID
3. Get Patient by ID → Verify creation
4. Get Patients with Pagination → List all patients

### 2. Visit Scheduling Workflow
1. Login → Get auth token
2. Get All Research Projects → Select project
3. Create Reserve Visit → Schedule visit
4. Get Visits by Date → Verify scheduling

### 3. Quality Control Workflow
1. Login → Get auth token
2. Create Quality Control AE Event → Record adverse event
3. Get Quality Control Events → Review events

## Troubleshooting

### Common Issues

1. **401 Unauthorized**
   - Ensure you've logged in and have a valid token
   - Check if token has expired (30-day validity)

2. **404 Not Found**
   - Verify the base_url is correct
   - Check if the API server is running

3. **400 Bad Request**
   - Validate request body format
   - Check required fields are provided
   - Ensure data types match API expectations

### Debug Tips

1. **Check Console**: View test results and logs in Postman console
2. **Inspect Variables**: Verify environment variables are set correctly
3. **Review Documentation**: Refer to API documentation for detailed requirements

## Advanced Features

### Pre-request Scripts
- Automatic timestamp generation
- Dynamic data preparation
- Environment variable validation

### Test Automation
- Comprehensive response validation
- Automatic ID extraction and storage
- Performance monitoring
- Error detection and reporting

## Support

For additional support:
1. Review the complete API documentation in `/docs/gcpm-api-documentation.md`
2. Check the quick reference guide in `/docs/gcpm-api-quick-reference.md`
3. Consult the API summary in `/docs/gcpm-api-summary.md`

## Version Information

- **Collection Version**: 1.0.0
- **API Version**: Based on OpenAPI 3.0.0 specification
- **Last Updated**: January 2024

This collection provides comprehensive testing capabilities for the GCPM API and will be updated as the API evolves.
