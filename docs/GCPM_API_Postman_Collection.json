{"info": {"name": "GCPM API Collection", "description": "Comprehensive Postman collection for GCPM (Good Clinical Practice Management) API testing. This collection includes all available endpoints with proper authentication, request examples, and test scripts.", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Global pre-request script", "console.log('Making request to:', pm.request.url.toString());"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Global test script", "pm.test('Response time is less than 5000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});", "", "pm.test('Response has valid JSON', function () {", "    try {", "        pm.response.json();", "    } catch (e) {", "        pm.expect.fail('Response is not valid JSON');", "    }", "});"]}}], "variable": [{"key": "base_url", "value": "http://localhost:11451", "type": "string", "description": "Base URL for the GCPM API"}, {"key": "auth_token", "value": "", "type": "string", "description": "Bearer token for authentication"}, {"key": "patient_id", "value": "", "type": "string", "description": "Sample patient ID for testing"}, {"key": "project_id", "value": "", "type": "string", "description": "Sample project ID for testing"}, {"key": "visit_id", "value": "", "type": "string", "description": "<PERSON><PERSON> visit ID for testing"}], "item": [{"name": "Authentication", "description": "User authentication and authorization endpoints", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"your_username\",\n  \"password\": \"your_password\"\n}"}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}, "description": "Authenticate user and obtain access token"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains access token', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('access_token');", "    ", "    // Store token for subsequent requests", "    pm.collectionVariables.set('auth_token', responseJson.access_token);", "});", "", "pm.test('Response contains user information', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('user');", "});"]}}]}, {"name": "Register", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"new_user\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"secure_password\"\n}"}, "url": {"raw": "{{base_url}}/auth/register", "host": ["{{base_url}}"], "path": ["auth", "register"]}, "description": "Register a new user account"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Registration successful', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('access_token');", "});"]}}]}]}, {"name": "Patient Management", "description": "Patient information management endpoints", "item": [{"name": "Create Patient", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"张三\",\n  \"phone\": \"***********\",\n  \"gender\": 1,\n  \"birthday\": \"1990-01-01\",\n  \"address\": \"北京市朝阳区\",\n  \"idNumber\": \"110101199001011234\",\n  \"diseases\": [1, 2]\n}"}, "url": {"raw": "{{base_url}}/patients", "host": ["{{base_url}}"], "path": ["patients"]}, "description": "Create a new patient record"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200 or 201', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201]);", "});", "", "pm.test('Patient created successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('id');", "    ", "    // Store patient ID for subsequent requests", "    pm.collectionVariables.set('patient_id', responseJson.id);", "});"]}}]}, {"name": "Get Patients with Pagination", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/patients/pagination?limit=10&cursor=&name=&phone=", "host": ["{{base_url}}"], "path": ["patients", "pagination"], "query": [{"key": "limit", "value": "10", "description": "Number of records per page"}, {"key": "cursor", "value": "", "description": "Pagination cursor"}, {"key": "name", "value": "", "description": "Filter by patient name"}, {"key": "phone", "value": "", "description": "Filter by patient phone"}]}, "description": "Get paginated list of patients with optional filters"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains patient data', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});"]}}]}, {"name": "Get Patient by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/patients/{{patient_id}}", "host": ["{{base_url}}"], "path": ["patients", "{{patient_id}}"]}, "description": "Get detailed information about a specific patient"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Patient details returned', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('id');", "    pm.expect(responseJson).to.have.property('name');", "    pm.expect(responseJson).to.have.property('phone');", "});"]}}]}]}, {"name": "Visit Management", "description": "Visit scheduling and management endpoints", "item": [{"name": "Create Visit Reserve from Goldata", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"serialNumber\": 12345,\n  \"visit_date\": \"2024-01-15\",\n  \"user_id\": \"user123\",\n  \"user_name\": \"张三\",\n  \"phone\": \"***********\",\n  \"project_serial_number\": 67890,\n  \"project_id\": \"proj123\",\n  \"project_shortname\": \"临床试验A\",\n  \"crcname\": \"李医生\",\n  \"visit_name\": \"筛选访视\",\n  \"visit_type\": \"screening\",\n  \"lungfunctionovertime_list\": \"无\",\n  \"dispense_drug_needs\": 1,\n  \"drug_saving_type\": \"冷藏\",\n  \"drug_saving_location\": \"药房冰箱\",\n  \"goldata_created_at\": \"2024-01-01T10:00:00Z\",\n  \"goldata_updated_at\": \"2024-01-01T10:00:00Z\",\n  \"info_platform\": \"金数据\",\n  \"info_filling_duration\": 300,\n  \"patient_fasting_needs\": 1,\n  \"patient_blood_test_needs\": 1,\n  \"nurse_overtime_for_blood_test\": [],\n  \"special_examination\": []\n}"}, "url": {"raw": "{{base_url}}/visits/reserve/from-goldata", "host": ["{{base_url}}"], "path": ["visits", "reserve", "from-goldata"]}, "description": "Create a visit reservation from Goldata platform"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Visit reservation created', function () {", "    // Additional validation can be added based on response structure", "    pm.expect(pm.response.code).to.equal(201);", "});"]}}]}, {"name": "Get Visits by Date", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/visits/reserve/date/2024-01-15", "host": ["{{base_url}}"], "path": ["visits", "reserve", "date", "2024-01-15"]}, "description": "Get visit reservations for a specific date (format: YYYY-MM-DD)"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response is an array', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.be.an('array');", "});"]}}]}, {"name": "Get All Visits", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/visits", "host": ["{{base_url}}"], "path": ["visits"]}, "description": "Get all visits in the system"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});"]}}]}, {"name": "Get Visit by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/visits/{{visit_id}}", "host": ["{{base_url}}"], "path": ["visits", "{{visit_id}}"]}, "description": "Get detailed information about a specific visit"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Visit details returned', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.be.an('object');", "});"]}}]}]}, {"name": "Research Projects", "description": "Research project management endpoints", "item": [{"name": "Get All Research Projects", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/research-projects/all-projects", "host": ["{{base_url}}"], "path": ["research-projects", "all-projects"]}, "description": "Get all research projects in the system"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains projects array', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.be.an('array');", "    ", "    if (responseJson.length > 0) {", "        pm.collectionVariables.set('project_id', responseJson[0].id);", "    }", "});"]}}]}, {"name": "Create Research Project", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"projectFullname\": \"新药临床试验研究项目\",\n  \"projectCode\": \"PROJ2024001\",\n  \"launchDate\": \"2024-01-01\",\n  \"sponsorId\": \"sponsor123\",\n  \"objectTypeId\": \"type123\",\n  \"diseaseIds\": [1, 2],\n  \"phaseId\": \"phase123\",\n  \"statusId\": \"status123\"\n}"}, "url": {"raw": "{{base_url}}/research-projects/create-project", "host": ["{{base_url}}"], "path": ["research-projects", "create-project"]}, "description": "Create a new research project"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200 or 201', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201]);", "});", "", "pm.test('Project created successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('projectID');", "    pm.collectionVariables.set('project_id', responseJson.projectID);", "});"]}}]}, {"name": "Get Project by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/research-projects/project-by-id?projectID={{project_id}}", "host": ["{{base_url}}"], "path": ["research-projects", "project-by-id"], "query": [{"key": "projectID", "value": "{{project_id}}", "description": "Project ID to retrieve"}]}, "description": "Get detailed information about a specific research project"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Project details returned', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('id');", "    pm.expect(responseJson).to.have.property('name');", "});"]}}]}]}, {"name": "CRC Work Panel", "description": "<PERSON><PERSON> (Clinical Research Coordinator) work panel endpoints", "item": [{"name": "Create Reserve Visit", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"mbglUserID\": \"12345\",\n  \"mbglProjectID\": \"67890\",\n  \"visitDate\": \"2024-01-15\",\n  \"visitName\": \"筛选访视\",\n  \"lungfunctionovertimeList\": [],\n  \"patientFastingNeeds\": \"是\",\n  \"patientBloodTestNeeds\": \"是\",\n  \"needECG\": \"否\",\n  \"needCentrifuge\": \"是\",\n  \"needFreezer\": \"是\",\n  \"patientStayInCenter\": \"否\",\n  \"nurseOvertimeForBloodTest\": [],\n  \"specialExamination\": [],\n  \"dispenseDrugNeeds\": \"是\",\n  \"drugSavingLocation\": \"药房冰箱\",\n  \"needCenterVisitAssistance\": [],\n  \"remark\": \"常规筛选访视\",\n  \"specialExaminationOtherInfo\": \"\",\n  \"needCenterVisitAssistanceOtherInfo\": \"\",\n  \"drugSavingLocationOtherInfo\": \"\",\n  \"mainVisitContent\": [\"体格检查\", \"实验室检查\"],\n  \"mainVisitContentOtherInfo\": \"\",\n  \"isUnplannedVisit\": false\n}"}, "url": {"raw": "{{base_url}}/crc/reserve-visit", "host": ["{{base_url}}"], "path": ["crc", "reserve-visit"]}, "description": "Create a visit reservation through CRC work panel"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Visit reservation created successfully', function () {", "    pm.expect(pm.response.code).to.equal(201);", "});"]}}]}, {"name": "Get Reserve Visit with Pagination", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/crc/reserve-visit/pagination?limit=10&cursor=&visitName=&visitDate=", "host": ["{{base_url}}"], "path": ["crc", "reserve-visit", "pagination"], "query": [{"key": "limit", "value": "10", "description": "Number of records per page"}, {"key": "cursor", "value": "", "description": "Pagination cursor (visit reservation ID)"}, {"key": "visitName", "value": "", "description": "Filter by visit name (optional)"}, {"key": "visitDate", "value": "", "description": "Filter by visit date (optional)"}]}, "description": "Get paginated list of visit reservations for CRC"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains visit data', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});"]}}]}, {"name": "Create Quality Control AE Event", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"mbglPatientID\": \"12345\",\n  \"mbglProjectID\": \"67890\",\n  \"judgementDoctorId\": \"doctor123\",\n  \"detail\": \"患者出现轻微头痛症状\",\n  \"date\": \"2024-01-15\",\n  \"drugRelated\": \"可能相关\"\n}"}, "url": {"raw": "{{base_url}}/crc/quality-control/ae", "host": ["{{base_url}}"], "path": ["crc", "quality-control", "ae"]}, "description": "Create a quality control AE (Adverse Event) event"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('AE event created successfully', function () {", "    pm.expect(pm.response.code).to.equal(201);", "});"]}}]}]}, {"name": "Clinical Device Management", "description": "Clinical device management endpoints", "item": [{"name": "Get All Clinical Devices", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/clinical-device?skip=0&take=10&orderBy=id", "host": ["{{base_url}}"], "path": ["clinical-device"], "query": [{"key": "skip", "value": "0", "description": "Number of records to skip"}, {"key": "take", "value": "10", "description": "Number of records to take"}, {"key": "orderBy", "value": "id", "description": "Field to order by"}]}, "description": "Get all clinical devices with pagination"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains devices array', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.be.an('array');", "});"]}}]}, {"name": "Create Clinical Device Event", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"createdAt\": \"2024-01-15T10:00:00Z\",\n  \"updatedAt\": \"2024-01-15T10:00:00Z\",\n  \"eventDate\": \"2024-01-15\",\n  \"deviceId\": 1,\n  \"managementEventType\": \"温度检查\",\n  \"minTemperature\": 2.0,\n  \"maxTemperature\": 8.0,\n  \"clinicalProjectId\": \"proj123\",\n  \"crcName\": \"李医生\",\n  \"drugExpirationDate\": \"2025-01-15\",\n  \"createBy\": \"admin\"\n}"}, "url": {"raw": "{{base_url}}/clinical-device/event/from-goldata", "host": ["{{base_url}}"], "path": ["clinical-device", "event", "from-goldata"]}, "description": "Create a clinical device event from Goldata"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Device event created successfully', function () {", "    pm.expect(pm.response.code).to.equal(200);", "});"]}}]}]}, {"name": "Reference Data", "description": "Reference data and lookup endpoints", "item": [{"name": "Get All Diseases", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/reference-data/diseases", "host": ["{{base_url}}"], "path": ["reference-data", "diseases"]}, "description": "Get all available diseases"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});"]}}]}, {"name": "Get All Sponsors", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/reference-data/sponsors", "host": ["{{base_url}}"], "path": ["reference-data", "sponsors"]}, "description": "Get all available sponsors"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});"]}}]}, {"name": "Get All Project Phases", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/reference-data/research-project-phases", "host": ["{{base_url}}"], "path": ["reference-data", "research-project-phases"]}, "description": "Get all research project phases"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});"]}}]}]}]}