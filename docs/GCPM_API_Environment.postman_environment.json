{"id": "gcpm-api-environment", "name": "GCPM API Environment", "values": [{"key": "base_url", "value": "http://localhost:11451", "description": "Base URL for the GCPM API server", "type": "default", "enabled": true}, {"key": "auth_token", "value": "", "description": "Bearer token for API authentication (automatically set after login)", "type": "secret", "enabled": true}, {"key": "username", "value": "your_username", "description": "Username for login", "type": "default", "enabled": true}, {"key": "password", "value": "your_password", "description": "Password for login", "type": "secret", "enabled": true}, {"key": "patient_id", "value": "", "description": "Sample patient ID for testing (automatically set after creating a patient)", "type": "default", "enabled": true}, {"key": "project_id", "value": "", "description": "Sample project ID for testing (automatically set after creating a project)", "type": "default", "enabled": true}, {"key": "visit_id", "value": "", "description": "<PERSON><PERSON> visit ID for testing", "type": "default", "enabled": true}, {"key": "device_id", "value": "1", "description": "Sample device ID for testing", "type": "default", "enabled": true}, {"key": "mbgl_user_id", "value": "12345", "description": "Sample MBGL (慢病管理系统) user ID", "type": "default", "enabled": true}, {"key": "mbgl_project_id", "value": "67890", "description": "Sample MBGL (慢病管理系统) project ID", "type": "default", "enabled": true}, {"key": "current_date", "value": "2024-01-15", "description": "Current date for testing (format: YYYY-MM-DD)", "type": "default", "enabled": true}, {"key": "pagination_limit", "value": "10", "description": "Default pagination limit", "type": "default", "enabled": true}, {"key": "pagination_cursor", "value": "", "description": "Pagination cursor for next page", "type": "default", "enabled": true}, {"key": "sponsor_id", "value": "sponsor123", "description": "Sample sponsor ID for testing", "type": "default", "enabled": true}, {"key": "object_type_id", "value": "type123", "description": "Sample object type ID for testing", "type": "default", "enabled": true}, {"key": "phase_id", "value": "phase123", "description": "Sample phase ID for testing", "type": "default", "enabled": true}, {"key": "status_id", "value": "status123", "description": "Sample status ID for testing", "type": "default", "enabled": true}, {"key": "doctor_id", "value": "doctor123", "description": "Sample doctor ID for testing", "type": "default", "enabled": true}, {"key": "crc_name", "value": "李医生", "description": "Sample CRC name for testing", "type": "default", "enabled": true}], "_postman_variable_scope": "environment"}