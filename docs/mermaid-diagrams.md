---
noteId: "51e653f03d2c11f09966d5386bb2acfc"
tags: []

---

# 临床研究项目管理系统 - Mermaid图表集合

本文档包含了系统的各种Mermaid图表，可用于文档、演示和系统设计参考。

## 1. 功能架构图

```mermaid
graph TB
    %% 主系统
    System["临床研究项目管理系统<br/>Clinical Research Project Management System"]

    %% 核心功能模块
    subgraph CoreModules["核心功能模块"]
        PM["项目管理<br/>Project Management"]
        RuleEngine["入排标准规则引擎<br/>Inclusion/Exclusion Criteria Rules"]
        Staff["人员管理<br/>Staff Management"]
        Dict["数据字典管理<br/>Data Dictionary Management"]
        Notes["智能笔记系统<br/>Intelligent Notes System"]
        Dashboard["数据分析仪表盘<br/>Data Analytics Dashboard"]
    end
    
    %% 外部系统集成
    subgraph ExternalIntegrations["外部系统集成"]
        Notion["Notion 集成<br/>Notion Integration"]
        Lighthouse["Lighthouse API 集成<br/>Lighthouse API Integration"]
        Referrer["转诊管理 API 集成<br/>Referral Management API"]
    end

    %% 系统配置与AI
    subgraph SystemConfig["系统配置与AI"]
        Config["系统配置管理<br/>System Configuration"]
        AI["AI 功能集成<br/>AI Integration with Langchain.js"]
    end

    %% 数据存储层
    subgraph DataStorage["数据存储层"]
        SQLite[("SQLite 数据库<br/>本地主数据库")]
        MongoDB[("MongoDB<br/>配置与集成数据")]
        LocalJSON["本地 JSON 文件<br/>静态配置"]
        Browser["浏览器存储<br/>客户端设置"]
        NotionDB[("Notion 数据库<br/>笔记存储")]
    end

    %% 技术架构层
    subgraph TechStack["技术架构"]
        Frontend["前端 SvelteKit + TypeScript"]
        Backend["后端 Tauri + Rust"]
        APIs["外部 API 服务"]
    end

    %% 项目管理详细功能
    subgraph PMDetails["项目管理功能详情"]
        PMBasic["项目基本信息管理<br/>• 项目创建与编辑<br/>• 状态跟踪<br/>• 招募状态管理"]
        PMSponsor["申办方管理<br/>• 多申办方支持<br/>• 申办方搜索"]
        PMDrug["研究药物管理<br/>• 药物信息录入<br/>• 药物分组管理"]
        PMPersonnel["人员角色分配<br/>• 研究人员管理<br/>• 权限控制<br/>• PI 标识"]
        PMSubsidy["补贴方案管理<br/>• 补贴项目配置<br/>• 费用计算"]
        PMFile["项目文件管理<br/>• 文件夹访问<br/>• 文档管理"]
    end
    
    %% 连接关系
    System --> CoreModules
    System --> ExternalIntegrations
    System --> SystemConfig
    
    %% 核心模块连接
    PM --> PMDetails
    
    %% 数据存储连接
    PM --> SQLite
    RuleEngine --> SQLite
    Staff --> SQLite
    Dict --> SQLite
    Dashboard --> SQLite
    
    Config --> MongoDB
    Notion --> NotionDB
    Notes --> NotionDB
    
    %% AI 集成连接
    AI --> RuleEngine
    AI --> Notes
    
    %% 外部集成连接
    Notion --> Notes
    Lighthouse --> Staff
    Referrer --> Staff
    
    %% 技术架构连接
    Frontend --> Backend
    Backend --> APIs
    Backend --> DataStorage
    
    %% 样式定义
    classDef coreModule fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef external fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef storage fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef tech fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef detail fill:#fafafa,stroke:#424242,stroke-width:1px
    
    class PM,RuleEngine,Staff,Dict,Notes,Dashboard coreModule
    class Notion,Lighthouse,Referrer external
    class SQLite,MongoDB,LocalJSON,Browser,NotionDB storage
    class Frontend,Backend,APIs tech
    class PMDetails,PMBasic,PMSponsor,PMDrug,PMPersonnel,PMSubsidy,PMFile detail
```

## 2. 系统架构概览

```mermaid
graph TD
    %% 用户界面层
    subgraph UI["用户界面层 - SvelteKit Frontend"]
        ProjectUI["项目管理界面"]
        CriteriaUI["入排标准界面"]
        StaffUI["人员管理界面"]
        DictUI["数据字典界面"]
        NotesUI["智能笔记界面"]
        DashboardUI["仪表盘界面"]
        SettingsUI["系统设置界面"]
    end

    %% 业务逻辑层
    subgraph BL["业务逻辑层 - Tauri Backend Rust"]
        ProjectService["项目管理服务"]
        RuleService["规则引擎服务"]
        StaffService["人员管理服务"]
        DictService["字典管理服务"]
        NotionService["Notion集成服务"]
        LighthouseService["Lighthouse API服务"]
        ConfigService["配置管理服务"]
        AIService["AI集成服务"]
    end
    
    %% 数据访问层
    subgraph DAL["数据访问层"]
        SQLiteRepo["SQLite 仓储"]
        MongoRepo["MongoDB 仓储"]
        FileRepo["文件系统仓储"]
    end

    %% 数据存储层
    subgraph Storage["数据存储层"]
        SQLiteDB[("SQLite 数据库<br/>项目、人员、字典、规则")]
        MongoDB[("MongoDB<br/>系统配置、API密钥")]
        LocalFiles["本地文件<br/>项目文档、配置文件"]
    end

    %% 外部服务层
    subgraph External["外部服务"]
        NotionAPI["Notion API<br/>笔记同步"]
        LighthouseAPI["Lighthouse API<br/>临床数据管理"]
        ReferrerAPI["转诊管理 API<br/>转诊关系管理"]
        OpenAI["OpenAI API<br/>AI功能支持"]
    end

    %% AI 处理层
    subgraph AI["AI 处理层 - Langchain.js"]
        TextClassifier["文本分类器<br/>笔记智能分类"]
        CriteriaGenerator["标准生成器<br/>入排标准自动生成"]
        ContentAnalyzer["内容分析器<br/>智能内容分析"]
    end
    
    %% 连接关系 - UI到服务
    ProjectUI --> ProjectService
    CriteriaUI --> RuleService
    StaffUI --> StaffService
    DictUI --> DictService
    NotesUI --> NotionService
    DashboardUI --> ProjectService
    SettingsUI --> ConfigService
    
    %% 连接关系 - 服务到仓储
    ProjectService --> SQLiteRepo
    RuleService --> SQLiteRepo
    StaffService --> SQLiteRepo
    DictService --> SQLiteRepo
    ConfigService --> MongoRepo
    NotionService --> FileRepo
    
    %% 连接关系 - 仓储到存储
    SQLiteRepo --> SQLiteDB
    MongoRepo --> MongoDB
    FileRepo --> LocalFiles
    
    %% 连接关系 - 服务到外部API
    NotionService --> NotionAPI
    LighthouseService --> LighthouseAPI
    ConfigService --> ReferrerAPI
    AIService --> OpenAI
    
    %% 连接关系 - AI集成
    NotesUI --> AI
    CriteriaUI --> AI
    AI --> TextClassifier
    AI --> CriteriaGenerator
    AI --> ContentAnalyzer
    
    %% 跨服务连接
    RuleService --> AIService
    NotionService --> AIService
    
    %% 样式定义
    classDef uiLayer fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef serviceLayer fill:#f1f8e9,stroke:#388e3c,stroke-width:2px
    classDef dataLayer fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef externalLayer fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef aiLayer fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    
    class ProjectUI,CriteriaUI,StaffUI,DictUI,NotesUI,DashboardUI,SettingsUI uiLayer
    class ProjectService,RuleService,StaffService,DictService,NotionService,LighthouseService,ConfigService,AIService serviceLayer
    class SQLiteRepo,MongoRepo,FileRepo,SQLiteDB,MongoDB,LocalFiles dataLayer
    class NotionAPI,LighthouseAPI,ReferrerAPI,OpenAI externalLayer
    class TextClassifier,CriteriaGenerator,ContentAnalyzer aiLayer
```

## 3. 数据流图

```mermaid
flowchart LR
    %% 用户操作起点
    User["👤 用户"]

    %% 主要功能模块
    subgraph Core["核心功能模块"]
        PM["📋 项目管理"]
        Rules["⚙️ 入排标准规则"]
        Staff["👥 人员管理"]
        Dict["📚 数据字典"]
        Notes["📝 智能笔记"]
        Dashboard["📊 数据仪表盘"]
    end

    %% 数据处理中心
    subgraph Processing["数据处理中心"]
        AI["🤖 AI处理引擎<br/>Langchain.js"]
        Export["📤 数据导出"]
        Import["📥 数据导入"]
    end

    %% 数据存储
    subgraph Storage["数据存储"]
        SQLite[("🗄️ SQLite<br/>主数据库")]
        Mongo[("🍃 MongoDB<br/>配置数据")]
        Files["📁 本地文件"]
    end

    %% 外部系统
    subgraph External["外部系统"]
        Notion["📋 Notion"]
        Lighthouse["🏥 Lighthouse"]
        Referrer["🔄 转诊系统"]
        OpenAI["🧠 OpenAI API"]
    end

    %% 用户交互流
    User --> PM
    User --> Rules
    User --> Staff
    User --> Dict
    User --> Notes
    User --> Dashboard

    %% 项目管理数据流
    PM --> SQLite
    PM --> Files
    PM --> Export
    Staff --> PM
    Dict --> PM

    %% 入排标准规则数据流
    Rules --> SQLite
    Rules --> AI
    AI --> Rules
    PM --> Rules

    %% 人员管理数据流
    Staff --> SQLite
    Staff --> Lighthouse
    Staff --> Referrer

    %% 数据字典数据流
    Dict --> SQLite
    Dict --> PM
    Dict --> Staff
    Dict --> Rules

    %% 智能笔记数据流
    Notes --> AI
    AI --> Notes
    Notes --> Notion
    Notion --> Notes

    %% 仪表盘数据流
    Dashboard --> SQLite
    PM --> Dashboard
    Staff --> Dashboard
    Rules --> Dashboard

    %% AI处理数据流
    AI --> OpenAI
    OpenAI --> AI

    %% 数据导入导出流
    Import --> PM
    Import --> Rules
    Import --> Staff
    Import --> Dict

    PM --> Export
    Rules --> Export
    Staff --> Export
    Dict --> Export

    %% 配置管理数据流
    Mongo --> PM
    Mongo --> Rules
    Mongo --> Notes
    Mongo --> External

    %% 文件管理数据流
    Files --> PM
    Export --> Files

    %% 样式定义
    classDef userStyle fill:#e8f5e8,stroke:#2e7d32,stroke-width:3px
    classDef coreStyle fill:#e3f2fd,stroke:#1565c0,stroke-width:2px
    classDef processStyle fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
    classDef storageStyle fill:#f1f8e9,stroke:#558b2f,stroke-width:2px
    classDef externalStyle fill:#fce4ec,stroke:#ad1457,stroke-width:2px

    class User userStyle
    class PM,Rules,Staff,Dict,Notes,Dashboard coreStyle
    class AI,Export,Import processStyle
    class SQLite,Mongo,Files storageStyle
    class Notion,Lighthouse,Referrer,OpenAI externalStyle
```

## 使用说明

### 在线预览和编辑
1. **Mermaid Live Editor**: https://mermaid.live/
2. **GitHub**: 直接在README.md中使用
3. **GitLab**: 支持Mermaid图表渲染
4. **Notion**: 使用代码块并选择mermaid语言

### 本地使用
1. **VS Code**: 安装Mermaid Preview插件
2. **Typora**: 原生支持Mermaid
3. **Obsidian**: 原生支持Mermaid
4. **Jupyter Notebook**: 使用mermaid-py包

### 导出格式
- PNG图片
- SVG矢量图
- PDF文档
- HTML页面

### 集成到项目文档
建议将这些图表集成到以下位置：
- README.md（系统概览）
- 技术文档（架构设计）
- 用户手册（功能说明）
- 演示文稿（项目展示）

## 4. 简化版图表（兼容性更好）

### 简化功能架构图
```mermaid
graph TB
    System["Clinical Research Management System"]

    subgraph Core["Core Modules"]
        PM["Project Management"]
        Rules["Criteria Rules Engine"]
        Staff["Staff Management"]
        Dict["Data Dictionary"]
        Notes["Smart Notes"]
        Dashboard["Analytics Dashboard"]
    end

    subgraph External["External Integrations"]
        Notion["Notion Integration"]
        Lighthouse["Lighthouse API"]
        Referrer["Referral Management"]
    end

    subgraph Storage["Data Storage"]
        SQLite[("SQLite Database")]
        MongoDB[("MongoDB")]
        Files["Local Files"]
    end

    System --> Core
    System --> External
    Core --> Storage
    External --> Storage

    classDef coreModule fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef external fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef storage fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px

    class PM,Rules,Staff,Dict,Notes,Dashboard coreModule
    class Notion,Lighthouse,Referrer external
    class SQLite,MongoDB,Files storage
```

### 简化系统架构图
```mermaid
graph TD
    subgraph UI["Frontend - SvelteKit"]
        ProjectUI["Project Management UI"]
        RulesUI["Criteria Rules UI"]
        StaffUI["Staff Management UI"]
        NotesUI["Smart Notes UI"]
    end

    subgraph Backend["Backend - Tauri Rust"]
        ProjectSvc["Project Service"]
        RulesSvc["Rules Service"]
        StaffSvc["Staff Service"]
        NotionSvc["Notion Service"]
    end

    subgraph Data["Data Layer"]
        SQLiteDB[("SQLite")]
        MongoDB[("MongoDB")]
        LocalFiles["Files"]
    end

    UI --> Backend
    Backend --> Data

    classDef frontend fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef backend fill:#f1f8e9,stroke:#388e3c,stroke-width:2px
    classDef data fill:#fff3e0,stroke:#f57c00,stroke-width:2px

    class ProjectUI,RulesUI,StaffUI,NotesUI frontend
    class ProjectSvc,RulesSvc,StaffSvc,NotionSvc backend
    class SQLiteDB,MongoDB,LocalFiles data
```
