## 数据迁移注意事项

### 核心原则

- **数据安全第一：** 任何操作前必须有可靠的备份和回滚计划。
- **充分测试：** 在与生产环境尽可能一致的测试环境中演练所有步骤。
- **最小化影响：** 尽量减少对生产环境的干扰和停机时间。
- **清晰沟通：** 确保所有相关方都了解变更内容、时间和潜在影响。
- **谨慎操作：** 尤其对于删除操作，务必确认数据不再被任何应用或报表依赖。

### 关键点

- 在计划过程中记录完整的依赖关系与变更
  - 新增字段
  - 删除字段
- 分离应用层依赖和数据库依赖
  - 首先将需要删除的数据库字段的依赖从应用程序层面移除
- 避免同时进行字段删除和新增
  - 参考后文的"假删除"过渡
- 应用迁移前检查变更

## 迁移工作工作清单

### TL;DR

**数据迁移核心原则与关键点：**

- **安全第一：** 任何操作前必须有可靠备份和回滚计划。
- **充分测试：** 在与生产环境一致的测试环境中演练所有步骤。
- **最小影响：** 尽量减少对生产环境的干扰和停机时间。
- **清晰沟通：** 确保所有相关方了解变更内容、时间和潜在影响。
- **谨慎操作：** 删除操作前务必确认数据不再被任何应用或报表依赖。
- **完整记录：** 在计划过程中记录完整的依赖关系与变更（新增/删除字段）。
- **分离依赖：** 先移除应用层对将删除数据库字段的依赖。
- **避免同时操作：** 不建议同时进行字段删除和新增，可考虑“假删除”过渡。
- **迁移前检查：** 应用迁移前务必检查所有变更。

**迁移工作清单核心阶段：**

1. **准备与规划：**
    - 明确需求与影响分析（特别是依赖分析）。
    - 制定备份与归档策略（尤其是待删除字段数据）。
    - 制定详细的执行与回滚计划（包括SQL脚本和应用代码变更）。
    - 制定沟通计划。
2. **开发与测试：**
    - 搭建与生产一致的测试环境。
    - 编写和测试SQL脚本及应用程序代码。
    - 在测试环境中完整演练所有变更和回滚计划。
    - 进行数据验证和性能测试。
3. **生产环境部署：**
    - 选择维护窗口并进行最终检查（包括最终备份和归档确认）。
    - 按顺序执行数据库变更（增加字段、填充数据、删除字段），**再次确认应用不再依赖将删除的字段**。
    - 部署/重启应用程序。
    - 进行快速验证。
4. **部署后监控与收尾：**
    - 强化监控，准备回滚。
    - 进行全面验证。
    - 管理好已归档的数据（不要立即删除）。
    - 更新相关文档。
    - 进行复盘与总结。

**避免数据丢失的关键强调：**

- **备份是生命线。**
- **极度谨慎对待删除操作：** 考虑“假删除”，优先修改应用层。
- **了解数据库特性**以优化操作。

### **工作流程阶段**

**阶段一：准备与规划 (Preparation & Planning) - 防患于未然的关键**

1. **1.1. 需求明确与影响分析 (Requirement Definition & Impact Analysis):**

    - **精确定义：** 准确列出需要增加的字段（名称、数据类型、是否允许NULL、默认值等）和需要删除的字段。
    - **依赖分析 (至关重要):**
        - **对于删除字段：** 彻底排查所有依赖该字段的应用程序、API接口、报表、ETL过程、存储过程、触发器等。确认删除后这些组件将如何调整，或确认它们已不再使用该字段。**这是避免因删除字段导致应用故障或隐性数据问题的核心。**
        - **对于增加字段：** 分析现有应用程序是否需要立即感知和使用新字段。如果新字段为 `NOT NULL` 且没有默认值，需要考虑如何处理现有记录。
    - **数据迁移逻辑：**
        - **对于增加字段：** 明确新字段的数据来源。是根据其他字段计算生成？是固定默认值？还是需要从外部源导入？如何处理现有记录的新字段填充？
        - **对于删除字段：** 虽然是删除，但其数据是否需要归档或迁移到其他地方（例如，归档表、NoSQL数据库）以备将来审计或特定查询？
    - **性能评估：** 预估字段增删操作（尤其是涉及大量数据表的默认值填充或数据迁移）对数据库性能的潜在影响。
2. **1.2. 备份与归档策略 (Backup & Archival Strategy):**

    - **数据库全量备份：** 在进行任何生产变更前，执行一次完整的、经过验证的数据库备份。确保备份文件安全存储，并且团队清楚如何进行恢复操作。
    - **特定表备份：** 对将要修改的表进行表级备份（如果数据库支持）。
    - **待删除字段数据专项归档 (极其重要)：** 在物理删除字段之前，务必将该字段的所有数据导出并安全存储（例如，存为CSV文件、存入专门的归档表或数据库）。记录归档数据的来源、时间和方法。**这是数据丢失的最后一道防线。**
3. **1.3. 制定详细的执行与回滚计划 (Detailed Execution & Rollback Plan):**

    - **变更脚本 (SQL Scripts):**
        - **增加字段脚本：** `ALTER TABLE ... ADD COLUMN ...` (明确数据类型、NULL约束、默认值)。
        - **数据填充脚本 (针对新增字段)：** `UPDATE ... SET new_column = ...` (如果需要为现有行填充数据，考虑分批处理大表以避免长时间锁定和日志压力)。
        - **删除字段脚本：** `ALTER TABLE ... DROP COLUMN ...`。
    - **应用程序代码变更计划：**
        - 对于删除字段：确保相关的应用程序代码已修改，不再读取或写入该字段。
        - 对于增加字段：如果应用程序需要使用新字段，确保代码已更新。
    - **回滚脚本/步骤：**
        - **Schema回滚：** 如何撤销字段的增加（`ALTER TABLE ... DROP COLUMN ...`）或重新添加已删除的字段（`ALTER TABLE ... ADD COLUMN ...`，需要原始的字段定义）。
        - **数据回滚：**
            - 对于误删字段：如何从归档数据中恢复。
            - 对于新增字段错误填充：如何清除或修正已填充的数据。
            - 最坏情况：从数据库全量备份中恢复。
        - **应用回滚：** 如何将应用程序代码回滚到变更前的版本。
    - **时间表与顺序：** 明确每个步骤的执行顺序和预计耗时，包括应用部署的协调。
4. **1.4. 沟通计划 (Communication Plan):**

    - 提前通知所有相关团队（开发、测试、运维、业务方）关于变更的内容、计划执行时间、预期的业务影响（例如，是否需要停机维护窗口）以及联系人。

**阶段二：开发与测试 (Development & Testing) - 在非生产环境验证一切**

1. **2.1. 搭建/准备测试环境 (Setup/Prepare Test Environment):**
    - 使用与生产环境尽可能一致的硬件配置、操作系统、数据库版本和应用版本。
    - 导入生产数据库的最新脱敏备份（或结构和代表性数据子集）。
2. **2.2. 编写与单元测试脚本 (Write & Unit Test Scripts):**
    - 编写所有需要的SQL脚本（增删字段、数据迁移/填充）。
    - 在开发数据库或隔离的测试实例上进行单元测试。
3. **2.3. 应用程序代码调整与测试 (Application Code Modification & Testing):**
    - 同步修改应用程序代码，使其适应数据库结构的变化。
    - 进行单元测试和集成测试。
4. **2.4. 在测试环境中完整演练 (Full Rehearsal in Test Environment):**
    - **执行所有变更脚本：** 模拟生产环境的顺序执行字段增加、数据迁移/填充、字段删除脚本。
    - **数据验证 (核心)：**
        - **新增字段：** 验证新字段是否已正确添加，数据类型、约束是否符合预期。验证为现有记录填充的数据是否准确无误。
        - **删除字段：** 验证字段是否已成功删除。更重要的是，验证归档的该字段数据是否完整和准确。
        - **其他数据：** 验证表内其他未修改字段的数据是否保持不变，未受影响。
    - **应用程序功能验证：** 部署更新后的应用程序到测试环境，全面测试所有与被修改表相关的功能模块，确保其按预期工作。特别关注CRUD（创建、读取、更新、删除）操作。
    - **性能测试：** 检查变更后，相关查询和操作的性能是否在可接受范围内。
    - **回滚计划演练 (关键)：** 在测试环境中完整执行一次回滚计划，确保所有步骤都可操作且能成功恢复到变更前状态。记录回滚所需时间。
5. **2.5. 优化与确认 (Refine & Confirm):**
    - 根据测试结果，优化脚本、调整计划、修正预估时间。
    - 获得相关负责人（如技术主管、测试经理）的确认。

**阶段三：生产环境部署 (Production Deployment) - 小心谨慎，严格按计划执行**

1. **3.1. 选择维护窗口 (Schedule Maintenance Window):**

    - 选择业务低峰期进行操作，以最小化对用户和业务的影响。
    - 再次向所有相关方确认维护窗口时间。
2. **3.2. 部署前最终检查 (Pre-Deployment Final Checks):**

    - **最终生产数据库备份：** 在开始任何操作前，立即执行一次新的、经验证的生产数据库全量备份。
    - **最终待删除字段数据归档确认：** 再次确认待删除字段的数据已按计划完整归档，并且归档文件可访问、可恢复。
    - **应用程序准备：**
        - **针对删除字段：** 如果应用代码的更新（不再使用该字段）是独立部署的，确保这个版本的应用已经上线并稳定运行。**或者，在数据库字段删除前，先将应用置于维护模式或停止相关服务，以防应用在字段不存在时出错。**
        - **针对增加字段：** 如果应用需要立即使用新字段，协调应用部署时间。
    - **人员与工具：** 确保执行人员在位，所有脚本、工具、访问权限均已准备就绪。
    - **回滚计划备查：** 确保回滚计划文档触手可及，并且执行团队成员都清楚流程。
3. **3.3. 执行数据库变更 (Execute Database Changes):**

    - **（可选）停止应用服务/设置维护模式：** 为了绝对安全，尤其对于删除操作或复杂的数据迁移，建议先停止访问该数据库的相关应用服务，或将站点置于维护模式。
    - **步骤1：执行增加字段脚本 (Add Columns Script):** `ALTER TABLE your_table ADD COLUMN new_column_name DATATYPE [NULL | NOT NULL] [DEFAULT default_value];`
        - 考虑将新字段初始设置为 `NULL`able，即使最终目标是 `NOT NULL`。这可以减少 `ALTER TABLE` 的初始锁定时间。后续可以通过 `UPDATE` 填充数据，然后再修改为 `NOT NULL` (如果数据库支持且无 `NULL` 值)。
    - **步骤2：执行数据迁移/填充脚本 (Data Population Script - for new columns):** `UPDATE your_table SET new_column_name = ... WHERE ...;`
        - 对于大表，分批次执行 `UPDATE`，避免产生过大的事务日志和长时间锁定。
        - 监控执行过程中的数据库性能（CPU、IO、锁）。
    - **步骤3：执行删除字段脚本 (Drop Column Script):** **再次确认：应用已不再使用此字段，且此字段数据已成功归档！** `ALTER TABLE your_table DROP COLUMN old_column_name;`
    - **记录与监控：** 详细记录每一步操作的开始和结束时间。密切监控数据库日志、错误信息和性能指标。
4. **3.4. 部署/重启应用程序 (Deploy/Restart Applications):**

    - 如果应用服务被停止，此时可以部署更新后的应用程序代码（如果尚未部署），然后重启服务。
    - 如果应用之前已更新（例如，不再使用待删除字段），则确保其正常连接和运行。
5. **3.5. 快速验证 (Quick Verification):**

    - **Schema验证：** 查询数据库系统表（如 `INFORMATION_SCHEMA.COLUMNS`）确认字段已按预期增加或删除。
    - **数据抽样检查：** 随机抽取几条记录，检查新字段数据是否正确，其他字段数据是否未受影响。
    - **应用冒烟测试：** 对核心应用功能进行快速测试，确保基本可用。

**阶段四：部署后监控与收尾 (Post-Deployment Monitoring & Finalization)**

1. **4.1. 强化监控 (Intensive Monitoring):**
    - 在变更完成后的至少24-72小时内，加强对数据库性能、服务器资源、应用程序错误日志和用户反馈的监控。
    - 准备好在出现严重问题且无法快速修复时，立即启动回滚计划。
2. **4.2. 全面验证 (Comprehensive Validation):**
    - 根据需要进行更广泛和深入的数据验证。
    - 如果适用，安排业务用户进行用户验收测试 (UAT)。
3. **4.3. 归档数据管理 (Archived Data Management):**
    - **不要立即删除已归档的“被删除字段”的数据。** 根据组织的数据保留策略和风险评估，将其安全保存一段时间（例如几周到几个月），作为额外的保障。
    - 在确认新系统长时间稳定运行，并且所有相关方都同意后，再根据策略处理这些归档数据（可能是永久删除，或转入长期归档存储）。
4. **4.4. 文档更新 (Update Documentation):**
    - 更新所有相关的数据库设计文档、数据字典、ER图以及应用程序文档，准确反映当前的数据库结构。
5. **4.5. 复盘与总结 (Review & Lessons Learned):**
    - 召开项目回顾会议，总结经验教训，记录下哪些做得好，哪些可以改进，为未来的数据库变更项目提供参考。

#### 其他

**变更窗口内操作的原子性与最小化：**

- 尽量确保在单个维护窗口内执行的数据库结构变更（DDL）操作数量最少，避免将多个不直接相关的结构变更捆绑在一起。
- 如果一个业务需求涉及多个字段的增删改，优先评估是否能将其分解为更小、更独立的变更包，分批执行，以降低单次变更的风险和影响半径。比如使用扩展和收缩模式.

---

**特别强调以避免数据丢失：**

- **备份是生命线：** 在任何关键步骤前都进行备份，并验证备份的有效性。
- **删除操作的极度谨慎：**
  - **“假删除”过渡：** 对于非常关键的字段，可以考虑先将字段重命名（标记为 `_deprecated` 或类似），并让应用在一段时间内不再使用它，确认无影响后再物理删除。
  - **应用层优先修改：** 确保应用层面已经不再依赖即将删除的字段，这是最安全的做法。
- **数据迁移的原子性与幂等性：** 如果数据迁移脚本复杂，尽量设计为可重复执行（幂等）且在可能的情况下使用事务。对于大批量数据，注意事务大小对性能和日志的影响。
- **了解你的数据库特性：** 不同的数据库系统（MySQL, PostgreSQL, SQL Server, Oracle等）在执行DDL操作（如 `ALTER TABLE`）时的行为（例如锁机制、是否支持在线操作）可能不同。利用数据库提供的特性来减少停机时间并确保数据一致性。

遵循这个流程，虽然步骤较多，但能最大限度地保障您在生产环境中增删字段及迁移数据时的安全性，避免数据丢失。

## 例子

### 反例1

#### 场景

需要完善数据库结构, 同时涉及取消对旧字段的依赖并且增加新的字段

#### 实际操作

- 直接在数据库层面删除生产中存在数据的字段 A

#### 分析(LLM生成)

> Prompt咒语:
> 根据我的注意事项检查我的迁移工作.
