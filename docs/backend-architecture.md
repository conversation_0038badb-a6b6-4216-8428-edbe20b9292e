---
noteId: "ac708c403ec211f0b4ec13362c7e567c"
tags: []

---

# GCPM Backend Architecture Documentation

## Overview

The GCPM (Good Clinical Practice Management) backend is a comprehensive clinical trial management system built with **NestJS**, **TypeScript**, and **Prisma ORM**. It follows a modular, domain-driven architecture with CQRS patterns for complex business logic.

## Technology Stack

### Core Technologies
- **Framework**: NestJS v11.0.11
- **Language**: TypeScript v5.4.4
- **Database ORM**: Prisma v6.7.0
- **Database**: MySQL (multiple databases)
- **Authentication**: JWT with Passport.js
- **API Documentation**: Swagger/OpenAPI
- **Testing**: Jest
- **Process Management**: BullMQ for background jobs
- **Caching**: Cache Manager with Memcache support

### Key Dependencies
- **@nestjs/cqrs**: Command Query Responsibility Segregation
- **@nestjs/passport**: Authentication strategies
- **@nestjs/swagger**: API documentation
- **bcrypt**: Password hashing
- **kafkajs**: Event streaming
- **winston**: Logging
- **class-validator**: Input validation

## System Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        FE[Frontend App]
        API[API Clients]
    end
    
    subgraph "API Gateway Layer"
        MAIN[Main Entry Point]
        AUTH[Authentication]
        GUARD[Guards & Interceptors]
    end
    
    subgraph "Application Layer"
        CTRL[Controllers]
        SVC[Services]
        CQRS[CQRS Handlers]
    end
    
    subgraph "Domain Layer"
        DOM[Domain Models]
        AGG[Aggregates]
        EVT[Domain Events]
    end
    
    subgraph "Infrastructure Layer"
        PRISMA[Prisma ORM]
        CACHE[Cache Manager]
        KAFKA[Kafka Events]
        LOG[Winston Logger]
    end
    
    subgraph "Data Layer"
        CLINICAL[Clinical Data DB]
        WORKDATA[Work Data DB]
        MBGL[MBGL DB]
        REFERRAL[Referral DB]
    end
    
    FE --> MAIN
    API --> MAIN
    MAIN --> AUTH
    AUTH --> GUARD
    GUARD --> CTRL
    CTRL --> SVC
    CTRL --> CQRS
    SVC --> DOM
    CQRS --> DOM
    DOM --> AGG
    AGG --> EVT
    SVC --> PRISMA
    CQRS --> PRISMA
    PRISMA --> CLINICAL
    PRISMA --> WORKDATA
    PRISMA --> MBGL
    PRISMA --> REFERRAL
    SVC --> CACHE
    SVC --> KAFKA
    SVC --> LOG
```

## Module Structure

### Core Modules

#### 1. Authentication Module (`auth/`)
- **Purpose**: User authentication and authorization
- **Components**:
  - `AuthController`: Login/register endpoints
  - `AuthService`: Authentication logic
  - `JwtStrategy`: JWT token validation
  - `LocalStrategy`: Username/password validation
  - Guards: `JwtAuthGuard`, `LocalAuthGuard`

#### 2. User Management (`user/`, `users/`)
- **Purpose**: User and profile management
- **Architecture**: CQRS pattern with domain models
- **Components**:
  - User aggregate with domain events
  - Command/Query handlers
  - User repository with Prisma
  - CRC whitelist management

#### 3. Patient Management (`patients/`, `patient-cqrs/`)
- **Purpose**: Patient data management
- **Dual Implementation**: 
  - Traditional service-based (`patients/`)
  - CQRS-based (`patient-cqrs/`)
- **Features**:
  - Patient CRUD operations
  - Disease associations
  - MBGL system synchronization

#### 4. Research Projects (`research-projects/`, `ddd-research-projects/`)
- **Purpose**: Clinical trial project management
- **Components**:
  - Project lifecycle management
  - Team member assignments
  - Project phases and statuses
  - Sponsor management

#### 5. Visit Management (`visits/`)
- **Purpose**: Clinical visit scheduling and tracking
- **Features**:
  - Visit reservations
  - Visit completion records
  - Integration with external systems (Goldata)
  - Department staff management

#### 6. Clinical Events (`clinical-events/`)
- **Purpose**: Adverse events and quality control
- **Components**:
  - AE/SAE event tracking
  - Quality issue management
  - Event reporting and analytics

#### 7. Clinical Devices (`clinical-device/`)
- **Purpose**: Medical device management
- **Features**:
  - Device registration and tracking
  - Temperature monitoring
  - Drug storage management
  - Device-project bindings

### Infrastructure Modules

#### 1. Prisma Module (`prisma/`)
- **Purpose**: Database access layer
- **Multiple Clients**:
  - Clinical Data Client
  - Work Data Client
  - MBGL Client
  - Referral Client

#### 2. Logging Module (`infrastructure/logging/`)
- **Purpose**: Centralized logging
- **Features**:
  - Winston-based logging
  - Request/response logging
  - Error tracking
  - Log rotation

#### 3. Mailer Module (`mailer/`)
- **Purpose**: Email notifications
- **Features**:
  - Template-based emails
  - CQRS command handling
  - Multiple email providers

### Integration Modules

#### 1. MBGL Integration (`mbgl/`)
- **Purpose**: Integration with chronic disease management system
- **Features**:
  - Patient data synchronization
  - Project management
  - Visit data exchange

#### 2. Goldata Integration (`goldata/`)
- **Purpose**: Integration with Goldata platform
- **Features**:
  - Data ETL processes
  - Visit reservation sync
  - Form data processing

#### 3. DIDA Integration (`dida/`)
- **Purpose**: Integration with DIDA system
- **Features**:
  - External API communication
  - Data transformation

## Database Architecture

### Multi-Database Strategy

The system uses multiple MySQL databases for different domains:

1. **Clinical Data Database** (`clinicaldata`)
   - Core clinical trial data
   - User management
   - Research projects
   - Patient information

2. **Work Data Database** (`workdata`)
   - Visit reservations
   - Department staff
   - Operational data

3. **MBGL Database** (`mbgl`)
   - Legacy system integration
   - Patient synchronization

4. **Referral Database** (`referral`)
   - Patient referral management
   - Cross-system references

### Key Database Models

#### User Management
- `User`: Core user entity with authentication
- `UserProfile`: Extended user information
- `UserTags`: Role-based tagging system
- `Department`: Organizational structure

#### Clinical Data
- `ResearchProjects`: Clinical trial projects
- `Patient`: Patient master data
- `Subject`: Patients enrolled in studies
- `ClinicalEvent`: Adverse events and incidents

#### Visit Management
- `VisitReserve`: Visit appointments
- `VisitCompleteRecords`: Visit completion data
- `PatientVisits`: Visit history

## Design Patterns

### 1. CQRS (Command Query Responsibility Segregation)
- **Used in**: User, Patient, Document, Guidance modules
- **Benefits**: 
  - Separation of read/write operations
  - Scalable query optimization
  - Event-driven architecture

### 2. Domain-Driven Design (DDD)
- **Aggregates**: User, Patient, ResearchProject
- **Domain Events**: User creation, patient updates
- **Value Objects**: Email, Phone, Address

### 3. Repository Pattern
- **Implementation**: Prisma-based repositories
- **Benefits**: Data access abstraction
- **Examples**: UserRepository, PatientRepository

### 4. Dependency Injection
- **Framework**: NestJS built-in DI container
- **Scopes**: Singleton, Request, Transient
- **Custom Providers**: Database clients, external APIs

## Security Architecture

### Authentication Flow
1. **Login**: Username/password validation
2. **Token Generation**: JWT with 30-day expiration
3. **Token Validation**: Passport JWT strategy
4. **Authorization**: Guard-based route protection

### Authorization Mechanisms
- **JWT Guards**: Route-level protection
- **User Existence Guards**: Validate user still exists
- **Role-based Access**: User type validation
- **CRC Whitelist**: Special access control for CRC users

### Data Protection
- **Password Hashing**: bcrypt with salt
- **Field Encryption**: Sensitive data encryption
- **Input Validation**: class-validator decorators
- **SQL Injection Prevention**: Prisma ORM protection

## API Design

### RESTful Principles
- **Resource-based URLs**: `/patients`, `/visits`, `/research-projects`
- **HTTP Methods**: GET, POST, PUT, PATCH, DELETE
- **Status Codes**: Proper HTTP status code usage
- **Content Negotiation**: JSON-based communication

### OpenAPI Documentation
- **Swagger Integration**: Automatic API documentation
- **Type Safety**: TypeScript-first approach
- **SDK Generation**: Auto-generated client SDKs
- **Interactive Testing**: Swagger UI for API exploration

### Error Handling
- **Global Exception Filter**: Centralized error handling
- **Custom Exceptions**: Domain-specific error types
- **Error Logging**: Winston-based error tracking
- **Client-friendly Messages**: User-readable error responses

## Performance Considerations

### Caching Strategy
- **Cache Manager**: Redis/Memcache integration
- **Query Caching**: Frequently accessed data
- **Session Caching**: User session data

### Database Optimization
- **Connection Pooling**: Prisma connection management
- **Query Optimization**: Efficient Prisma queries
- **Indexing Strategy**: Database index optimization
- **Pagination**: Cursor-based pagination for large datasets

### Background Processing
- **BullMQ**: Job queue management
- **Scheduled Tasks**: Cron-based operations
- **Event Processing**: Asynchronous event handling

## Monitoring and Observability

### Logging Strategy
- **Structured Logging**: JSON-formatted logs
- **Log Levels**: Error, Warn, Info, Debug
- **Request Tracing**: Request/response logging
- **Performance Metrics**: Response time tracking

### Health Checks
- **Database Connectivity**: Prisma health checks
- **External Services**: Third-party API monitoring
- **Memory Usage**: Application resource monitoring

## Authentication and Authorization Flow

```mermaid
sequenceDiagram
    participant C as Client
    participant API as API Gateway
    participant AUTH as Auth Service
    participant JWT as JWT Strategy
    participant DB as Database
    participant GUARD as Guards

    C->>API: POST /auth/login
    API->>AUTH: validateUser(username, password)
    AUTH->>DB: findUser(username)
    DB-->>AUTH: User data
    AUTH->>AUTH: bcrypt.compare(password, hash)
    AUTH-->>API: User validated
    API->>JWT: generateToken(user)
    JWT-->>API: JWT Token
    API-->>C: {access_token, user}

    Note over C,API: Subsequent requests
    C->>API: Request + Bearer Token
    API->>GUARD: JwtAuthGuard
    GUARD->>JWT: validateToken(token)
    JWT->>JWT: verify & decode
    JWT-->>GUARD: User payload
    GUARD->>DB: checkUserExists(userId)
    DB-->>GUARD: User exists
    GUARD-->>API: Request authorized
    API->>API: Process request
    API-->>C: Response
```

## CQRS Implementation Pattern

```mermaid
graph TB
    subgraph "CQRS Architecture"
        subgraph "Command Side"
            CMD[Commands]
            CMDH[Command Handlers]
            AGG[Aggregates]
            EVENTS[Domain Events]
        end

        subgraph "Query Side"
            QRY[Queries]
            QRYH[Query Handlers]
            REPO[Repositories]
            DTO[DTOs]
        end

        subgraph "Infrastructure"
            DB[(Database)]
            CACHE[(Cache)]
            EVENTBUS[Event Bus]
        end
    end

    CMD --> CMDH
    CMDH --> AGG
    AGG --> EVENTS
    EVENTS --> EVENTBUS

    QRY --> QRYH
    QRYH --> REPO
    REPO --> DB
    QRYH --> DTO

    CMDH --> DB
    QRYH --> CACHE
```

## Module Dependency Graph

```mermaid
graph TD
    APP[App Module] --> AUTH[Auth Module]
    APP --> USERS[User Module]
    APP --> PATIENTS[Patient Module]
    APP --> VISITS[Visits Module]
    APP --> PROJECTS[Research Projects]
    APP --> DEVICES[Clinical Devices]
    APP --> EVENTS[Clinical Events]

    AUTH --> USERS
    PATIENTS --> USERS
    VISITS --> PATIENTS
    PROJECTS --> USERS
    DEVICES --> PROJECTS
    EVENTS --> PROJECTS

    subgraph "Infrastructure"
        PRISMA[Prisma Module]
        LOGGING[Logging Module]
        MAILER[Mailer Module]
        CACHE[Cache Module]
    end

    USERS --> PRISMA
    PATIENTS --> PRISMA
    VISITS --> PRISMA
    PROJECTS --> PRISMA
    DEVICES --> PRISMA
    EVENTS --> PRISMA

    APP --> LOGGING
    APP --> MAILER

    subgraph "External Integrations"
        MBGL[MBGL Module]
        GOLDATA[Goldata Module]
        DIDA[DIDA Module]
    end

    PATIENTS --> MBGL
    VISITS --> GOLDATA
    PROJECTS --> DIDA
```

## Deployment Architecture

### Environment Configuration
- **Multi-environment**: Development, Staging, Production
- **Environment Variables**: Secure configuration management
- **Docker Support**: Containerized deployment
- **Database Migrations**: Prisma migration management

### Scalability Considerations
- **Horizontal Scaling**: Stateless application design
- **Load Balancing**: Multiple instance support
- **Database Scaling**: Read replica support
- **Microservice Ready**: Modular architecture for future splitting

## Request Processing Flow

```mermaid
sequenceDiagram
    participant Client
    participant Gateway
    participant Controller
    participant Service
    participant Repository
    participant Database
    participant Cache

    Client->>Gateway: HTTP Request
    Gateway->>Gateway: Authentication Check
    Gateway->>Gateway: Input Validation
    Gateway->>Controller: Route to Controller
    Controller->>Service: Business Logic

    alt Cache Hit
        Service->>Cache: Check Cache
        Cache-->>Service: Cached Data
    else Cache Miss
        Service->>Repository: Data Access
        Repository->>Database: Query
        Database-->>Repository: Result
        Repository-->>Service: Domain Object
        Service->>Cache: Store in Cache
    end

    Service-->>Controller: Response Data
    Controller->>Controller: Transform to DTO
    Controller-->>Gateway: HTTP Response
    Gateway-->>Client: JSON Response
```
