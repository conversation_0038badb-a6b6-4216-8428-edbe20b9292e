---
noteId: "dec38b203ec211f0b4ec13362c7e567c"
tags: []

---

# Frontend-Backend Communication Documentation

## Overview

The GCPM system implements a modern, type-safe communication architecture between the Svelte 5 frontend and NestJS backend. The communication is built on RESTful APIs with auto-generated TypeScript SDKs, JWT authentication, and comprehensive error handling.

## Communication Architecture

```mermaid
sequenceDiagram
    participant FE as Frontend (Svelte)
    participant SDK as @gcpm/sdk
    participant API as Backend API
    participant AUTH as Auth Service
    participant DB as Database

    FE->>SDK: API Call with Types
    SDK->>AUTH: Validate JWT Token
    AUTH-->>SDK: Token Valid/Invalid
    
    alt Token Valid
        SDK->>API: HTTP Request + Bearer Token
        API->>DB: Query/Mutation
        DB-->>API: Data Response
        API-->>SDK: JSON Response
        SDK-->>FE: Typed Response
    else Token Invalid
        AUTH-->>FE: Redirect to Login
    end
```

## SDK-Based Communication

### Auto-Generated TypeScript SDK

The frontend uses an auto-generated TypeScript SDK (`@gcpm/sdk`) that provides:

- **Type Safety**: Full TypeScript types for all API endpoints
- **Automatic Validation**: Request/response validation
- **Error Handling**: Consistent error handling across all endpoints
- **IDE Support**: IntelliSense and auto-completion

#### SDK Generation Process

```mermaid
graph LR
    A[NestJS Backend] --> B[OpenAPI Spec]
    B --> C[@hey-api/openapi-ts]
    C --> D[@gcpm/sdk Package]
    D --> E[Frontend Import]
```

### SDK Usage Examples

#### Basic API Call
```typescript
import { visitsControllerFindByReserveDate } from '@gcpm/sdk';

// Type-safe API call with automatic validation
const visits = await visitsControllerFindByReserveDate({
  path: { date: '2024-01-15' }
});
```

#### API Call with Request Body
```typescript
import { patientCqrsControllerCreatePatient } from '@gcpm/sdk';
import type { CreatePatientRequestDto } from '@gcpm/sdk';

const patientData: CreatePatientRequestDto = {
  name: "张三",
  phone: "13800138000",
  gender: 1,
  birthday: "1990-01-01"
};

const response = await patientCqrsControllerCreatePatient({
  body: patientData
});
```

## Authentication Flow

### JWT Token Management

```mermaid
sequenceDiagram
    participant U as User
    participant FE as Frontend
    participant AUTH as AuthService
    participant API as Backend
    participant LS as LocalStorage

    U->>FE: Enter Credentials
    FE->>API: POST /auth/login
    API-->>FE: JWT Token + User Data
    FE->>LS: Store Token
    FE->>AUTH: Set Token in Service
    
    loop Subsequent Requests
        FE->>AUTH: Get Token
        AUTH-->>FE: Current Token
        FE->>API: Request + Bearer Token
        API-->>FE: Response
    end
    
    alt Token Expired
        API-->>FE: 401 Unauthorized
        FE->>LS: Clear Token
        FE->>U: Redirect to Login
    end
```

### Authentication Implementation

#### Frontend Authentication Service
```typescript
// apps/gcpm-frontend/src/lib/services/auth.service.ts
export class AuthService {
  static getToken(): string | null {
    if (!browser) return null;
    return localStorage.getItem('jwt_token');
  }

  static setToken(token: string): void {
    if (!browser) return;
    localStorage.setItem('jwt_token', token);
  }

  static clearToken(): void {
    if (!browser) return;
    localStorage.removeItem('jwt_token');
  }
}
```

#### Request Interceptor
```typescript
// apps/gcpm-frontend/src/hooks.client.ts
client.interceptors.request.use((request) => {
  const token = AuthService.getToken();
  
  if (!token && !isPublicEndpoint(request.url)) {
    AuthService.setPreviousPage(window.location.pathname);
    goto("/login");
    throw new Error("请登录再访问服务");
  }
  
  request.headers.set("Authorization", `Bearer ${token}`);
  return request;
});
```

#### Response Interceptor
```typescript
client.interceptors.response.use((response) => {
  if ([401, 403, 407, 499].includes(response.status)) {
    AuthService.setPreviousPage(window.location.pathname);
    goto("/login");
    throw new Error("未授权，请重新登录");
  }
  return response;
});
```

## Frontend Architecture Integration

```mermaid
graph TB
    subgraph "Frontend Application"
        subgraph "Svelte Components"
            PAGES[Pages]
            COMPONENTS[Components]
            LAYOUTS[Layouts]
        end

        subgraph "State Management"
            STORES[Svelte Stores]
            SERVICES[Service Classes]
            CACHE[TanStack Query]
        end

        subgraph "API Layer"
            SDK[@gcpm/sdk]
            CLIENT[HTTP Client]
            INTERCEPTORS[Interceptors]
        end
    end

    subgraph "Backend API"
        ENDPOINTS[REST Endpoints]
        AUTH[JWT Auth]
        BUSINESS[Business Logic]
    end

    PAGES --> STORES
    COMPONENTS --> STORES
    PAGES --> SERVICES
    COMPONENTS --> SERVICES

    STORES --> SDK
    SERVICES --> SDK
    CACHE --> SDK

    SDK --> CLIENT
    CLIENT --> INTERCEPTORS
    INTERCEPTORS --> ENDPOINTS

    ENDPOINTS --> AUTH
    AUTH --> BUSINESS
```

## Data Flow Patterns

### 1. Page Load Data Fetching

```typescript
// +page.ts (Server-side data loading)
import type { PageLoad } from './$types';
import { visitsControllerFindByReserveDate } from '@gcpm/sdk';

export const load: PageLoad = async ({ params }) => {
  const visits = await visitsControllerFindByReserveDate({
    path: { date: params.date }
  });
  
  return {
    visits: visits.data
  };
};
```

### 2. Client-side Reactive Data

```typescript
// Svelte component with reactive data
<script lang="ts">
import { onMount } from 'svelte';
import { patientsControllerQueryWithPagination } from '@gcpm/sdk';

let patients = $state([]);
let loading = $state(false);

const loadPatients = async () => {
  loading = true;
  try {
    const response = await patientsControllerQueryWithPagination({
      query: { limit: 10, cursor: undefined }
    });
    patients = response.data.patients;
  } catch (error) {
    console.error('Failed to load patients:', error);
  } finally {
    loading = false;
  }
};

onMount(loadPatients);
</script>
```

### 3. Form Submission

```typescript
// Form handling with validation
<script lang="ts">
import { patientCqrsControllerCreatePatient } from '@gcpm/sdk';
import type { CreatePatientRequestDto } from '@gcpm/sdk';

let formData = $state<CreatePatientRequestDto>({
  name: '',
  phone: '',
  gender: 0,
  birthday: ''
});

const handleSubmit = async () => {
  try {
    const response = await patientCqrsControllerCreatePatient({
      body: formData
    });
    
    toast.success('患者创建成功');
    goto('/patients');
  } catch (error) {
    toast.error('创建失败: ' + error.message);
  }
};
</script>
```

## Error Handling Strategy

### Centralized Error Handling

```mermaid
graph TD
    A[API Error] --> B{Error Type}
    B -->|401/403| C[Authentication Error]
    B -->|400| D[Validation Error]
    B -->|404| E[Not Found Error]
    B -->|500| F[Server Error]
    
    C --> G[Redirect to Login]
    D --> H[Show Validation Messages]
    E --> I[Show Not Found Message]
    F --> J[Show Generic Error]
    
    G --> K[Clear Token]
    H --> L[Toast Notification]
    I --> L
    J --> L
```

### Error Handling Implementation

#### Global Error Handler
```typescript
// Error handling utility
export async function handleApiError(error: any) {
  if (error.status === 401 || error.status === 403) {
    AuthService.clearToken();
    AuthService.setPreviousPage(window.location.pathname);
    goto('/login');
    return;
  }
  
  if (error.status === 400) {
    // Handle validation errors
    const message = error.body?.message || '请求数据无效';
    toast.error(message);
    return;
  }
  
  if (error.status === 404) {
    toast.error('请求的资源不存在');
    return;
  }
  
  // Generic server error
  toast.error('服务器错误，请稍后重试');
}
```

#### Component-level Error Handling
```typescript
<script lang="ts">
const loadData = async () => {
  try {
    const response = await apiCall();
    data = response.data;
  } catch (error) {
    await handleApiError(error);
    // Component-specific error handling
    errorMessage = '加载数据失败';
  }
};
</script>
```

## Real-time Communication

### WebSocket Integration (Planned)

```typescript
// Future WebSocket implementation
class WebSocketService {
  private ws: WebSocket;
  
  connect() {
    this.ws = new WebSocket(`ws://localhost:3001/ws`);
    this.ws.onmessage = this.handleMessage;
  }
  
  private handleMessage = (event: MessageEvent) => {
    const data = JSON.parse(event.data);
    // Update reactive stores
    updateStore(data);
  };
}
```

## State Management Integration

### State Management Flow

```mermaid
sequenceDiagram
    participant Component
    participant Store
    participant Service
    participant SDK
    participant API
    participant Cache

    Component->>Store: Subscribe to state
    Component->>Service: Trigger action
    Service->>Cache: Check cache

    alt Cache Hit
        Cache-->>Service: Cached data
        Service->>Store: Update store
        Store-->>Component: Reactive update
    else Cache Miss
        Service->>SDK: API call
        SDK->>API: HTTP request
        API-->>SDK: Response
        SDK-->>Service: Typed data
        Service->>Cache: Store in cache
        Service->>Store: Update store
        Store-->>Component: Reactive update
    end
```

### Svelte Stores with API Data

```typescript
// Patient store with API integration
import { writable } from 'svelte/store';
import { patientsControllerQueryWithPagination } from '@gcpm/sdk';

export const patientsStore = writable([]);

export const patientService = {
  async loadPatients(params: PaginationParams) {
    const response = await patientsControllerQueryWithPagination({
      query: params
    });
    patientsStore.set(response.data.patients);
    return response.data;
  },

  async createPatient(data: CreatePatientRequestDto) {
    const response = await patientCqrsControllerCreatePatient({
      body: data
    });
    // Update store
    patientsStore.update(patients => [...patients, response.data]);
    return response.data;
  }
};
```

## Performance Optimization

### Request Optimization

#### Debounced Search
```typescript
import { debounce } from 'lodash-es';

const debouncedSearch = debounce(async (query: string) => {
  const results = await patientsControllerQuery({
    query: { name: query }
  });
  searchResults = results.data;
}, 300);
```

#### Request Caching
```typescript
// Simple cache implementation
const cache = new Map();

const cachedApiCall = async (key: string, apiCall: () => Promise<any>) => {
  if (cache.has(key)) {
    return cache.get(key);
  }
  
  const result = await apiCall();
  cache.set(key, result);
  return result;
};
```

### Response Optimization

#### Pagination
```typescript
// Cursor-based pagination
let cursor = $state<string | undefined>(undefined);
let hasMore = $state(true);

const loadMore = async () => {
  const response = await patientsControllerQueryWithPagination({
    query: { limit: 20, cursor }
  });
  
  patients = [...patients, ...response.data.patients];
  cursor = response.data.nextCursor;
  hasMore = response.data.hasMore;
};
```

## Development Workflow

### API-First Development

1. **Backend Development**: Define OpenAPI specifications
2. **SDK Generation**: Auto-generate TypeScript SDK
3. **Frontend Integration**: Import and use typed SDK functions
4. **Type Safety**: Compile-time validation of API contracts

### Testing Strategy

#### API Integration Tests
```typescript
// Playwright E2E test
test('should create patient successfully', async ({ page }) => {
  await page.goto('/patients/new');
  await page.fill('[data-testid="name"]', '张三');
  await page.fill('[data-testid="phone"]', '13800138000');
  await page.click('[data-testid="submit"]');
  
  await expect(page.locator('.success-message')).toBeVisible();
});
```

#### Mock API for Development
```typescript
// Mock service for development
if (import.meta.env.DEV) {
  // Use mock data
  const mockPatients = [
    { id: '1', name: '张三', phone: '13800138000' }
  ];
}
```

## Security Considerations

### Token Security
- **Storage**: localStorage for persistence
- **Transmission**: HTTPS only
- **Expiration**: 30-day token lifetime
- **Refresh**: Manual re-authentication required

### Request Security
- **CORS**: Configured for specific origins
- **Content-Type**: Enforced JSON content type
- **Input Validation**: Client and server-side validation
- **XSS Protection**: Svelte's built-in sanitization

### Data Protection
- **Sensitive Data**: Masked in logs
- **Error Messages**: Generic messages in production
- **API Keys**: Environment variable configuration
