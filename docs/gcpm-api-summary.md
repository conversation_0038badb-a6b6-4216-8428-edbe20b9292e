---
noteId: "46ce43123e9411f0b4ec13362c7e567c"
tags: []

---

# GCPM API 系统概览

## 系统简介

GCPM (Good Clinical Practice Management) 是一个临床试验管理系统，提供了完整的 RESTful API 接口用于管理临床试验的各个方面。

## 核心功能模块

### 1. 访视管理 (22个接口)
- **访视预约**: 创建、查询、更新访视预约
- **访视记录**: 完整信息记录的创建和管理
- **历史记录**: 访视历史记录查询
- **数据同步**: 与金数据(Goldata)和慢病管理系统的数据同步

**主要接口**:
- `POST /visits/reserve/from-goldata` - 从Goldata创建访视预约
- `POST /visits/complete-info-record/from-gcpm-frontend` - 从前端创建结束访视记录
- `GET /visits/complete-info-record/history` - 获取历史结束访视记录
- `GET /visits/reserve/date/{date}` - 通过日期获取访视预约

### 2. 研究项目管理 (32个接口)
- **项目信息**: 项目基本信息管理
- **项目成员**: 项目团队成员管理
- **项目状态**: 项目状态和阶段管理
- **统计分析**: 项目相关的统计数据

**主要接口**:
- `GET /research-projects/all-projects` - 获取所有研究项目
- `POST /research-projects/create-project-filing` - 创建项目备案表单
- `GET /research-projects/{projectID}/event-statistics` - 获取项目临床事件统计

### 3. 患者管理 (15个接口)
- **患者信息**: 患者基本信息的CRUD操作
- **患者查询**: 支持分页和条件查询
- **数据同步**: 与慢病管理系统的患者数据同步
- **软删除**: 患者数据的软删除和恢复

**主要接口**:
- `POST /patients` - 创建患者
- `GET /patients/pagination` - 分页查询患者
- `POST /patients/{id}/restore` - 恢复已删除的患者

### 4. CRC工作面板 (10个接口)
- **访视预约**: CRC专用的访视预约管理
- **质量控制**: AE、SAE、PD事件的质量控制
- **工作内容**: 主要访视内容管理

**主要接口**:
- `POST /crc/reserve-visit` - 创建访视预约
- `POST /crc/quality-control/ae` - 创建质量控制AE事件
- `GET /crc/reserve-visit/pagination` - 分页获取访视预约

### 5. 临床设备管理 (7个接口)
- **设备注册**: 临床设备的注册和管理
- **设备事件**: 设备相关事件记录
- **设备绑定**: 设备与其他实体的绑定关系

**主要接口**:
- `GET /clinical-device` - 获取所有临床设备
- `POST /clinical-device/event/from-goldata` - 从金数据添加设备事件

### 6. 团队管理 (7个接口)
- **成员管理**: 团队成员的增删改查
- **部门管理**: 部门信息管理
- **状态管理**: 在职状态管理

**主要接口**:
- `POST /team-management/add-member` - 添加团队成员
- `GET /team-management/all-department` - 获取所有部门

### 7. 慢病管理系统集成 (7个接口)
- **患者同步**: 与慢病管理系统的患者数据同步
- **项目同步**: 项目信息同步
- **疾病管理**: 疾病信息管理

**主要接口**:
- `GET /mbgl/patient` - 查询慢病管理系统中的患者
- `GET /mbgl/project` - 查询慢病管理系统中的项目

### 8. 导诊工作面板 (5个接口)
- **访视查询**: 导诊专用的访视查询功能
- **访视创建**: 导诊创建访视预约
- **未完成访视**: 查询未完成的访视

**主要接口**:
- `GET /guidance/reserve-visit/date/{date}` - 按日期获取访视预约
- `POST /guidance/reserve-visit` - 创建访视预约

### 9. 引用数据管理 (9个接口)
- **基础数据**: 系统基础数据管理
- **受试者状态**: 受试者状态定义
- **事件类型**: 各种事件类型定义

**主要接口**:
- `GET /reference-data/subject-states` - 获取所有受试者状态
- `GET /reference-data/diseases` - 获取所有疾病

### 10. 用户认证与授权 (9个接口)
- **用户登录**: 用户身份验证
- **用户注册**: 新用户注册
- **CRC白名单**: CRC用户白名单管理

**主要接口**:
- `POST /auth/login` - 用户登录
- `POST /auth/register` - 用户注册
- `GET /user/crc/whitelist` - 获取CRC白名单

## 技术特点

### API设计规范
- **RESTful架构**: 遵循REST设计原则
- **统一响应格式**: 标准化的API响应结构
- **错误处理**: 完善的HTTP状态码和错误信息
- **分页支持**: 大数据量查询的分页机制

### 数据同步机制
- **金数据集成**: 与金数据平台的双向数据同步
- **慢病管理系统**: 与慢病管理系统的数据交换
- **实时同步**: 支持实时和批量数据同步

### 安全性
- **身份验证**: 基于Token的身份验证机制
- **权限控制**: 细粒度的权限控制
- **数据保护**: 敏感数据的加密和保护

### 可扩展性
- **模块化设计**: 功能模块清晰分离
- **标准化接口**: 统一的接口设计规范
- **版本控制**: API版本管理机制

## 使用建议

1. **开发前准备**: 仔细阅读API文档，了解数据模型和接口规范
2. **错误处理**: 实现完善的错误处理机制，处理各种HTTP状态码
3. **数据验证**: 在客户端进行数据验证，确保数据质量
4. **性能优化**: 合理使用分页查询，避免大数据量请求
5. **安全考虑**: 妥善保管认证Token，实现安全的API调用

## 相关文档

- [完整API文档](./gcpm-api-documentation.md) - 详细的接口文档
- [数据模型说明](./gcpm-api-documentation.md#数据模型) - 数据结构定义
- [错误码说明](./gcpm-api-documentation.md#详细接口文档) - 错误处理指南
