# 工作流程 - 精简版

## TL;DR

**数据迁移核心原则与关键点：**

- **安全第一：** 任何操作前必须有可靠备份和回滚计划。
- **充分测试：** 在与生产环境一致的测试环境中演练所有步骤。
- **最小影响：** 尽量减少对生产环境的干扰和停机时间。
- **清晰沟通：** 确保所有相关方了解变更内容、时间和潜在影响。
- **谨慎操作：** 删除操作前务必确认数据不再被任何应用或报表依赖。
- **完整记录：** 在计划过程中记录完整的依赖关系与变更（新增/删除字段）。
- **分离依赖：** 先移除应用层对将删除数据库字段的依赖。
- **避免同时操作：** 不建议同时进行字段删除和新增，可考虑“假删除”过渡。
- **迁移前检查：** 应用迁移前务必检查所有变更。

**迁移工作清单核心阶段：**

1. **准备与规划：**
    - 明确需求与影响分析（特别是依赖分析）。
    - 制定备份与归档策略（尤其是待删除字段数据）。
    - 制定详细的执行与回滚计划（包括SQL脚本和应用代码变更）。
    - 制定沟通计划。
2. **开发与测试：**
    - 搭建与生产一致的测试环境。
    - 编写和测试SQL脚本及应用程序代码。
    - 在测试环境中完整演练所有变更和回滚计划。
    - 进行数据验证和性能测试。
3. **生产环境部署：**
    - 选择维护窗口并进行最终检查（包括最终备份和归档确认）。
    - 按顺序执行数据库变更（增加字段、填充数据、删除字段），**再次确认应用不再依赖将删除的字段**。
    - 部署/重启应用程序。
    - 进行快速验证。
4. **部署后监控与收尾：**
    - 强化监控，准备回滚。
    - 进行全面验证。
    - 管理好已归档的数据（不要立即删除）。
    - 更新相关文档。
    - 进行复盘与总结。

**避免数据丢失的关键强调：**

- **备份是生命线。**
- **极度谨慎对待删除操作：** 考虑“假删除”，优先修改应用层。
- **了解数据库特性**以优化操作。

## 基于拓展伸缩策略的迁移步骤

参考:
[How to migrate data with Prisma ORM using the expand and contract pattern | Prisma Documentation](https://www.prisma.io/docs/guides/data-migration)

这个版本将每个阶段的核心活动进行了提炼，并明确了在规划、测试和部署阶段如何考虑和应用“拓展与收缩”模式，以期达到更安全、平滑的迁移。

核心思想:

- 分离拓展 schema和 收缩 schema

1. **阶段一：周密准备与规划**

    - **核心分析与计划：** 明确迁移需求、范围及潜在影响，**彻底梳理数据和应用依赖关系**。制定包含数据备份、待变更/删除数据归档、详细执行步骤、时间表及应急回滚的完整计划。
    - **策略选择与沟通：** **针对复杂变更或要求高可用性的场景，优先考虑并规划“拓展与收缩”(Expand and Contract)模式的实施方案。** 建立清晰的沟通机制，确保所有相关方（业务、开发、测试、运维）知情并协调一致。
2. **阶段二：严谨开发与测试**

    - **环境与脚本：** 搭建与生产环境尽可能一致的测试环境。开发并单元测试所有数据库脚本（DDL、DML、数据迁移脚本）和必要的应用程序代码变更。
    - **完整演练与验证：** 在测试环境中模拟完整的迁移过程，包括数据迁移的准确性、变更后的应用功能、系统性能以及回滚计划的有效性。**若采用“拓展与收缩”模式，需对拓展、数据迁移、应用切换、收缩等各阶段分别进行充分测试和验证。**
    - **优化与确认：** 根据测试结果优化方案和脚本，获得相关负责人批准后方可进入下一阶段。
3. **阶段三：谨慎生产部署**

    - **最终准备：** 选择业务低峰期作为维护窗口。执行部署前最后一次生产数据库全量备份和特定数据的归档。**再次确认所有前置条件均已满足（例如：应用层对即将删除字段的依赖已解除并通过验证）。**
    - **执行数据库与应用变更（推荐采用“拓展与收缩”模式）：**
        - **1. 拓展 (Expand)：**
            - 在数据库中添加新的结构（如新字段、新表）。旧结构保持不变。
            - （按需）部署已能识别新结构、同时兼容旧结构的应用版本。
        - **2. 迁移 (Migrate)：**
            - 执行数据迁移脚本，将数据从旧结构填充或同步到新结构。进行数据校验。
            - 应用逐步开始双写（如果适用）或切换读写操作到新结构。监控应用行为和数据一致性。
        - **3. 收缩 (Contract)：**
            - 在确认所有应用均已稳定切换到新结构，且旧结构数据已安全归档、**并通过日志和监控确认旧结构已无任何访问**后，物理删除旧的数据库结构。
    - **应用适配与验证：** （按需）部署最终的应用程序版本（如完全移除对旧结构依赖的代码）。进行快速的Schema检查、数据抽样验证和核心应用功能冒烟测试。
4. **阶段四：持续监控与总结**

    - **强化监控与全面验证：** 部署完成后的指定周期内（如24-72小时），加强对数据库性能、服务器资源、应用错误日志和业务指标的监控。准备好在出现严重问题时快速执行回滚。进行更全面的数据验证和业务功能验收。
    - **收尾与复盘：** 妥善管理已归档的数据（遵循数据保留策略，不要立即删除）。更新所有相关的数据库设计文档、数据字典及应用程序文档。组织项目回顾会议，总结经验教训，持续改进流程。
