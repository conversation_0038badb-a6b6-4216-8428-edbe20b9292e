---
noteId: "665f7fd03d2e11f09966d5386bb2acfc"
tags: []

---

# 项目管理仪表盘业务逻辑分析

本文档通过 Mermaid 图表详细展示项目管理仪表盘的完整业务逻辑和数据流，包括前端组件结构、数据获取流程、数据库关联关系和统计指标计算逻辑。

## 1. 数据流程图 - 从数据库到前端显示的完整流程

```mermaid
flowchart TD
    A[用户访问仪表盘页面] --> B[前端页面加载]
    B --> C[调用 onMount 生命周期]
    C --> D[并行加载多个数据源]
    
    D --> E1[loadDashboardOverview]
    D --> E2[loadProjectStatusDistribution]
    D --> E3[loadProjectStageDistribution]
    D --> E4[loadRecruitmentStatusDistribution]
    D --> E5[loadDiseaseDistribution]
    D --> E6[loadSponsorDistribution]
    D --> E7[loadMonthlyNewProjects]
    D --> E8[loadFinancialMetrics]
    D --> E9[loadPersonnelMetrics]
    D --> E10[loadTimelineMetrics]
    
    E1 --> F1[dashboardService.getDashboardOverview]
    E2 --> F2[dashboardService.getProjectStatusDistribution]
    E3 --> F3[dashboardService.getProjectStageDistribution]
    E4 --> F4[dashboardService.getRecruitmentStatusDistribution]
    E5 --> F5[dashboardService.getDiseaseDistribution]
    E6 --> F6[dashboardService.getSponsorDistribution]
    E7 --> F7[dashboardService.getMonthlyNewProjects]
    E8 --> F8[dashboardService.getFinancialMetrics]
    E9 --> F9[dashboardService.getPersonnelMetrics]
    E10 --> F10[dashboardService.getTimelineMetrics]
    
    F1 --> G1[Tauri invoke: get_dashboard_overview]
    F2 --> G2[Tauri invoke: get_project_status_distribution]
    F3 --> G3[Tauri invoke: get_project_stage_distribution]
    F4 --> G4[Tauri invoke: get_recruitment_status_distribution]
    F5 --> G5[Tauri invoke: get_disease_distribution]
    F6 --> G6[Tauri invoke: get_sponsor_distribution]
    F7 --> G7[Tauri invoke: get_monthly_new_projects]
    F8 --> G8[Tauri invoke: get_financial_metrics]
    F9 --> G9[Tauri invoke: get_personnel_metrics]
    F10 --> G10[Tauri invoke: get_timeline_metrics]
    
    G1 --> H1[DashboardService.get_dashboard_overview]
    G2 --> H2[DashboardService.get_project_status_distribution]
    G3 --> H3[DashboardService.get_project_stage_distribution]
    G4 --> H4[DashboardService.get_recruitment_status_distribution]
    G5 --> H5[DashboardService.get_disease_distribution]
    G6 --> H6[DashboardService.get_sponsor_distribution]
    G7 --> H7[DashboardService.get_monthly_new_projects]
    G8 --> H8[DashboardService.get_financial_metrics]
    G9 --> H9[DashboardService.get_personnel_metrics]
    G10 --> H10[DashboardService.get_timeline_metrics]
    
    H1 --> I1[DashboardRepository.get_dashboard_overview]
    H2 --> I2[DashboardRepository.get_project_status_distribution]
    H3 --> I3[DashboardRepository.get_project_stage_distribution]
    H4 --> I4[DashboardRepository.get_recruitment_status_distribution]
    H5 --> I5[DashboardRepository.get_disease_distribution]
    H6 --> I6[DashboardRepository.get_sponsor_distribution]
    H7 --> I7[DashboardRepository.get_monthly_new_projects]
    H8 --> I8[DashboardRepository.get_project_subsidy_overview + 计算]
    H9 --> I9[DashboardRepository.get_role_distribution + 计算]
    H10 --> I10[DashboardRepository.get_projects_by_start_month + 计算]
    
    I1 --> J[SQLite 数据库查询]
    I2 --> J
    I3 --> J
    I4 --> J
    I5 --> J
    I6 --> J
    I7 --> J
    I8 --> J
    I9 --> J
    I10 --> J
    
    J --> K[返回查询结果]
    K --> L[数据转换和聚合]
    L --> M[返回到前端]
    M --> N[更新组件状态]
    N --> O[渲染图表和指标卡片]
    
    P[用户筛选操作] --> Q[更新筛选参数]
    Q --> R[重新调用相关数据加载函数]
    R --> F1
```

## 2. 数据库实体关系图 - 仪表盘涉及的表关系

```mermaid
erDiagram
    projects ||--o{ project_sponsors : "has"
    projects ||--o{ research_drugs : "has"
    projects ||--o{ drug_groups : "has"
    projects ||--o{ project_personnel_roles : "has"
    projects ||--o{ subsidies : "has"
    projects ||--o{ subsidy_schemes : "has"
    projects ||--o{ project_criteria : "has"
    
    dictionary_items ||--o{ projects : "disease_item_id"
    dictionary_items ||--o{ projects : "project_stage_item_id"
    dictionary_items ||--o{ projects : "project_status_item_id"
    dictionary_items ||--o{ projects : "recruitment_status_item_id"
    dictionary_items ||--o{ project_sponsors : "sponsor_item_id"
    dictionary_items ||--o{ subsidies : "subsidy_type_item_id"
    dictionary_items ||--o{ subsidies : "unit_item_id"
    dictionary_items ||--o{ project_personnel_roles : "role_item_id"
    dictionary_items ||--o{ staff : "position_item_id"
    
    staff ||--o{ project_personnel_roles : "personnel_id"
    
    subsidy_schemes ||--o{ subsidy_scheme_items : "scheme_id"
    subsidies ||--o{ subsidy_scheme_items : "subsidy_item_id"
    
    rule_definitions ||--o{ project_criteria : "rule_definition_id"
    
    projects {
        TEXT project_id PK
        TEXT project_name
        TEXT project_short_name
        TEXT project_path
        INTEGER disease_item_id FK
        INTEGER project_stage_item_id FK
        INTEGER project_status_item_id FK
        INTEGER recruitment_status_item_id FK
        INTEGER contract_case_center
        INTEGER contract_case_total
        TEXT project_start_date
        TEXT last_updated
    }
    
    dictionary_items {
        INTEGER item_id PK
        INTEGER dictionary_id FK
        TEXT item_key
        TEXT item_value
        TEXT item_description
        TEXT status
    }
    
    staff {
        INTEGER id PK
        TEXT name
        TEXT gender
        TEXT birthday
        TEXT phone
        TEXT email
        INTEGER position_item_id FK
        INTEGER isPI
        TEXT organization
        TEXT created_at
        TEXT updated_at
    }
    
    project_personnel_roles {
        INTEGER assignment_id PK
        TEXT project_id FK
        INTEGER personnel_id FK
        INTEGER role_item_id FK
    }
    
    subsidies {
        INTEGER subsidy_item_id PK
        TEXT project_id FK
        INTEGER subsidy_type_item_id FK
        REAL unit_amount
        INTEGER total_units
        INTEGER unit_item_id FK
        REAL total_amount
        TEXT last_updated
    }
    
    subsidy_schemes {
        INTEGER scheme_id PK
        TEXT project_id FK
        TEXT scheme_name
        REAL total_amount
    }
    
    project_sponsors {
        INTEGER id PK
        TEXT project_id FK
        INTEGER sponsor_item_id FK
    }
```

## 3. 前端组件架构图 - 组件层次结构和依赖关系

```mermaid
graph TD
    A[+page.svelte - 仪表盘主页面] --> B[状态管理]
    A --> C[数据加载逻辑]
    A --> D[UI 布局组件]

    B --> B1[rawDashboardData Store]
    B --> B2[activeFilters Store]
    B --> B3[isLoading Store]
    B --> B4[errors Store]

    C --> C1[dashboardService]
    C1 --> C11[getDashboardOverview]
    C1 --> C12[getProjectStatusDistribution]
    C1 --> C13[getFinancialMetrics]
    C1 --> C14[getPersonnelMetrics]
    C1 --> C15[getTimelineMetrics]

    D --> D1[筛选器区域]
    D --> D2[概览指标卡片区域]
    D --> D3[图表展示区域]
    D --> D4[详细数据表格区域]

    D1 --> D11[DateRangeFilter]
    D1 --> D12[ProjectStatusFilter]
    D1 --> D13[SponsorFilter]
    D1 --> D14[DiseaseFilter]

    D2 --> D21[KPI Card - 总项目数]
    D2 --> D22[KPI Card - 活跃项目数]
    D2 --> D23[KPI Card - 招募中项目数]
    D2 --> D24[KPI Card - 总补贴金额]
    D2 --> D25[KPI Card - 总人员数]

    D3 --> D31[MetricCard - 项目状态分布]
    D3 --> D32[MetricCard - 项目阶段分布]
    D3 --> D33[MetricCard - 招募状态分布]
    D3 --> D34[MetricCard - 疾病领域分布]
    D3 --> D35[MetricCard - 申办方分布]
    D3 --> D36[MetricCard - 月度新项目趋势]
    D3 --> D37[MetricCard - 补贴类型分布]
    D3 --> D38[MetricCard - 人员角色分布]

    D4 --> D41[补贴最高项目表格]

    E[MetricCard.svelte - 通用图表组件] --> E1[图表类型支持]
    E1 --> E11[饼图 - pie]
    E1 --> E12[柱状图 - bar]
    E1 --> E13[折线图 - line]
    E1 --> E14[表格 - table]

    E --> E2[图表配置生成]
    E --> E3[数据格式转换]
    E --> E4[加载状态处理]
    E --> E5[错误状态处理]
```

## 4. 用户操作时序图 - 用户交互和系统响应

```mermaid
sequenceDiagram
    participant U as 用户
    participant P as 仪表盘页面
    participant S as dashboardService
    participant T as Tauri Backend
    participant DS as DashboardService
    participant DR as DashboardRepository
    participant DB as SQLite Database

    U->>P: 访问仪表盘页面
    P->>P: onMount() 触发

    par 并行加载数据
        P->>S: getDashboardOverview()
        S->>T: invoke('get_dashboard_overview')
        T->>DS: get_dashboard_overview()
        DS->>DR: get_dashboard_overview()
        DR->>DB: SELECT COUNT(*) FROM projects WHERE...
        DB-->>DR: 返回统计数据
        DR-->>DS: DashboardOverview
        DS-->>T: DashboardOverview
        T-->>S: DashboardOverview
        S-->>P: 更新 overview 状态
    and
        P->>S: getProjectStatusDistribution()
        S->>T: invoke('get_project_status_distribution')
        T->>DS: get_project_status_distribution()
        DS->>DR: get_project_status_distribution()
        DR->>DB: SELECT di.item_value, COUNT(p.project_id) FROM projects p JOIN dictionary_items di...
        DB-->>DR: 返回状态分布数据
        DR-->>DS: Vec<ProjectStatusDistribution>
        DS-->>T: Vec<ProjectStatusDistribution>
        T-->>S: Vec<ProjectStatusDistribution>
        S-->>P: 更新 projectStatusDistribution 状态
    and
        P->>S: getFinancialMetrics()
        S->>T: invoke('get_financial_metrics')
        T->>DS: get_financial_metrics()
        DS->>DR: get_project_subsidy_overview()
        DR->>DB: SELECT project_id, SUM(total_amount) FROM subsidies...
        DB-->>DR: 返回补贴数据
        DR-->>DS: Vec<ProjectSubsidyOverview>
        DS->>DS: 计算总金额、平均值、排序
        DS-->>T: FinancialMetrics
        T-->>S: FinancialMetrics
        S-->>P: 更新 financialMetrics 状态
    end

    P->>P: 渲染图表和指标卡片

    U->>P: 选择日期范围筛选
    P->>P: 更新 startDate, endDate
    P->>P: 触发响应式更新

    P->>S: getDashboardOverview(filterParams)
    S->>T: invoke('get_dashboard_overview', {filterParams})
    T->>DS: get_dashboard_overview(filter_params)
    DS->>DR: get_dashboard_overview(filter_params)
    DR->>DB: SELECT ... WHERE project_start_date BETWEEN ? AND ?
    DB-->>DR: 返回筛选后数据
    DR-->>DS: DashboardOverview
    DS-->>T: DashboardOverview
    T-->>S: DashboardOverview
    S-->>P: 更新状态
    P->>P: 重新渲染图表

    U->>P: 点击项目状态筛选
    P->>P: 更新 selectedStatusIds
    P->>P: 触发所有相关数据重新加载
    Note over P,DB: 类似上述流程，但筛选条件为项目状态
```

## 5. 统计指标计算逻辑图 - 关键业务指标的计算方式

```mermaid
flowchart TD
    A[统计指标计算] --> B[基础项目统计]
    A --> C[财务指标计算]
    A --> D[人员指标计算]
    A --> E[时间线指标计算]

    B --> B1[总项目数]
    B --> B2[活跃项目数]
    B --> B3[招募中项目数]

    B1 --> B11["SELECT COUNT(*) FROM projects"]
    B2 --> B21["SELECT COUNT(*) FROM projects p<br/>JOIN dictionary_items di ON p.project_status_item_id = di.item_id<br/>WHERE di.item_value = '进行中'"]
    B3 --> B31["SELECT COUNT(*) FROM projects p<br/>JOIN dictionary_items di ON p.recruitment_status_item_id = di.item_id<br/>WHERE di.item_value = '招募中'"]

    C --> C1[总补贴金额]
    C --> C2[平均项目补贴]
    C --> C3[补贴类型分布]
    C --> C4[补贴最高项目TOP10]

    C1 --> C11["SELECT SUM(total_amount) FROM subsidies"]
    C2 --> C21["总补贴金额 / 项目数量"]
    C3 --> C31["SELECT di.item_value, SUM(s.total_amount)<br/>FROM subsidies s<br/>JOIN dictionary_items di ON s.subsidy_type_item_id = di.item_id<br/>GROUP BY di.item_value"]
    C4 --> C41["SELECT project_id, SUM(total_amount)<br/>FROM subsidies<br/>GROUP BY project_id<br/>ORDER BY SUM(total_amount) DESC<br/>LIMIT 10"]

    D --> D1[总人员数]
    D --> D2[人员角色分布]
    D --> D3[人员工作负荷]
    D --> D4[PI分布统计]

    D1 --> D11["SELECT COUNT(DISTINCT id) FROM staff"]
    D2 --> D21["SELECT di.item_value, COUNT(DISTINCT ppr.personnel_id)<br/>FROM project_personnel_roles ppr<br/>JOIN dictionary_items di ON ppr.role_item_id = di.item_id<br/>GROUP BY di.item_value"]
    D3 --> D31["SELECT s.name, COUNT(DISTINCT ppr.project_id)<br/>FROM staff s<br/>LEFT JOIN project_personnel_roles ppr ON s.id = ppr.personnel_id<br/>GROUP BY s.id, s.name"]
    D4 --> D41["SELECT name, isPI, COUNT(project_count)<br/>FROM staff s<br/>LEFT JOIN project_personnel_roles ppr ON s.id = ppr.personnel_id<br/>GROUP BY s.id"]

    E --> E1[月度新项目趋势]
    E --> E2[平均项目周期]
    E --> E3[即将到期项目]
    E --> E4[项目阶段转换]

    E1 --> E11["SELECT strftime('%Y-%m', project_start_date) as month,<br/>COUNT(project_id)<br/>FROM projects<br/>GROUP BY month<br/>ORDER BY month"]
    E2 --> E21["基于项目开始日期和当前日期计算<br/>或基于项目状态变更历史"]
    E3 --> E31["基于项目预期结束日期<br/>和当前日期比较"]
    E4 --> E41["基于项目阶段变更历史<br/>统计阶段转换模式"]
```

## 6. 数据筛选逻辑图 - 筛选器如何影响数据查询

```mermaid
flowchart TD
    A[用户筛选操作] --> B[构建筛选参数]
    B --> C[DashboardFilterParams]

    C --> C1[日期范围筛选]
    C --> C2[项目状态筛选]
    C --> C3[申办方筛选]
    C --> C4[疾病领域筛选]

    C1 --> D1[DateRangeParams]
    C2 --> D2[ProjectStatusFilterParams]
    C3 --> D3[SponsorFilterParams]
    C4 --> D4[DiseaseFilterParams]

    D1 --> E1["WHERE project_start_date BETWEEN ? AND ?"]
    D2 --> E2["WHERE project_status_item_id IN (?, ?, ...)"]
    D3 --> E3["JOIN project_sponsors ps ON p.project_id = ps.project_id<br/>WHERE ps.sponsor_item_id IN (?, ?, ...)"]
    D4 --> E4["WHERE disease_item_id IN (?, ?, ...)"]

    E1 --> F[动态SQL构建]
    E2 --> F
    E3 --> F
    E4 --> F

    F --> G[组合WHERE条件]
    G --> H["WHERE condition1 AND condition2 AND condition3..."]
    H --> I[执行数据库查询]
    I --> J[返回筛选后的结果]

    K[筛选器状态管理] --> K1[activeFilters Store]
    K1 --> K2[响应式更新]
    K2 --> K3[触发数据重新加载]
    K3 --> A
```

## 7. 错误处理和加载状态管理

```mermaid
stateDiagram-v2
    [*] --> Initial
    Initial --> Loading : 开始加载数据
    Loading --> Success : 数据加载成功
    Loading --> Error : 数据加载失败
    Success --> Loading : 用户筛选/刷新
    Error --> Loading : 用户重试
    Error --> [*] : 用户离开页面
    Success --> [*] : 用户离开页面

    state Loading {
        [*] --> ShowSpinner
        ShowSpinner --> DisableInteraction
        DisableInteraction --> UpdateLoadingStore
    }

    state Success {
        [*] --> UpdateDataStore
        UpdateDataStore --> RenderCharts
        RenderCharts --> EnableInteraction
    }

    state Error {
        [*] --> UpdateErrorStore
        UpdateErrorStore --> ShowErrorMessage
        ShowErrorMessage --> EnableRetry
    }
```

## 8. 核心业务逻辑总结

### 8.1 数据获取策略
- **并行加载**: 页面初始化时并行加载多个数据源，提升用户体验
- **响应式更新**: 基于 Svelte stores 实现数据状态管理和响应式更新
- **筛选联动**: 筛选器变更时触发相关数据重新加载

### 8.2 统计指标计算
- **基础统计**: 直接从数据库聚合计算（COUNT、SUM等）
- **复合指标**: 在服务层进行二次计算（平均值、排序、TOP N等）
- **实时计算**: 所有指标都是实时从数据库计算，确保数据准确性

### 8.3 数据库查询优化
- **JOIN优化**: 合理使用 JOIN 减少查询次数
- **索引利用**: 基于外键关系进行高效查询
- **条件筛选**: 动态构建 WHERE 条件，支持多维度筛选

### 8.4 前端架构特点
- **组件复用**: MetricCard 组件支持多种图表类型
- **状态分离**: 数据状态、加载状态、错误状态分别管理
- **类型安全**: TypeScript 接口确保前后端数据一致性

### 8.5 错误处理机制
- **分层错误处理**: 从数据库到前端的每一层都有错误处理
- **用户友好**: 提供清晰的错误信息和重试机制
- **状态恢复**: 错误状态可以通过用户操作恢复

### 8.6 性能优化考虑
- **数据缓存**: 可考虑在前端缓存不常变化的字典数据
- **分页加载**: 对于大数据量的表格展示可考虑分页
- **懒加载**: 非关键图表可考虑懒加载策略

## 9. 已移除的功能组件

### 9.1 项目路径快速访问 (Project Path Quick Access)
**移除时间**: 2025-05-30
**移除原因**: 简化仪表盘界面，减少不必要的功能复杂度

**已移除的组件**:
- 后端模型: `ProjectPathAccess`
- 后端命令: `get_project_paths`
- 后端仓库方法: `get_project_paths`
- 后端服务方法: `get_project_paths`
- 前端服务方法: `getProjectPaths`
- 前端状态管理: `projectPaths` 相关状态
- 前端UI组件: 项目路径快速访问表格

**原功能描述**:
- 显示项目列表及其对应的文件路径
- 支持按申办方、项目状态、疾病领域等条件过滤
- 在仪表盘中以表格形式展示项目路径信息

### 9.2 项目人员分配统计 (Project Personnel Assignment Statistics)
**移除时间**: 2025-05-30
**移除原因**: 功能与人员指标重复，简化仪表盘数据展示

**已移除的组件**:
- 后端模型: `ProjectPersonnelCount`
- 后端命令: `get_project_personnel_counts`
- 后端仓库方法: `get_project_personnel_counts`
- 后端服务方法: `get_project_personnel_counts`
- 前端服务方法: `getProjectPersonnelCounts`
- 前端状态管理: `projectPersonnel` 相关状态
- 前端UI组件: 项目人员分配统计表格

**原功能描述**:
- 显示各项目分配的人员数量统计
- 包含项目名称、人员数量、主要研究者、项目状态等信息
- 支持多维度筛选和排序
- 以表格形式展示项目人员分配详情

**替代方案**:
- 人员相关统计已整合到"人员分析视图"中
- 通过人员指标卡片和图表提供更直观的人员数据展示
- 人员工作负荷和角色分布图表提供更详细的人员分析
