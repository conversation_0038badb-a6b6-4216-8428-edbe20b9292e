---
noteId: "46ce43103e9411f0b4ec13362c7e567c"
tags: []

---

# GCPM 前端架构分析

## 执行摘要

GCPM（良好临床实践管理）前端是一个使用现代Web技术构建的综合性临床研究管理系统。该应用程序使用 **Svelte 5** 和 **SvelteKit v2** 作为核心框架，采用 **Tailwind CSS** 进行样式设计，并使用 **shadcn-svelte** 组件库构建UI组件。

### 关键架构亮点
- **框架**: Svelte 5 + SvelteKit v2 with TypeScript
- **UI库**: shadcn-svelte、bits-ui 和自定义组件
- **状态管理**: 具有响应式模式的 Svelte stores
- **API集成**: 与 @gcpm/sdk 的类型安全 SDK 集成
- **身份验证**: 基于JWT的身份验证和路由保护
- **路由**: 基于文件的路由和分组布局
- **测试**: Playwright 用于E2E测试，Vitest 用于单元测试

## 前端架构概述

### 技术栈

该应用程序采用现代化、类型安全的技术栈：

**核心技术**
- **Svelte 5**: 具有增强响应性和性能的最新版本
- **SvelteKit v2**: 用于路由、SSR和构建优化的全栈框架
- **TypeScript**: 整个应用程序的类型安全
- **Vite**: 快速构建工具和开发服务器

**UI与样式**
- **Tailwind CSS v4**: 实用优先的CSS框架
- **shadcn-svelte**: 高质量、可访问的组件库
- **bits-ui**: 用于复杂交互的无头UI组件
- **Lucide Svelte**: 用于一致图标的图标库

**数据管理**
- **@tanstack/svelte-query**: 服务器状态管理和缓存
- **Svelte Stores**: 客户端状态管理
- **@gcpm/sdk**: 从后端OpenAPI规范生成的类型安全API客户端

**开发与测试**
- **Playwright**: 端到端测试框架
- **Vitest**: 单元测试框架
- **ESLint + Prettier**: 代码质量和格式化

### 项目结构

```
apps/gcpm-frontend/
├── src/
│   ├── lib/                    # 共享工具和组件
│   │   ├── components/         # 可重用的UI组件
│   │   │   ├── ui/            # shadcn-svelte 组件
│   │   │   └── *.svelte       # 自定义应用程序组件
│   │   ├── api/               # API服务层
│   │   ├── stores/            # 用于状态管理的 Svelte stores
│   │   ├── services/          # 业务逻辑服务
│   │   ├── types/             # TypeScript 类型定义
│   │   ├── utils/             # 工具函数
│   │   └── schemas/           # 验证模式
│   └── routes/                # 基于文件的路由结构
│       ├── (gcpm)/           # 主应用程序路由
│       ├── (guidance)/       # 指导系统路由
│       ├── (secret)/         # 受保护的路由
│       ├── crc/              # CRC（临床研究协调员）路由
│       ├── docs/             # 文档路由
│       └── login/            # 身份验证路由
├── static/                    # 静态资源
├── tests/                     # 测试文件
└── scripts/                   # 构建和部署脚本
```

## 主要功能和特性

### 1. 项目管理
- **研究项目**: 全面的项目生命周期管理
- **项目创建**: 带验证的多步骤项目设置
- **项目详情**: 详细的项目信息，包含不同方面的选项卡
- **项目归档**: 管理项目归档系统

### 2. 患者管理
- **患者注册**: 完整的患者信息管理
- **患者创建**: 带疾病选择的新患者注册
- **患者档案**: 详细的患者信息和病史
- **访问调度**: 患者访问预约系统

### 3. 研究管理
- **设备管理**: 临床设备跟踪和管理
- **质量控制**: 研究质量保证工作流程
- **访问管理**: 临床访问调度和跟踪

### 4. 团队管理
- **用户管理**: 团队成员管理
- **角色管理**: 项目角色分配
- **CRC白名单**: 临床研究协调员访问控制
- **部门管理**: 组织结构管理

### 5. 统计与报告
- **患者统计**: 全面的患者数据分析
- **疾病分析**: 疾病特定报告
- **项目指标**: 项目绩效指标
- **导出功能**: 各种格式的数据导出

### 6. 身份验证与授权
- **JWT身份验证**: 安全的基于令牌的身份验证
- **路由保护**: 带自动重定向的受保护路由
- **基于角色的访问**: 不同用户类型的不同访问级别

## 组件层次结构和组织

### UI组件结构

应用程序遵循良好组织的组件层次结构：

**基础UI组件 (`lib/components/ui/`)**
- 基于 shadcn-svelte 和 bits-ui 构建
- 高度可重用和可访问
- 一致的设计系统实现

**应用程序组件 (`lib/components/`)**
- `app-sidebar.svelte`: 主导航侧边栏
- `nav-main.svelte`: 主要导航组件
- `nav-user.svelte`: 用户配置文件导航
- `page-layout.svelte`: 通用页面布局包装器
- `loading-spinner/`: 加载状态组件
- `date-picker.svelte`: 自定义日期选择组件

**功能特定组件**
- 每个路由组包含自己的 `(components)` 目录
- 组件与其使用位置共同定位
- 共享组件提升到 `lib/components/`

### 状态管理模式

应用程序使用混合状态管理方法：

**Svelte Stores**
- `auth.store.ts`: 身份验证状态
- `researchProjectsStore.ts`: 项目相关数据
- `usersStore.ts`: 用户管理数据
- `patientsStore.ts`: 患者信息
- `devicesStore.ts`: 设备管理状态

**服务类**
- `PatientService`: 具有响应式状态的患者数据管理
- `AuthService`: 身份验证逻辑和令牌管理
- 用于复杂状态管理的页面特定服务

**TanStack Query**
- 服务器状态管理和缓存
- 自动后台重新获取
- 乐观更新和错误处理

## 数据流和API集成

### API集成架构

应用程序使用复杂的API集成策略：

**基于SDK的方法**
- `@gcpm/sdk`: 从OpenAPI规范自动生成的TypeScript SDK
- 具有自动验证的类型安全API调用
- 整个应用程序的一致错误处理

**身份验证流程**
1. JWT令牌存储在localStorage中
2. 通过请求拦截器自动注入令牌
3. 身份验证失败时自动重定向到登录页面
4. 令牌刷新和会话管理

**数据获取模式**
1. **页面加载函数**: 在 `+page.ts` 文件中进行服务器端数据获取
2. **客户端服务**: 用于动态数据的响应式服务
3. **TanStack Query**: 高级缓存和同步
4. **手动获取**: 针对特定操作的直接API调用

### SDK优先方法

应用程序利用生成的SDK进行类型安全的API交互：

**优势**
- 从OpenAPI规范自动生成类型
- 跨端点的一致错误处理
- 内置请求/响应验证
- IDE自动完成和类型检查

**实现**
- 具有生成函数的 `@gcpm/sdk` 包
- 在 `hooks.client.ts` 中的集中配置
- 用于身份验证的请求拦截器
- 用于错误处理的响应拦截器

### 身份验证流程

**JWT令牌管理**
1. 在localStorage中存储令牌以实现持久化
2. 通过拦截器自动注入令牌
3. 令牌验证和刷新逻辑
4. 令牌过期时自动注销

**路由保护**
- 在配置中定义的受保护路由
- 对未认证用户自动重定向到登录页面
- 登录后恢复之前的页面
- 基于角色的访问控制（计划中）

### 错误处理策略

**集中式错误处理**
- 一致的错误响应处理
- 带有toast通知的用户友好错误消息
- 网络故障的优雅降级
- 加载状态和错误边界

## 路由结构

应用程序使用SvelteKit的基于文件的路由和分组布局：

### 路由组

**主应用程序 (`(gcpm)/`)**
- 主要应用程序界面
- 侧边栏导航布局
- 需要身份验证的受保护路由

**指导系统 (`(guidance)/`)**
- 专门的指导工作流程
- 用于专注任务的简化布局

**CRC界面 (`crc/`)**
- 临床研究协调员专用界面
- 定制的导航和功能

**文档 (`docs/`)**
- 组件文档
- 模式文档
- 开发指南

### 布局层次结构

```
+layout.svelte (根布局)
├── (gcpm)/+layout.svelte (主应用)
├── (guidance)/+layout.svelte (指导)
├── crc/+layout.svelte (CRC界面)
└── docs/+layout.svelte (文档)
```

## 关键架构模式

### 1. 组件共同定位
- 组件在其使用位置附近组织
- 路由目录中的功能特定组件
- 共享组件提升到 `lib/components/`

### 2. 类型安全
- 全面的TypeScript使用
- 从后端API生成的类型
- 使用Zod进行模式验证

### 3. 响应式状态管理
- Svelte的内置响应性
- 用于全局状态的Stores
- 用于复杂状态逻辑的服务类

### 4. 渐进式增强
- 适当的服务器端渲染
- 用于交互性的客户端水合
- 对JavaScript禁用环境的优雅回退

### 5. 可访问性优先
- 具有内置可访问性的shadcn-svelte组件
- 语义HTML结构
- 键盘导航支持
- 屏幕阅读器兼容性

## Mermaid 图表

### 1. 组件关系图

此图显示了应用程序中主要组件之间的层次关系：

```mermaid
graph TD
    A[根布局] --> B[Toaster]
    A --> C[TooltipProvider]
    C --> D[路由组]

    D --> E[GCPM布局]
    D --> F[指导布局]
    D --> G[CRC布局]
    D --> H[文档布局]

    E --> I[侧边栏提供者]
    I --> J[应用侧边栏]
    I --> K[侧边栏插入]

    J --> L[团队切换器]
    J --> M[主导航]
    J --> N[项目导航]
    J --> O[用户导航]

    K --> P[头部]
    K --> Q[主内容]

    Q --> R[页面组件]
    R --> S[患者管理]
    R --> T[研究项目]
    R --> U[团队管理]
    R --> V[统计]
    R --> W[设备管理]

    S --> S1[患者表格]
    S --> S2[患者表单]
    S --> S3[患者对话框]

    T --> T1[项目表格]
    T --> T2[项目表单]
    T --> T3[项目详情]

    U --> U1[团队表格]
    U --> U2[用户管理]
    U --> U3[角色分配]

    V --> V1[统计概览]
    V --> V2[疾病网格]
    V --> V3[图表]

    W --> W1[设备列表]
    W --> W2[设备表单]
    W --> W3[检查表单]
```

### 2. 用户流程图

此图说明了用户在应用程序中的主要使用流程：

```mermaid
flowchart TD
    A[用户访问] --> B{已认证?}
    B -->|否| C[登录页面]
    B -->|是| D[主仪表板]

    C --> E[输入凭据]
    E --> F{有效?}
    F -->|否| C
    F -->|是| G[存储JWT令牌]
    G --> D

    D --> H[选择模块]
    H --> I[项目管理]
    H --> J[患者管理]
    H --> K[研究管理]
    H --> L[团队管理]
    H --> M[统计]

    I --> I1[查看项目]
    I --> I2[创建项目]
    I --> I3[编辑项目]
    I1 --> I4[项目详情]
    I4 --> I5[管理成员]
    I4 --> I6[查看受试者]
    I4 --> I7[质量控制]

    J --> J1[查看患者]
    J --> J2[创建患者]
    J --> J3[编辑患者]
    J1 --> J4[患者档案]
    J4 --> J5[安排访问]
    J4 --> J6[病史]

    K --> K1[设备管理]
    K --> K2[访问管理]
    K1 --> K3[设备检查]
    K2 --> K4[安排访问]

    L --> L1[查看团队]
    L --> L2[管理用户]
    L --> L3[分配角色]
    L --> L4[CRC白名单]

    M --> M1[患者统计]
    M --> M2[项目报告]
    M --> M3[导出数据]
```

### 3. 架构概览图

此图显示了整体前端架构以及不同层之间的交互方式：

```mermaid
graph TB
    subgraph "表现层"
        A[Svelte组件]
        B[shadcn-svelte UI]
        C[自定义组件]
        D[页面布局]
    end

    subgraph "状态管理层"
        E[Svelte Stores]
        F[TanStack Query]
        G[服务类]
        H[本地状态]
    end

    subgraph "业务逻辑层"
        I[API服务]
        J[身份验证服务]
        K[数据验证]
        L[错误处理]
    end

    subgraph "数据层"
        M[@gcpm/sdk]
        N[HTTP客户端]
        O[本地存储]
        P[会话存储]
    end

    subgraph "外部服务"
        Q[GCPM后端API]
        R[MBGL API]
        S[身份验证服务器]
    end

    A --> E
    B --> E
    C --> E
    D --> E

    E --> I
    F --> I
    G --> I
    H --> I

    I --> M
    J --> M
    K --> M
    L --> M

    M --> Q
    N --> Q
    N --> R
    N --> S

    O --> J
    P --> J
```

### 4. 数据流图

此图显示了数据如何从用户交互流向API调用：

```mermaid
sequenceDiagram
    participant U as 用户
    participant C as 组件
    participant S as Store/服务
    participant API as API层
    participant SDK as @gcpm/sdk
    participant BE as 后端

    U->>C: 用户交互
    C->>S: 更新状态/触发操作
    S->>API: 调用API服务
    API->>SDK: 使用SDK函数
    SDK->>BE: HTTP请求
    BE-->>SDK: 响应数据
    SDK-->>API: 类型化响应
    API-->>S: 处理数据
    S-->>C: 更新响应式状态
    C-->>U: UI更新

    Note over C,S: 响应式更新
    Note over API,SDK: 类型安全
    Note over SDK,BE: 身份验证
```

## 详细组件分析

### 核心应用程序组件

**AppSidebar组件**:
- 应用程序的中央导航中心
- 集成TeamSwitcher、NavMain、NavProjects和NavUser
- 具有可折叠功能的响应式设计
- 使用ScrollArea处理溢出

**导航组件**:
- `NavMain`: 具有层次菜单结构的主导航
- `NavUser`: 用户配置文件和身份验证控件
- `TeamSwitcher`: 组织/团队选择界面
- `NavProjects`: 项目特定导航项

**布局组件**:
- `PageLayout`: 具有加载和错误状态的通用包装器
- 不同应用程序部分的路由特定布局
- 具有侧边栏触发器的一致头部结构

### 功能特定组件

**患者管理**:
- `PatientTable`: 具有过滤和分页的数据表
- `PatientInfoDialog`: 患者详情模态框
- `PatientSyncDialog`: 数据同步界面
- `PatientFilterDialog`: 高级过滤选项

**研究项目**:
- `ProjectTable`: 具有状态指示器的项目列表
- `ProjectDetailsMembers`: 团队成员管理
- `ProjectSyncDialog`: 项目同步
- 用于状态可视化的各种徽章组件

**统计与报告**:
- `StatisticsOverview`: 仪表板摘要组件
- `DiseaseGrid`: 疾病特定数据可视化
- 使用LayerChart库的图表组件
- 报告的导出功能

## 状态管理深入分析

### Store架构

应用程序使用结构良好的store架构：

**身份验证Store (`auth.store.ts`)**:
- 具有localStorage持久化的JWT令牌管理
- 应用程序加载时自动初始化
- 用于SSR兼容性的浏览器环境检测

**领域特定Stores**:
- `researchProjectsStore.ts`: 项目数据和相关实体
- `usersStore.ts`: 用户管理和团队数据
- `patientsStore.ts`: 患者信息（已弃用，改用服务）
- `devicesStore.ts`: 临床设备管理

**基于服务的状态管理**:
- `PatientService`: 具有内置状态的响应式服务类
- 分页和过滤功能
- 错误处理和加载状态
- 基于游标的分页以提高性能

### 数据同步

**TanStack Query集成**:
- 服务器状态缓存和同步
- 用于数据新鲜度的后台重新获取
- 用于更好用户体验的乐观更新
- 查询失效策略

**实时更新**:
- 用于响应式更新的Store订阅
- 事件驱动的状态变化
- 自动UI同步

## 性能考虑

### 优化策略

**代码分割**:
- 使用SvelteKit的基于路由的代码分割
- 重型组件的动态导入
- 非关键功能的延迟加载

**数据加载**:
- 初始页面加载的服务器端渲染
- 交互性的客户端水合
- 大型数据集的基于游标的分页
- 使用TanStack Query的高效缓存

**包优化**:
- 用于消除未使用代码的树摇
- Vite的优化构建过程
- 使用Tailwind CSS的CSS清理
- 资源优化和压缩

### 可访问性功能

**内置可访问性**:
- 具有ARIA支持的shadcn-svelte组件
- 整个应用程序的键盘导航
- 屏幕阅读器兼容性
- 模态框和对话框的焦点管理

**自定义可访问性**:
- 语义HTML结构
- 正确的标题层次结构
- 图像和图标的替代文本
- 颜色对比度合规性

## 测试策略

### 测试框架

**端到端测试**:
- 用于全面E2E测试的Playwright
- 跨浏览器测试功能
- 视觉回归测试
- 用于隔离测试的API模拟

**单元测试**:
- 用于快速单元测试的Vitest
- 使用Svelte Testing Library的组件测试
- Store和服务测试
- 工具函数测试

### 质量保证

**代码质量**:
- 用于类型安全的TypeScript
- 用于代码检查的ESLint
- 用于代码格式化的Prettier
- 用于质量门控的预提交钩子

**性能监控**:
- Vite的内置性能指标
- 包分析工具
- 运行时性能监控
- Core Web Vitals跟踪

## 关键发现和架构优势

### 优势

1. **现代技术栈**: 利用Svelte 5和SvelteKit v2等前沿技术
2. **类型安全**: 全面的TypeScript使用和生成的API类型
3. **组件架构**: 组织良好、可重用的组件结构
4. **状态管理**: 结合Svelte stores和服务类的混合方法
5. **API集成**: 从后端规范自动生成的类型安全SDK
6. **可访问性**: 使用shadcn-svelte的内置可访问性功能
7. **性能**: 具有代码分割和缓存的优化构建过程
8. **测试**: 包含E2E和单元测试的全面测试策略

### 改进领域

1. **身份验证**: 路由保护可以通过更强大的中间件得到增强
2. **错误边界**: 更全面的错误边界实现
3. **离线支持**: 用于离线功能的渐进式Web应用功能
4. **实时功能**: 用于实时更新的WebSocket集成
5. **国际化**: 用于全球部署的多语言支持

## 未来开发建议

### 短期改进

1. **增强错误处理**: 实现全局错误边界和更好的错误恢复
2. **性能监控**: 添加运行时性能监控和分析
3. **测试覆盖率**: 增加测试覆盖率，特别是复杂用户流程
4. **文档**: 扩展组件文档和使用示例

### 长期增强

1. **微前端架构**: 考虑拆分为更小的独立应用程序
2. **实时协作**: 为协作工作流程添加实时功能
3. **高级分析**: 实现全面的用户分析和报告
4. **移动优化**: 通过响应式设计改进增强移动体验
5. **渐进式Web应用**: 添加PWA功能以实现离线功能和类似应用的体验

## 前端业务模块与API端点映射

### 概述

GCPM前端应用程序通过类型安全的SDK与后端API进行交互，该SDK从OpenAPI规范自动生成。本节提供了各个前端业务模块与其对应API端点的详细映射关系，包括数据流、依赖关系和具体的使用场景。

**API总览**:
- **总接口数**: 156个
- **分类总数**: 24个
- **数据模型**: 134个
- **SDK版本**: 基于OpenAPI 3.0.0规范自动生成

**核心特性**:
- 类型安全的API调用
- 自动请求/响应验证
- 统一的错误处理机制
- JWT令牌自动注入
- 响应式数据缓存

### API分类概览

| 分类 | 接口数量 | 主要功能 | 前端模块 |
|------|----------|----------|----------|
| **患者管理** | 10 | 患者CRUD、疾病管理、同步 | `/patient/*` |
| **研究项目列表** | 32 | 项目管理、成员管理、受试者 | `/research-projects/*` |
| **访视** | 22 | 访视预约、完成记录、同步 | `/visits/*`, CRC, 导诊 |
| **CRC工作面板** | 10 | CRC专用功能、质量控制 | `/crc/*` |
| **导诊工作面板** | 5 | 导诊专用访视管理 | `/(guidance)/*` |
| **团队管理** | 7 | 成员管理、部门管理 | `/team-management/*` |
| **临床设备** | 7 | 设备管理、事件记录 | `/(guidance)/device-management/*` |
| **推荐联系人管理** | 9 | 推荐人管理、关联记录 | 各模块的推荐功能 |
| **CRCWhitelist** | 5 | CRC白名单管理 | 团队管理模块 |
| **引用数据** | 9 | 基础数据、选择器 | 全局选择器组件 |
| **慢病管理系统患者** | 3 | MBGL患者数据 | 患者搜索、统计 |
| **慢病管理系统项目** | 3 | MBGL项目数据 | 项目选择器 |
| **慢病管理系统疾病** | 1 | MBGL疾病数据 | 疾病信息 |
| **auth** | 2 | 用户认证 | `/login`, `/auth/*` |
| **User** | 2 | 用户查询 | 用户选择器 |
| **CRCRegister** | 2 | CRC注册 | CRC注册流程 |
| **临床事件** | 4 | 临床事件管理 | 事件记录功能 |
| **文档管理** | 2 | 文档上传管理 | 文档相关功能 |
| **Goldata** | 5 | 金数据集成 | 外部数据同步 |
| **MBGLSystemUsers** | 7 | MBGL用户管理 | 用户数据同步 |
| **PatientCqrs** | 5 | 患者CQRS操作 | 患者管理增强 |
| **Schedule** | 2 | 定时任务 | 后台数据同步 |
| **项目成员用户列表** | 1 | 项目成员查询 | 项目成员管理 |
| **App** | 1 | 应用基础信息 | 应用初始化 |

### 数据流与依赖关系

#### 核心数据流模式

```mermaid
graph TD
    A[用户认证] --> B[引用数据加载]
    B --> C[业务模块初始化]

    C --> D[患者管理]
    C --> E[项目管理]
    C --> F[访视管理]
    C --> G[团队管理]

    D --> H[MBGL患者同步]
    E --> I[MBGL项目同步]
    F --> J[访视完成记录]

    H --> K[统计分析]
    I --> K
    J --> K

    D --> L[推荐联系人]
    E --> M[项目成员]
    F --> N[CRC工作面板]
    F --> O[导诊工作面板]

    G --> P[CRC白名单]
    N --> Q[质量控制事件]

    R[临床设备] --> S[设备事件记录]
    T[金数据集成] --> U[外部数据同步]
```

#### API调用依赖关系

**1. 认证依赖链**
- 所有API调用 → `auth/login` (JWT令牌获取)
- JWT令牌 → 自动注入到所有后续请求头

**2. 引用数据依赖**
- 表单组件 → `reference-data/*` (选择器数据)
- 患者创建 → `reference-data/diseases` (疾病选择)
- 项目创建 → `reference-data/sponsors`, `reference-data/object-type` 等

**3. 跨模块数据依赖**
- 访视管理 → 患者管理 (患者信息)
- 访视管理 → 项目管理 (项目信息)
- 统计分析 → 访视数据 + MBGL患者数据
- CRC工作面板 → 访视管理 + 质量控制
- 推荐联系人 → 患者管理 (关联记录)

**4. 外部系统集成**
- GCPM → MBGL (慢病管理系统)
- GCPM → 金数据 (外部表单系统)
- 双向数据同步和状态检查

## 详细模块映射

### 1. 患者管理模块 (Patient Management)

**前端路由**: `/patient/*`

**主要功能**:
- 患者信息的创建、查询、更新和删除
- 患者分页查询和过滤
- 患者与慢病管理系统的同步
- 患者疾病信息管理
- 推荐联系人关联管理

#### 核心API端点

**PatientCqrs (新版患者API)**:

| HTTP方法 | API端点 | 功能描述 | 数据模型 | 前端使用位置 |
|---------|---------|----------|----------|-------------|
| `POST` | `/patient` | 创建新患者 | `CreatePatientRequestDto` → `CreatePatientResponseDto` | 患者创建表单 |
| `GET` | `/patient` | 分页查询患者列表 | 查询参数 → `GetPatientWithPaginationResponseDto` | `PatientTableService.loadPatients()` |
| `GET` | `/patient/{id}` | 获取患者详情 | `GetPatientResponseDto` | 患者详情页面 |
| `PUT` | `/patient/{id}` | 更新患者信息 | `UpdatePatientRequestDto` → `UpdatePatientResponseDto` | 患者编辑表单 |
| `GET` | `/patient/{patientID}/sync-to-mbgl` | 同步患者到MBGL | 同步状态响应 | 患者同步功能 |

**患者管理 (传统API)**:

| HTTP方法 | API端点 | 功能描述 | 数据模型 | 前端使用位置 |
|---------|---------|----------|----------|-------------|
| `POST` | `/patients` | 创建患者 | `PatientCreateRequestDto` → `PatientCreateResponseDto` | 患者创建表单 |
| `GET` | `/patients` | 查找患者 | 查询参数 → 患者列表 | 患者搜索功能 |
| `GET` | `/patients/pagination` | 分页查询患者 | 分页参数 → `PatientPaginationResponseDto` | 患者列表分页 |
| `GET` | `/patients/{id}` | 根据ID获取患者详情 | `PatientQueryResponseDto` | 患者详情查看 |
| `PATCH` | `/patients/{id}` | 更新患者信息 | `PatientUpdateRequestDto` → `PatientUpdateResponseDto` | 患者信息编辑 |
| `DELETE` | `/patients/{id}` | 软删除患者 | `PatientUpdateResponseDto` | 患者删除功能 |
| `POST` | `/patients/{id}/restore` | 恢复已删除患者 | `PatientUpdateResponseDto` | 患者恢复功能 |
| `GET` | `/patients/deleted` | 查询已删除患者 | `PatientPaginationResponseDto` | 已删除患者管理 |
| `GET` | `/patients/{id}/diseases` | 获取患者疾病列表 | 疾病列表 | 患者疾病信息 |
| `POST` | `/patients/patients/{patientId}/mbgl-sync` | 同步GCPM患者到MBGL | `MbglPatientCreateRequestDto` | 患者同步功能 |

#### 数据流特点

**创建流程**:
1. 前端表单 → `reference-data/diseases` (获取疾病选项)
2. 用户填写 → `POST /patient` 或 `POST /patients`
3. 创建成功 → 可选择同步到MBGL系统

**查询流程**:
1. 列表页面 → `GET /patient` (分页查询)
2. 详情页面 → `GET /patient/{id}` (详细信息)
3. 疾病信息 → `GET /patients/{id}/diseases`

**更新流程**:
1. 获取当前数据 → `GET /patient/{id}`
2. 表单编辑 → `PUT /patient/{id}` 或 `PATCH /patients/{id}`
3. 同步更新 → 可选择同步到MBGL

**关键特性**:
- 支持基于游标的分页以提高性能
- 集成慢病管理系统(MBGL)数据同步
- 提供软删除和恢复功能
- 支持多种过滤条件（姓名、电话、性别、生日等）
- 双API体系支持（新版PatientCqrs + 传统患者管理）

### 2. 研究项目管理模块 (Research Project Management)

**前端路由**: `/research-projects/*`

**主要功能**:
- 研究项目的创建、查询、更新
- 项目成员管理
- 项目状态和阶段管理
- 项目与慢病管理系统同步
- 受试者管理
- 项目统计分析

#### 核心API端点

**DDD研究项目 (新版API)**:

| HTTP方法 | API端点 | 功能描述 | 数据模型 | 前端使用位置 |
|---------|---------|----------|----------|-------------|
| `GET` | `/ddd-research-projects` | 获取所有研究项目 | `GetAllResearchProjectResponseDto[]` | 项目列表页面 |
| `POST` | `/ddd-research-projects` | 创建研究项目 | `CreateProjectRequestDto` → `CreateProjectResponseDto` | 项目创建表单 |
| `GET` | `/ddd-research-projects/{id}` | 获取项目详情 | `GetProjectDetailsResponseDto` | 项目详情页面 |
| `PATCH` | `/ddd-research-projects/{id}` | 更新研究项目 | 更新请求 → 项目信息 | 项目编辑功能 |
| `POST` | `/ddd-research-projects/{projectID}/members` | 添加项目成员 | `AddMemberRequestDto` → `AddMemberResponseDto` | 成员管理 |
| `PUT` | `/ddd-research-projects/{projectID}/members` | 更新项目成员 | `UpdateProjectMemberRequestDto` → `UpdateProjectMemberResponseDto` | 成员信息编辑 |

**研究项目列表 (传统API)**:

| HTTP方法 | API端点 | 功能描述 | 数据模型 | 前端使用位置 |
|---------|---------|----------|----------|-------------|
| `GET` | `/research-projects/all-projects` | 获取所有研究项目 | 项目列表 | 兼容性支持 |
| `GET` | `/research-projects/project-by-id` | 根据ID获取项目 | 项目详情 | 项目查询 |
| `GET` | `/research-projects/all-project-statuses` | 获取所有项目状态 | `GetProjectStatusResponseDto[]` | 状态选择器 |
| `GET` | `/research-projects/all-project-phases` | 获取所有项目阶段 | `GetProjectPhaseResponseDto[]` | 阶段选择器 |
| `GET` | `/research-projects/all-research-sponsors` | 获取所有申办方 | `GetSponsorResponseDto[]` | 申办方选择器 |
| `GET` | `/research-projects/all-projects-members` | 获取所有项目成员 | `GetProjectMemberResponseDto[]` | 成员管理 |
| `GET` | `/research-projects/project-members` | 获取项目成员 | 项目成员列表 | 特定项目成员 |
| `GET` | `/research-projects/all-project-role-types` | 获取所有角色类型 | `GetProjectRoleTypeResponseDto[]` | 角色分配 |
| `GET` | `/research-projects/project-diseases` | 获取项目疾病对应表 | 疾病关联数据 | 疾病关联 |
| `GET` | `/research-projects/all-diseases` | 获取所有疾病 | `GetDiseaseResponseDto[]` | 疾病选择器 |
| `POST` | `/research-projects/create-project-filing` | 创建项目备案 | `CreateProjectFilingDto` | 项目备案表单 |
| `GET` | `/research-projects/all-research-object-types` | 获取研究对象类型 | `GetObjectTypeResponseDto[]` | 对象类型选择 |
| `GET` | `/research-projects/{projectID}/event-statistics` | 获取项目事件统计 | 统计数据 | 项目统计页面 |
| `POST` | `/research-projects/{projectID}/update-project-basic-info` | 更新项目基本信息 | `ResearchProjectEditBasicInfoDto` | 基本信息编辑 |
| `POST` | `/research-projects/{projectID}/sync-to-mbgl` | 同步到慢病管理系统 | 同步结果 | 项目同步功能 |

**受试者相关API**:

| HTTP方法 | API端点 | 功能描述 | 数据模型 | 前端使用位置 |
|---------|---------|----------|----------|-------------|
| `GET` | `/research-projects/subjects/{id}/subjects` | 获取项目受试者 | `Subject[]` | 受试者列表 |
| `GET` | `/research-projects/subjects/{id}/subject/{subjectId}/events` | 获取受试者事件 | `SubjectEvent[]` | 受试者事件历史 |
| `GET` | `/research-projects/subjects/{id}/patient` | 通过受试者ID获取患者 | 患者信息 | 受试者详情 |
| `GET` | `/research-projects/subjects/{id}/project` | 通过受试者ID获取项目 | `ProjectQueryResponseDto` | 项目关联信息 |
| `GET` | `/research-projects/subjects/analyzer/subject-event` | 获取受试者事件统计 | `SubjectEventQueryDto[]` | 统计分析 |

#### 数据流特点

**项目创建流程**:
1. 获取基础数据 → `reference-data/sponsors`, `reference-data/object-type`, `reference-data/research-project-phases` 等
2. 表单填写 → `POST /ddd-research-projects`
3. 成员添加 → `POST /ddd-research-projects/{projectID}/members`
4. 可选同步 → `POST /research-projects/{projectID}/sync-to-mbgl`

**项目管理流程**:
1. 项目列表 → `GET /ddd-research-projects`
2. 项目详情 → `GET /ddd-research-projects/{id}`
3. 成员管理 → `GET /research-projects/project-members`
4. 受试者管理 → `GET /research-projects/subjects/{id}/subjects`

**统计分析流程**:
1. 项目事件统计 → `GET /research-projects/{projectID}/event-statistics`
2. 受试者事件分析 → `GET /research-projects/subjects/analyzer/subject-event`
3. 数据可视化 → 前端图表组件

**关键特性**:
- 双API体系支持（DDD新版 + 传统API）
- 完整的项目生命周期管理
- 集成MBGL系统同步
- 受试者事件追踪
- 项目成员角色管理
- 统计分析支持

### 3. 访视管理模块 (Visit Management)

**前端路由**: `/visits/*`, CRC和导诊模块中的访视功能

**主要功能**:
- 访视预约的创建、查询、更新、删除
- 访视完成记录管理
- 访视与慢病管理系统同步
- 科室人员信息管理
- 访视统计分析

#### 核心API端点

**访视基础管理**:

| HTTP方法 | API端点 | 功能描述 | 数据模型 | 前端使用位置 |
|---------|---------|----------|----------|-------------|
| `GET` | `/visits` | 获取所有访视 | 访视列表 | 访视列表 |
| `GET` | `/visits/{id}` | 通过ID获取访视 | 访视详情 | 访视详情 |
| `PATCH` | `/visits/{id}` | 更新访视 | 更新请求 | 访视编辑 |
| `DELETE` | `/visits/{id}` | 删除访视 | 删除结果 | 访视删除 |

**访视预约管理**:

| HTTP方法 | API端点 | 功能描述 | 数据模型 | 前端使用位置 |
|---------|---------|----------|----------|-------------|
| `GET` | `/visits/reserve/date/{date}` | 通过日期获取访视预约 | 预约列表 | 日程管理 |
| `POST` | `/visits/reserve` | 创建访视预约 | `CreateReserveVisitRequestDto` | 访视预约创建 |
| `GET` | `/visits/reserve/{id}` | 获取访视预约详情 | 预约详情 | 预约详情查看 |
| `PATCH` | `/visits/reserve/{id}` | 更新访视预约 | `UpdateReserveVisitRequestDto` | 预约信息编辑 |
| `GET` | `/visits/reserve/department-staff` | 获取科室人员信息 | `DepartmentStaff[]` | 人员选择器 |
| `GET` | `/visits/reserve/is-visit-complete` | 检查访视是否完成 | `IsVisitCompleteDto` | 状态检查 |
| `POST` | `/visits/reserve/patient-state` | 获取患者状态 | `ReserveVisitPatientStateDto` | 患者状态查询 |
| `GET` | `/visits/reserve/{id}/check-mbgl-sync` | 检查MBGL同步状态 | `ReserveCheckMBGLSyncResultDto` | 同步状态检查 |
| `GET` | `/visits/reserve/{id}/resync-mbgl` | 重新同步到MBGL | 同步结果 | 重新同步功能 |
| `POST` | `/visits/reserve/crc` | CRC创建访视预约 | `ClinicalDataVisitReserveCreateRequestDto` | CRC访视预约 |
| `GET` | `/visits/reserve/analyzer/total` | 获取访视预约总量 | 统计数据 | 统计分析 |

**访视完成记录API**:

| HTTP方法 | API端点 | 功能描述 | 数据模型 | 前端使用位置 |
|---------|---------|----------|----------|-------------|
| `POST` | `/visits/complete-info-record/from-goldata` | 从金数据创建完成记录 | `CreateVisitCompleteRecordRequestDto` | 外部数据集成 |
| `POST` | `/visits/complete-info-record/from-gcpm-frontend` | 从前端创建完成记录 | `FinishVisitCreateDto` | 访视完成登记 |
| `GET` | `/visits/complete-info-record/history` | 获取历史完成记录 | 历史记录列表 | 历史记录查看 |
| `GET` | `/visits/complete-info-record/current-history` | 获取最新历史记录 | 最新记录 | 最新记录查看 |
| `POST` | `/visits/complete-info-record/mbgl-sync` | 同步到慢病管理系统 | 同步结果 | 数据同步 |
| `POST` | `/visits/complete-info-record/mbgl-check` | 检查MBGL表单有效性 | `FinishVisitMbglCheckDto` | 表单验证 |

#### 数据流特点

**访视预约流程**:
1. 获取基础数据 → `visits/reserve/department-staff` (人员信息)
2. 患者状态检查 → `POST /visits/reserve/patient-state`
3. 创建预约 → `POST /visits/reserve`
4. 同步检查 → `GET /visits/reserve/{id}/check-mbgl-sync`

**访视完成流程**:
1. 访视执行 → 前端表单填写
2. 完成记录 → `POST /visits/complete-info-record/from-gcpm-frontend`
3. MBGL验证 → `POST /visits/complete-info-record/mbgl-check`
4. 数据同步 → `POST /visits/complete-info-record/mbgl-sync`

**统计分析流程**:
1. 访视总量 → `GET /visits/reserve/analyzer/total`
2. 历史记录 → `GET /visits/complete-info-record/history`
3. 数据可视化 → 前端统计组件

**关键特性**:
- 完整的访视生命周期管理
- 多角色访视创建支持（CRC、导诊）
- MBGL系统双向同步
- 访视状态实时检查
- 科室人员信息集成
- 外部系统数据集成（金数据）

### 4. 团队管理模块 (Team Management)

**前端路由**: `/team-management/*`

**主要功能**:
- 团队成员的添加、更新、删除
- 部门管理
- 用户状态管理
- CRC白名单管理

#### 核心API端点

**团队管理**:

| HTTP方法 | API端点 | 功能描述 | 数据模型 | 前端使用位置 |
|---------|---------|----------|----------|-------------|
| `DELETE` | `/team-management/{userID}` | 删除团队成员 | 删除结果 | 成员删除功能 |
| `PUT` | `/team-management/{userID}` | 更新成员信息 | `UpdateTeamMemberInfoDto` | 成员信息编辑 |
| `GET` | `/team-management/all-user-profile` | 获取所有用户基本信息 | `GetUserProfileResponseDto[]` | 用户列表 |
| `GET` | `/team-management/all-department` | 获取所有部门 | 部门列表 | 部门选择器 |
| `GET` | `/team-management/all-status` | 获取所有在职状态 | 状态列表 | 状态选择器 |
| `GET` | `/team-management/all-department-member` | 获取所有部门成员 | 部门成员列表 | 部门成员管理 |
| `POST` | `/team-management/add-member` | 添加团队成员 | `AddTeamMemberDto` | 成员添加表单 |

**用户查询**:

| HTTP方法 | API端点 | 功能描述 | 数据模型 | 前端使用位置 |
|---------|---------|----------|----------|-------------|
| `GET` | `/user` | 获取所有CRC用户 | `TempGetUserQueryResponseDto[]` | CRC用户列表 |
| `GET` | `/user/judgment-doctors` | 获取所有判定医生 | 医生列表 | 医生选择器 |

**CRC白名单管理**:

| HTTP方法 | API端点 | 功能描述 | 数据模型 | 前端使用位置 |
|---------|---------|----------|----------|-------------|
| `GET` | `/user/crc/whitelist` | 获取白名单用户列表 | `CRCWhitelistQueryResponseDto[]` | 白名单管理 |
| `POST` | `/user/crc/whitelist` | 添加CRC到白名单 | `CRCWhitelistCreateRequestDto` | 白名单添加 |
| `DELETE` | `/user/crc/whitelist/{id}` | 从白名单删除CRC | 删除结果 | 白名单删除 |
| `PATCH` | `/user/crc/whitelist/{id}` | 更新白名单CRC信息 | `CRCUpdateRequestDto` | 白名单编辑 |
| `GET` | `/user/crc/whitelist/{id}` | 获取白名单CRC信息 | `GetCrcWhitelistResponseDto` | 白名单详情 |

### 5. CRC工作面板模块 (CRC Work Panel)

**前端路由**: `/crc/*`

**主要功能**:
- CRC专用访视预约管理
- 质量控制事件管理（AE、SAE、PD）
- CRC注册和认证

#### 核心API端点

**访视预约管理**:

| HTTP方法 | API端点 | 功能描述 | 数据模型 | 前端使用位置 |
|---------|---------|----------|----------|-------------|
| `POST` | `/crc/reserve-visit` | 创建访视预约 | `CreateReserveVisitRequestDto` | CRC访视预约创建 |
| `GET` | `/crc/reserve-visit` | 获取访视预约 | `CrcReserveVisitResponseDto` | CRC访视预约查询 |
| `PATCH` | `/crc/reserve-visit/{id}` | 更新访视预约 | `UpdateReserveVisitRequestDto` | CRC访视预约编辑 |
| `POST` | `/crc/reserve-visit/{id}/cancel` | 取消访视预约 | `CancelReserveVisitDto` | CRC访视预约取消 |
| `GET` | `/crc/reserve-visit/pagination` | 分页获取访视预约 | `ReserveVisitPaginationResponseDto` | CRC访视列表 |
| `GET` | `/crc/main-visit-content` | 获取主要访视内容 | 访视内容选项 | 访视内容选择器 |

**质量控制事件管理**:

| HTTP方法 | API端点 | 功能描述 | 数据模型 | 前端使用位置 |
|---------|---------|----------|----------|-------------|
| `POST` | `/crc/quality-control/ae` | 创建AE事件 | `CreateQualityControlAEEventRequestDto` | AE事件创建表单 |
| `POST` | `/crc/quality-control/sae` | 创建SAE事件 | `CreateQualityControlSAEEventRequestDto` | SAE事件创建表单 |
| `POST` | `/crc/quality-control/pd` | 创建PD事件 | `CreateQualityControlPDEventRequestDto` | PD事件创建表单 |
| `GET` | `/crc/quality-control/events` | 获取质量控制事件 | `QueryQualityControlEventDto` | 质量控制事件列表 |

**CRC注册**:

| HTTP方法 | API端点 | 功能描述 | 数据模型 | 前端使用位置 |
|---------|---------|----------|----------|-------------|
| `POST` | `/user/crc/register/verification-code` | 获取验证码 | `CRCRegisterSendVerificationCodeRequestDto` | CRC注册验证 |
| `POST` | `/user/crc/register` | CRC注册 | `CRCRegisterRequestDto` | CRC注册表单 |

### 6. 导诊工作面板模块 (Guidance Work Panel)

**前端路由**: `/(guidance)/*`

**主要功能**:
- 导诊专用访视预约管理
- 按日期查询访视预约
- 未完成访视管理

#### 核心API端点

| HTTP方法 | API端点 | 功能描述 | 数据模型 | 前端使用位置 |
|---------|---------|----------|----------|-------------|
| `GET` | `/guidance/reserve-visit/date/{date}` | 按日期获取访视预约 | `GuidanceQueryReserveVisitResponseDto` | 导诊日程管理 |
| `GET` | `/guidance/reserve-visit` | 根据ID获取访视预约 | `GuidanceQueryReserveVisitResponseDto` | 导诊访视查询 |
| `POST` | `/guidance/reserve-visit` | 创建访视预约 | `GuidanceCreateReserveVisitRequestDto` | 导诊访视创建 |
| `GET` | `/guidance/reserve-visit/unfinished` | 获取未完成访视 | `GuidanceQueryReserveVisitResponseDto` | 未完成访视列表 |
| `PATCH` | `/guidance/reserve-visit/{id}` | 更新访视预约 | `GuidanceUpdateReserveVisitRequestDto` | 导诊访视编辑 |

### 7. 临床设备管理模块 (Clinical Device Management)

**前端路由**: `/(guidance)/device-management/*`

**主要功能**:
- 临床设备信息管理
- 设备检查和事件记录
- 设备绑定管理

#### 核心API端点

| HTTP方法 | API端点 | 功能描述 | 数据模型 | 前端使用位置 |
|---------|---------|----------|----------|-------------|
| `GET` | `/clinical-device` | 获取所有临床设备 | 设备列表 | 设备列表页面 |
| `GET` | `/clinical-device/{id}` | 根据ID获取设备 | 设备详情 | 设备详情页面 |
| `PUT` | `/clinical-device/{id}` | 更新设备信息 | 更新结果 | 设备信息编辑 |
| `POST` | `/clinical-device/event` | 创建设备事件 | `CreateClinicalDeviceEventDto` | 设备检查记录 |
| `POST` | `/clinical-device/event/from-goldata` | 从金数据添加设备事件 | `CreateClinicalDeviceEventRequestDto` | 外部数据集成 |
| `POST` | `/clinical-device/binding/from-goldata` | 从金数据添加设备绑定 | `CreateClinicalDeviceBindingRequestDto` | 设备绑定管理 |
| `POST` | `/clinical-device/from-goldata` | 从金数据注册设备 | `CreateClinicalDeviceRequestDto` | 设备注册 |

### 8. 身份验证模块 (Authentication)

**前端路由**: `/login`, `/auth/*`

**主要功能**:
- 用户登录和注册
- JWT令牌管理
- 会话管理

#### 核心API端点

| HTTP方法 | API端点 | 功能描述 | 数据模型 | 前端使用位置 |
|---------|---------|----------|----------|-------------|
| `POST` | `/auth/login` | 用户登录 | `LoginRequestDto` → `LoginResponseDto` | 登录表单 |
| `POST` | `/auth/register` | 用户注册 | `RegisterRequestDto` → `RegisterResponseDto` | 注册表单 |

### 9. 引用数据模块 (Reference Data)

**前端路由**: 各模块的选择器和下拉菜单

**主要功能**:
- 提供各种选择器数据
- 支持表单验证和数据一致性

#### 核心API端点

| HTTP方法 | API端点 | 功能描述 | 数据模型 | 前端使用位置 |
|---------|---------|----------|----------|-------------|
| `GET` | `/reference-data/subject-states` | 获取所有受试者状态 | `SubjectState[]` | 受试者状态选择器 |
| `GET` | `/reference-data/subject-event-types` | 获取所有受试者事件类型 | `SubjectEventTypeResponseDto[]` | 事件类型选择器 |
| `GET` | `/reference-data/visit-types` | 获取所有访视类型 | `TempJSJVisitTypeResponseDto[]` | 访视类型选择器 |
| `GET` | `/reference-data/diseases` | 获取所有疾病 | `DiseaseResponseDto[]` | 疾病选择器 |
| `GET` | `/reference-data/research-project-phases` | 获取所有研究分期 | `ProjectPhaseResponseDto[]` | 项目分期选择器 |
| `GET` | `/reference-data/research-project-statuses` | 获取所有研究状态 | `StatusResponseDto[]` | 项目状态选择器 |
| `GET` | `/reference-data/sponsors` | 获取所有申办方 | `SponsorResponseDto[]` | 申办方选择器 |
| `GET` | `/reference-data/object-type` | 获取所有研究对象类型 | `ObjectTypeResponseDto[]` | 对象类型选择器 |
| `GET` | `/reference-data/project-role-types` | 获取所有项目角色类型 | 角色类型列表 | 角色类型选择器 |

### 10. 慢病管理系统集成模块 (MBGL Integration)

**前端路由**: 各模块中的MBGL相关功能

**主要功能**:
- 与慢病管理系统的数据同步
- 患者和项目信息查询
- 疾病信息管理

#### 核心API端点

**患者相关**:

| HTTP方法 | API端点 | 功能描述 | 数据模型 | 前端使用位置 |
|---------|---------|----------|----------|-------------|
| `GET` | `/mbgl/patient` | 查询慢病系统患者 | `MbglPatientResponseDto[]` | 患者搜索功能 |
| `GET` | `/mbgl/patient/{patientId}` | 根据ID获取患者详情 | `MbglPatientQueryResponseDto` | 患者详情查看 |
| `GET` | `/mbgl/patient/report/total` | 获取时间段内新增患者 | `MbglSysRecommendPatientResponseDto[]` | 统计报告 |

**项目相关**:

| HTTP方法 | API端点 | 功能描述 | 数据模型 | 前端使用位置 |
|---------|---------|----------|----------|-------------|
| `GET` | `/mbgl/project` | 查询慢病系统项目 | `MbglProjectResponseDto[]` | 项目选择器 |
| `GET` | `/mbgl/project/{id}` | 获取项目信息 | 项目详情 | 项目详情 |
| `GET` | `/mbgl/project/{id}/patient-state/{patientId}` | 获取患者受试状态 | 患者状态 | 患者状态查询 |

**疾病相关**:

| HTTP方法 | API端点 | 功能描述 | 数据模型 | 前端使用位置 |
|---------|---------|----------|----------|-------------|
| `GET` | `/mbgl/disease/{id}` | 根据疾病ID获取疾病信息 | `MbglDiseaseQueryResponseDto` | 疾病详情查看 |

### 11. 推荐联系人管理模块 (Referrer Management)

**前端路由**: 各模块的推荐功能

**主要功能**:
- 推荐人信息管理
- 推荐关联记录管理
- 无推荐人患者管理

#### 核心API端点

| HTTP方法 | API端点 | 功能描述 | 数据模型 | 前端使用位置 |
|---------|---------|----------|----------|-------------|
| `POST` | `/referrer` | 创建推荐人 | `CreateReferrerRequestDto` → `CreateReferrerResponseDto` | 推荐人创建 |
| `GET` | `/referrer` | 分页查询推荐人 | `CursorPaginatedGetReferrersResponseDto` | 推荐人列表 |
| `GET` | `/referrer/{id}` | 获取推荐人详情 | `GetReferrersResponseDto` | 推荐人详情 |
| `PATCH` | `/referrer/{id}` | 更新推荐人信息 | 更新结果 | 推荐人编辑 |
| `DELETE` | `/referrer/{id}` | 删除推荐人 | 删除结果 | 推荐人删除 |
| `POST` | `/referrer/association-record` | 创建关联记录 | `CreateAssociationRecordDto` | 关联记录创建 |
| `GET` | `/referrer/association-record` | 查询关联记录 | `CursorPaginatedGetAssociationRecordResponseDto` | 关联记录列表 |
| `PATCH` | `/referrer/association-record/{id}` | 更新关联记录 | `UpdateAssociationRecordDto` → `UpdateAssociationRecordResponseDto` | 关联记录编辑 |
| `GET` | `/referrer/no-referrer-mbgl-patients` | 获取无推荐人的MBGL患者 | `CursorPaginatedGetNoReferrerMbglPatientsResponseDto` | 无推荐人患者管理 |

### 12. 数据统计分析中心 (Data Statistics Analysis Center)

**前端路由**: `/statstics/*` (注意：路径中存在拼写错误，应为"statistics")

**主要功能**
- **双数据源统计**: 同时分析访视数据和慢病管理系统患者数据
- **时间范围筛选**: 支持自定义日期范围的数据查询
- **多视图展示**: 提供访视数据和患者数据的分标签页展示
- **实时数据导出**: 支持将统计结果导出为Markdown格式
- **响应式设计**: 适配移动端和桌面端的不同显示需求

**访视数据统计**
- 访视登记总量统计
- 随机访视事件分析
- 知情访视事件统计
- 筛选失败访视事件追踪
- 出组访视事件记录
- 项目视图和受试者视图切换

**患者数据统计**
- 新增患者总量统计
- 按疾病类型分类统计
- 患者详细信息展示
- 疾病分布可视化

#### 核心API端点详细分析

**访视数据API**

| HTTP方法 | API端点 | 功能描述 | 请求参数 | 响应数据类型 | 使用位置 |
|---------|---------|----------|----------|-------------|----------|
| `GET` | `/research-projects/subjects/analyzer/subject-event` | 获取受试者事件总量 | `startDate`, `endDate`, `subjectEventTypeId?` | `SubjectEventQueryDto[]` | 访视统计数据获取 |
| `GET` | `/research-projects/subjects/{id}/project` | 根据受试者ID获取项目信息 | `id` (路径参数) | `ProjectQueryResponseDto` | 项目视图数据关联 |
| `GET` | `/visits/reserve/analyzer/total` | 获取访视预约总量 | `startDate`, `endDate` | `number` | 访视总量统计 |

**患者数据API**

| HTTP方法 | API端点 | 功能描述 | 请求参数 | 响应数据类型 | 使用位置 |
|---------|---------|----------|----------|-------------|----------|
| `GET` | `/mbgl/patient/report/total` | 查询时间段内新增患者 | `startDate`, `endDate` | `MbglSysRecommendPatientResponseDto[]` | 患者统计数据获取 |
| `GET` | `/mbgl/disease/{id}` | 根据疾病ID获取疾病信息 | `id` (路径参数) | `MbglDiseaseQueryResponseDto` | 疾病详情查询 |

#### 数据模型与结构

**SubjectEventQueryDto (受试者事件数据模型)**
```typescript
interface SubjectEventQueryDto {
  id: number;                    // 事件ID
  subjectID: number;             // 受试者ID
  eventTypeID: number;           // 事件类型ID (2=知情, 3=筛选, 4=随机, 5=出组)
  eventTime: Date;               // 事件时间
  remark: string;                // 备注
  parentEventID: number;         // 父事件ID
  content: {                     // 事件内容 (动态JSON对象)
    [key: string]: unknown;
    筛选结果?: "成功" | "失败";   // 筛选事件特有字段
  };
}
```

**MbglSysRecommendPatientResponseDto (患者数据模型)**
```typescript
interface MbglSysRecommendPatientResponseDto {
  userId: number;                           // 患者ID
  userName: string;                         // 患者姓名
  phone: string;                           // 患者电话
  diseases: MbglDiseaseQueryResponseDto[]; // 疾病列表
  gender: string;                          // 患者性别
  age: number;                            // 患者年龄
}

interface MbglDiseaseQueryResponseDto {
  id: number;                             // 疾病ID
  name: string;                           // 疾病名称
}
```

**ProjectQueryResponseDto (项目信息数据模型)**
```typescript
interface ProjectQueryResponseDto {
  name: string;                           // 项目名称
  id: number;                            // 项目ID
}
```

**前端数据转换模型**
```typescript
// 访视统计数据结构
interface VisitStatistics {
  finishedVisitReserves: SubjectEventQueryDto[];
  randomSubjectEvents: SubjectEventQueryDto[];
  informedSubjectEvents: SubjectEventQueryDto[];
  outGroupSubjectEvents: SubjectEventQueryDto[];
  filterFailedSubjectEvents: SubjectEventQueryDto[];
  randomSubjectEventsBySubjectId: Record<number, SubjectEventQueryDto[]>;
  informedSubjectEventsBySubjectId: Record<number, SubjectEventQueryDto[]>;
  outGroupSubjectEventsBySubjectId: Record<number, SubjectEventQueryDto[]>;
  filterFailedSubjectEventsBySubjectId: Record<number, SubjectEventQueryDto[]>;
}

// 疾病统计项目
interface DiseaseItem {
  id: string;                             // 疾病ID (字符串形式)
  name: string;                           // 疾病名称
  count: number;                          // 患者数量
  patients: MbglSysRecommendPatientResponseDto[]; // 患者列表
}
```

#### 架构特点

#### 前端组件架构

**主要组件层次结构**
```
/statstics/+page.svelte (主页面)
├── DateRangePicker (日期选择器)
├── Tabs (标签页容器)
│   ├── TabsContent[value="visit"] (访视数据标签页)
│   │   ├── StatisticCards (访视统计卡片)
│   │   └── EventCard[] (事件详情卡片组)
│   └── TabsContent[value="patients"] (患者数据标签页)
│       ├── StatisticsOverview (患者统计概览)
│       └── DiseaseGrid (疾病网格)
│           └── DiseaseCard[] (疾病卡片组)
└── Loading (加载状态组件)
```

**核心组件分析**

1. **StatisticsOverview** (`/mbgl-patient/(components)/statistics-overview.svelte`)
   - 显示患者总量和疾病种类数量
   - 使用响应式设计，支持移动端适配
   - 包含悬停动画效果

2. **DiseaseGrid** (`/mbgl-patient/(components)/disease-grid.svelte`)
   - 支持拖拽排序的疾病卡片网格
   - 使用 `svelte-dnd-action` 库实现拖拽功能
   - 响应式网格布局 (1列→2列→3列)

3. **DiseaseCard** (`/mbgl-patient/(components)/disease-card.svelte`)
   - 单个疾病的详细信息卡片
   - 包含患者列表滚动区域
   - 动态颜色主题生成

4. **EventCard** (访视事件卡片)
   - 显示不同类型的访视事件统计
   - 支持项目视图和受试者视图切换
   - 包含图标和颜色主题

**状态管理**
- 使用Svelte 5的新 `$state` 语法进行响应式状态管理
- 使用 `$derived` 计算属性进行数据转换和分组
- 分离患者数据和访视数据的状态管理
- 支持项目视图和受试者视图的切换状态

#### 数据流与处理逻辑

**数据获取流程**
```mermaid
graph TD
    A[用户选择日期范围] --> B[并行API调用]
    B --> C[fetchPatientData]
    B --> D[fetchVisitStatistics]
    C --> E[mbglPatientControllerQueryTotalPatients]
    D --> F[subjectControllerGetSubjectEventTotal]
    E --> G[患者数据处理]
    F --> H[访视数据处理]
    G --> I[按疾病分组]
    H --> J[按事件类型分类]
    I --> K[生成DiseaseItems]
    J --> L[生成VisitStatistics]
    K --> M[更新UI状态]
    L --> M
```

**访视数据处理逻辑**
```typescript
// 1. 获取原始受试者事件数据
const subjectEventTotal = await subjectControllerGetSubjectEventTotal({
  query: { startDate, endDate }
});

// 2. 处理访视登记总量 (去重逻辑)
const finishedVisitReserveTotal = subjectEventTotal.data.reduce((acc, event) => {
  if (event.parentEventID === null) {
    acc.push(event);
    return acc;
  }
  acc.push(event);
  return acc.filter((e) => e.id !== event.parentEventID);
}, []);

// 3. 按事件类型分类
const filterFailedEvents = events.filter(
  event => event.eventTypeID === 3 && event.content.筛选结果 === "失败"
);
const randomEvents = events.filter(event => event.eventTypeID === 4);
const informedEvents = events.filter(event => event.eventTypeID === 2);
const outGroupEvents = events.filter(event => event.eventTypeID === 5);

// 4. 按受试者ID分组 (用于受试者视图)
const eventsBySubjectId = events.reduce((acc, event) => {
  if (!acc[event.subjectID]) acc[event.subjectID] = [];
  acc[event.subjectID].push(event);
  return acc;
}, {});
```

**患者数据处理逻辑**
```typescript
// 1. 获取患者数据
const patients = await mbglPatientControllerQueryTotalPatients({
  query: { startDate, endDate }
});

// 2. 按疾病分组
function groupPatientsByDisease(patientList) {
  const result = {};

  for (const patient of patientList) {
    if (patient.diseases.length === 0) {
      // 处理无疾病患者
      if (!result["unknown"]) {
        result["unknown"] = {
          name: "未分类患者",
          count: 0,
          patients: []
        };
      }
      result["unknown"].count++;
      result["unknown"].patients.push(patient);
    } else {
      // 处理有疾病患者
      for (const disease of patient.diseases) {
        const diseaseId = String(disease.id);
        if (!result[diseaseId]) {
          result[diseaseId] = {
            name: disease.name,
            count: 0,
            patients: []
          };
        }
        result[diseaseId].count++;
        result[diseaseId].patients.push(patient);
      }
    }
  }

  return result;
}

// 3. 转换为可拖拽的疾病项目
const diseaseItems = Object.entries(patientsByDisease).map(([diseaseId, data]) => ({
  id: diseaseId,
  name: data.name,
  count: data.count,
  patients: data.patients
}));
```

**性能优化**
- 使用 `Promise.all` 同时获取患者数据和访视数据
- 减少串行API调用的等待时间
- 在访视服务中实现项目信息缓存
- 使用Map数据结构进行高效缓存

#### 后端服务实现

**访视数据服务 (SubjectService)**
```typescript
// 获取受试者事件总量
public async getSubjectEventTotal(
  startDate: Date,
  endDate: Date,
  subjectEventTypeId?: number
): Promise<SubjectEventEntity[]> {
  if (subjectEventTypeId === undefined) {
    return await this.subjectEventRepository.getSubjectEventTotal(startDate, endDate);
  }
  return await this.subjectEventRepository.getSubjectEventTotal(
    startDate, endDate, subjectEventTypeId
  );
}
```

**患者数据服务 (MbglPatientService)**
```typescript
// 获取某个时间段内新增的患者
public async getTotalPatients(
  startDate: Date,
  endDate: Date
): Promise<MbglSysRecommendPatientEntity[]> {
  return await this.mbglSysUserRepository.getTotalPatients(startDate, endDate);
}
```

**访视预约服务 (VisitsService)**
```typescript
// 获取访视预约总量
public async getVisitTotal(startDate: Date, endDate: Date): Promise<number> {
  // 日期验证
  if (endDate > new Date()) {
    throw new HttpException("结束日期不能大于当前日期", HttpStatus.BAD_REQUEST);
  }
  if (startDate > endDate) {
    throw new HttpException("开始日期不能大于结束日期", HttpStatus.BAD_REQUEST);
  }

  const visits = await this.clinicalDataVisitReserveRepository.findAll(
    startDate, endDate
  );
  return visits.length;
}
```

#### 架构模式分析

**1. 服务层模式 (Service Layer Pattern)**
- 业务逻辑封装在专门的服务类中
- 控制器仅负责HTTP请求处理和响应
- 服务类处理复杂的数据查询和业务规则

**2. 数据传输对象模式 (DTO Pattern)**
- 使用专门的DTO类定义API输入输出格式
- 通过Mapper类实现Entity到DTO的转换
- 确保API接口的稳定性和类型安全

**3. 仓储模式 (Repository Pattern)**
- 数据访问逻辑封装在Repository类中
- 提供统一的数据查询接口
- 支持不同数据源的抽象化访问

**4. 前端状态管理模式**
- 使用Svelte 5的响应式状态管理
- 计算属性自动更新依赖数据
- 分离关注点，独立管理不同类型的数据

**错误处理**
- 使用try-catch块捕获异步操作错误
- 通过svelte-sonner库显示用户友好的错误提示
- 错误信息包含具体的错误描述和状态码
- 在错误发生时保持UI状态的一致性

**数据导出**
- 支持结构化的Markdown报告导出功能
- 包含访视统计、患者统计和详细数据
- 一键复制到剪贴板功能

## API调用模式与最佳实践

### 1. 分页查询模式

**基于游标的分页**:
- 使用 `cursor` 参数进行高效分页
- 支持 `limit` 参数控制每页数量
- 返回 `nextCursor` 用于下一页查询

**示例**:
```typescript
// 患者分页查询
GET /patient?limit=20&cursor=abc123&name=张三&phone=138

// 响应格式
{
  "patients": [...],
  "nextCursor": "def456"
}
```

### 2. CRUD操作模式

**标准操作流程**:
- **创建**: POST → 返回创建的资源ID
- **查询**: GET → 返回资源详情或列表
- **更新**: PUT/PATCH → 返回更新后的资源
- **删除**: DELETE → 软删除，支持恢复

### 3. 数据同步模式

**MBGL系统同步**:
1. 数据创建/更新 → GCPM系统
2. 同步检查 → `check-mbgl-sync` 端点
3. 数据同步 → `sync-to-mbgl` 端点
4. 状态验证 → 同步结果确认

### 4. 错误处理模式

**统一错误响应**:
- HTTP状态码标准化
- 错误消息结构化
- 前端统一错误处理

## 模块间依赖关系总结

### 核心依赖链

```mermaid
graph LR
    A[身份验证] --> B[引用数据]
    B --> C[患者管理]
    B --> D[项目管理]
    C --> E[访视管理]
    D --> E
    E --> F[CRC工作面板]
    E --> G[导诊工作面板]
    C --> H[推荐联系人]
    I[MBGL集成] --> C
    I --> D
    I --> E
    J[统计分析] --> C
    J --> D
    J --> E
```

### 关键特性总结

1. **类型安全**: 全面的TypeScript类型定义和SDK自动生成
2. **数据一致性**: 统一的引用数据管理和验证
3. **系统集成**: 与MBGL和金数据的无缝集成
4. **性能优化**: 基于游标的分页和数据缓存
5. **用户体验**: 统一的错误处理和加载状态管理
6. **可扩展性**: 模块化设计支持功能独立扩展

## 结论

GCPM前端代表了一个架构良好的现代Web应用程序，有效地利用了最新的技术和最佳实践。Svelte 5、SvelteKit v2和全面组件库的结合为临床研究管理创建了坚实的基础。

应用程序的优势在于其类型安全的架构、全面的状态管理和组织良好的组件层次结构。SDK优先的API集成方法确保了整个应用程序的一致性和可靠性。

通过详细的API端点映射，我们可以看到前端业务模块与后端服务之间的清晰对应关系，这种设计模式有助于：

1. **提高开发效率** - 开发人员可以快速定位相关API端点
2. **简化维护工作** - 清晰的模块边界便于代码维护和更新
3. **增强系统可扩展性** - 模块化设计支持功能的独立扩展
4. **保证数据一致性** - 统一的API调用模式确保数据处理的一致性

随着持续开发专注于推荐的改进，GCPM前端已经很好地定位为随着临床研究管理不断增长的需求而扩展和发展。
