---
noteId: "c4d5d1503ec211f0b4ec13362c7e567c"
tags: []

---

# GCPM API Documentation

## Overview

The GCPM API is a comprehensive RESTful API for clinical trial management, built with NestJS and following OpenAPI 3.0 specifications. The API provides endpoints for managing patients, research projects, clinical visits, devices, and user authentication.

## Base Configuration

- **Base URL**: Configurable via environment variables
- **API Version**: v0.5.2
- **Content Type**: `application/json`
- **Authentication**: Bearer <PERSON>ken (JWT)
- **Documentation**: Available at `/api-docs` (non-production environments)

## API Architecture Overview

```mermaid
graph TB
    subgraph "Client Applications"
        WEB[Web Frontend]
        MOBILE[Mobile App]
        API_CLIENT[API Clients]
    end

    subgraph "API Gateway Layer"
        GATEWAY[NestJS API Gateway]
        AUTH[Authentication]
        VALIDATION[Input Validation]
        RATE_LIMIT[Rate Limiting]
    end

    subgraph "API Endpoints"
        AUTH_EP[/auth/*]
        PATIENT_EP[/patient/*]
        VISITS_EP[/visits/*]
        PROJECTS_EP[/research-projects/*]
        DEVICES_EP[/clinical-device/*]
        EVENTS_EP[/clinical-events/*]
    end

    subgraph "Business Logic"
        CONTROLLERS[Controllers]
        SERVICES[Services]
        REPOSITORIES[Repositories]
    end

    subgraph "Data Layer"
        CLINICAL_DB[(Clinical Data)]
        WORK_DB[(Work Data)]
        MBGL_DB[(MBGL)]
        REFERRAL_DB[(Referral)]
    end

    WEB --> GATEWAY
    MOBILE --> GATEWAY
    API_CLIENT --> GATEWAY

    GATEWAY --> AUTH
    GATEWAY --> VALIDATION
    GATEWAY --> RATE_LIMIT

    GATEWAY --> AUTH_EP
    GATEWAY --> PATIENT_EP
    GATEWAY --> VISITS_EP
    GATEWAY --> PROJECTS_EP
    GATEWAY --> DEVICES_EP
    GATEWAY --> EVENTS_EP

    AUTH_EP --> CONTROLLERS
    PATIENT_EP --> CONTROLLERS
    VISITS_EP --> CONTROLLERS
    PROJECTS_EP --> CONTROLLERS
    DEVICES_EP --> CONTROLLERS
    EVENTS_EP --> CONTROLLERS

    CONTROLLERS --> SERVICES
    SERVICES --> REPOSITORIES

    REPOSITORIES --> CLINICAL_DB
    REPOSITORIES --> WORK_DB
    REPOSITORIES --> MBGL_DB
    REPOSITORIES --> REFERRAL_DB
```

## Authentication

### JWT Token Authentication

All protected endpoints require a valid JWT token in the Authorization header:

```http
Authorization: Bearer <jwt_token>
```

### Authentication Endpoints

#### POST /auth/login
**Summary**: User login  
**Request Body**:
```json
{
  "username": "string",
  "password": "string"
}
```

**Response**:
```json
{
  "access_token": "string",
  "user": {
    "id": "string",
    "username": "string",
    "email": "string",
    "userTypeId": "string"
  }
}
```

#### POST /auth/register
**Summary**: User registration  
**Request Body**:
```json
{
  "username": "string",
  "email": "string",
  "password": "string",
  "phone": "string",
  "userTypeId": "string"
}
```

## Core API Modules

### 1. Patient Management

#### POST /patient
**Summary**: Create patient  
**Authentication**: Required  
**Request Body**:
```json
{
  "name": "string",
  "phone": "string",
  "gender": 0,
  "birthday": "2024-01-01",
  "address": "string",
  "idNumber": "string",
  "diseases": ["string"]
}
```

#### PUT /patient/:id
**Summary**: Update patient  
**Parameters**: 
- `id` (path): Patient ID

#### GET /patients
**Summary**: Query patients  
**Query Parameters**:
- `name` (optional): Patient name
- `phone` (optional): Patient phone

#### GET /patients/pagination
**Summary**: Paginated patient query  
**Query Parameters**:
- `limit`: Number of records per page
- `cursor`: Pagination cursor
- `name` (optional): Filter by name
- `phone` (optional): Filter by phone

### 2. Visit Management

#### POST /visits/reserve/from-goldata
**Summary**: Create visit reservation from Goldata  
**Request Body**:
```json
{
  "project_serial_number": 0,
  "visit_date": "2024-01-01",
  "user_id": "string",
  "user_name": "string",
  "phone": "string",
  "project_id": "string",
  "visit_name": "string",
  "visit_type": "string"
}
```

#### POST /visits/complete-info-record/from-gcpm-frontend
**Summary**: Create visit completion record  
**Request Body**:
```json
{
  "reserveId": 0,
  "userId": 0,
  "recordInfos": {},
  "mainVisitContent": "string",
  "isUnplannedVisit": false
}
```

#### GET /visits/reserve/date/:date
**Summary**: Get visit reservations by date  
**Parameters**:
- `date` (path): Date in YYYY-MM-DD format

#### GET /visits/complete-info-record/history
**Summary**: Get visit completion history

### 3. Research Projects

#### GET /research-projects/all-projects
**Summary**: Get all research projects  
**Authentication**: Required

#### GET /research-projects/project-by-id
**Summary**: Get project by ID  
**Query Parameters**:
- `project_id`: Project identifier

#### POST /research-projects/create-project-filing
**Summary**: Create project filing  
**Request Body**:
```json
{
  "projectName": "string",
  "projectFullname": "string",
  "projectCode": "string",
  "sponsorUUID": "string",
  "launchDate": "2024-01-01",
  "objectTypeUUID": "string"
}
```

#### GET /research-projects/:projectID/event-statistics
**Summary**: Get project clinical event statistics  
**Parameters**:
- `projectID` (path): Project identifier

### 4. Clinical Device Management

#### GET /clinical-device
**Summary**: Get all clinical devices  
**Response**:
```json
{
  "devices": [
    {
      "id": 0,
      "deviceType": "string",
      "serialNumber": 0,
      "refrigeratorType": "string",
      "provider": "string",
      "deviceLocation": "string"
    }
  ]
}
```

#### POST /clinical-device
**Summary**: Create clinical device from Goldata

#### POST /clinical-device/event
**Summary**: Create clinical device event  
**Request Body**:
```json
{
  "deviceId": 0,
  "managementEventType": "string",
  "eventDate": "2024-01-01T00:00:00Z",
  "minTemperature": 0,
  "maxTemperature": 0,
  "clinicalProjectId": "string",
  "crcName": "string"
}
```

### 5. Clinical Events

#### POST /clinical-events
**Summary**: Create clinical event  
**Request Body**:
```json
{
  "serial_number": 0,
  "role_name": "string",
  "event_type": "string",
  "project_code": "string",
  "patient_id": 0,
  "patient_name": "string",
  "event_date": "2024-01-01T00:00:00Z",
  "event_details": "string"
}
```

#### GET /clinical-events/pagination
**Summary**: Get paginated clinical events  
**Query Parameters**:
- `page`: Page number
- `limit`: Records per page
- `project_code` (optional): Filter by project
- `event_type` (optional): Filter by event type

### 6. CRC Work Panel

#### POST /crc/reserve-visit
**Summary**: Create visit reservation (CRC)  
**Authentication**: Required

#### POST /crc/quality-control/ae
**Summary**: Create AE quality control event

#### POST /crc/quality-control/sae
**Summary**: Create SAE quality control event

#### GET /crc/reserve-visit/pagination
**Summary**: Get paginated visit reservations

### 7. Reference Data

#### GET /reference-data/diseases
**Summary**: Get all diseases

#### GET /reference-data/subject-states
**Summary**: Get all subject states

#### GET /reference-data/project-phases
**Summary**: Get all project phases

#### GET /reference-data/project-statuses
**Summary**: Get all project statuses

## Request/Response Patterns

### API Request Flow

```mermaid
sequenceDiagram
    participant Client
    participant API as API Gateway
    participant Auth as Auth Service
    participant Controller
    participant Service
    participant DB as Database

    Client->>API: HTTP Request + JWT Token
    API->>Auth: Validate Token

    alt Token Valid
        Auth-->>API: User Context
        API->>API: Input Validation

        alt Validation Passes
            API->>Controller: Route Request
            Controller->>Service: Business Logic
            Service->>DB: Data Operation
            DB-->>Service: Result
            Service-->>Controller: Processed Data
            Controller-->>API: Response DTO
            API-->>Client: 200 OK + Data
        else Validation Fails
            API-->>Client: 400 Bad Request
        end
    else Token Invalid
        Auth-->>API: Unauthorized
        API-->>Client: 401 Unauthorized
    end
```

### Standard Response Format

All API responses follow a consistent structure:

**Success Response**:
```json
{
  "data": {},
  "message": "string",
  "statusCode": 200
}
```

**Error Response**:
```json
{
  "error": "string",
  "message": "string",
  "statusCode": 400
}
```

### Pagination Response

Paginated endpoints return:
```json
{
  "data": [],
  "pagination": {
    "nextCursor": "string",
    "hasMore": boolean,
    "total": 0
  }
}
```

### Common HTTP Status Codes

- **200 OK**: Successful request
- **201 Created**: Resource created successfully
- **400 Bad Request**: Invalid request data
- **401 Unauthorized**: Authentication required
- **403 Forbidden**: Insufficient permissions
- **404 Not Found**: Resource not found
- **409 Conflict**: Resource already exists
- **500 Internal Server Error**: Server error

## Data Validation

### Input Validation

All request bodies are validated using class-validator decorators:

- **Required Fields**: Must be present and non-empty
- **Type Validation**: Automatic type checking
- **Format Validation**: Email, phone, date formats
- **Range Validation**: Numeric ranges and string lengths

### Common Validation Rules

```typescript
// Example validation decorators
@IsNotEmpty()
@IsString()
@Length(1, 100)
name: string;

@IsEmail()
email: string;

@IsPhoneNumber('CN')
phone: string;

@IsDateString()
birthday: string;

@IsOptional()
@IsArray()
diseases: string[];
```

## Error Handling

### Global Exception Filter

All errors are processed through a global exception filter that:

1. Logs errors with Winston
2. Transforms errors to client-friendly format
3. Returns appropriate HTTP status codes
4. Masks sensitive information in production

### Common Error Types

#### Validation Errors (400)
```json
{
  "error": "Validation failed",
  "message": "name should not be empty",
  "statusCode": 400
}
```

#### Authentication Errors (401)
```json
{
  "error": "Unauthorized",
  "message": "Invalid token",
  "statusCode": 401
}
```

#### Not Found Errors (404)
```json
{
  "error": "Not Found",
  "message": "Patient not found",
  "statusCode": 404
}
```

## Rate Limiting and Security

### Security Headers

- **CORS**: Enabled for cross-origin requests
- **Content-Type**: Enforced JSON content type
- **Authorization**: Bearer token validation

### Input Sanitization

- **SQL Injection**: Prevented by Prisma ORM
- **XSS Protection**: Input validation and sanitization
- **Data Validation**: Comprehensive input validation

## API Versioning

### Current Version Strategy

- **Version**: v0.5.2
- **Approach**: URL-based versioning (planned)
- **Backward Compatibility**: Maintained for major versions

## Integration Guidelines

### SDK Usage

The API provides auto-generated TypeScript SDKs:

```typescript
import { visitsControllerFindOne } from '@gcpm/sdk';

const visit = await visitsControllerFindOne({
  path: { id: visitId }
});
```

### Error Handling in Clients

```typescript
try {
  const response = await apiCall();
  return response.data;
} catch (error) {
  if (error.status === 401) {
    // Handle authentication error
    redirectToLogin();
  } else if (error.status === 400) {
    // Handle validation error
    showValidationErrors(error.message);
  }
  throw error;
}
```

### Best Practices

1. **Always handle authentication errors**
2. **Implement proper error boundaries**
3. **Use pagination for large datasets**
4. **Cache frequently accessed reference data**
5. **Implement retry logic for transient failures**
6. **Validate data on both client and server**
