---
noteId: "fbc9a8803ec211f0b4ec13362c7e567c"
tags: []

---

# GCPM Database Schema Documentation

## Overview

The GCPM system uses a multi-database architecture with MySQL as the primary database engine. The system is designed with four separate databases to handle different domains and maintain clear separation of concerns.

## Database Architecture

```mermaid
graph TB
    subgraph "GCPM Database Architecture"
        subgraph "Clinical Data Database"
            CD[Clinical Data]
            CD --> USERS[Users & Profiles]
            CD --> PROJECTS[Research Projects]
            CD --> PATIENTS[Patients & Subjects]
            CD --> EVENTS[Clinical Events]
            CD --> DEVICES[Clinical Devices]
        end
        
        subgraph "Work Data Database"
            WD[Work Data]
            WD --> VISITS[Visit Reservations]
            WD --> STAFF[Department Staff]
            WD --> RECORDS[Visit Records]
        end
        
        subgraph "MBGL Database"
            MBGL[MBGL Integration]
            MBGL --> LEGACY[Legacy System Data]
            MBGL --> SYNC[Sync Records]
        end
        
        subgraph "Referral Database"
            REF[Referral Data]
            REF --> R<PERSON><PERSON><PERSON><PERSON>[Patient Referrals]
            REF --> CROSS[Cross-references]
        end
    end
```

## Database Connections

### 1. Clinical Data Database (`clinicaldata`)
- **Purpose**: Core clinical trial management data
- **URL**: `CLINICALDATA_DATABASE_URL`
- **Primary Tables**: Users, Research Projects, Patients, Clinical Events

### 2. Work Data Database (`workdata`)
- **Purpose**: Operational and workflow data
- **URL**: `WORKDATA_DATABASE_URL`
- **Primary Tables**: Visit Reservations, Department Staff

### 3. MBGL Database (`mbgl`)
- **Purpose**: Integration with legacy chronic disease management system
- **URL**: `MBGL_DATABASE_URL`
- **Primary Tables**: Legacy system mappings

### 4. Referral Database (`referral`)
- **Purpose**: Patient referral management
- **URL**: `REFERRAL_DATABASE_URL`
- **Primary Tables**: Referral records and cross-system references

## Core Entity Relationships

```mermaid
erDiagram
    User {
        bigint id PK
        string uuid UK
        string username UK
        string email UK
        string password_hash
        string user_type_id
        string phone
        timestamp created_at
        timestamp updated_at
        timestamp last_login
    }

    UserProfile {
        bigint id PK
        string uuid UK
        string user_uuid FK
        string first_name
        string last_name
        string phone
        string status
        string status_uuid FK
    }

    ResearchProjects {
        int id PK
        string uuid UK
        string project_name
        text project_fullname
        string project_code
        string sponsor_uuid FK
        date launch_date
        string object_type_uuid FK
        string project_phase_uuid FK
        string status_uuid FK
    }

    Patient {
        int id PK
        string uuid UK
        string name
        string phone
        int mbgl_user_id
        int current_research_status_id FK
        boolean is_active
        boolean deleted
    }

    PatientProfile {
        string id PK
        string patient_uuid FK
        tinyint gender
        date birthday
        string address
        string id_number UK
    }

    Subject {
        int id PK
        int state_id FK
        string patient_uuid FK
        int research_project_id FK
        string research_project_uuid FK
        string screening_number
    }

    ClinicalEvent {
        int id PK
        int serial_number UK
        string role_name
        string event_type
        string project_code
        int patient_id
        string patient_name
        datetime event_date
        text event_details
    }

    ClinicalStorageDeviceInfo {
        int id PK
        string device_type
        int serial_number UK
        string refrigerator_type
        string provider
        string device_location
        boolean is_active
    }

    VisitReserve {
        int serial_number PK
        int project_serial_number
        date visit_date
        string user_id
        string user_name
        string phone
        string project_id
        string visit_name
        string visit_type
        tinyint finished_status
    }

    User ||--o| UserProfile : "has"
    User ||--o{ ProjectMembers : "belongs to"
    User ||--o{ UserTagRelation : "has"

    ResearchProjects ||--o{ ProjectMembers : "has"
    ResearchProjects ||--o{ Subject : "enrolls"
    ResearchProjects ||--o{ ProjectDiseases : "studies"
    ResearchProjects ||--o{ ClinicalTrialDeviceBinding : "uses"

    Patient ||--o{ Subject : "participates as"
    Patient ||--o| PatientProfile : "has"
    Patient ||--o{ PatientDisease : "diagnosed with"
    Patient ||--o{ PatientVisits : "attends"

    Subject ||--o{ SubjectEvent : "experiences"

    ClinicalStorageDeviceInfo ||--o{ ClinicalTrialDeviceBinding : "bound to"
    ClinicalStorageDeviceInfo ||--o{ ClinicalDrugManagementEvent : "monitors"

    Diseases ||--o{ ProjectDiseases : "studied in"
    Diseases ||--o{ PatientDisease : "affects"
```

## Clinical Data Database Schema

### User Management

#### Users Table
```sql
CREATE TABLE users (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  uuid VARCHAR(36) UNIQUE NOT NULL,
  username VARCHAR(255) UNIQUE NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  user_type_id VARCHAR(36) NOT NULL,
  phone VARCHAR(20) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  last_login TIMESTAMP NULL
);
```

**Key Features**:
- UUID-based identification for external references
- Secure password hashing with bcrypt
- User type classification system
- Audit trail with timestamps

#### User Profiles Table
```sql
CREATE TABLE user_profiles (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  uuid VARCHAR(36) UNIQUE,
  user_uuid VARCHAR(36) UNIQUE,
  first_name VARCHAR(100) NOT NULL,
  last_name VARCHAR(100) NOT NULL,
  phone VARCHAR(20),
  status VARCHAR(255),
  status_uuid VARCHAR(36),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_uuid) REFERENCES users(uuid),
  FOREIGN KEY (status_uuid) REFERENCES staff_status(uuid)
);
```

### Research Project Management

#### Research Projects Table
```sql
CREATE TABLE research_projects (
  id INT PRIMARY KEY AUTO_INCREMENT,
  uuid VARCHAR(36) UNIQUE NOT NULL,
  project_name VARCHAR(255),
  project_fullname TEXT,
  project_code VARCHAR(50),
  sponsor_uuid VARCHAR(36),
  launch_date DATE,
  project_code_local VARCHAR(10),
  object_type_uuid VARCHAR(36),
  project_phase_uuid VARCHAR(36),
  status_uuid VARCHAR(36),
  end_date DATE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (sponsor_uuid) REFERENCES research_sponsors(uuid),
  FOREIGN KEY (object_type_uuid) REFERENCES research_object_types(uuid),
  FOREIGN KEY (project_phase_uuid) REFERENCES research_project_phases(uuid),
  FOREIGN KEY (status_uuid) REFERENCES research_project_statuses(uuid)
);
```

**Key Features**:
- Comprehensive project metadata
- Sponsor and phase tracking
- Status management
- Local and external project codes

#### Project Members Table
```sql
CREATE TABLE project_members (
  uuid VARCHAR(36) PRIMARY KEY,
  project_uuid VARCHAR(36) NOT NULL,
  user_uuid VARCHAR(36) NOT NULL,
  project_role_uuid VARCHAR(36) NOT NULL,
  join_at TIMESTAMP,
  left_at TIMESTAMP,
  is_active BOOLEAN NOT NULL,
  is_blinded BOOLEAN,
  remarks TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (project_uuid) REFERENCES research_projects(uuid),
  FOREIGN KEY (user_uuid) REFERENCES users(uuid),
  FOREIGN KEY (project_role_uuid) REFERENCES project_role_types(uuid)
);
```

### Patient Management

#### Patient Table
```sql
CREATE TABLE patient (
  id INT PRIMARY KEY AUTO_INCREMENT,
  uuid VARCHAR(36) UNIQUE NOT NULL,
  name VARCHAR(20) NOT NULL,
  phone VARCHAR(20),
  mbgl_user_id INT,
  jsj_user_id INT,
  current_research_status_id INT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  is_active BOOLEAN DEFAULT TRUE,
  deleted BOOLEAN DEFAULT FALSE,
  deleted_at TIMESTAMP,
  FOREIGN KEY (current_research_status_id) REFERENCES patient_state(id)
);
```

#### Patient Profile Table
```sql
CREATE TABLE patient_profile (
  id VARCHAR(36) PRIMARY KEY,
  patient_uuid VARCHAR(36) UNIQUE NOT NULL,
  gender TINYINT NOT NULL,
  birthday DATE,
  address VARCHAR(255),
  id_number VARCHAR(20) UNIQUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (patient_uuid) REFERENCES patient(uuid)
);
```

### Subject Management

#### Subject Table
```sql
CREATE TABLE subject (
  id INT PRIMARY KEY AUTO_INCREMENT,
  state_id INT,
  patient_uuid VARCHAR(36) NOT NULL,
  research_project_id INT,
  research_project_uuid VARCHAR(36),
  screening_number VARCHAR(255),
  temp_jsj_project_id INT,
  temp_mbgl_user_id INT,
  temp_mbgl_project_id INT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (patient_uuid) REFERENCES patient(uuid),
  FOREIGN KEY (research_project_id) REFERENCES research_projects(id),
  FOREIGN KEY (state_id) REFERENCES subject_state(id)
);
```

### Clinical Events

#### Clinical Events Table
```sql
CREATE TABLE clinical_events (
  id INT PRIMARY KEY AUTO_INCREMENT,
  serial_number INT UNIQUE NOT NULL,
  role_name VARCHAR(255) NOT NULL,
  event_type VARCHAR(255) NOT NULL,
  project_code VARCHAR(255) NOT NULL,
  patient_id INT NOT NULL,
  patient_name VARCHAR(255) NOT NULL,
  drug_related VARCHAR(255),
  event_date DATETIME NOT NULL,
  event_details TEXT,
  pi_name VARCHAR(255),
  project_short_name VARCHAR(255),
  screening_number VARCHAR(255),
  submitter_name VARCHAR(255) NOT NULL,
  diag_doctor_name VARCHAR(255),
  diag_doctor_phone VARCHAR(255),
  project_disease VARCHAR(255),
  source_create_time DATETIME NOT NULL,
  source_update_time DATETIME NOT NULL,
  create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### Clinical Device Management

#### Clinical Storage Device Info Table
```sql
CREATE TABLE clinical_storage_device_info (
  id INT PRIMARY KEY,
  device_type VARCHAR(255) NOT NULL,
  serial_number INT UNIQUE NOT NULL,
  refrigerator_type VARCHAR(255) NOT NULL,
  provider VARCHAR(255) NOT NULL,
  device_location VARCHAR(255) NOT NULL,
  device_label VARCHAR(255),
  device_manager VARCHAR(255),
  is_active INT DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  create_by VARCHAR(255) NOT NULL
);
```

#### Clinical Drug Management Events Table
```sql
CREATE TABLE clinical_drug_management_events (
  id INT PRIMARY KEY AUTO_INCREMENT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  event_date DATETIME NOT NULL,
  device_id INT NOT NULL,
  management_event_type VARCHAR(255) NOT NULL,
  min_temperature FLOAT,
  max_temperature FLOAT,
  clinical_project_id VARCHAR(255),
  crc_name VARCHAR(255),
  drug_expiration_date DATETIME,
  create_by VARCHAR(255) NOT NULL,
  FOREIGN KEY (device_id) REFERENCES clinical_storage_device_info(id)
);
```

## Work Data Database Schema

### Visit Management

#### Visit Reserve Table
```sql
CREATE TABLE 患者访视预约 (
  serial_number INT PRIMARY KEY,
  project_serial_number INT NOT NULL,
  访视日期 DATE NOT NULL,
  user_id VARCHAR(20) NOT NULL,
  患者姓名 VARCHAR(20) NOT NULL,
  患者电话 VARCHAR(20) NOT NULL,
  项目代号 VARCHAR(20),
  项目简称 VARCHAR(200),
  crc VARCHAR(20),
  访视名称 VARCHAR(20),
  访视类型 VARCHAR(30),
  是否需要肺功能加班 JSON,
  patient_fasting_needs TINYINT NOT NULL,
  patient_blood_test_needs TINYINT NOT NULL,
  nurse_overtime_for_blood_test JSON,
  special_examination JSON,
  dispense_drug_needs TINYINT,
  drug_saving_type VARCHAR(20),
  drug_saving_location VARCHAR(20),
  encrypted_reserve_infos BINARY(32),
  goldata_created_at DATETIME NOT NULL,
  goldata_updated_at DATETIME NOT NULL,
  info_platform VARCHAR(20),
  info_filling_duration INT,
  完成状态 TINYINT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### Department Staff Table
```sql
CREATE TABLE 科室人员信息管理 (
  姓名 VARCHAR(255) PRIMARY KEY,
  联系方式 VARCHAR(255) NOT NULL
);
```

## Data Relationships and Constraints

### Primary Key Strategy
- **Auto-increment IDs**: For internal references
- **UUIDs**: For external references and API exposure
- **Composite Keys**: For many-to-many relationships

### Foreign Key Relationships
- **Cascading Deletes**: Limited to prevent data loss
- **Soft Deletes**: Preferred for audit trail preservation
- **Referential Integrity**: Enforced at database level

### Indexing Strategy

#### Performance Indexes
```sql
-- User lookup indexes
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_uuid ON users(uuid);

-- Project member lookups
CREATE INDEX idx_project_members_project ON project_members(project_uuid);
CREATE INDEX idx_project_members_user ON project_members(user_uuid);

-- Patient search indexes
CREATE INDEX idx_patient_name ON patient(name);
CREATE INDEX idx_patient_phone ON patient(phone);
CREATE INDEX idx_patient_uuid ON patient(uuid);

-- Clinical event queries
CREATE INDEX idx_clinical_events_project ON clinical_events(project_code);
CREATE INDEX idx_clinical_events_patient ON clinical_events(patient_id);
CREATE INDEX idx_clinical_events_date ON clinical_events(event_date);

-- Visit reservation lookups
CREATE INDEX idx_visit_reserve_date ON 患者访视预约(访视日期);
CREATE INDEX idx_visit_reserve_user ON 患者访视预约(user_id);
```

## Data Migration Strategy

### Version Control
- **Prisma Migrations**: Automated schema versioning
- **Migration Scripts**: Custom data transformation
- **Rollback Support**: Safe migration rollback procedures

### Data Integrity
- **Validation Rules**: Database-level constraints
- **Business Rules**: Application-level validation
- **Audit Trails**: Change tracking for critical data

## Security Considerations

### Data Protection
- **Encrypted Fields**: Sensitive data encryption at rest
- **Access Control**: Role-based database access
- **Audit Logging**: Database operation logging

### Backup Strategy
- **Regular Backups**: Automated daily backups
- **Point-in-time Recovery**: Transaction log backups
- **Cross-region Replication**: Disaster recovery setup

## Performance Optimization

### Query Optimization
- **Efficient Joins**: Optimized relationship queries
- **Pagination**: Cursor-based pagination for large datasets
- **Connection Pooling**: Prisma connection management

### Monitoring
- **Query Performance**: Slow query identification
- **Index Usage**: Index effectiveness monitoring
- **Connection Metrics**: Database connection tracking
