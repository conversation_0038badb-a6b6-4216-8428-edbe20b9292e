---
noteId: "46ce43113e9411f0b4ec13362c7e567c"
tags: []

---

# GCPM API 快速参考

## 常用接口速查

### 认证相关
```http
POST /auth/login          # 用户登录
POST /auth/register       # 用户注册
```

### 患者管理
```http
GET  /patients                    # 查询患者
POST /patients                    # 创建患者
GET  /patients/{id}               # 获取患者详情
PATCH /patients/{id}              # 更新患者信息
DELETE /patients/{id}             # 删除患者(软删除)
GET  /patients/pagination         # 分页查询患者
```

### 访视管理
```http
GET  /visits                                    # 获取所有访视
GET  /visits/{id}                               # 通过ID获取访视
POST /visits/reserve/from-goldata               # 从Goldata创建访视预约
POST /visits/complete-info-record/from-gcpm-frontend  # 从前端创建结束访视记录
GET  /visits/reserve/date/{date}                # 通过日期获取访视预约
GET  /visits/complete-info-record/history       # 获取历史结束访视记录
```

### 研究项目
```http
GET  /research-projects/all-projects            # 获取所有研究项目
GET  /research-projects/project-by-id           # 获取指定项目
POST /research-projects/create-project-filing   # 创建项目备案
GET  /research-projects/{projectID}/event-statistics  # 获取项目统计
```

### CRC工作面板
```http
POST /crc/reserve-visit                         # 创建访视预约
GET  /crc/reserve-visit                         # 获取访视预约
GET  /crc/reserve-visit/pagination              # 分页获取访视预约
POST /crc/quality-control/ae                    # 创建AE事件
POST /crc/quality-control/sae                   # 创建SAE事件
```

### 临床设备
```http
GET  /clinical-device                           # 获取所有临床设备
GET  /clinical-device/{id}                      # 根据ID获取设备
POST /clinical-device/event                     # 创建设备事件
POST /clinical-device/from-goldata              # 从金数据注册设备
```

## 常用查询参数

### 分页参数
- `limit`: 每页条数 (默认: 10)
- `cursor`: 游标 (用于分页)

### 日期参数
- `date`: 日期格式 YYYY-MM-DD
- `startDate`: 开始日期
- `endDate`: 结束日期

### 过滤参数
- `name`: 姓名过滤
- `phone`: 电话过滤
- `project_id`: 项目ID过滤

## 常用响应状态码

### 成功响应
- `200`: 请求成功
- `201`: 创建成功

### 客户端错误
- `400`: 请求参数无效
- `401`: 身份验证失败
- `403`: 无权访问
- `404`: 资源不存在
- `409`: 资源冲突

### 服务器错误
- `500`: 服务器内部错误

## 常用数据模型

### 患者信息
```json
{
  "id": "string",
  "name": "string",
  "phone": "string",
  "gender": "number",
  "birthday": "string",
  "address": "string",
  "idNumber": "string"
}
```

### 访视预约
```json
{
  "id": "string",
  "visitDate": "string",
  "visitName": "string",
  "patientId": "string",
  "projectId": "string",
  "status": "string"
}
```

### 研究项目
```json
{
  "id": "string",
  "projectCode": "string",
  "projectName": "string",
  "status": "string",
  "phase": "string",
  "sponsor": "string"
}
```

## 开发提示

### 1. 认证机制
- 大部分接口需要认证
- 使用Bearer Token进行身份验证
- Token需要在请求头中携带: `Authorization: Bearer <token>`

### 2. 错误处理
```javascript
// 示例错误处理
try {
  const response = await fetch('/api/patients', {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });
  
  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }
  
  const data = await response.json();
  return data;
} catch (error) {
  console.error('API调用失败:', error);
  // 处理错误
}
```

### 3. 分页查询
```javascript
// 分页查询示例
const fetchPatientsWithPagination = async (limit = 10, cursor = null) => {
  const params = new URLSearchParams({
    limit: limit.toString()
  });
  
  if (cursor) {
    params.append('cursor', cursor);
  }
  
  const response = await fetch(`/api/patients/pagination?${params}`);
  return response.json();
};
```

### 4. 日期格式
- 统一使用 ISO 8601 格式: `YYYY-MM-DD`
- 时间戳使用 UTC 时间

### 5. 数据验证
- 客户端应进行基本数据验证
- 服务端会进行完整的数据验证
- 注意必需字段和数据类型

## 调试技巧

### 1. 使用浏览器开发者工具
- Network 标签查看请求和响应
- Console 标签查看错误信息

### 2. API测试工具
- 推荐使用 Postman 或 Insomnia
- 可以导入 OpenAPI 规范文件

### 3. 日志记录
- 记录所有API调用
- 包含请求参数和响应数据
- 便于问题排查

## 性能优化

### 1. 减少请求次数
- 合理使用批量接口
- 避免不必要的重复请求

### 2. 使用分页
- 大数据量查询必须使用分页
- 合理设置页面大小

### 3. 缓存策略
- 对不经常变化的数据进行缓存
- 注意缓存失效策略

## 安全注意事项

### 1. Token管理
- 安全存储认证Token
- 定期刷新Token
- 退出时清除Token

### 2. 数据传输
- 使用HTTPS协议
- 敏感数据加密传输

### 3. 输入验证
- 对所有用户输入进行验证
- 防止SQL注入和XSS攻击
