<script lang="ts">
  import * as Dialog from "$lib/components/ui/dialog";
  import { Button } from "$lib/components/ui/button";
  import settings, { loadSettings, saveSettings } from "$lib/stores/settings";
  import lighthouseSettings, {
    loadLighthouseSettings,
    saveLighthouseSettings,
    isTokenValid,
    clearLighthouseToken,
    setLighthouseToken
  } from "$lib/stores/lighthouseSettings";
  import lighthouseApiService from "$lib/services/lighthouseApiService";

  let { open = $bindable(false) } = $props();

  // 状态管理
  let openrouterApiKey = $state("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");
  let openrouterSiteUrl = $state("https://openrouter.ai/api/v1/chat/completions");
  let notionApiKey = $state("ntn_V40341044546PN6Fp3PvPWFVHa9qOvfuv2BAEQiuPddbhP");
  let notionDatabaseId = $state("1ca0f0738c8680edb18cde821a4158a8");
  let notionInspirationDbId = $state("1cd0f0738c868027b960dc8129fbd159");
  let advancedSettings = $state(false);
  let isSaving = $state(false);
  let saveError = $state("");
  let saveSuccess = $state(false);
  let activeTab = $state("api");

  // Lighthouse 设置
  let lighthouseServerUrl = $state("http://43.139.167.155");
  let lighthousePort = $state(3001);
  let isTestingConnection = $state(false);
  let connectionTestResult = $state<{success: boolean; message: string} | null>(null);
  let loginUsername = $state("");
  let loginPassword = $state("");
  let isLoggingIn = $state(false);
  let loginError = $state("");

  // 模型管理
  let models = $state<{id: string; name: string}[]>([]);
  let newModelId = $state("");
  let newModelName = $state("");
  let editingModelIndex = $state<number | null>(null);
  let modelError = $state("");

  // 监听设置变化
  $effect(() => {
    if ($settings) {
      openrouterApiKey = $settings.openrouterApiKey || "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";
      openrouterSiteUrl = $settings.openrouterSiteUrl || "https://openrouter.ai/api/v1/chat/completions";
      notionApiKey = $settings.notionApiKey || "ntn_V40341044546PN6Fp3PvPWFVHa9qOvfuv2BAEQiuPddbhP";
      notionDatabaseId = $settings.notionDatabaseId || "1ca0f0738c8680edb18cde821a4158a8";
      notionInspirationDbId = $settings.notionInspirationDbId || "1cd0f0738c868027b960dc8129fbd159";
      models = [...($settings.models || [])];
    }
  });

  // 监听 Lighthouse 设置变化
  $effect(() => {
    if ($lighthouseSettings) {
      lighthouseServerUrl = $lighthouseSettings.serverUrl || "http://43.139.167.155";
      lighthousePort = $lighthouseSettings.port || 3001;
    }
  });

  // 打开时加载设置
  $effect(() => {
    if (open) {
      if (!$settings.initialized) {
        loadSettings();
      }
      if (!$lighthouseSettings.initialized) {
        loadLighthouseSettings();
      }
    }
  });

  // 保存设置
  async function handleSaveSettings() {
    if (!openrouterApiKey || !notionApiKey || !notionDatabaseId) {
      saveError = "API密钥和主数据库ID是必填项";
      return;
    }

    saveError = "";
    isSaving = true;
    saveSuccess = false;

    try {
      // 保存 OpenRouter 和 Notion 设置
      const success = await saveSettings({
        openrouterApiKey,
        openrouterSiteUrl,
        notionApiKey,
        notionDatabaseId,
        notionInspirationDbId,
        models
      });

      // 保存 Lighthouse 设置
      const lighthouseSuccess = await saveLighthouseSettings({
        serverUrl: lighthouseServerUrl,
        port: lighthousePort
      });

      if (success && lighthouseSuccess) {
        saveSuccess = true;

        // 3秒后关闭成功提示
        setTimeout(() => {
          saveSuccess = false;
        }, 3000);
      } else {
        saveError = "保存设置时出现错误";
      }
    } catch (err: unknown) {
      console.error("保存设置出错:", err);
      saveError = `保存失败: ${err instanceof Error ? err.message : '未知错误'}`;
    } finally {
      isSaving = false;
    }
  }

  // 测试 Lighthouse 连接
  async function testLighthouseConnection() {
    connectionTestResult = null;
    isTestingConnection = true;

    try {
      // 先保存当前设置
      await saveLighthouseSettings({
        serverUrl: lighthouseServerUrl,
        port: lighthousePort
      });

      // 测试连接
      const success = await lighthouseApiService.testConnection();

      if (success) {
        connectionTestResult = {
          success: true,
          message: "连接成功！服务器可访问。"
        };
      } else {
        connectionTestResult = {
          success: false,
          message: "连接失败，无法访问服务器。"
        };
      }
    } catch (err: any) {
      connectionTestResult = {
        success: false,
        message: `连接错误: ${err.message || "未知错误"}`
      };
    } finally {
      isTestingConnection = false;
    }
  }

  // 登录到 Lighthouse
  async function loginToLighthouse() {
    loginError = "";
    isLoggingIn = true;

    try {
      if (!loginUsername || !loginPassword) {
        loginError = "用户名和密码不能为空";
        isLoggingIn = false;
        return;
      }

      // 先保存当前设置
      await saveLighthouseSettings({
        serverUrl: lighthouseServerUrl,
        port: lighthousePort
      });

      // 登录
      const response = await lighthouseApiService.login(loginUsername, loginPassword);

      // 保存 token
      setLighthouseToken(response.access_token, response.user.username);

      connectionTestResult = {
        success: true,
        message: `登录成功！欢迎 ${response.user.username}`
      };

      // 清空登录表单
      loginUsername = "";
      loginPassword = "";
    } catch (err: any) {
      loginError = `登录失败: ${err.message || "未知错误"}`;
      connectionTestResult = {
        success: false,
        message: `登录失败: ${err.message || "未知错误"}`
      };
    } finally {
      isLoggingIn = false;
    }
  }

  // 注销 Lighthouse
  function logoutLighthouse() {
    clearLighthouseToken();
    connectionTestResult = {
      success: true,
      message: "已注销登录"
    };
  }

  // 重置为默认设置
  function resetToDefaults() {
    openrouterApiKey = "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";
    openrouterSiteUrl = "https://openrouter.ai/api/v1/chat/completions";
    notionApiKey = "ntn_V40341044546PN6Fp3PvPWFVHa9qOvfuv2BAEQiuPddbhP";
    notionDatabaseId = "1ca0f0738c8680edb18cde821a4158a8";
    notionInspirationDbId = "1cd0f0738c868027b960dc8129fbd159";
    models = [
      { id: "google/gemini-2.0-flash-001", name: "Gemini 2" },
      { id: "google/gemini-2.5-pro-exp-03-25:free", name: "Gemini 2.5" },
      { id: "deepseek/deepseek-chat-v3-0324", name: "Deepseek Chat v3" },
      { id: "openrouter/optimus-alpha", name: "Quasar Alpha" }
    ];
  }

  // 添加模型
  function addModel() {
    modelError = "";

    if (!newModelId || !newModelName) {
      modelError = "模型 ID 和名称不能为空";
      return;
    }

    // 检查是否已存在相同 ID 的模型
    if (models.some(m => m.id === newModelId)) {
      modelError = `模型 ID "${newModelId}" 已存在`;
      return;
    }

    models = [...models, { id: newModelId, name: newModelName }];
    newModelId = "";
    newModelName = "";
  }

  // 删除模型
  function deleteModel(index: number) {
    models = models.filter((_, i) => i !== index);
    if (editingModelIndex === index) {
      editingModelIndex = null;
    }
  }

  // 开始编辑模型
  function startEditModel(index: number) {
    editingModelIndex = index;
    newModelId = models[index].id;
    newModelName = models[index].name;
  }

  // 保存编辑的模型
  function saveEditModel() {
    modelError = "";

    if (editingModelIndex === null) return;

    if (!newModelId || !newModelName) {
      modelError = "模型 ID 和名称不能为空";
      return;
    }

    // 检查是否与其他模型 ID 冲突
    if (models.some((m, i) => m.id === newModelId && i !== editingModelIndex)) {
      modelError = `模型 ID "${newModelId}" 已存在`;
      return;
    }

    models = models.map((model, i) => {
      if (i === editingModelIndex) {
        return { id: newModelId, name: newModelName };
      }
      return model;
    });

    editingModelIndex = null;
    newModelId = "";
    newModelName = "";
  }

  // 取消编辑
  function cancelEdit() {
    editingModelIndex = null;
    newModelId = "";
    newModelName = "";
    modelError = "";
  }
</script>

<Dialog.Root bind:open>
  <Dialog.Content class="sm:max-w-[800px]">
    <Dialog.Header>
      <Dialog.Title>系统配置</Dialog.Title>
      <Dialog.Description>
        配置应用所需的API密钥、模型和系统参数
      </Dialog.Description>
    </Dialog.Header>

    <div class="flex h-[500px] overflow-hidden">
      <!-- 左侧导航菜单 - VS Code 风格 -->
      <div class="w-48 bg-slate-100 dark:bg-slate-800 border-r border-slate-200 dark:border-slate-700 flex-shrink-0">
        <div class="py-2">
          <button
            class={`w-full text-left px-4 py-2 text-sm ${activeTab === 'api' ? 'bg-slate-200 dark:bg-slate-700 font-medium' : 'hover:bg-slate-200 dark:hover:bg-slate-700'}`}
            onclick={() => activeTab = 'api'}
          >
            API 配置
          </button>
          <button
            class={`w-full text-left px-4 py-2 text-sm ${activeTab === 'models' ? 'bg-slate-200 dark:bg-slate-700 font-medium' : 'hover:bg-slate-200 dark:hover:bg-slate-700'}`}
            onclick={() => activeTab = 'models'}
          >
            模型管理
          </button>
          <button
            class={`w-full text-left px-4 py-2 text-sm ${activeTab === 'lighthouse' ? 'bg-slate-200 dark:bg-slate-700 font-medium' : 'hover:bg-slate-200 dark:hover:bg-slate-700'}`}
            onclick={() => activeTab = 'lighthouse'}
          >
            Lighthouse
          </button>
        </div>
      </div>

      <!-- 右侧内容区域 -->
      <div class="flex-1 overflow-y-auto p-4">
      {#if activeTab === 'api'}
        <!-- API 配置面板 -->
        <div class="border-b pb-2">
          <h3 class="font-medium text-lg mb-2">OpenRouter配置</h3>

          <div class="grid grid-cols-4 items-center gap-4 mb-3">
            <label for="openrouter-key" class="text-right text-sm font-medium">
              API密钥
            </label>
            <input
              id="openrouter-key"
              type="password"
              bind:value={openrouterApiKey}
              class="col-span-3 h-10 w-full rounded-md border border-slate-300 bg-transparent px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-slate-400"
            />
          </div>

          {#if advancedSettings}
            <div class="grid grid-cols-4 items-center gap-4">
              <label for="openrouter-url" class="text-right text-sm font-medium">
                API地址
              </label>
              <input
                id="openrouter-url"
                type="text"
                bind:value={openrouterSiteUrl}
                class="col-span-3 h-10 w-full rounded-md border border-slate-300 bg-transparent px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-slate-400"
              />
            </div>
          {/if}
        </div>

        <div class="border-b pb-2">
          <h3 class="font-medium text-lg mb-2">Notion配置</h3>

          <div class="grid grid-cols-4 items-center gap-4 mb-3">
            <label for="notion-key" class="text-right text-sm font-medium">
              API密钥
            </label>
            <input
              id="notion-key"
              type="password"
              bind:value={notionApiKey}
              class="col-span-3 h-10 w-full rounded-md border border-slate-300 bg-transparent px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-slate-400"
            />
          </div>

          <div class="grid grid-cols-4 items-center gap-4 mb-3">
            <label for="notion-db" class="text-right text-sm font-medium">
              主数据库ID
            </label>
            <input
              id="notion-db"
              type="text"
              bind:value={notionDatabaseId}
              class="col-span-3 h-10 w-full rounded-md border border-slate-300 bg-transparent px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-slate-400"
            />
          </div>

          {#if advancedSettings}
            <div class="grid grid-cols-4 items-center gap-4">
              <label for="notion-inspiration-db" class="text-right text-sm font-medium">
                灵感数据库ID
              </label>
              <input
                id="notion-inspiration-db"
                type="text"
                bind:value={notionInspirationDbId}
                class="col-span-3 h-10 w-full rounded-md border border-slate-300 bg-transparent px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-slate-400"
              />
            </div>
          {/if}
        </div>

        <div class="flex items-center">
          <label class="flex items-center cursor-pointer">
            <input
              type="checkbox"
              bind:checked={advancedSettings}
              class="sr-only peer"
            />
            <div class="relative w-11 h-6 bg-slate-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-slate-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-slate-600"></div>
            <span class="ms-3 text-sm font-medium">显示高级设置</span>
          </label>
        </div>
      {:else if activeTab === 'models'}
        <!-- 模型管理面板 -->
        <div>
          <h3 class="font-medium text-lg mb-4">大语言模型管理</h3>

          <!-- 模型列表 -->
          <div class="border rounded-md overflow-hidden mb-6">
            <table class="min-w-full divide-y divide-slate-200">
              <thead class="bg-slate-50">
                <tr>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">模型名称</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">模型 ID</th>
                  <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-slate-500 uppercase tracking-wider">操作</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-slate-200">
                {#if models.length === 0}
                  <tr>
                    <td colspan="3" class="px-6 py-4 text-center text-sm text-slate-500">暂无模型，请添加模型</td>
                  </tr>
                {:else}
                  {#each models as model, index}
                    <tr class="hover:bg-slate-50">
                      <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-slate-900">{model.name}</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500">{model.id}</td>
                      <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                          onclick={() => startEditModel(index)}
                          class="text-blue-600 hover:text-blue-900 mr-3"
                        >
                          编辑
                        </button>
                        <button
                          onclick={() => deleteModel(index)}
                          class="text-red-600 hover:text-red-900"
                        >
                          删除
                        </button>
                      </td>
                    </tr>
                  {/each}
                {/if}
              </tbody>
            </table>
          </div>

          <!-- 添加/编辑模型表单 -->
          <div class="bg-slate-50 p-4 rounded-md">
            <h4 class="font-medium text-base mb-3">{editingModelIndex !== null ? '编辑模型' : '添加新模型'}</h4>

            <div class="grid grid-cols-1 gap-4 mb-4 sm:grid-cols-2">
              <div>
                <label for="model-name" class="block text-sm font-medium text-slate-700 mb-1">模型名称</label>
                <input
                  id="model-name"
                  type="text"
                  bind:value={newModelName}
                  placeholder="例如: Gemini 2"
                  class="w-full h-10 rounded-md border border-slate-300 bg-white px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label for="model-id" class="block text-sm font-medium text-slate-700 mb-1">模型 ID</label>
                <input
                  id="model-id"
                  type="text"
                  bind:value={newModelId}
                  placeholder="例如: google/gemini-2.0-flash-001"
                  class="w-full h-10 rounded-md border border-slate-300 bg-white px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            {#if modelError}
              <div class="text-red-500 text-sm mb-3">{modelError}</div>
            {/if}

            <div class="flex justify-end gap-2">
              {#if editingModelIndex !== null}
                <Button variant="outline" size="sm" on:click={cancelEdit}>取消</Button>
                <Button size="sm" on:click={saveEditModel}>保存更改</Button>
              {:else}
                <Button size="sm" on:click={addModel}>添加模型</Button>
              {/if}
            </div>
          </div>
        </div>
      {:else if activeTab === 'lighthouse'}
        <!-- Lighthouse 配置面板 -->
        <div>
          <h3 class="font-medium text-lg mb-4">Lighthouse 后端配置</h3>

          <div class="border-b pb-4 mb-4">
            <h4 class="font-medium text-base mb-3">服务器设置</h4>

            <div class="grid grid-cols-4 items-center gap-4 mb-3">
              <label for="lighthouse-url" class="text-right text-sm font-medium">
                服务器地址
              </label>
              <input
                id="lighthouse-url"
                type="text"
                bind:value={lighthouseServerUrl}
                placeholder="例如: http://43.139.167.155"
                class="col-span-3 h-10 w-full rounded-md border border-slate-300 bg-transparent px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-slate-400"
              />
            </div>

            <div class="grid grid-cols-4 items-center gap-4 mb-3">
              <label for="lighthouse-port" class="text-right text-sm font-medium">
                端口号
              </label>
              <input
                id="lighthouse-port"
                type="number"
                bind:value={lighthousePort}
                placeholder="例如: 3001"
                class="col-span-3 h-10 w-full rounded-md border border-slate-300 bg-transparent px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-slate-400"
              />
            </div>

            <div class="flex justify-end">
              <Button
                variant="outline"
                size="sm"
                on:click={testLighthouseConnection}
                disabled={isTestingConnection}
              >
                {#if isTestingConnection}
                  <span class="inline-block w-4 h-4 border-2 border-t-transparent border-slate-500 rounded-full animate-spin mr-2"></span>
                {/if}
                测试连接
              </Button>
            </div>

            {#if connectionTestResult}
              <div class={`mt-3 p-3 rounded-md ${connectionTestResult.success ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'}`}>
                <div class="flex items-center">
                  {#if connectionTestResult.success}
                    <span class="w-2 h-2 rounded-full bg-green-500 mr-2"></span>
                  {:else}
                    <span class="w-2 h-2 rounded-full bg-red-500 mr-2"></span>
                  {/if}
                  <span>{connectionTestResult.message}</span>
                </div>
              </div>
            {/if}
          </div>

          <div>
            <h4 class="font-medium text-base mb-3">用户认证</h4>

            {#if isTokenValid()}
              <div class="bg-green-50 text-green-800 p-3 rounded-md mb-3">
                <div class="flex items-center">
                  <span class="w-2 h-2 rounded-full bg-green-500 mr-2"></span>
                  <span>已登录为: {$lighthouseSettings.username}</span>
                </div>
                <div class="mt-2 text-sm">
                  Token 有效期至: {new Date($lighthouseSettings.tokenExpiry || '').toLocaleString()}
                </div>
              </div>

              <div class="flex justify-end">
                <Button
                  variant="outline"
                  size="sm"
                  on:click={logoutLighthouse}
                >
                  注销登录
                </Button>
              </div>
            {:else}
              <div class="grid grid-cols-4 items-center gap-4 mb-3">
                <label for="login-username" class="text-right text-sm font-medium">
                  用户名
                </label>
                <input
                  id="login-username"
                  type="text"
                  bind:value={loginUsername}
                  class="col-span-3 h-10 w-full rounded-md border border-slate-300 bg-transparent px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-slate-400"
                />
              </div>

              <div class="grid grid-cols-4 items-center gap-4 mb-3">
                <label for="login-password" class="text-right text-sm font-medium">
                  密码
                </label>
                <input
                  id="login-password"
                  type="password"
                  bind:value={loginPassword}
                  class="col-span-3 h-10 w-full rounded-md border border-slate-300 bg-transparent px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-slate-400"
                />
              </div>

              {#if loginError}
                <div class="text-red-500 text-sm mb-3">{loginError}</div>
              {/if}

              <div class="flex justify-end">
                <Button
                  size="sm"
                  on:click={loginToLighthouse}
                  disabled={isLoggingIn}
                >
                  {#if isLoggingIn}
                    <span class="inline-block w-4 h-4 border-2 border-t-transparent border-white rounded-full animate-spin mr-2"></span>
                  {/if}
                  登录
                </Button>
              </div>
            {/if}
          </div>
        </div>
      {/if}

      {#if saveError}
        <div class="text-red-500 text-sm mt-2">
          {saveError}
        </div>
      {/if}

      {#if saveSuccess}
        <div class="text-green-500 text-sm mt-2">
          设置已保存成功！
        </div>
      {/if}
      </div>
    </div>

    <Dialog.Footer>
      <div class="flex justify-between w-full">
        <Button variant="outline" size="sm" on:click={resetToDefaults} type="button">
          重置为默认值
        </Button>
        <div class="flex gap-2">
          <Button variant="outline" on:click={() => open = false}>
            取消
          </Button>
          <Button on:click={handleSaveSettings} disabled={isSaving}>
            {#if isSaving}
              <span class="inline-block w-4 h-4 border-2 border-t-transparent border-white rounded-full animate-spin mr-2"></span>
            {/if}
            保存
          </Button>
        </div>
      </div>
    </Dialog.Footer>
  </Dialog.Content>
</Dialog.Root>