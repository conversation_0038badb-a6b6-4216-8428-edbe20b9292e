<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import { onMount } from 'svelte';
  import { Button } from '$lib/components/ui/button';
  import { ruleDesignerService, type RuleDefinition, type ParameterDefinition, type ParameterSchema } from '$lib/services/ruleDesignerService';
  import { sqliteDictionaryService } from '$lib/services/sqliteDictionaryService';
  import { Plus, Trash2, Save } from 'lucide-svelte';

  const dispatch = createEventDispatcher();

  // Props
  const { ruleDefinition = null } = $props<{ ruleDefinition?: RuleDefinition | null }>();

  // 状态管理
  let isSubmitting = $state(false);
  let error = $state<string | null>(null);
  let success = $state<string | null>(null);
  let categories = $state<string[]>([]);

  // 表单数据
  let formData = $state<RuleDefinition>({
    rule_name: '',
    rule_description: '',
    category: '',
    parameter_schema: JSON.stringify({ parameters: [] }, null, 2)
  });

  // 参数模式
  let parameterSchema = $state<ParameterSchema>({ parameters: [] });

  // 新参数
  let newParameter = $state<ParameterDefinition>({
    name: '',
    label: '',
    type: 'string',
    required: false,
    options: [],
    unit: '',
    readonly: false
  });

  // 新选项
  let newOption = $state('');

  // 加载分类
  async function loadCategories() {
    try {
      // 尝试从字典中获取分类
      const categoryDict = await sqliteDictionaryService.getDictByName('rule_categories');
      if (categoryDict && categoryDict.id !== undefined) {
        const items = await sqliteDictionaryService.getDictItems(categoryDict.id);
        categories = items.map(item => item.value);
      } else {
        console.log('未找到rule_categories字典，使用默认分类');
        // 如果没有字典，使用默认分类
        categories = ['入组标准', '排除标准', '其他'];
      }
    } catch (error) {
      console.error('加载分类失败:', error);
      // 使用默认分类
      categories = ['入组标准', '排除标准', '其他'];
    }
  }

  // 添加参数
  function addParameter() {
    if (!newParameter.name || !newParameter.label) {
      error = '参数名称和标签不能为空';
      return;
    }

    // 检查参数名称是否已存在
    if (parameterSchema.parameters.some(p => p.name === newParameter.name)) {
      error = `参数名称 "${newParameter.name}" 已存在`;
      return;
    }

    // 如果是枚举类型，确保有选项
    if (newParameter.type === 'enum' && (!newParameter.options || newParameter.options.length === 0)) {
      error = '枚举类型参数必须有至少一个选项';
      return;
    }

    // 添加参数
    parameterSchema.parameters = [...parameterSchema.parameters, { ...newParameter }];

    // 更新参数模式字符串
    formData.parameter_schema = JSON.stringify(parameterSchema, null, 2);

    console.log('参数已添加:', newParameter);
    console.log('当前参数列表:', parameterSchema.parameters);
    console.log('参数模式JSON:', formData.parameter_schema);

    // 重置新参数
    newParameter = {
      name: '',
      label: '',
      type: 'string',
      required: false,
      options: [],
      unit: '',
      readonly: false
    };

    error = null;
  }

  // 删除参数
  function removeParameter(index: number) {
    parameterSchema.parameters = parameterSchema.parameters.filter((_, i) => i !== index);
    formData.parameter_schema = JSON.stringify(parameterSchema, null, 2);
  }

  // 添加选项
  function addOption() {
    if (!newOption) {
      error = '选项不能为空';
      return;
    }

    if (!newParameter.options) {
      newParameter.options = [];
    }

    // 检查选项是否已存在
    if (newParameter.options.includes(newOption)) {
      error = `选项 "${newOption}" 已存在`;
      return;
    }

    newParameter.options = [...newParameter.options, newOption];
    newOption = '';
    error = null;
  }

  // 删除选项
  function removeOption(index: number) {
    if (newParameter.options) {
      newParameter.options = newParameter.options.filter((_, i) => i !== index);
    }
  }

  // 验证表单
  function validateForm(): boolean {
    if (!formData.rule_name) {
      error = '规则名称不能为空';
      return false;
    }

    try {
      // 验证参数模式是否为有效的JSON
      JSON.parse(formData.parameter_schema);
    } catch (e) {
      error = '参数模式格式无效';
      return false;
    }

    return true;
  }

  // 提交表单
  async function handleSubmit(e: Event) {
    e.preventDefault();
    console.log('表单提交被触发');

    if (!validateForm()) {
      console.log('表单验证失败');
      return;
    }

    isSubmitting = true;
    error = null;
    success = null;

    try {
      console.log('提交表单，参数模式:', formData.parameter_schema);
      console.log('参数模式对象:', parameterSchema);

      // 确保参数模式是最新的
      formData.parameter_schema = JSON.stringify(parameterSchema, null, 2);

      if (ruleDefinition?.rule_definition_id) {
        // 更新
        const updateRequest = {
          rule_name: formData.rule_name,
          rule_description: formData.rule_description,
          category: formData.category,
          parameter_schema: formData.parameter_schema
        };

        console.log('更新请求:', updateRequest);

        const updatedRule = await ruleDesignerService.updateRuleDefinition(
          ruleDefinition.rule_definition_id,
          updateRequest
        );

        success = '规则定义更新成功';
        console.log('规则定义更新成功');

        // 分发更新成功事件
        dispatch('save', { rule: updatedRule, action: 'update' });
      } else {
        // 创建
        const createRequest = {
          rule_name: formData.rule_name,
          rule_description: formData.rule_description,
          category: formData.category,
          parameter_schema: formData.parameter_schema
        };

        console.log('创建请求:', createRequest);

        const result = await ruleDesignerService.createRuleDefinition(createRequest);
        console.log('创建结果:', result);

        success = '规则定义创建成功';
        console.log('规则定义创建成功');

        // 分发创建成功事件
        dispatch('save', { rule: result, action: 'create' });

        // 重置表单
        formData = {
          rule_name: '',
          rule_description: '',
          category: '',
          parameter_schema: JSON.stringify({ parameters: [] }, null, 2)
        };

        parameterSchema = { parameters: [] };
      }
    } catch (err: any) {
      error = err.message || (ruleDefinition ? '更新规则定义失败' : '创建规则定义失败');
      console.error(ruleDefinition ? '更新规则定义失败:' : '创建规则定义失败:', err);
    } finally {
      isSubmitting = false;
    }
  }

  // 监听参数模式文本框的变化
  function handleParameterSchemaChange() {
    try {
      // 尝试解析参数模式
      const newSchema = JSON.parse(formData.parameter_schema);
      // 如果解析成功，更新参数模式对象
      parameterSchema = newSchema;
      error = null;
    } catch (e) {
      console.error('解析参数模式失败:', e);
      error = '参数模式格式无效';
    }
  }

  // 组件挂载时初始化
  onMount(() => {
    // 加载分类
    loadCategories();

    // 如果是编辑模式，使用传入的数据初始化表单
    if (ruleDefinition) {
      formData = { ...ruleDefinition };

      try {
        // 解析参数模式
        parameterSchema = JSON.parse(ruleDefinition.parameter_schema);
      } catch (e) {
        console.error('解析参数模式失败:', e);
        parameterSchema = { parameters: [] };
      }
    }
  });
</script>

<div class="w-full space-y-6">
  {#if error}
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
      <span class="block sm:inline">{error}</span>
    </div>
  {/if}

  {#if success}
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
      <span class="block sm:inline">{success}</span>
    </div>
  {/if}

  <form on:submit|preventDefault={handleSubmit} class="space-y-6">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- 规则名称和分类 -->
      <div class="space-y-4">
        <div>
          <label for="rule-name" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
            规则名称 <span class="text-red-500">*</span>
          </label>
          <input
            type="text"
            id="rule-name"
            bind:value={formData.rule_name}
            class="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md text-sm text-slate-900 dark:text-slate-100 bg-white dark:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600"
            placeholder="请输入规则名称"
          />
        </div>

        <div>
          <label for="category" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
            分类
          </label>
          <select
            id="category"
            bind:value={formData.category}
            class="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md text-sm text-slate-900 dark:text-slate-100 bg-white dark:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600"
          >
            <option value="">请选择分类</option>
            {#each categories as category}
              <option value={category}>{category}</option>
            {/each}
          </select>
        </div>
      </div>

      <!-- 规则描述 -->
      <div>
        <label for="rule-description" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
          规则描述
        </label>
        <textarea
          id="rule-description"
          bind:value={formData.rule_description}
          class="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md text-sm text-slate-900 dark:text-slate-100 bg-white dark:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600"
          rows="4"
          placeholder="请输入规则描述"
        ></textarea>
      </div>
    </div>

    <!-- 参数定义 -->
    <div class="border border-slate-200 dark:border-slate-700 rounded-md">
      <div class="p-4 bg-slate-50 dark:bg-slate-800/50">
        <h3 class="text-lg font-medium">参数定义</h3>
      </div>

      <div class="p-4 space-y-6">
        <!-- 已定义的参数列表 -->
        {#if parameterSchema.parameters.length > 0}
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-slate-200 dark:divide-slate-700">
              <thead class="bg-slate-50 dark:bg-slate-800">
                <tr>
                  <th class="px-3 py-2 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">名称</th>
                  <th class="px-3 py-2 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">标签</th>
                  <th class="px-3 py-2 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">类型</th>
                  <th class="px-3 py-2 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">必填</th>
                  <th class="px-3 py-2 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">单位</th>
                  <th class="px-3 py-2 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">选项</th>
                  <th class="px-3 py-2 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">操作</th>
                </tr>
              </thead>
              <tbody class="bg-white dark:bg-slate-900 divide-y divide-slate-200 dark:divide-slate-800">
                {#each parameterSchema.parameters as param, index}
                  <tr>
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-slate-900 dark:text-slate-100">{param.name}</td>
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-slate-900 dark:text-slate-100">{param.label}</td>
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-slate-900 dark:text-slate-100">{param.type}</td>
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-slate-900 dark:text-slate-100">{param.required ? '是' : '否'}</td>
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-slate-900 dark:text-slate-100">{param.unit || '-'}</td>
                    <td class="px-3 py-2 text-sm text-slate-900 dark:text-slate-100">
                      {#if param.type === 'enum' && param.options && param.options.length > 0}
                        <div class="flex flex-wrap gap-1">
                          {#each param.options as option}
                            <span class="px-2 py-0.5 text-xs rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900/40 dark:text-blue-300">
                              {option}
                            </span>
                          {/each}
                        </div>
                      {:else}
                        -
                      {/if}
                    </td>
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-slate-900 dark:text-slate-100">
                      <button
                        type="button"
                        class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                        on:click={() => removeParameter(index)}
                      >
                        <Trash2 class="h-4 w-4" />
                      </button>
                    </td>
                  </tr>
                {/each}
              </tbody>
            </table>
          </div>
        {/if}

        <!-- 添加新参数 -->
        <div class="bg-slate-50 dark:bg-slate-800 rounded-md p-4">
          <h4 class="text-sm font-medium mb-4">添加新参数</h4>

          <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <!-- 参数基本信息 -->
            <div class="space-y-4">
              <div>
                <label for="param-name" class="block text-xs font-medium text-slate-700 dark:text-slate-300 mb-1">
                  参数名称 <span class="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  id="param-name"
                  bind:value={newParameter.name}
                  class="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md text-sm"
                  placeholder="例如: min_age"
                />
                <p class="mt-1 text-xs text-slate-500">参数名称只能包含英文字母、数字和下划线</p>
              </div>

              <div>
                <label for="param-label" class="block text-xs font-medium text-slate-700 dark:text-slate-300 mb-1">
                  参数标签 <span class="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  id="param-label"
                  bind:value={newParameter.label}
                  class="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md text-sm"
                  placeholder="例如: 最小年龄"
                />
              </div>
            </div>

            <!-- 参数配置 -->
            <div class="space-y-4">
              <div class="grid grid-cols-2 gap-4">
                <div>
                  <label for="param-type" class="block text-xs font-medium text-slate-700 dark:text-slate-300 mb-1">
                    参数类型
                  </label>
                  <select
                    id="param-type"
                    bind:value={newParameter.type}
                    class="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md text-sm"
                  >
                    <option value="string">文本 (string)</option>
                    <option value="integer">整数 (integer)</option>
                    <option value="number">数字 (number)</option>
                    <option value="boolean">布尔值 (boolean)</option>
                    <option value="enum">枚举 (enum)</option>
                  </select>
                </div>

                <div>
                  <label for="param-unit" class="block text-xs font-medium text-slate-700 dark:text-slate-300 mb-1">
                    单位
                  </label>
                  <input
                    type="text"
                    id="param-unit"
                    bind:value={newParameter.unit}
                    class="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md text-sm"
                    placeholder="例如: 岁、月、天"
                  />
                </div>
              </div>

              <div class="flex items-center space-x-6">
                <label class="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    bind:checked={newParameter.required}
                    class="rounded border-slate-300 dark:border-slate-600 text-blue-600"
                  />
                  <span class="text-sm">必填</span>
                </label>

                <label class="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    bind:checked={newParameter.readonly}
                    class="rounded border-slate-300 dark:border-slate-600 text-blue-600"
                  />
                  <span class="text-sm">只读</span>
                </label>
              </div>
            </div>
          </div>

          <!-- 枚举选项 -->
          {#if newParameter.type === 'enum'}
            <div class="mt-4">
              <label class="block text-xs font-medium text-slate-700 dark:text-slate-300 mb-2">
                枚举选项
              </label>
              <div class="flex space-x-2">
                <input
                  type="text"
                  bind:value={newOption}
                  class="flex-1 px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md text-sm"
                  placeholder="输入选项值"
                />
                <Button type="button" variant="outline" class="flex items-center space-x-1" on:click={addOption}>
                  <Plus class="h-4 w-4" />
                  <span>添加</span>
                </Button>
              </div>

              {#if newParameter.options && newParameter.options.length > 0}
                <div class="mt-2 flex flex-wrap gap-2">
                  {#each newParameter.options as option, index}
                    <div class="flex items-center space-x-1 px-2 py-1 bg-slate-100 dark:bg-slate-700 rounded-md">
                      <span class="text-xs">{option}</span>
                      <button
                        type="button"
                        class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                        on:click={() => removeOption(index)}
                      >
                        <Trash2 class="h-3 w-3" />
                      </button>
                    </div>
                  {/each}
                </div>
              {/if}
            </div>
          {/if}

          <div class="mt-4 flex justify-end">
            <Button type="button" variant="outline" class="flex items-center space-x-1" on:click={addParameter}>
              <Plus class="h-4 w-4" />
              <span>添加参数</span>
            </Button>
          </div>
        </div>

        <!-- 参数模式 JSON -->
        <div class="space-y-2">
          <label for="parameter-schema" class="block text-sm font-medium text-slate-700 dark:text-slate-300">
            参数模式 JSON
          </label>
          <textarea
            id="parameter-schema"
            bind:value={formData.parameter_schema}
            on:input={handleParameterSchemaChange}
            class="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md text-sm font-mono"
            rows="8"
          ></textarea>
          <p class="text-xs text-slate-500">高级用户可以直接编辑 JSON，修改后会覆盖上面的参数定义</p>
        </div>
      </div>
    </div>

    <!-- 提交按钮 -->
    <div class="flex justify-end">
      <Button type="submit" disabled={isSubmitting} class="flex items-center space-x-2">
        {#if isSubmitting}
          <span class="inline-block w-4 h-4 border-2 border-t-transparent border-current rounded-full animate-spin"></span>
        {/if}
        <Save class="h-4 w-4" />
        <span>{ruleDefinition ? '更新规则' : '保存规则'}</span>
      </Button>
    </div>
  </form>
</div>
