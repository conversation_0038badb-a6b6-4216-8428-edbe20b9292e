<script lang="ts">
  import { onMount } from 'svelte';
  import { Button } from '$lib/components/ui/button';
  import * as Dialog from '$lib/components/ui/dialog';
  import { ruleDesignerService, type RuleDefinition } from '$lib/services/ruleDesignerService';
  import RuleDefinitionForm from './RuleDefinitionForm.svelte';
  import { Edit, Trash2, Plus, Search, FileText } from 'lucide-svelte';

  // 状态管理
  let isLoading = $state(true);
  let error = $state<string | null>(null);
  let rules = $state<RuleDefinition[]>([]);
  let filteredRules = $state<RuleDefinition[]>([]);
  let selectedCategory = $state<string | null>(null);
  let searchQuery = $state('');
  let categories = $state<string[]>([]);
  let affectedProjects = $state<Array<[string, string, string]>>([]);
  let isCheckingProjects = $state(false);

  // 对话框状态
  let showCreateDialog = $state(false);
  let showEditDialog = $state(false);
  let showDeleteDialog = $state(false);
  let selectedRule = $state<RuleDefinition | null>(null);

  // 加载规则定义
  async function loadRuleDefinitions() {
    isLoading = true;
    error = null;

    try {
      const query = selectedCategory ? { category: selectedCategory } : {};
      rules = await ruleDesignerService.getRuleDefinitions(query);
      applySearch();

      // 提取所有分类
      const uniqueCategories = new Set<string>();
      rules.forEach(rule => {
        if (rule.category) {
          uniqueCategories.add(rule.category);
        }
      });
      categories = Array.from(uniqueCategories);
    } catch (err: any) {
      error = err.message || '加载规则定义失败';
      console.error('加载规则定义失败:', err);
    } finally {
      isLoading = false;
    }
  }

  // 应用搜索
  function applySearch() {
    if (!searchQuery) {
      filteredRules = [...rules];
      return;
    }

    const query = searchQuery.toLowerCase();
    filteredRules = rules.filter(rule =>
      rule.rule_name.toLowerCase().includes(query) ||
      (rule.rule_description && rule.rule_description.toLowerCase().includes(query))
    );
  }

  // 筛选分类
  function filterByCategory(category: string | null) {
    selectedCategory = category;
    loadRuleDefinitions();
  }

  // 编辑规则
  function editRule(rule: RuleDefinition) {
    selectedRule = rule;
    showEditDialog = true;
  }

  // 删除规则
  async function confirmDeleteRule(rule: RuleDefinition) {
    selectedRule = rule;

    if (!rule.rule_definition_id) return;

    isCheckingProjects = true;
    affectedProjects = [];

    try {
      // 查找使用该规则的项目
      affectedProjects = await ruleDesignerService.findProjectsUsingRule(rule.rule_definition_id);
      showDeleteDialog = true;
    } catch (err: any) {
      error = err.message || '查找使用规则的项目失败';
      console.error('查找使用规则的项目失败:', err);
    } finally {
      isCheckingProjects = false;
    }
  }

  // 执行删除
  async function deleteRule() {
    if (!selectedRule || !selectedRule.rule_definition_id) return;

    try {
      await ruleDesignerService.deleteRuleDefinition(selectedRule.rule_definition_id);
      showDeleteDialog = false;
      loadRuleDefinitions();
    } catch (err: any) {
      error = err.message || '删除规则定义失败';
      console.error('删除规则定义失败:', err);
    }
  }

  // 处理对话框关闭
  function handleDialogClose() {
    // 重新加载规则定义
    loadRuleDefinitions();
  }

  // 组件挂载时初始化
  onMount(() => {
    loadRuleDefinitions();
  });
</script>

<div class="w-full">
  {#if error}
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
      <span class="block sm:inline">{error}</span>
    </div>
  {/if}

  <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
    <div class="flex flex-wrap gap-2">
      <Button
        type="button"
        variant={selectedCategory === null ? 'default' : 'outline'}
        on:click={() => filterByCategory(null)}
      >
        全部
      </Button>
      {#each categories as category}
        <Button
          type="button"
          variant={selectedCategory === category ? 'default' : 'outline'}
          on:click={() => filterByCategory(category)}
        >
          {category}
        </Button>
      {/each}
    </div>

    <div class="flex gap-2 w-full md:w-auto">
      <div class="relative flex-1 md:flex-none md:w-64">
        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search class="h-4 w-4 text-slate-400" />
        </div>
        <input
          type="text"
          bind:value={searchQuery}
          oninput={applySearch}
          placeholder="搜索规则..."
          class="pl-10 w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md text-sm text-slate-900 dark:text-slate-100 bg-white dark:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600"
        />
      </div>
      <Button onclick={() => showCreateDialog = true} class="flex items-center gap-1">
        <Plus class="h-4 w-4" />
        <span>新建规则</span>
      </Button>
    </div>
  </div>

  {#if isLoading}
    <div class="flex justify-center items-center py-12">
      <div class="inline-block w-6 h-6 border-2 border-t-transparent border-blue-600 rounded-full animate-spin"></div>
      <span class="ml-2 text-slate-600 dark:text-slate-300">加载中...</span>
    </div>
  {:else if filteredRules.length === 0}
    <div class="bg-slate-50 dark:bg-slate-800 rounded-md p-12 text-center">
      <FileText class="h-12 w-12 mx-auto text-slate-400" />
      <h3 class="mt-4 text-lg font-medium text-slate-900 dark:text-slate-100">没有找到规则定义</h3>
      <p class="mt-2 text-sm text-slate-500 dark:text-slate-400">
        {searchQuery ? '没有符合搜索条件的规则定义' : '点击"新建规则"按钮创建第一个规则定义'}
      </p>
    </div>
  {:else}
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-slate-200 dark:divide-slate-700">
        <thead class="bg-slate-50 dark:bg-slate-800">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">规则名称</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">分类</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">描述</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">参数数量</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">创建时间</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">操作</th>
          </tr>
        </thead>
        <tbody class="bg-white dark:bg-slate-900 divide-y divide-slate-200 dark:divide-slate-800">
          {#each filteredRules as rule}
            <tr class="hover:bg-slate-50 dark:hover:bg-slate-800/50">
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-slate-900 dark:text-slate-100">{rule.rule_name}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500 dark:text-slate-400">
                {#if rule.category}
                  <span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900/40 dark:text-blue-300">
                    {rule.category}
                  </span>
                {:else}
                  -
                {/if}
              </td>
              <td class="px-6 py-4 text-sm text-slate-500 dark:text-slate-400">
                <div class="max-w-xs truncate">{rule.rule_description || '-'}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500 dark:text-slate-400">
                {(() => {
                  try {
                    return JSON.parse(rule.parameter_schema).parameters?.length || 0;
                  } catch (e) {
                    return 0;
                  }
                })()}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500 dark:text-slate-400">
                {rule.created_at ? new Date(rule.created_at).toLocaleString() : '-'}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500 dark:text-slate-400">
                <div class="flex space-x-2">
                  <button
                    type="button"
                    class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                    onclick={() => editRule(rule)}
                  >
                    <Edit class="h-4 w-4" />
                  </button>
                  <button
                    type="button"
                    class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                    onclick={() => confirmDeleteRule(rule)}
                  >
                    <Trash2 class="h-4 w-4" />
                  </button>
                </div>
              </td>
            </tr>
          {/each}
        </tbody>
      </table>
    </div>
  {/if}

  <!-- 创建规则对话框 -->
  <Dialog.Root
    bind:open={showCreateDialog}
    onOpenChange={(open) => {
      if (!open) handleDialogClose();
    }}
  >
    <Dialog.Content class="w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
      <Dialog.Header class="shrink-0">
        <Dialog.Title>创建规则定义</Dialog.Title>
        <Dialog.Description>
          创建一个新的规则定义，用于项目的入组或排除标准。
        </Dialog.Description>
      </Dialog.Header>

      <div class="flex-1 overflow-y-auto py-4 px-1">
        <RuleDefinitionForm
          on:save={async (event) => {
            const { action } = event.detail;
            await loadRuleDefinitions();
            if (action === 'create') {
              showCreateDialog = false;
            }
          }}
        />
      </div>

      <Dialog.Footer class="shrink-0 border-t border-slate-200 dark:border-slate-700 pt-4">
        <Button variant="outline" onclick={() => showCreateDialog = false}>
          关闭
        </Button>
      </Dialog.Footer>
    </Dialog.Content>
  </Dialog.Root>

  <!-- 编辑规则对话框 -->
  <Dialog.Root
    bind:open={showEditDialog}
    onOpenChange={(open) => {
      if (!open) handleDialogClose();
    }}
  >
    <Dialog.Content class="w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
      <Dialog.Header class="shrink-0">
        <Dialog.Title>编辑规则定义</Dialog.Title>
        <Dialog.Description>
          编辑规则定义的信息和参数。
        </Dialog.Description>
      </Dialog.Header>

      <div class="flex-1 overflow-y-auto py-4 px-1">
        <RuleDefinitionForm
          ruleDefinition={selectedRule}
          on:save={async (event) => {
            const { action } = event.detail;
            await loadRuleDefinitions();
            if (action === 'update') {
              showEditDialog = false;
            }
          }}
        />
      </div>

      <Dialog.Footer class="shrink-0 border-t border-slate-200 dark:border-slate-700 pt-4">
        <Button variant="outline" onclick={() => showEditDialog = false}>
          关闭
        </Button>
      </Dialog.Footer>
    </Dialog.Content>
  </Dialog.Root>

  <!-- 删除确认对话框 -->
  <Dialog.Root bind:open={showDeleteDialog}>
    <Dialog.Content class="max-w-md">
      <Dialog.Header>
        <Dialog.Title>删除规则定义</Dialog.Title>
        <Dialog.Description>
          确定要删除规则定义"{selectedRule?.rule_name}"吗？此操作不可撤销。
        </Dialog.Description>
      </Dialog.Header>

      <div class="py-4">
        {#if isCheckingProjects}
          <div class="flex items-center justify-center py-4">
            <div class="inline-block w-5 h-5 border-2 border-t-transparent border-blue-600 rounded-full animate-spin mr-2"></div>
            <span>正在检查使用该规则的项目...</span>
          </div>
        {:else if affectedProjects.length > 0}
          <div class="mb-4">
            <p class="text-sm font-medium text-red-600 dark:text-red-400 mb-2">
              以下项目正在使用此规则，删除规则将同时删除这些项目中的相关标准：
            </p>
            <div class="max-h-48 overflow-y-auto border border-slate-200 dark:border-slate-700 rounded-md">
              <table class="min-w-full divide-y divide-slate-200 dark:divide-slate-700">
                <thead class="bg-slate-50 dark:bg-slate-800">
                  <tr>
                    <th class="px-3 py-2 text-left text-xs font-medium text-slate-500 dark:text-slate-400">项目名称</th>
                    <th class="px-3 py-2 text-left text-xs font-medium text-slate-500 dark:text-slate-400">标准类型</th>
                  </tr>
                </thead>
                <tbody class="bg-white dark:bg-slate-900 divide-y divide-slate-200 dark:divide-slate-800">
                  {#each affectedProjects as [_projectId, projectName, criterionType]}
                    <tr class="hover:bg-slate-50 dark:hover:bg-slate-800/50">
                      <td class="px-3 py-2 text-xs text-slate-900 dark:text-slate-100">{projectName}</td>
                      <td class="px-3 py-2 text-xs">
                        {#if criterionType === 'inclusion'}
                          <span class="px-2 py-0.5 text-xs rounded-full bg-green-100 text-green-800 dark:bg-green-900/40 dark:text-green-300">
                            入选标准
                          </span>
                        {:else if criterionType === 'exclusion'}
                          <span class="px-2 py-0.5 text-xs rounded-full bg-red-100 text-red-800 dark:bg-red-900/40 dark:text-red-300">
                            排除标准
                          </span>
                        {:else}
                          {criterionType}
                        {/if}
                      </td>
                    </tr>
                  {/each}
                </tbody>
              </table>
            </div>
          </div>
          <p class="text-sm text-slate-500 dark:text-slate-400 mt-2">
            确认删除将会同时删除上述项目中的相关标准。此操作不可撤销。
          </p>
        {:else}
          <p class="text-sm text-slate-500 dark:text-slate-400">
            没有项目正在使用此规则。删除操作安全，不会影响任何项目的标准。
          </p>
        {/if}
      </div>

      <Dialog.Footer>
        <Button variant="outline" onclick={() => showDeleteDialog = false}>
          取消
        </Button>
        <Button
          variant="destructive"
          onclick={deleteRule}
          disabled={isCheckingProjects}
        >
          确认删除
        </Button>
      </Dialog.Footer>
    </Dialog.Content>
  </Dialog.Root>
</div>
