<script lang="ts">
  import { onMount } from 'svelte';
  import { Button } from '$lib/components/ui/button';
  import { csvImportService, type QualityControlResult } from '$lib/services/csvImportService';

  // Props
  const { projectIds = null } = $props<{
    projectIds?: string[] | null;
  }>();

  // 状态管理
  let qualityResults = $state<QualityControlResult[]>([]);
  let isLoading = $state(false);
  let error = $state<string | null>(null);
  let showOnlyFailed = $state(false);

  // 计算属性
  let filteredResults = $derived(() => {
    if (showOnlyFailed) {
      return qualityResults.filter(result => !result.quality_passed);
    }
    return qualityResults;
  });

  let statistics = $derived(() => {
    const total = qualityResults.length;
    const passed = qualityResults.filter(r => r.quality_passed).length;
    const failed = total - passed;
    const passRate = total > 0 ? Math.round((passed / total) * 100) : 0;

    return { total, passed, failed, passRate };
  });

  // 执行质量控制检查
  async function performQualityCheck() {
    isLoading = true;
    error = null;

    try {
      qualityResults = await csvImportService.performProjectQualityControl(projectIds);
      console.log('质量控制检查完成:', qualityResults);
    } catch (err: any) {
      error = err.message;
      console.error('质量控制检查失败:', err);
    } finally {
      isLoading = false;
    }
  }

  // 获取角色状态样式
  function getRoleStatusClass(roleName: string, missingRoles: string[]) {
    const isMissing = missingRoles.includes(roleName);
    return isMissing 
      ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300'
      : 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300';
  }

  // 组件挂载时自动执行检查
  onMount(() => {
    performQualityCheck();
  });
</script>

<div class="space-y-6">
  <!-- 标题和操作栏 -->
  <div class="flex items-center justify-between">
    <div>
      <h2 class="text-xl font-semibold text-slate-900 dark:text-slate-100">
        项目质量控制检查
      </h2>
      <p class="text-sm text-slate-600 dark:text-slate-400 mt-1">
        检查项目是否配置了关键角色（PI、CRC、CRA）
      </p>
    </div>
    <div class="flex items-center space-x-3">
      <label class="flex items-center space-x-2">
        <input
          type="checkbox"
          bind:checked={showOnlyFailed}
          class="rounded border-slate-300 text-blue-600 focus:ring-blue-500"
        />
        <span class="text-sm text-slate-700 dark:text-slate-300">只显示未通过项目</span>
      </label>
      <Button
        onclick={performQualityCheck}
        disabled={isLoading}
        variant="outline"
        size="sm"
      >
        {isLoading ? '检查中...' : '重新检查'}
      </Button>
    </div>
  </div>

  <!-- 错误提示 -->
  {#if error}
    <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
      <div class="flex items-center">
        <svg class="w-5 h-5 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
        </svg>
        <span class="text-red-700 dark:text-red-300">{error}</span>
      </div>
    </div>
  {/if}

  <!-- 统计概览 -->
  {#if qualityResults.length > 0}
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
      <div class="bg-slate-50 dark:bg-slate-700 rounded-lg p-4 text-center">
        <div class="text-2xl font-bold text-slate-900 dark:text-slate-100">
          {statistics.total}
        </div>
        <div class="text-sm text-slate-600 dark:text-slate-400">总项目数</div>
      </div>
      <div class="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 text-center">
        <div class="text-2xl font-bold text-green-600 dark:text-green-400">
          {statistics.passed}
        </div>
        <div class="text-sm text-green-600 dark:text-green-400">通过检查</div>
      </div>
      <div class="bg-red-50 dark:bg-red-900/20 rounded-lg p-4 text-center">
        <div class="text-2xl font-bold text-red-600 dark:text-red-400">
          {statistics.failed}
        </div>
        <div class="text-sm text-red-600 dark:text-red-400">未通过检查</div>
      </div>
      <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 text-center">
        <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
          {statistics.passRate}%
        </div>
        <div class="text-sm text-blue-600 dark:text-blue-400">通过率</div>
      </div>
    </div>
  {/if}

  <!-- 加载状态 -->
  {#if isLoading}
    <div class="text-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
      <p class="text-slate-600 dark:text-slate-400">正在执行质量控制检查...</p>
    </div>
  {/if}

  <!-- 检查结果列表 -->
  {#if !isLoading && filteredResults.length > 0}
    <div class="space-y-4">
      {#each filteredResults as result}
        <div class="bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-lg p-6">
          <div class="flex items-start justify-between mb-4">
            <div class="flex-1">
              <h3 class="font-medium text-slate-900 dark:text-slate-100 mb-1">
                {result.project_name}
              </h3>
              <p class="text-sm text-slate-600 dark:text-slate-400">
                项目ID: {result.project_id}
              </p>
            </div>
            <div class="flex items-center space-x-2">
              {#if result.quality_passed}
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300">
                  <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                  通过检查
                </span>
              {:else}
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300">
                  <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                  </svg>
                  未通过检查
                </span>
              {/if}
            </div>
          </div>

          <!-- 关键角色状态 -->
          <div class="mb-4">
            <h4 class="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
              关键角色配置状态
            </h4>
            <div class="flex flex-wrap gap-2">
              {#each ['PI', 'CRC', 'CRA'] as role}
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {getRoleStatusClass(role, result.missing_critical_roles)}">
                  {role}
                  {#if result.missing_critical_roles.includes(role)}
                    <svg class="w-3 h-3 ml-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                    </svg>
                  {:else}
                    <svg class="w-3 h-3 ml-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                  {/if}
                </span>
              {/each}
            </div>
          </div>

          <!-- 缺失角色提醒 -->
          {#if result.missing_critical_roles.length > 0}
            <div class="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg p-3 mb-4">
              <div class="flex items-start">
                <svg class="w-5 h-5 text-orange-500 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
                <div>
                  <h5 class="font-medium text-orange-900 dark:text-orange-100 mb-1">
                    缺失关键角色
                  </h5>
                  <p class="text-sm text-orange-700 dark:text-orange-300">
                    该项目缺少以下关键角色：{result.missing_critical_roles.join('、')}
                  </p>
                </div>
              </div>
            </div>
          {/if}

          <!-- 建议信息 -->
          {#if result.recommendations.length > 0}
            <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
              <h5 class="font-medium text-blue-900 dark:text-blue-100 mb-2">
                建议
              </h5>
              <ul class="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                {#each result.recommendations as recommendation}
                  <li class="flex items-start">
                    <svg class="w-4 h-4 text-blue-500 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    {recommendation}
                  </li>
                {/each}
              </ul>
            </div>
          {/if}
        </div>
      {/each}
    </div>
  {/if}

  <!-- 空状态 -->
  {#if !isLoading && filteredResults.length === 0 && qualityResults.length > 0}
    <div class="text-center py-8">
      <svg class="w-12 h-12 text-slate-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
      </svg>
      <h3 class="text-lg font-medium text-slate-900 dark:text-slate-100 mb-2">
        所有项目都已通过检查
      </h3>
      <p class="text-slate-600 dark:text-slate-400">
        所有项目都已配置了必要的关键角色
      </p>
    </div>
  {/if}

  {#if !isLoading && qualityResults.length === 0}
    <div class="text-center py-8">
      <svg class="w-12 h-12 text-slate-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"></path>
      </svg>
      <h3 class="text-lg font-medium text-slate-900 dark:text-slate-100 mb-2">
        暂无项目数据
      </h3>
      <p class="text-slate-600 dark:text-slate-400">
        没有找到可检查的项目
      </p>
    </div>
  {/if}
</div>
