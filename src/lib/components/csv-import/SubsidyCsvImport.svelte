<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import { Button } from '$lib/components/ui/button';
  import { fileSystemService } from '$lib/services/fileSystemService';
  import { sqliteDictionaryService } from '$lib/services/sqliteDictionaryService';
  import type { SubsidyWithDetails } from '$lib/services/projectManagementService';

  // Props
  let { project_id = '' } = $props();

  // Event dispatcher
  const dispatch = createEventDispatcher();

  // State
  let isDialogOpen = $state(false);
  let isProcessing = $state(false);
  let step = $state<'select' | 'preview' | 'importing' | 'complete'>('select');
  let inputMethod = $state<'file' | 'paste'>('file'); // 新增：输入方式选择
  
  // File and data
  let selectedFile = $state<File | null>(null);
  let csvTextInput = $state(''); // 新增：粘贴的CSV文本
  let csvData = $state<any[]>([]);
  let previewData = $state<SubsidyWithDetails[]>([]);
  let importResult = $state<{success: number, errors: string[]}>({success: 0, errors: []});
  
  // Dictionary data for mapping
  let subsidyTypes = $state<{item_id: number, item_value: string}[]>([]);
  let units = $state<{item_id: number, item_value: string}[]>([]);

  // Error state
  let error = $state<string | null>(null);

  // Open dialog and load dictionary data
  async function openDialog() {
    isDialogOpen = true;
    step = 'select';
    error = null;
    await loadDictionaryData();
  }

  // Load dictionary data for mapping
  async function loadDictionaryData() {
    try {
      // Load subsidy types
      const typesDict = await sqliteDictionaryService.getDictByName('补贴类型');
      if (typesDict?.items) {
        subsidyTypes = typesDict.items.map(item => ({
          item_id: item.item_id!,
          item_value: item.value
        }));
      }

      // Load units
      const unitsDict = await sqliteDictionaryService.getDictByName('补贴的单位');
      if (unitsDict?.items) {
        units = unitsDict.items.map(item => ({
          item_id: item.item_id!,
          item_value: item.value
        }));
      }
    } catch (err) {
      console.error('Failed to load dictionary data:', err);
      error = '加载字典数据失败';
    }
  }

  // Handle file selection
  function handleFileSelect(event: Event) {
    const target = event.target as HTMLInputElement;
    const file = target.files?.[0];
    
    if (file) {
      if (!file.name.toLowerCase().endsWith('.csv')) {
        error = '请选择CSV文件';
        return;
      }
      selectedFile = file;
      error = null;
    }
  }

  // Parse CSV data (from file or text input)
  async function parseCSV() {
    let csvText = '';
    
    // Get CSV content based on input method
    if (inputMethod === 'file') {
      if (!selectedFile) return;
      csvText = await selectedFile.text();
    } else {
      if (!csvTextInput.trim()) {
        error = '请输入CSV数据';
        return;
      }
      csvText = csvTextInput.trim();
    }

    isProcessing = true;
    error = null;

    try {
      await parseCsvText(csvText);
      step = 'preview';
    } catch (err) {
      error = err instanceof Error ? err.message : '解析CSV数据失败';
    } finally {
      isProcessing = false;
    }
  }

  // Parse CSV text content
  async function parseCsvText(csvText: string) {
    const lines = csvText.split('\n').filter(line => line.trim());
    
    if (lines.length < 2) {
      throw new Error('CSV数据至少需要包含标题行和一行数据');
    }

    // Parse header
    const header = lines[0].split(',').map(h => h.trim());
    const requiredHeaders = ['补贴类型', '单位金额', '总单位数', '单位'];
    
    const missingHeaders = requiredHeaders.filter(h => !header.includes(h));
    if (missingHeaders.length > 0) {
      throw new Error(`缺少必需的列：${missingHeaders.join(', ')}`);
    }

    // Parse data rows
    csvData = [];
    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',').map(v => v.trim());
      if (values.length < requiredHeaders.length) continue;

      const row: any = {};
      header.forEach((h, index) => {
        row[h] = values[index] || '';
      });
      csvData.push(row);
    }

    // Convert to preview data
    await convertToPreviewData();
  }

  // Convert CSV data to preview data with validation
  async function convertToPreviewData() {
    previewData = [];
    const errors: string[] = [];

    for (let i = 0; i < csvData.length; i++) {
      const row = csvData[i];
      try {
        // Find subsidy type
        const subsidyType = subsidyTypes.find(type => 
          type.item_value === row['补贴类型']
        );
        if (!subsidyType) {
          errors.push(`第${i + 2}行：未找到补贴类型"${row['补贴类型']}"`);
          continue;
        }

        // Find unit
        const unit = units.find(u => 
          u.item_value === row['单位']
        );
        if (!unit) {
          errors.push(`第${i + 2}行：未找到单位"${row['单位']}"`);
          continue;
        }

        // Validate numbers
        const unitAmount = parseFloat(row['单位金额']);
        const totalUnits = parseInt(row['总单位数']);
        
        if (isNaN(unitAmount) || unitAmount <= 0) {
          errors.push(`第${i + 2}行：单位金额必须是正数`);
          continue;
        }
        
        if (isNaN(totalUnits) || totalUnits <= 0) {
          errors.push(`第${i + 2}行：总单位数必须是正整数`);
          continue;
        }

        // Create preview item
        const subsidyItem: SubsidyWithDetails = {
          subsidy_item_id: -(i + 1), // Temporary negative ID
          project_id: project_id,
          subsidy_type_item_id: subsidyType.item_id,
          unit_amount: unitAmount,
          total_units: totalUnits,
          unit_item_id: unit.item_id,
          total_amount: unitAmount * totalUnits,
          subsidy_type: {
            item_id: subsidyType.item_id,
            dictionary_id: 0,
            item_key: '',
            item_value: subsidyType.item_value
          },
          unit: {
            item_id: unit.item_id,
            dictionary_id: 0,
            item_key: '',
            item_value: unit.item_value
          }
        };

        previewData.push(subsidyItem);

      } catch (err) {
        errors.push(`第${i + 2}行：处理数据时出错`);
      }
    }

    if (errors.length > 0) {
      error = errors.join('\n');
    }
  }

  // Confirm and import data
  async function confirmImport() {
    if (previewData.length === 0) return;

    step = 'importing';
    isProcessing = true;

    try {
      // Dispatch event to parent component to handle the actual import
      dispatch('import', { subsidies: previewData });
      
      importResult = {
        success: previewData.length,
        errors: []
      };
      
      step = 'complete';
    } catch (err) {
      error = err instanceof Error ? err.message : '导入失败';
      step = 'preview';
    } finally {
      isProcessing = false;
    }
  }

  // Close dialog and reset state
  function closeDialog() {
    isDialogOpen = false;
    step = 'select';
    inputMethod = 'file';
    selectedFile = null;
    csvTextInput = '';
    csvData = [];
    previewData = [];
    error = null;
    importResult = {success: 0, errors: []};
  }

  // Switch input method
  function switchInputMethod(method: 'file' | 'paste') {
    inputMethod = method;
    selectedFile = null;
    csvTextInput = '';
    error = null;
  }

  // Handle paste from clipboard
  async function handlePasteFromClipboard() {
    try {
      const clipboardText = await navigator.clipboard.readText();
      if (clipboardText.trim()) {
        csvTextInput = clipboardText.trim();
        error = null;
      }
    } catch (err) {
      error = '无法读取剪贴板内容，请手动粘贴';
    }
  }

  // Download CSV template
  async function downloadTemplate() {
    try {
      isProcessing = true;
      error = null;

      // 模板内容
      const template = `补贴类型,单位金额,总单位数,单位,备注
交通补贴,50.00,20,次,每次往返交通费
餐饮补贴,30.00,15,餐,工作餐补贴
住宿补贴,200.00,5,晚,出差住宿费
材料费,100.00,10,份,研究材料费用
检查费,150.00,8,次,定期检查费用
药品费,80.00,12,盒,试验用药费
通讯补贴,20.00,24,月,电话通讯费
停车费,15.00,30,次,医院停车费
复印费,5.00,50,页,文件复印费
体检费,300.00,3,次,参与者体检费`;

      // 添加BOM以确保Excel正确识别UTF-8编码
      const BOM = '\uFEFF';
      const csvContent = BOM + template;

      // 使用Tauri的文件保存对话框
      const { save } = await import('@tauri-apps/plugin-dialog');
      
      const filePath = await save({
        title: '保存补贴导入模板文件',
        defaultPath: '补贴导入模板.csv',
        filters: [{
          name: 'CSV文件',
          extensions: ['csv']
        }]
      });

      if (filePath) {
        // 使用Tauri的文件系统API保存文件
        const { writeTextFile } = await import('@tauri-apps/plugin-fs');
        await writeTextFile(filePath, csvContent);
        
        console.log('模板文件保存成功:', filePath);
        // 可以添加成功提示
      }
    } catch (err) {
      console.error('下载模板失败:', err);
      error = err instanceof Error ? err.message : '下载模板失败';
    } finally {
      isProcessing = false;
    }
  }
</script>

<!-- Import Button -->
<Button on:click={openDialog} variant="outline" class="gap-2">
  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-upload">
    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
    <polyline points="7,10 12,5 17,10"/>
    <line x1="12" y1="5" x2="12" y2="15"/>
  </svg>
  CSV批量导入
</Button>

<!-- Import Dialog -->
{#if isDialogOpen}
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-lg w-full max-w-4xl max-h-[90vh] overflow-hidden">
      
      <!-- Header -->
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-lg font-semibold">CSV批量导入补贴信息</h3>
            <p class="text-sm text-gray-500 mt-1">
              {#if step === 'select'}
                选择CSV文件并导入补贴数据
              {:else if step === 'preview'}
                预览导入数据
              {:else if step === 'importing'}
                正在导入数据...
              {:else if step === 'complete'}
                导入完成
              {/if}
            </p>
          </div>
          <Button on:click={closeDialog} variant="ghost" class="h-8 w-8 p-0">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M18 6L6 18"/>
              <path d="M6 6l12 12"/>
            </svg>
          </Button>
        </div>
      </div>

      <!-- Content -->
      <div class="px-6 py-4 overflow-y-auto" style="max-height: calc(90vh - 140px);">
        
        <!-- Step 1: File Selection -->
        {#if step === 'select'}
          <div class="space-y-6">
            
            <!-- Template Download -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div class="flex items-start gap-3">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-blue-600 mt-0.5">
                  <circle cx="12" cy="12" r="10"/>
                  <path d="M12 6v6l4 2"/>
                </svg>
                <div class="flex-1">
                  <h4 class="font-medium text-blue-800">下载模板文件</h4>
                  <p class="text-sm text-blue-700 mt-1">
                    建议先下载CSV模板文件，按照模板格式填写数据后再导入
                  </p>
                  <Button on:click={downloadTemplate} variant="outline" size="sm" class="mt-2" disabled={isProcessing}>
                    {isProcessing ? '保存中...' : '下载CSV模板'}
                  </Button>
                </div>
              </div>
            </div>

            <!-- Input Method Selection -->
            <div class="flex items-center justify-center">
              <div class="bg-gray-100 p-1 rounded-lg flex">
                <button
                  class="px-4 py-2 rounded-md text-sm font-medium transition-colors {inputMethod === 'file' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-500 hover:text-gray-700'}"
                  onclick={() => switchInputMethod('file')}
                >
                  上传文件
                </button>
                <button
                  class="px-4 py-2 rounded-md text-sm font-medium transition-colors {inputMethod === 'paste' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-500 hover:text-gray-700'}"
                  onclick={() => switchInputMethod('paste')}
                >
                  粘贴数据
                </button>
              </div>
            </div>

            <!-- File Upload or Text Input -->
            {#if inputMethod === 'file'}
              <div class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="mx-auto text-gray-400 mb-4">
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                  <polyline points="7,10 12,5 17,10"/>
                  <line x1="12" y1="5" x2="12" y2="15"/>
                </svg>
                
                <div class="space-y-2">
                  <p class="text-lg font-medium text-gray-900">选择CSV文件</p>
                  <p class="text-sm text-gray-500">支持CSV格式文件，最大10MB</p>
                </div>
                
                <input
                  type="file"
                  accept=".csv"
                  onchange={handleFileSelect}
                  class="hidden"
                  id="csv-file-input"
                />
                
                <label for="csv-file-input" class="inline-flex items-center justify-center gap-2 mt-4 px-4 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 cursor-pointer">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                    <polyline points="17,8 12,3 7,8"/>
                    <line x1="12" y1="3" x2="12" y2="15"/>
                  </svg>
                  选择文件
                </label>
                
                {#if selectedFile}
                  <div class="mt-4 p-3 bg-green-50 border border-green-200 rounded-md">
                    <p class="text-sm text-green-800">
                      已选择: {selectedFile.name} ({(selectedFile.size / 1024).toFixed(1)} KB)
                    </p>
                  </div>
                {/if}
              </div>
            {:else}
              <!-- Text Input Area -->
              <div class="space-y-4">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-lg font-medium text-gray-900">粘贴CSV数据</p>
                    <p class="text-sm text-gray-500">直接粘贴CSV格式的文本数据</p>
                  </div>
                  <Button on:click={handlePasteFromClipboard} variant="outline" size="sm" class="gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <rect width="8" height="4" x="8" y="2" rx="1" ry="1"/>
                      <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"/>
                    </svg>
                    从剪贴板粘贴
                  </Button>
                </div>
                
                <div class="relative">
                  <textarea
                    bind:value={csvTextInput}
                    placeholder="请粘贴CSV数据，格式如下：
补贴类型,单位金额,总单位数,单位,备注
交通补贴,50.00,20,次,每次往返交通费
餐饮补贴,30.00,15,餐,工作餐补贴"
                    class="w-full h-48 p-4 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-sm"
                  ></textarea>
                  
                  {#if csvTextInput.trim()}
                    <div class="absolute top-2 right-2">
                      <button
                        onclick={() => csvTextInput = ''}
                        class="p-1 text-gray-400 hover:text-gray-600 rounded"
                        title="清空内容"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                          <path d="M18 6L6 18"/>
                          <path d="M6 6l12 12"/>
                        </svg>
                      </button>
                    </div>
                  {/if}
                </div>
                
                {#if csvTextInput.trim()}
                  <div class="p-3 bg-green-50 border border-green-200 rounded-md">
                    <p class="text-sm text-green-800">
                      已输入 {csvTextInput.split('\n').filter(line => line.trim()).length} 行数据
                    </p>
                  </div>
                {/if}
              </div>
            {/if}

            <!-- CSV Format Guide -->
            <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <h4 class="font-medium text-gray-800 mb-3">CSV格式说明</h4>
              <div class="space-y-2 text-sm text-gray-600">
                <p><strong>必需列:</strong></p>
                <ul class="list-disc pl-5 space-y-1">
                  <li><code>补贴类型</code> - 必须匹配系统中的补贴类型字典（如：交通补贴、餐饮补贴等）</li>
                  <li><code>单位金额</code> - 数值，表示每单位的金额（如：50.00）</li>
                  <li><code>总单位数</code> - 整数，表示总数量（如：20）</li>
                  <li><code>单位</code> - 必须匹配系统中的单位字典（如：次、餐、晚等）</li>
                </ul>
                <p class="mt-3"><strong>可选列:</strong></p>
                <ul class="list-disc pl-5">
                  <li><code>备注</code> - 补贴项目的说明信息（如：每次往返交通费）</li>
                </ul>
                <p class="mt-3 text-blue-600"><strong>提示:</strong> 建议先下载模板文件，按照示例格式填写数据</p>
              </div>
            </div>
          </div>

        <!-- Step 2: Preview -->
        {:else if step === 'preview'}
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <div>
                <h4 class="font-medium text-gray-800">数据预览</h4>
                <p class="text-sm text-gray-500">
                  共解析到 {previewData.length} 条有效数据，请确认后导入
                </p>
              </div>
            </div>

            {#if error}
              <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                <h5 class="font-medium text-red-800 mb-2">数据验证错误</h5>
                <pre class="text-sm text-red-700 whitespace-pre-wrap">{error}</pre>
              </div>
            {/if}

            {#if previewData.length > 0}
              <div class="border rounded-lg overflow-hidden">
                <table class="w-full">
                  <thead class="bg-gray-50">
                    <tr>
                      <th class="text-left py-3 px-4 font-medium text-gray-700">补贴类型</th>
                      <th class="text-left py-3 px-4 font-medium text-gray-700">单位金额</th>
                      <th class="text-left py-3 px-4 font-medium text-gray-700">总单位数</th>
                      <th class="text-left py-3 px-4 font-medium text-gray-700">单位</th>
                      <th class="text-left py-3 px-4 font-medium text-gray-700">总金额</th>
                    </tr>
                  </thead>
                  <tbody>
                    {#each previewData as item}
                      <tr class="border-t">
                        <td class="py-3 px-4">{item.subsidy_type?.item_value}</td>
                        <td class="py-3 px-4">{item.unit_amount} 元</td>
                        <td class="py-3 px-4">{item.total_units}</td>
                        <td class="py-3 px-4">{item.unit?.item_value}</td>
                        <td class="py-3 px-4 font-medium text-green-600">{item.total_amount} 元</td>
                      </tr>
                    {/each}
                  </tbody>
                </table>
              </div>
            {/if}
          </div>

        <!-- Step 3: Importing -->
        {:else if step === 'importing'}
          <div class="text-center py-8">
            <div class="animate-spin h-12 w-12 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
            <h4 class="text-lg font-medium text-gray-800">正在导入数据...</h4>
            <p class="text-sm text-gray-500 mt-2">请稍候，正在处理 {previewData.length} 条记录</p>
          </div>

        <!-- Step 4: Complete -->
        {:else if step === 'complete'}
          <div class="text-center py-8">
            <div class="mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-green-600">
                <polyline points="20,6 9,17 4,12"/>
              </svg>
            </div>
            <h4 class="text-lg font-medium text-gray-800">导入完成</h4>
            <p class="text-sm text-gray-500 mt-2">
              成功导入 {importResult.success} 条补贴记录
            </p>
            
            {#if importResult.errors.length > 0}
              <div class="mt-4 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <h5 class="font-medium text-yellow-800 mb-2">部分数据导入失败</h5>
                <ul class="text-sm text-yellow-700 space-y-1">
                  {#each importResult.errors as error}
                    <li>{error}</li>
                  {/each}
                </ul>
              </div>
            {/if}
          </div>
        {/if}
      </div>

      <!-- Footer -->
      <div class="px-6 py-4 border-t border-gray-200 flex justify-end gap-2">
        {#if step === 'select'}
          <Button on:click={closeDialog} variant="outline">取消</Button>
          <Button 
            on:click={parseCSV} 
            disabled={(inputMethod === 'file' && !selectedFile) || (inputMethod === 'paste' && !csvTextInput.trim()) || isProcessing}
          >
            {#if isProcessing}
              <div class="animate-spin h-4 w-4 mr-2 border-2 border-white rounded-full border-t-transparent"></div>
            {/if}
            {inputMethod === 'file' ? '解析文件' : '解析数据'}
          </Button>
        {:else if step === 'preview'}
          <Button on:click={() => step = 'select'} variant="outline">返回</Button>
          <Button on:click={confirmImport} disabled={previewData.length === 0 || isProcessing}>
            确认导入 ({previewData.length} 条)
          </Button>
        {:else if step === 'importing'}
          <!-- No buttons during import -->
        {:else if step === 'complete'}
          <Button on:click={closeDialog}>完成</Button>
        {/if}
      </div>
    </div>
  </div>
{/if}