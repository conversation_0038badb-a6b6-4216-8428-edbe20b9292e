<script lang="ts">
  import { cn } from "$lib/utils";
  import FlippableCard from "./FlippableCard.svelte";
  import { activeCardId, setActiveCard, selectedDiseaseStore, setSelectedDisease, resetAllCardsAndFilters } from "$lib/stores/cardStore";
  import { onDestroy } from 'svelte';



  // Props
  export let title = "";
  export let count = 0;
  export let icon: any;
  export let color = "blue";
  export let isActive = false;
  export let diseaseDistribution: Array<{disease: string, count: number}> = [];
  export let selectedDisease: string | null = null;
  export let onClick = () => {};
  export let onDiseaseClick = (_disease: string) => {};
  export let id: string; // 添加一个唯一ID属性，用于标识卡片

  // State - 每个卡片的翻转状态
  let isFlipped = false;

  // 本地疾病选择状态，将与全局 store 同步
  let localSelectedDisease = selectedDisease;

  // 订阅 activeCardId store
  const unsubscribeActiveCard = activeCardId.subscribe(activeId => {
    console.log(`[${id}] activeCardId changed to:`, activeId);

    if (activeId === id) {
      // 如果当前卡片是活动卡片，则翻转到背面
      console.log(`[${id}] This card is now active, flipping to back`);
      isFlipped = true;
    } else if (isFlipped) {
      // 如果当前卡片不是活动卡片，且当前是翻转状态，则恢复为正面
      console.log(`[${id}] This card is no longer active, flipping to front`);
      isFlipped = false;

      // 当卡片被自动翻转回正面时，也清除疾病筛选
      if (localSelectedDisease) {
        // 不需要调用resetAllCardsAndFilters()，因为这会导致循环更新
        // 只需要通知父组件疾病筛选已清除
        console.log(`[${id}] Clearing disease filter:`, localSelectedDisease);
        onDiseaseClick('');
      }
    }
  });

  // 订阅 selectedDiseaseStore
  const unsubscribeSelectedDisease = selectedDiseaseStore.subscribe(disease => {
    // 更新本地选中的疾病
    localSelectedDisease = disease;
    // 如果父组件提供了 selectedDisease 属性，我们需要通知父组件
    if (selectedDisease !== disease) {
      // 使用 setTimeout 避免循环更新
      setTimeout(() => {
        if (disease !== null) {
          onDiseaseClick(disease);
        } else {
          // 如果 disease 为 null，则清除选择
          onDiseaseClick('');
        }
      }, 0);
    }
  });

  // 组件销毁时取消订阅
  onDestroy(() => {
    unsubscribeActiveCard();
    unsubscribeSelectedDisease();
  });

  // 处理卡片标题区域点击（翻转按钮）
  function handleHeaderClick(event?: MouseEvent) {
    if (event) {
      event.stopPropagation();
    }

    console.log(`[${id}] Flip button clicked, current isFlipped:`, isFlipped);

    // 翻转卡片
    isFlipped = !isFlipped;

    // 如果卡片被翻转到背面，则设置为活动卡片
    if (isFlipped) {
      console.log(`[${id}] Setting this card as active`);
      setActiveCard(id);
    } else {
      // 如果卡片被翻转回正面，则清除活动卡片和疾病筛选
      console.log(`[${id}] Resetting all cards and filters`);
      resetAllCardsAndFilters();

      // 通知父组件疾病筛选已清除
      if (localSelectedDisease) {
        console.log(`[${id}] Clearing disease filter:`, localSelectedDisease);
        onDiseaseClick('');
      }
    }
  }

  // 处理卡片正面点击（不包括翻转按钮）
  function handleCardFrontClick(event: MouseEvent | KeyboardEvent) {
    // 检查点击的元素是否是翻转按钮或其子元素
    const target = event.target as HTMLElement;
    const isFlipButton = target.closest('.flip-button') !== null;

    // 如果点击的是翻转按钮，不执行筛选操作
    if (isFlipButton) {
      return;
    }

    // 触发筛选
    onClick();

    // 确保所有卡片都翻转到正面并清除疾病筛选
    resetAllCardsAndFilters();
    isFlipped = false;

    // 通知父组件疾病筛选已清除
    if (localSelectedDisease) {
      onDiseaseClick('');
    }
  }

  // Handle disease click
  function handleDiseaseClick(disease: string, event: MouseEvent) {
    event.preventDefault();
    event.stopPropagation(); // 阻止事件冒泡，防止触发卡片翻转

    // 尝试使用所有可能的方法阻止事件冒泡
    if (event) {
      if (typeof event.stopImmediatePropagation === 'function') {
        event.stopImmediatePropagation();
      }

      // 确保事件不会继续传播
      setTimeout(() => {
        // 如果点击的是当前已选中的疾病，则清除选择
        if (disease === selectedDisease) {
          setSelectedDisease(null);
          onDiseaseClick('');
        } else {
          // 否则，选中新的疾病
          setSelectedDisease(disease);
          onDiseaseClick(disease);
        }
      }, 0);

      return false;
    } else {
      // 如果点击的是当前已选中的疾病，则清除选择
      if (disease === selectedDisease) {
        setSelectedDisease(null);
        onDiseaseClick('');
      } else {
        // 否则，选中新的疾病
        setSelectedDisease(disease);
        onDiseaseClick(disease);
      }
    }
    // 不再自动翻转卡片，只触发筛选
  }

  // Get color classes based on the provided color
  function getColorClasses(colorName: string) {
    const colorMap: Record<string, {
      bg: string,
      border: string,
      text: string,
      lightBg: string,
      lightText: string,
      gradient: string,
      hoverBg: string,
      activeBg: string,
      shadow: string
    }> = {
      blue: {
        bg: "bg-blue-500",
        border: "border-blue-500",
        text: "text-blue-600",
        lightBg: "bg-blue-50",
        lightText: "text-blue-700",
        gradient: "bg-gradient-to-br from-blue-400 to-blue-600",
        hoverBg: "hover:bg-blue-600",
        activeBg: "bg-blue-600",
        shadow: "shadow-blue-200"
      },
      green: {
        bg: "bg-green-500",
        border: "border-green-500",
        text: "text-green-600",
        lightBg: "bg-green-50",
        lightText: "text-green-700",
        gradient: "bg-gradient-to-br from-green-400 to-green-600",
        hoverBg: "hover:bg-green-600",
        activeBg: "bg-green-600",
        shadow: "shadow-green-200"
      },
      purple: {
        bg: "bg-purple-500",
        border: "border-purple-500",
        text: "text-purple-600",
        lightBg: "bg-purple-50",
        lightText: "text-purple-700",
        gradient: "bg-gradient-to-br from-purple-400 to-purple-600",
        hoverBg: "hover:bg-purple-600",
        activeBg: "bg-purple-600",
        shadow: "shadow-purple-200"
      },
      orange: {
        bg: "bg-orange-500",
        border: "border-orange-500",
        text: "text-orange-600",
        lightBg: "bg-orange-50",
        lightText: "text-orange-700",
        gradient: "bg-gradient-to-br from-orange-400 to-orange-600",
        hoverBg: "hover:bg-orange-600",
        activeBg: "bg-orange-600",
        shadow: "shadow-orange-200"
      }
    };

    return colorMap[colorName] || colorMap.blue;
  }

  const colorClasses = getColorClasses(color);
</script>

<div class="relative h-full min-h-[200px]">
  <FlippableCard
    isFlipped={isFlipped}
    onFlip={handleHeaderClick}
    containerClass="h-full"
    frontClass={cn(
      "bg-white p-5 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 text-left overflow-hidden",
      isActive ? `ring-2 ${colorClasses.border} ${colorClasses.shadow}` : ""
    )}
    backClass={cn(
      "bg-white p-5 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 text-left overflow-auto",
      isActive ? `ring-2 ${colorClasses.border} ${colorClasses.shadow}` : ""
    )}
  >
    <svelte:fragment slot="front">
      <!-- 卡片内容区域，点击时触发筛选 -->
      <div
        class="absolute inset-0 cursor-pointer"
        onclick={handleCardFrontClick}
        role="button"
        tabindex="0"
        onkeydown={(e) => e.key === 'Enter' && handleCardFrontClick(e)}
        aria-label={`筛选${title}项目`}
      >
        <!-- 顶部装饰条 -->
        <div class={cn("absolute top-0 left-0 right-0 h-1.5", colorClasses.gradient)}></div>

        <!-- 可筛选指示器 -->
        <div class="absolute top-3 right-3 flex items-center">
          {#if diseaseDistribution.length > 0}
            <div class="flex items-center px-2 py-0.5 rounded-full bg-white shadow-sm border border-gray-100">
              <div class={cn("w-2 h-2 rounded-full mr-1.5", colorClasses.bg)}></div>
              <span class={cn("text-xs font-medium", colorClasses.text)}>可筛选</span>
            </div>
          {/if}
        </div>

        <!-- 图标区域 -->
        <div class={cn(`absolute top-5 left-5 ${colorClasses.gradient} p-3.5 rounded-xl shadow-md transition-all duration-300 hover:shadow-lg group-hover:scale-105 transform`)}>
          <svelte:component this={icon} class="h-7 w-7 text-white transition-transform duration-300 group-hover:rotate-12" />
        </div>

        <!-- 内容区域 -->
        <div class="absolute top-5 left-20 right-4">
          <p class="text-sm text-gray-500 font-medium tracking-wide uppercase">{title}</p>
          <div class="flex items-baseline mt-1 group">
            <p class={cn("text-4xl font-bold tracking-tight transition-all duration-300 group-hover:scale-110 transform origin-left", colorClasses.text)}>
              {count}
            </p>
            <div class="flex flex-col ml-2">
              <p class="text-sm text-gray-500 font-medium">个项目</p>
              <div class={cn("h-0.5 w-0 group-hover:w-full transition-all duration-300", colorClasses.bg)}></div>
            </div>
          </div>
        </div>

        <!-- 活动指示器 -->
        {#if isActive}
          <div class="absolute bottom-0 left-0 right-0 flex justify-center">
            <div class={cn("w-24 h-1.5 rounded-t-full", colorClasses.gradient, "animate-pulse shadow-sm")}></div>
          </div>
          <div class="absolute top-0 right-0 bottom-0 left-0 pointer-events-none">
            <div class={cn("absolute inset-0 rounded-xl opacity-10", colorClasses.bg)}></div>
          </div>
        {/if}
      </div>

      <!-- 翻转按钮，单独放置在最上层 -->
      <button
        type="button"
        class={cn("absolute bottom-3 right-3 flex items-center px-2.5 py-1.5 rounded-full border-0 transition-all duration-300 flip-button z-20 bg-white shadow-sm",
          `hover:${colorClasses.lightBg} hover:shadow hover:scale-105 transform`,
          `border border-gray-100 group`
        )}
        onclick={(e) => {
          e.stopPropagation();
          handleHeaderClick(e);
        }}
        aria-label="翻转卡片显示疾病分布"
        title="翻转卡片显示疾病分布"
      >
        <svg xmlns="http://www.w3.org/2000/svg" class={cn("h-3.5 w-3.5 mr-1.5 transition-transform duration-300 group-hover:rotate-180", colorClasses.text)} fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
        </svg>

      </button>
    </svelte:fragment>

    <svelte:fragment slot="back">
      <!-- 顶部装饰条 -->
      <div class={cn("absolute top-0 left-0 right-0 h-1.5", colorClasses.gradient)}></div>

      <!-- 返回按钮 -->
      <button
        type="button"
        class={cn("absolute top-3 right-3 px-2.5 py-1.5 rounded-full border-0 transition-all duration-300 flip-button z-20 bg-white shadow-sm",
          `hover:${colorClasses.lightBg} hover:shadow hover:scale-105 transform`,
          `border border-gray-100 group`
        )}
        onclick={(e) => {
          e.stopPropagation();
          handleHeaderClick(e);
        }}
        aria-label="返回卡片正面"
        title="返回卡片正面"
      >
        <div class="flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class={cn("h-3.5 w-3.5 mr-1.5", colorClasses.text)} fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
          <span class={cn("text-xs font-medium", colorClasses.text)}>返回</span>
        </div>
      </button>

      <!-- 标题区域 -->
      <div class="flex items-center justify-between mb-4 mt-2">
        <div class="flex items-center">
          <div class={cn(`${colorClasses.gradient} p-2 rounded-lg shadow-sm mr-2`)}>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01" />
            </svg>
          </div>
          <span class={cn("text-base font-semibold", colorClasses.text)}>疾病分布</span>
        </div>

        {#if localSelectedDisease}
          <button
            class={cn("text-xs flex items-center gap-1.5 px-2 py-1 rounded-full",
              colorClasses.lightBg, colorClasses.text, "border border-gray-100 shadow-sm hover:shadow transition-all"
            )}
            onclick={(e) => {
              e.preventDefault();
              e.stopPropagation();

              // 尝试使用所有可能的方法阻止事件冒泡
              if (typeof e.stopImmediatePropagation === 'function') {
                e.stopImmediatePropagation();
              }

              // 确保事件不会继续传播
              setTimeout(() => {
                // 清除全局选中的疾病
                setSelectedDisease(null);
                onDiseaseClick('');
              }, 0);

              return false;
            }}
            aria-label="清除疾病筛选"
            title="清除疾病筛选"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
            <span class="font-medium">清除筛选</span>
          </button>
        {/if}
      </div>

      <!-- 疾病列表 -->
      <div
        class="flex flex-col gap-2.5 max-h-[250px] overflow-y-auto pr-1 disease-list mt-2"
        role="list"
      >
        {#if diseaseDistribution.length === 0}
          <div class="flex flex-col items-center justify-center text-gray-400 py-6">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 mb-2 opacity-50" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            <p class="text-sm">暂无疾病数据</p>
          </div>
        {:else}
          {#each diseaseDistribution as disease}
            <button
              type="button"
              class={cn(
                "px-4 py-2 text-sm rounded-lg transition-all duration-200 cursor-pointer flex items-center justify-between shadow-sm",
                localSelectedDisease === disease.disease
                  ? `${colorClasses.gradient} text-white font-medium shadow-md`
                  : `bg-white hover:${colorClasses.lightBg} ${colorClasses.text} border border-gray-100 hover:shadow`
              )}
              onclick={(e) => {
                // 阻止默认行为和事件冒泡
                e.preventDefault();
                e.stopPropagation();

                // 尝试使用所有可能的方法阻止事件冒泡
                if (typeof e.stopImmediatePropagation === 'function') {
                  e.stopImmediatePropagation();
                }

                // 添加一个自定义属性，标记这是疾病按钮点击
                (e.target as HTMLElement).setAttribute('data-disease-click', 'true');

                // 使用setTimeout确保事件不会继续传播
                setTimeout(() => {
                  handleDiseaseClick(disease.disease, e);
                }, 0);

                return false;
              }}
            >
              <span class="truncate max-w-[70%] font-medium">{disease.disease}</span>
              <span class={cn(
                "font-medium rounded-full px-2.5 py-0.5 text-xs ml-1 flex-shrink-0",
                localSelectedDisease === disease.disease
                  ? `bg-white ${colorClasses.text}`
                  : `${colorClasses.gradient} text-white`
              )}>
                {disease.count}
              </span>
            </button>
          {/each}
        {/if}
      </div>
    </svelte:fragment>
  </FlippableCard>
</div>
