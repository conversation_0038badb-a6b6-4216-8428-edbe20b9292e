<script lang="ts">
  import { staffService, type Staff, type StaffQuery } from '$lib/services/staffService';
  import { Button } from '$lib/components/ui/button';
  import { onMount } from 'svelte';
  import { fade } from 'svelte/transition';

  // 状态管理
  let staffList = $state<Staff[]>([]);
  let isLoading = $state(false);
  let error = $state<string | null>(null);
  let query = $state<StaffQuery>({});
  let showSearchForm = $state(false);
  let positions = $state<{id: number, key: string, value: string}[]>([]);

  // 分页
  let currentPage = $state(1);
  let pageSize = $state(10);
  let totalItems = $state(0);

  // 排序
  let sortField = $state<string>('name');
  let sortDirection = $state<'asc' | 'desc'>('asc');

  // 计算分页数据
  let paginatedStaff = $derived(staffList.slice((currentPage - 1) * pageSize, currentPage * pageSize));
  let totalPages = $derived(Math.ceil(staffList.length / pageSize));

  // 加载人员列表
  async function loadStaff() {
    isLoading = true;
    error = null;

    try {
      staffList = await staffService.getAllStaff();
      totalItems = staffList.length;
      sortStaff();

      // 加载职位信息
      await loadPositions();

      // 将职位ID映射到职位名称
      staffList = staffList.map(staff => {
        const position = positions.find(p => p.id === staff.position_item_id);
        return {
          ...staff,
          position_name: position ? position.value : '未知职位'
        };
      });
    } catch (err: any) {
      error = err.message || '加载人员失败';
      console.error('加载人员失败:', err);
    } finally {
      isLoading = false;
    }
  }

  // 加载用户角色信息
  async function loadPositions() {
    try {
      positions = await staffService.getPositions();
    } catch (err: any) {
      console.error('加载用户角色失败:', err);
    }
  }

  // 排序人员列表
  function sortStaff() {
    staffList = [...staffList].sort((a, b) => {
      const aValue = a[sortField as keyof Staff];
      const bValue = b[sortField as keyof Staff];

      if (aValue === undefined || bValue === undefined) return 0;

      const comparison = String(aValue).localeCompare(String(bValue));
      return sortDirection === 'asc' ? comparison : -comparison;
    });
  }

  // 切换排序
  function toggleSort(field: string) {
    if (sortField === field) {
      sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      sortField = field;
      sortDirection = 'asc';
    }
    sortStaff();
  }

  // 分页导航
  function goToPage(page: number) {
    if (page < 1 || page > totalPages) return;
    currentPage = page;
  }



  // 确认对话框状态
  let showConfirmDialog = $state(false);
  let confirmDialogTitle = $state('');
  let confirmDialogMessage = $state('');
  let confirmDialogCallback = $state<(() => void) | null>(null);

  // 显示确认对话框
  function showConfirm(title: string, message: string, callback: () => void) {
    confirmDialogTitle = title;
    confirmDialogMessage = message;
    confirmDialogCallback = callback;
    showConfirmDialog = true;
  }

  // 删除人员
  async function deleteStaff(id: number) {
    console.log(`开始删除人员，ID = ${id}`);

    // 使用自定义确认对话框
    showConfirm(
      '删除人员',
      '确定要删除此人员信息吗？此操作不可撤销。',
      async () => {
        console.log('用户确认删除人员');

        try {
          const success = await staffService.deleteStaff(id);

          if (success) {
            console.log('删除成功，重新加载人员列表');
            // 重新加载人员列表
            await loadStaff();
          } else {
            console.error('删除失败');
            error = '删除人员失败';
          }
        } catch (err: any) {
          console.error('删除失败:', err);
          error = err.message || '删除人员失败';
        }
      }
    );
  }

  // 搜索人员
  async function searchStaff() {
    isLoading = true;
    error = null;
    currentPage = 1; // 重置到第一页

    try {
      staffList = await staffService.queryStaff(query);
      totalItems = staffList.length;
      sortStaff();

      // 将职位ID映射到职位名称
      staffList = staffList.map(staff => {
        const position = positions.find(p => p.id === staff.position_item_id);
        return {
          ...staff,
          position_name: position ? position.value : '未知职位'
        };
      });
    } catch (err: any) {
      error = err.message || '搜索人员失败';
      console.error('搜索人员失败:', err);
    } finally {
      isLoading = false;
    }
  }

  // 清空搜索条件
  function clearSearch() {
    query = {};
    loadStaff();
    showSearchForm = false;
  }

  // 组件挂载时加载数据
  onMount(() => {
    loadStaff();
  });
</script>

<div class="w-full">
  <!-- 工具栏 -->
  <div class="mb-4 flex flex-wrap justify-between items-center gap-2 bg-white dark:bg-slate-800 p-4 rounded-lg border border-slate-200 dark:border-slate-700">
    <div class="flex items-center gap-2">
      <Button
        variant="ghost"
        size="sm"
        class="flex items-center gap-1"
        onclick={() => showSearchForm = !showSearchForm}
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search"><circle cx="11" cy="11" r="8"/><path d="m21 21-4.3-4.3"/></svg>
        {showSearchForm ? '隐藏搜索' : '搜索'}
      </Button>
      <Button variant="outline" size="sm" class="flex items-center gap-1" href="/staff/new">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus"><path d="M5 12h14"/><path d="M12 5v14"/></svg>
        新增人员
      </Button>
      <Button variant="outline" size="sm" class="flex items-center gap-1" onclick={loadStaff}>
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-refresh-cw"><path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/><path d="M21 3v5h-5"/><path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/><path d="M3 21v-5h5"/></svg>
        刷新
      </Button>
    </div>

    <div class="text-sm text-slate-500 dark:text-slate-400">
      共 {totalItems} 条记录
    </div>
  </div>

  <!-- 搜索表单 -->
  {#if showSearchForm}
    <div class="mb-4 bg-white dark:bg-slate-800 p-4 rounded-lg border border-slate-200 dark:border-slate-700" transition:fade={{ duration: 200 }}>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div>
          <label for="name" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">姓名</label>
          <input
            type="text"
            id="name"
            bind:value={query.name}
            class="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md text-sm text-slate-900 dark:text-slate-100 bg-white dark:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600"
            placeholder="输入姓名"
          />
        </div>

        <div>
          <label for="gender" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">性别</label>
          <select
            id="gender"
            bind:value={query.gender}
            class="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md text-sm text-slate-900 dark:text-slate-100 bg-white dark:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600"
          >
            <option value="">全部</option>
            <option value="男">男</option>
            <option value="女">女</option>
            <option value="其他">其他</option>
          </select>
        </div>

        <div>
          <label for="position" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">用户角色</label>
          <select
            id="position"
            bind:value={query.position_item_id}
            class="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md text-sm text-slate-900 dark:text-slate-100 bg-white dark:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600"
          >
            <option value="">全部</option>
            {#each positions as position}
              <option value={position.id}>{position.value}</option>
            {/each}
          </select>
        </div>

        <div>
          <label for="organization" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">组织</label>
          <input
            type="text"
            id="organization"
            bind:value={query.organization}
            class="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md text-sm text-slate-900 dark:text-slate-100 bg-white dark:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600"
            placeholder="输入组织"
          />
        </div>

        <div class="flex items-center mt-6">
          <input
            type="checkbox"
            id="isPI"
            bind:checked={query.isPI}
            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-300 rounded"
          />
          <label for="isPI" class="ml-2 block text-sm text-slate-700 dark:text-slate-300">
            是否为PI
          </label>
        </div>
      </div>

      <div class="flex justify-end gap-2 mt-4">
        <Button variant="outline" size="sm" onclick={clearSearch}>
          清空
        </Button>
        <Button variant="default" size="sm" onclick={searchStaff}>
          搜索
        </Button>
      </div>
    </div>
  {/if}

  <!-- 错误提示 -->
  {#if error}
    <div class="mb-4 bg-red-50 dark:bg-red-900/20 border border-red-100 dark:border-red-800 rounded-md p-4 text-red-800 dark:text-red-200">
      <p>{error}</p>
    </div>
  {/if}

  <!-- 数据表格 -->
  <div class="bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700 overflow-hidden">
    {#if isLoading}
      <div class="flex justify-center items-center p-8">
        <div class="inline-block w-6 h-6 border-2 border-t-transparent border-blue-600 dark:border-blue-400 rounded-full animate-spin mr-2"></div>
        <p>加载中...</p>
      </div>
    {:else if staffList.length === 0}
      <div class="p-8 text-center text-slate-500 dark:text-slate-400">
        <p>暂无人员数据</p>
      </div>
    {:else}
      <!-- 表格视图 -->
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead>
            <tr class="bg-slate-50 dark:bg-slate-700">
              <th class="px-4 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider cursor-pointer" onclick={() => toggleSort('id')}>
                <div class="flex items-center">
                  ID
                  {#if sortField === 'id'}
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-1">
                      {#if sortDirection === 'asc'}
                        <path d="m18 15-6-6-6 6"/>
                      {:else}
                        <path d="m6 9 6 6 6-6"/>
                      {/if}
                    </svg>
                  {/if}
                </div>
              </th>
              <th class="px-4 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider cursor-pointer" onclick={() => toggleSort('name')}>
                <div class="flex items-center">
                  姓名
                  {#if sortField === 'name'}
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-1">
                      {#if sortDirection === 'asc'}
                        <path d="m18 15-6-6-6 6"/>
                      {:else}
                        <path d="m6 9 6 6 6-6"/>
                      {/if}
                    </svg>
                  {/if}
                </div>
              </th>
              <th class="px-4 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">
                性别
              </th>
              <th class="px-4 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">
                用户角色
              </th>
              <th class="px-4 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">
                组织
              </th>
              <th class="px-4 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">
                联系方式
              </th>
              <th class="px-4 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody class="divide-y divide-slate-200 dark:divide-slate-700">
            {#each paginatedStaff as staff}
              <tr class="hover:bg-slate-50 dark:hover:bg-slate-700/50">
                <td class="px-4 py-3 text-sm text-slate-800 dark:text-slate-200">{staff.id}</td>
                <td class="px-4 py-3 text-sm text-slate-800 dark:text-slate-200">
                  <div class="flex items-center">
                    <span class="font-medium">{staff.name}</span>
                    {#if staff.isPI}
                      <span class="ml-2 px-2 py-0.5 text-xs rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900/40 dark:text-blue-300">
                        PI
                      </span>
                    {/if}
                  </div>
                </td>
                <td class="px-4 py-3 text-sm text-slate-800 dark:text-slate-200">{staff.gender}</td>
                <td class="px-4 py-3 text-sm text-slate-800 dark:text-slate-200">{staff.position_name}</td>
                <td class="px-4 py-3 text-sm text-slate-800 dark:text-slate-200">{staff.organization}</td>
                <td class="px-4 py-3 text-sm text-slate-800 dark:text-slate-200">
                  <div>
                    <div class="flex items-center gap-1">
                      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-phone"><path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/></svg>
                      <span>{staff.phone}</span>
                    </div>
                    <div class="flex items-center gap-1 mt-1">
                      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-mail"><rect width="20" height="16" x="2" y="4" rx="2"/><path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"/></svg>
                      <span>{staff.email}</span>
                    </div>
                  </div>
                </td>
                <td class="px-4 py-3 text-sm">
                  <div class="flex gap-2">
                    <a
                      href={`/staff/${staff.id}`}
                      class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                    >
                      查看
                    </a>
                    <a
                      href={`/staff/${staff.id}/edit`}
                      class="text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300"
                    >
                      编辑
                    </a>
                    <button
                      type="button"
                      class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                      onclick={() => {
                        console.log(`列表中删除人员按钮被点击，ID = ${staff.id || 0}`);
                        deleteStaff(staff.id || 0);
                      }}
                    >
                      删除
                    </button>
                  </div>
                </td>
              </tr>
            {/each}
          </tbody>
        </table>
      </div>

      <!-- 分页控件 -->
      {#if totalPages > 1}
        <div class="flex justify-between items-center p-4 border-t border-slate-200 dark:border-slate-700">
          <div class="text-sm text-slate-500 dark:text-slate-400">
            显示 {(currentPage - 1) * pageSize + 1} - {Math.min(currentPage * pageSize, totalItems)} 条，共 {totalItems} 条
          </div>

          <div class="flex gap-1">
            <button
              class="w-8 h-8 flex items-center justify-center rounded-md border border-slate-300 dark:border-slate-600 {currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-slate-100 dark:hover:bg-slate-700'}"
              disabled={currentPage === 1}
              onclick={() => goToPage(currentPage - 1)}
              aria-label="上一页"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left"><path d="m15 18-6-6 6-6"/></svg>
            </button>

            {#each Array(totalPages).fill(0).map((_, i) => i + 1) as page}
              {#if page === 1 || page === totalPages || (page >= currentPage - 1 && page <= currentPage + 1)}
                <button
                  class="w-8 h-8 flex items-center justify-center rounded-md border {page === currentPage ? 'bg-blue-50 border-blue-300 text-blue-600 dark:bg-blue-900/20 dark:border-blue-800 dark:text-blue-400' : 'border-slate-300 dark:border-slate-600 hover:bg-slate-100 dark:hover:bg-slate-700'}"
                  onclick={() => goToPage(page)}
                >
                  {page}
                </button>
              {:else if page === currentPage - 2 || page === currentPage + 2}
                <span class="w-8 h-8 flex items-center justify-center">...</span>
              {/if}
            {/each}

            <button
              class="w-8 h-8 flex items-center justify-center rounded-md border border-slate-300 dark:border-slate-600 {currentPage === totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-slate-100 dark:hover:bg-slate-700'}"
              disabled={currentPage === totalPages}
              onclick={() => goToPage(currentPage + 1)}
              aria-label="下一页"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"/></svg>
            </button>
          </div>
        </div>
      {/if}
    {/if}
  </div>

  <!-- 自定义确认对话框 -->
  {#if showConfirmDialog}
    <div class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div class="bg-white dark:bg-slate-800 rounded-lg shadow-lg max-w-md w-full p-6">
        <h3 class="text-lg font-semibold mb-2">{confirmDialogTitle}</h3>
        <p class="text-slate-600 dark:text-slate-300 mb-6">{confirmDialogMessage}</p>
        <div class="flex justify-end gap-3">
          <button
            type="button"
            class="px-4 py-2 text-sm font-medium rounded-md bg-slate-200 text-slate-800 hover:bg-slate-300 dark:bg-slate-700 dark:text-slate-200 dark:hover:bg-slate-600"
            onclick={() => showConfirmDialog = false}
          >
            取消
          </button>
          <button
            type="button"
            class="px-4 py-2 text-sm font-medium rounded-md bg-red-600 text-white hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-600"
            onclick={() => {
              showConfirmDialog = false;
              if (confirmDialogCallback) {
                confirmDialogCallback();
              }
            }}
          >
            确定
          </button>
        </div>
      </div>
    </div>
  {/if}
</div>
