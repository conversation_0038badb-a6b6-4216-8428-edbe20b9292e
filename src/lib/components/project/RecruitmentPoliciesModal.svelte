<script lang="ts">
  import { onMount } from 'svelte';
  import { Button } from '$lib/components/ui/button';
  import Input from '$lib/components/ui/input.svelte';
  import { X, Plus, Edit, Trash2, Building2, DollarSign, AlertCircle } from 'lucide-svelte';
  import * as Tooltip from "$lib/components/ui/tooltip";
  import {
    recruitmentPolicyService,
    type RecruitmentPolicyWithCompany,
    type DictionaryItem,
    type CreateRecruitmentPolicyRequest,
    type UpdateRecruitmentPolicyRequest
  } from '$lib/services/recruitmentPolicyService';
  
  export let projectId: string;
  export let show: boolean = false;
  export let onClose: () => void;

  let policies: RecruitmentPolicyWithCompany[] = [];
  let companies: DictionaryItem[] = [];
  let isLoading = false;
  let showAddForm = false;
  let editingPolicy: RecruitmentPolicyWithCompany | null = null;
  let error: string | null = null;
  let successMessage: string | null = null;

  // 表单数据
  let formData: Partial<CreateRecruitmentPolicyRequest> = {
    recruitment_company_item_id: 0,
    informed_consent_fee: 0,
    randomization_fee: 0,
    fee_currency: 'CNY',
    payment_method: '',
    description: '',
    notes: ''
  };
  
  // 加载数据
  async function loadData() {
    isLoading = true;
    error = null;

    try {
      // 初始化表
      await recruitmentPolicyService.initTables();

      // 并行加载招募公司和政策数据
      const [companiesData, policiesData] = await Promise.all([
        recruitmentPolicyService.getRecruitmentCompanies(),
        recruitmentPolicyService.getProjectPolicies(projectId)
      ]);

      companies = companiesData;
      policies = policiesData;

      console.log('加载数据成功:', { companies: companies.length, policies: policies.length });
    } catch (err) {
      error = err instanceof Error ? err.message : String(err);
      console.error('加载数据失败:', err);
    } finally {
      isLoading = false;
    }
  }
  
  // 计算费用差额
  function calculateFeeDifference(policy: RecruitmentPolicyWithCompany): number {
    return recruitmentPolicyService.calculateFeeDifference(policy.policy);
  }

  // 获取公司名称
  function getCompanyName(policyWithCompany: RecruitmentPolicyWithCompany): string {
    return policyWithCompany.company?.item_value || '未知公司';
  }
  
  // 打开添加表单
  function openAddForm() {
    editingPolicy = null;
    formData = {
      recruitment_company_item_id: 0,
      informed_consent_fee: 0,
      randomization_fee: 0,
      fee_currency: 'CNY',
      payment_method: '',
      description: '',
      notes: ''
    };
    showAddForm = true;
  }

  // 打开编辑表单
  function openEditForm(policyWithCompany: RecruitmentPolicyWithCompany) {
    editingPolicy = policyWithCompany;
    formData = {
      recruitment_company_item_id: policyWithCompany.policy.recruitment_company_item_id,
      informed_consent_fee: policyWithCompany.policy.informed_consent_fee,
      randomization_fee: policyWithCompany.policy.randomization_fee,
      fee_currency: policyWithCompany.policy.fee_currency || 'CNY',
      payment_method: policyWithCompany.policy.payment_method || '',
      description: policyWithCompany.policy.description || '',
      notes: policyWithCompany.policy.notes || ''
    };
    showAddForm = true;
  }
  
  // 保存政策
  async function savePolicy() {
    if (!formData.recruitment_company_item_id) {
      error = '请选择招募公司';
      return;
    }

    // 清除之前的错误
    error = null;

    try {
      if (editingPolicy) {
        // 更新现有政策
        const updateRequest: UpdateRecruitmentPolicyRequest = {
          recruitment_company_item_id: formData.recruitment_company_item_id,
          informed_consent_fee: formData.informed_consent_fee,
          randomization_fee: formData.randomization_fee,
          fee_currency: formData.fee_currency,
          payment_method: formData.payment_method,
          description: formData.description,
          notes: formData.notes
        };

        await recruitmentPolicyService.updatePolicy(editingPolicy.policy.policy_id!, updateRequest);
        successMessage = '政策更新成功';
      } else {
        // 创建新政策
        const createRequest: CreateRecruitmentPolicyRequest = {
          project_id: projectId,
          recruitment_company_item_id: formData.recruitment_company_item_id!,
          informed_consent_fee: formData.informed_consent_fee!,
          randomization_fee: formData.randomization_fee!,
          fee_currency: formData.fee_currency,
          payment_method: formData.payment_method,
          description: formData.description,
          notes: formData.notes
        };

        await recruitmentPolicyService.createPolicy(createRequest);
        successMessage = '政策创建成功';
      }

      // 重新加载数据
      await loadData();
      showAddForm = false;
      setTimeout(() => successMessage = null, 3000);
    } catch (err) {
      error = err instanceof Error ? err.message : String(err);
    }
  }

  // 删除政策
  async function deletePolicy(policyId: number) {
    if (!confirm('确定要删除这个招募政策吗？')) return;

    try {
      await recruitmentPolicyService.deletePolicy(policyId);
      successMessage = '政策删除成功';

      // 重新加载数据
      await loadData();
      setTimeout(() => successMessage = null, 3000);
    } catch (err) {
      error = err instanceof Error ? err.message : String(err);
    }
  }
  
  // 关闭弹窗
  function handleClose() {
    showAddForm = false;
    editingPolicy = null;
    error = null;
    successMessage = null;
    onClose();
  }
  
  // 组件挂载时加载数据
  onMount(() => {
    if (show) {
      loadData();
    }
  });
  
  // 监听show变化
  $: if (show) {
    loadData();
  }
</script>

<!-- 弹窗内容 -->
{#if show}
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-xl shadow-2xl max-w-7xl w-full max-h-[90vh] overflow-hidden flex flex-col mx-4">
      <!-- 头部 -->
      <div class="flex justify-between items-center p-6 border-b border-gray-200 bg-gray-50">
        <div class="flex items-center">
          <span class="bg-blue-50 text-blue-600 p-2 rounded-lg mr-3">
            <Building2 class="h-6 w-6" />
          </span>
          <div>
            <h2 class="text-2xl font-bold text-gray-800">招募公司费用管理</h2>
            <p class="text-sm text-gray-600 mt-1">管理不同招募公司的知情费用和随机费用政策</p>
          </div>
        </div>
        <Button variant="ghost" onclick={handleClose} class="hover:bg-gray-200">
          <X class="h-5 w-5" />
        </Button>
      </div>
      
      <!-- 内容区域 -->
      <div class="flex-1 overflow-y-auto p-6">
        <!-- 错误和成功提示 -->
        {#if error}
          <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-4 flex items-center">
            <AlertCircle class="h-5 w-5 mr-2" />
            <p>{error}</p>
          </div>
        {/if}
        
        {#if successMessage}
          <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-4">
            <p>{successMessage}</p>
          </div>
        {/if}
        
        <!-- 加载状态 -->
        {#if isLoading}
          <div class="flex justify-center items-center py-12">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        {:else}
          <!-- 费用对比表格 -->
          <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
            <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
              <div class="flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                  <DollarSign class="h-5 w-5 mr-2 text-green-600" />
                  费用政策对比
                </h3>
                <Button onclick={openAddForm} class="bg-blue-600 hover:bg-blue-700">
                  <Plus class="h-4 w-4 mr-2" />
                  添加政策
                </Button>
              </div>
            </div>
            
            {#if policies.length > 0}
              <div class="overflow-x-auto">
                <table class="w-full">
                  <thead class="bg-gray-50">
                    <tr>
                      <th class="px-6 py-4 text-left text-sm font-semibold text-gray-700 border-b border-gray-200">招募公司</th>
                      <th class="px-6 py-4 text-center text-sm font-semibold text-gray-700 border-b border-gray-200">知情费用</th>
                      <th class="px-6 py-4 text-center text-sm font-semibold text-gray-700 border-b border-gray-200">随机费用</th>
                      <th class="px-6 py-4 text-center text-sm font-semibold text-gray-700 border-b border-gray-200">费用差额</th>
                      <th class="px-6 py-4 text-center text-sm font-semibold text-gray-700 border-b border-gray-200">支付方式</th>
                      <th class="px-6 py-4 text-center text-sm font-semibold text-gray-700 border-b border-gray-200">操作</th>
                    </tr>
                  </thead>
                  <tbody class="divide-y divide-gray-200">
                    {#each policies as policy}
                      <tr class="hover:bg-gray-50 transition-colors">
                        <td class="px-6 py-4">
                          <div class="flex items-center">
                            <span class="bg-blue-50 text-blue-700 p-1 rounded mr-2">
                              <Building2 class="h-4 w-4" />
                            </span>
                            <div>
                              <div class="font-medium text-gray-900">{getCompanyName(policy)}</div>
                              {#if policy.policy.description}
                                <div class="text-sm text-gray-500">{policy.policy.description}</div>
                              {/if}
                            </div>
                          </div>
                        </td>
                        <td class="px-6 py-4 text-center">
                          <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                            ¥{policy.policy.informed_consent_fee.toLocaleString()}
                          </span>
                        </td>
                        <td class="px-6 py-4 text-center">
                          <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                            ¥{policy.policy.randomization_fee.toLocaleString()}
                          </span>
                        </td>
                        <td class="px-6 py-4 text-center">
                          <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                            ¥{calculateFeeDifference(policy).toLocaleString()}
                          </span>
                        </td>
                        <td class="px-6 py-4 text-center text-sm text-gray-600">
                          {policy.policy.payment_method || '-'}
                        </td>
                        <td class="px-6 py-4 text-center">
                          <div class="flex justify-center space-x-2">
                            <Tooltip.Root>
                              <Tooltip.Trigger>
                                <Button variant="ghost" size="sm" onclick={() => openEditForm(policy)} class="hover:bg-blue-50">
                                  <Edit class="h-4 w-4 text-blue-600" />
                                </Button>
                              </Tooltip.Trigger>
                              <Tooltip.Content>
                                <p>编辑政策</p>
                              </Tooltip.Content>
                            </Tooltip.Root>
                            
                            <Tooltip.Root>
                              <Tooltip.Trigger>
                                <Button variant="ghost" size="sm" onclick={() => deletePolicy(policy.policy.policy_id!)} class="hover:bg-red-50">
                                  <Trash2 class="h-4 w-4 text-red-600" />
                                </Button>
                              </Tooltip.Trigger>
                              <Tooltip.Content>
                                <p>删除政策</p>
                              </Tooltip.Content>
                            </Tooltip.Root>
                          </div>
                        </td>
                      </tr>
                    {/each}
                  </tbody>
                </table>
              </div>
            {:else}
              <div class="text-center py-12">
                <Building2 class="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 class="text-lg font-medium text-gray-900 mb-2">暂无招募政策</h3>
                <p class="text-gray-500 mb-4">开始添加招募公司的费用政策</p>
                <Button onclick={openAddForm} class="bg-blue-600 hover:bg-blue-700">
                  <Plus class="h-4 w-4 mr-2" />
                  添加第一个政策
                </Button>
              </div>
            {/if}
          </div>
        {/if}
      </div>
    </div>
  </div>
{/if}

<!-- 添加/编辑政策表单弹窗 -->
{#if showAddForm}
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60">
    <div class="bg-white rounded-xl shadow-2xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
      <div class="p-6 border-b border-gray-200">
        <h3 class="text-xl font-bold text-gray-800">
          {editingPolicy ? '编辑招募政策' : '添加招募政策'}
        </h3>
      </div>

      <div class="p-6 space-y-6">
        <!-- 招募公司选择 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">招募公司 *</label>
          <select
            bind:value={formData.recruitment_company_item_id}
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            required
          >
            <option value={0}>请选择招募公司</option>
            {#each companies as company}
              <option value={company.item_id}>{company.item_value}</option>
            {/each}
          </select>
        </div>

        <!-- 费用设置 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">知情费用 (¥) *</label>
            <Input
              type="number"
              bind:value={formData.informed_consent_fee}
              placeholder="0"
              min="0"
              step="0.01"
              required
            />
            <p class="text-xs text-gray-500 mt-1">患者签署知情同意书后的推荐费用</p>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">随机费用 (¥) *</label>
            <Input
              type="number"
              bind:value={formData.randomization_fee}
              placeholder="0"
              min="0"
              step="0.01"
              required
            />
            <p class="text-xs text-gray-500 mt-1">患者随机成功后的总费用（包含知情费用）</p>
          </div>
        </div>

        <!-- 费用差额显示 -->
        {#if formData.informed_consent_fee && formData.randomization_fee}
          <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div class="flex items-center justify-between">
              <span class="text-sm font-medium text-blue-800">随机成功额外费用：</span>
              <span class="text-lg font-bold text-blue-900">
                ¥{((formData.randomization_fee || 0) - (formData.informed_consent_fee || 0)).toLocaleString()}
              </span>
            </div>
          </div>
        {/if}

        <!-- 支付方式和货币 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">货币类型</label>
            <select
              bind:value={formData.fee_currency}
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="CNY">人民币 (CNY)</option>
              <option value="USD">美元 (USD)</option>
              <option value="EUR">欧元 (EUR)</option>
            </select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">支付方式</label>
            <Input
              type="text"
              bind:value={formData.payment_method}
              placeholder="如：银行转账、支票等"
            />
          </div>
        </div>

        <!-- 描述和备注 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">政策描述</label>
          <Input
            type="text"
            bind:value={formData.description}
            placeholder="简要描述这个费用政策"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">备注</label>
          <textarea
            bind:value={formData.notes}
            rows="3"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="其他备注信息，如结算周期、特殊条款等"
          ></textarea>
        </div>
      </div>

      <!-- 表单按钮 -->
      <div class="p-6 border-t border-gray-200 flex justify-end space-x-3">
        <Button variant="outline" onclick={() => showAddForm = false}>
          取消
        </Button>
        <Button onclick={savePolicy} class="bg-blue-600 hover:bg-blue-700">
          {editingPolicy ? '更新政策' : '创建政策'}
        </Button>
      </div>
    </div>
  </div>
{/if}
