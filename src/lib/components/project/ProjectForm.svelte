<script lang="ts">
  import { onMount } from 'svelte';
  import { Button } from '$lib/components/ui/button';
  import ProjectBasicInfo from './ProjectBasicInfo.svelte';
  import ProjectDrugs from './ProjectDrugs.svelte';
  import ProjectPersonnel from './ProjectPersonnel.svelte';
  import ProjectSubsidies from './ProjectSubsidies.svelte';
  import { Save } from 'lucide-svelte';

  // 组件属性
  let { 
    projectDetails = $bindable({} as any), // 标记为 bindable 并提供默认值
    onSave,
    onSaveDraft,
    isLoading = false,
    isBasicInfoValid
  } = $props();

  // 表单验证状态
  let formValidation = $state({
    basicInfo: false,
    drugs: true,     // 研究药物不是必填项
    personnel: true, // 研究人员不是必填项
    subsidies: true  // 补贴信息不是必填项
  });

  // 活动标签页
  let activeSection = $state('basic-info');

  // 更新表单验证状态
  function updateFormValidation() {
    // 基本信息（包含申办方）
    formValidation.basicInfo = isBasicInfoValid();

    // 研究药物
    formValidation.drugs = !!(projectDetails.research_drugs && projectDetails.research_drugs.length > 0);

    // 研究人员
    formValidation.personnel = !!(projectDetails.personnel && projectDetails.personnel.length > 0);

    // 补贴信息
    formValidation.subsidies = !!(projectDetails.subsidies && projectDetails.subsidies.length > 0);
  }

  // 获取当前完成进度（不修改状态）
  function getCompletionPercentage() {
    let completed = 0;
    let total = 0;

    // 基本信息（包含申办方）
    if (isBasicInfoValid()) completed++;
    total++;

    // 研究药物
    if (projectDetails.research_drugs && projectDetails.research_drugs.length > 0) {
      completed++;
    }
    total++;

    // 研究人员
    if (projectDetails.personnel && projectDetails.personnel.length > 0) {
      completed++;
    }
    total++;

    // 补贴信息
    if (projectDetails.subsidies && projectDetails.subsidies.length > 0) {
      completed++;
    }
    total++;

    return Math.round((completed / total) * 100);
  }

  // 设置活动部分
  function setActiveSection(section: string) {
    activeSection = section;
  }

  // 处理基本信息下一步
  function handleBasicInfoNext() {
    if (isBasicInfoValid()) {
      updateFormValidation(); // 更新状态以显示完成标记
      setActiveSection('drugs');
    }
  }

  // 组件挂载时初始化
  onMount(() => {
    // 初始化表单验证状态
    updateFormValidation();
    console.log('ProjectForm 组件挂载完成');
  });
</script>

<!-- 进度指示器 -->
<div class="mb-8 bg-white p-6 rounded-lg shadow">
  <div class="flex justify-between items-center mb-3">
    <span class="text-lg font-medium">项目完成进度</span>
    <span class="text-lg font-medium text-blue-600">{getCompletionPercentage()}%</span>
  </div>
  <div class="w-full bg-gray-200 rounded-full h-3">
    <div class="bg-blue-600 h-3 rounded-full transition-all duration-500" style="width: {getCompletionPercentage()}%"></div>
  </div>

  <div class="grid grid-cols-4 gap-4 mt-6">
    <div class="text-center">
      <div class="flex justify-center">
        <button
          class={`w-8 h-8 rounded-full flex items-center justify-center ${formValidation.basicInfo ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-400'}`}
          onclick={() => setActiveSection('basic-info')}
        >
          <span class="font-bold">{formValidation.basicInfo ? '✓' : '1'}</span>
        </button>
      </div>
      <p class="mt-2 text-sm">基本信息</p>
    </div>
    <div class="text-center">
      <div class="flex justify-center">
        <button
          class={`w-8 h-8 rounded-full flex items-center justify-center ${formValidation.drugs ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-400'}`}
          onclick={() => setActiveSection('drugs')}
        >
          <span class="font-bold">{formValidation.drugs ? '✓' : '2'}</span>
        </button>
      </div>
      <p class="mt-2 text-sm">研究药物</p>
    </div>
    <div class="text-center">
      <div class="flex justify-center">
        <button
          class={`w-8 h-8 rounded-full flex items-center justify-center ${formValidation.personnel ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-400'}`}
          onclick={() => setActiveSection('personnel')}
        >
          <span class="font-bold">{formValidation.personnel ? '✓' : '3'}</span>
        </button>
      </div>
      <p class="mt-2 text-sm">研究人员</p>
    </div>
    <div class="text-center">
      <div class="flex justify-center">
        <button
          class={`w-8 h-8 rounded-full flex items-center justify-center ${formValidation.subsidies ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-400'}`}
          onclick={() => setActiveSection('subsidies')}
        >
          <span class="font-bold">{formValidation.subsidies ? '✓' : '4'}</span>
        </button>
      </div>
      <p class="mt-2 text-sm">补贴信息</p>
    </div>
  </div>
</div>

<!-- 项目表单 -->
<div class="bg-white p-8 rounded-lg shadow">
  <!-- 当前部分指示器 -->
  <div class="mb-4 p-2 bg-gray-100 rounded text-sm font-medium">
    当前编辑: {activeSection === 'basic-info' ? '基本信息' :
              activeSection === 'drugs' ? '研究药物' :
              activeSection === 'personnel' ? '研究人员' : '补贴信息'}
  </div>

  <!-- 基本信息 -->
  {#if activeSection === 'basic-info'}
    <div class="mb-6">
      <h2 class="text-2xl font-bold mb-2">基本信息</h2>
      <p class="text-gray-600">填写项目的基本信息，带 <span class="text-red-500">*</span> 的字段为必填项</p>
    </div>

    <ProjectBasicInfo projectDetails={projectDetails} />

    <div class="mt-8 flex justify-between items-center border-t pt-6">
      <div>
        <p class="text-sm text-gray-500">填写完基本信息后，您可以继续填写研究药物信息</p>
      </div>
      <Button
        size="lg"
        onclick={handleBasicInfoNext}
      >
        下一步：研究药物
      </Button>
    </div>
  {/if}

  <!-- 申办方部分已移至基本信息页面 -->

  <!-- 研究药物 -->
  {#if activeSection === 'drugs'}
    <div class="mb-6">
      <h2 class="text-2xl font-bold mb-2">研究药物信息</h2>
      <p class="text-gray-600">添加项目的研究药物和药物分组信息（可选）</p>
    </div>

    <ProjectDrugs projectDetails={projectDetails} />

    <div class="mt-8 flex justify-between items-center border-t pt-6">
      <Button variant="outline" size="lg" onclick={() => setActiveSection('basic-info')}>
        上一步：基本信息
      </Button>
      <Button size="lg" onclick={() => setActiveSection('personnel')}>
        下一步：研究人员
      </Button>
    </div>
  {/if}

  <!-- 研究人员 -->
  {#if activeSection === 'personnel'}
    <div class="mb-6">
      <h2 class="text-2xl font-bold mb-2">研究人员信息</h2>
      <p class="text-gray-600">添加项目的研究人员信息（可选）</p>
    </div>

    <ProjectPersonnel {projectDetails} />

    <div class="mt-8 flex justify-between items-center border-t pt-6">
      <Button variant="outline" size="lg" onclick={() => setActiveSection('drugs')}>
        上一步：研究药物
      </Button>
      <Button size="lg" onclick={() => setActiveSection('subsidies')}>
        下一步：补贴信息
      </Button>
    </div>
  {/if}

  <!-- 补贴信息 -->
  {#if activeSection === 'subsidies'}
    <div class="mb-6">
      <h2 class="text-2xl font-bold mb-2">补贴信息</h2>
      <p class="text-gray-600">添加项目的补贴信息（可选）</p>
    </div>

    <ProjectSubsidies 
      bind:subsidies={projectDetails.subsidies} 
      bind:schemes={projectDetails.subsidy_schemes} 
      project_id={projectDetails.project?.project_id || ''} 
    />

    <div class="mt-8 flex justify-between items-center border-t pt-6">
      <Button variant="outline" size="lg" onclick={() => setActiveSection('personnel')}>
        上一步：研究人员
      </Button>
      <div class="flex gap-3">
        <Button
          variant="outline"
          size="lg"
          onclick={onSaveDraft}
          disabled={isLoading || !isBasicInfoValid()}
        >
          保存草稿
        </Button>
        <Button
          size="lg"
          onclick={onSave}
          disabled={isLoading || !isBasicInfoValid()}
        >
          <Save class="h-5 w-5 mr-2" />
          保存项目
        </Button>
      </div>
    </div>
  {/if}
</div>
