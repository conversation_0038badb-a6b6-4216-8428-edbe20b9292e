<script lang="ts">
  import { onMount } from 'svelte';
  import { Button } from '$lib/components/ui/button';
  import { sqliteDictionaryService } from '$lib/services/sqliteDictionaryService';
  import type { ProjectWithDetails, ProjectSponsorWithDetails } from '$lib/services/projectManagementService';

  // 组件属性
  const props = $props<{projectDetails: ProjectWithDetails}>();
  let projectDetailsData = $derived(props.projectDetails);

  console.log('ProjectSponsors组件接收到的项目详情:', projectDetailsData);

  // 状态管理
  let sponsors = $state<ProjectSponsorWithDetails[]>(projectDetailsData.sponsors || []);
  let dialogOpen = $state(false);
  let selectedSponsorId = $state<number | null>(null);
  let confirmDeleteIndex = $state<number | null>(null);
  let confirmDialogOpen = $state(false);

  // 字典项
  let sponsorItems = $state<{item_id: number | undefined, item_value: string}[]>([]);

  // 加载申办方字典项
  async function loadSponsorItems() {
    try {
      const sponsorsDict = await sqliteDictionaryService.getDictByName('申办方');
      if (sponsorsDict && sponsorsDict.items) {
        sponsorItems = sponsorsDict.items.map(item => ({
          item_id: item.item_id,
          item_value: item.value
        }));
      } else {
        console.error('未找到申办方字典');
      }
    } catch (err) {
      console.error('加载申办方字典项失败:', err);
    }
  }

  // 添加申办方
  function addSponsor() {
    if (!selectedSponsorId || !projectDetailsData.project.project_id) return;

    // 检查是否已存在
    const exists = sponsors.some((s: ProjectSponsorWithDetails) => s.sponsor_item_id === selectedSponsorId);
    if (exists) {
      alert('该申办方已添加');
      return;
    }

    // 获取申办方详情
    const sponsorItem = sponsorItems.find((item: {item_id: number | undefined, item_value: string}) => item.item_id === selectedSponsorId);

    // 创建新申办方
    const newSponsor: ProjectSponsorWithDetails = {
      project_id: projectDetailsData.project.project_id || '',
      sponsor_item_id: selectedSponsorId || 0,
      sponsor: sponsorItem ? {
        item_id: sponsorItem.item_id || 0,
        dictionary_id: 0, // 这里不重要，后端会忽略
        item_key: '',
        item_value: sponsorItem.item_value
      } : undefined
    };

    // 添加到列表
    projectDetailsData.sponsors = [...sponsors, newSponsor];

    // 重置选择并关闭对话框
    selectedSponsorId = null;
    dialogOpen = false;
  }

  // 打开删除确认对话框
  function openDeleteConfirm(index: number) {
    confirmDeleteIndex = index;
    confirmDialogOpen = true;
  }

  // 删除申办方
  function removeSponsor() {
    if (!projectDetailsData.sponsors || confirmDeleteIndex === null) return;

    // 更新本地状态
    sponsors = sponsors.filter((_: ProjectSponsorWithDetails, i: number) => i !== confirmDeleteIndex);

    // 更新 projectDetails
    projectDetailsData.sponsors = sponsors;

    // 关闭确认对话框
    confirmDialogOpen = false;
    confirmDeleteIndex = null;
  }

  // 组件挂载时加载申办方字典项
  onMount(async () => {
    await loadSponsorItems();
  });
</script>

<div>
  <div class="flex justify-between items-center mb-6">
    <div>
      <h2 class="text-xl font-semibold">申办方</h2>
      <p class="text-sm text-gray-500 mt-1">添加项目的申办方信息</p>
    </div>

    <div>
      <Button on:click={() => dialogOpen = true} variant="outline" class="gap-2">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus"><path d="M5 12h14"/><path d="M12 5v14"/></svg>
        添加申办方
      </Button>

      {#if dialogOpen}
        <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div class="bg-white p-6 rounded-lg shadow-lg w-full max-w-md">
            <h3 class="text-lg font-semibold mb-4">添加申办方</h3>
            <p class="text-gray-500 text-sm mb-4">请从下拉列表中选择一个申办方添加到项目中</p>

            <div class="py-4">
              <select
                bind:value={selectedSponsorId}
                class="h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
              >
                <option value={null}>请选择申办方</option>
                {#each sponsorItems as sponsor}
                  <option value={sponsor.item_id}>{sponsor.item_value}</option>
                {/each}
              </select>
            </div>

            <div class="flex justify-end gap-2">
              <Button on:click={() => dialogOpen = false} variant="outline">取消</Button>
              <Button on:click={addSponsor} disabled={!selectedSponsorId}>添加</Button>
            </div>
          </div>
        </div>
      {/if}
    </div>
  </div>

  {#if !sponsors || sponsors.length === 0}
    <div class="bg-gray-50 border border-gray-200 rounded-lg p-8 text-center">
      <div class="flex justify-center mb-4">
        <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="text-gray-400"><path d="M18 21a8 8 0 0 0-16 0"></path><circle cx="10" cy="8" r="5"></circle><path d="M22 20c0-3.37-2-6.5-4-8a5 5 0 0 0-.45-8.3"></path></svg>
      </div>
      <h3 class="text-lg font-medium text-gray-900 mb-2">暂无申办方数据</h3>
      <p class="text-gray-500 mb-4">点击"添加申办方"按钮来添加项目的申办方信息</p>
      <Button on:click={() => dialogOpen = true} variant="outline" class="gap-2">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus"><path d="M5 12h14"/><path d="M12 5v14"/></svg>
        添加申办方
      </Button>
    </div>
  {:else}
    <div class="bg-white border rounded-lg overflow-hidden">
      <table class="w-full border-collapse">
        <thead>
          <tr class="bg-gray-50">
            <th class="text-left py-3 px-4 font-medium text-gray-700">申办方名称</th>
            <th class="w-[100px] text-right py-3 px-4 font-medium text-gray-700">操作</th>
          </tr>
        </thead>
        <tbody>
          {#each sponsors as sponsor, index}
            <tr class="border-t hover:bg-gray-50">
              <td class="py-3 px-4">{sponsor.sponsor?.item_value || '未知申办方'}</td>
              <td class="text-right py-3 px-4">
                <Button variant="ghost" size="sm" on:click={() => openDeleteConfirm(index)} class="text-red-500 hover:text-red-700">
                  删除
                </Button>
              </td>
            </tr>
          {/each}
        </tbody>
      </table>
    </div>
  {/if}

  <!-- 帮助提示 -->
  <div class="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
    <h3 class="text-sm font-medium text-blue-800 mb-2">申办方信息说明</h3>
    <ul class="list-disc pl-5 text-sm text-blue-700 space-y-1">
      <li>申办方是指负责发起、管理或资助该项目的组织或机构</li>
      <li>一个项目可以有多个申办方</li>
      <li>申办方信息为可选项，您可以稍后再添加</li>
    </ul>
  </div>

  <!-- 删除确认对话框 -->
  {#if confirmDialogOpen}
    <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white p-6 rounded-lg shadow-lg w-full max-w-md">
        <h3 class="text-lg font-semibold mb-4">确认删除</h3>
        <p class="text-gray-700 mb-4">
          您确定要删除这个申办方吗？此操作无法撤销。
        </p>
        <div class="flex justify-end gap-2">
          <Button on:click={() => confirmDialogOpen = false} variant="outline">取消</Button>
          <Button on:click={removeSponsor} variant="destructive">确认删除</Button>
        </div>
      </div>
    </div>
  {/if}
</div>
