<script lang="ts">
  import { onMount } from 'svelte';
  import { Button } from '$lib/components/ui/button';
  import Input from '$lib/components/ui/input.svelte';
  import Label from '$lib/components/ui/label.svelte';
  import { sqliteDictionaryService } from '$lib/services/sqliteDictionaryService';
  import type { ProjectWithDetails, SubsidyScheme, Subsidy, SubsidyWithDetails } from '$lib/services/projectManagementService';
  import SubsidyCsvImport from '$lib/components/csv-import/SubsidyCsvImport.svelte';

  // --- 修改开始: 使用 let 解构 $props() 并正确使用 $bindable ---
  let {
    subsidies = $bindable([] as SubsidyWithDetails[]), // 使用 let 和 $bindable
    schemes = $bindable([] as SubsidyScheme[]),      // 使用 let 和 $bindable
    project_id = ''                                // 普通 prop
  } = $props();
  // --- 修改结束 ---

  // 状态管理 (现在直接使用传入的 props)
  let schemeDialogOpen = $state(false);
  let subsidyDialogOpen = $state(false);
  let editingSchemeIndex = $state<number | null>(null); // 当前正在编辑的方案索引

  // 删除确认对话框状态
  let confirmDeleteSchemeIndex = $state<number | null>(null);
  let confirmDeleteSubsidyIndex = $state<number | null>(null);
  let confirmSchemeDialogOpen = $state(false);
  let confirmSubsidyDialogOpen = $state(false);

  // 补贴方案表单数据
  let newSchemeName = $state('');
  let newSchemeAmount = $state(0);
  let selectedSubsidies = $state<number[]>([]); // 选中的补贴项ID列表

  // 计算选中补贴项的总金额
  function calculateSelectedSubsidiesAmount() {
    if (selectedSubsidies.length === 0) return 0;

    return subsidies
      .filter(subsidy => selectedSubsidies.includes(subsidy.subsidy_item_id || 0))
      .reduce((sum, subsidy) => sum + (subsidy.total_amount || 0), 0);
  }

  // 新补贴项
  let newSubsidyTypeId = $state<number | null>(null);
  let newSubsidyUnitAmount = $state(0);
  let newSubsidyTotalUnits = $state(0);
  let newSubsidyUnitId = $state<number | null>(null);
  let newSubsidyTotalAmount = $state(0);

  // 字典项
  let subsidyTypes = $state<{item_id: number | undefined, item_value: string}[]>([]);
  let units = $state<{item_id: number | undefined, item_value: string}[]>([]);

  // 临时ID生成器
  let tempSubsidyIdCounter = $state(-1);

  // 加载字典项
  async function loadDictionaryItems() {
    try {
      // 加载补贴类型字典
      const typesDict = await sqliteDictionaryService.getDictByName('补贴类型');
      if (typesDict && typesDict.items) {
        subsidyTypes = typesDict.items.map(item => ({
          item_id: item.item_id,
          item_value: item.value
        }));
      } else {
        console.error('未找到补贴类型字典');
      }

      // 加载单位字典
      const unitsDict = await sqliteDictionaryService.getDictByName('补贴的单位');
      if (unitsDict && unitsDict.items) {
        units = unitsDict.items.map(item => ({
          item_id: item.item_id,
          item_value: item.value
        }));
      } else {
        console.error('未找到补贴的单位字典');
      }
    } catch (err) {
      console.error('加载字典项失败:', err);
    }
  }

  // 打开添加补贴方案对话框
  function openAddSchemeDialog() {
    // 重置表单
    editingSchemeIndex = null;
    newSchemeName = '';
    newSchemeAmount = 0;
    selectedSubsidies = [];
    schemeDialogOpen = true;
  }

  // 打开编辑补贴方案对话框
  function openEditSchemeDialog(index: number) {
    const scheme = schemes[index];
    if (!scheme) return;

    // 设置编辑状态
    editingSchemeIndex = index;
    newSchemeName = scheme.scheme_name;
    newSchemeAmount = scheme.total_amount;
    selectedSubsidies = scheme.included_subsidies || [];
    schemeDialogOpen = true;
  }

  // 保存补贴方案（添加或更新）
  function saveSubsidyScheme() {
    if (!newSchemeName) return;

    // 计算选中补贴项的总金额
    const calculatedAmount = calculateSelectedSubsidiesAmount();

    // 使用用户输入的金额或计算的金额
    const finalAmount = newSchemeAmount > 0 ? newSchemeAmount : calculatedAmount;

    // 确保所有选中的补贴项ID都是有效的
    const validSubsidyIds = selectedSubsidies.filter(id => {
      // 检查ID是否存在于subsidies中
      return subsidies.some(subsidy => subsidy.subsidy_item_id === id);
    });

    console.log('保存补贴方案，包含补贴项IDs:', validSubsidyIds);

    // 创建补贴方案对象
    const scheme: SubsidyScheme = {
      project_id: project_id || '', // 使用 props 传入的 project_id
      scheme_name: newSchemeName,
      total_amount: finalAmount,
      included_subsidies: validSubsidyIds
    };

    // 如果是编辑现有方案
    if (editingSchemeIndex !== null && schemes[editingSchemeIndex]) {
      // 保留原方案的ID
      scheme.scheme_id = schemes[editingSchemeIndex].scheme_id;

      // 更新方案 (直接修改绑定的 prop)
      schemes = schemes.map((s, i) => i === editingSchemeIndex ? scheme : s);
    } else {
      // 添加新方案 (直接修改绑定的 prop)
      schemes = [...schemes, scheme];
    }

    // 更新所有方案的总金额
    updateSchemeAmounts();

    // 重置输入并关闭对话框
    newSchemeName = '';
    newSchemeAmount = 0;
    selectedSubsidies = [];
    editingSchemeIndex = null;
    schemeDialogOpen = false;
  }

  // 打开删除补贴方案确认对话框
  function openDeleteSchemeConfirm(index: number) {
    confirmDeleteSchemeIndex = index;
    confirmSchemeDialogOpen = true;
  }

  // 删除补贴方案
  function removeSubsidyScheme() {
    if (!schemes || confirmDeleteSchemeIndex === null) return;

    // 更新本地数组和项目详情 (直接修改绑定的 prop)
    schemes = schemes.filter((_: SubsidyScheme, i: number) => i !== confirmDeleteSchemeIndex);

    // 关闭确认对话框
    confirmSchemeDialogOpen = false;
    confirmDeleteSchemeIndex = null;
  }

  // 计算总金额
  function calculateTotalAmount() {
    // 确保数值有效，避免NaN结果
    const unitAmount = Number(newSubsidyUnitAmount) || 0;
    const totalUnits = Number(newSubsidyTotalUnits) || 0;
    newSubsidyTotalAmount = unitAmount * totalUnits;
  }

  // 计算现有补贴项的总金额
  function updateSubsidyTotalAmount(index: number) {
    if (subsidies[index]) {
      // 确保数值有效，避免NaN结果
      const unitAmount = Number(subsidies[index].unit_amount) || 0;
      const totalUnits = Number(subsidies[index].total_units) || 0;
      subsidies[index].total_amount = unitAmount * totalUnits;
      // 更新项目详情中的补贴数据 (直接修改绑定的 prop)
      subsidies = [...subsidies]; // 触发 Svelte 响应式更新数组本身

      // 更新包含此补贴项的方案总金额
      updateSchemeAmounts();
    }
  }

  // 更新所有补贴方案的总金额
  function updateSchemeAmounts() {
    if (!schemes || schemes.length === 0) return;

    const updatedSchemes = schemes.map(scheme => {
      let updatedScheme = {...scheme}; // 创建副本以避免直接修改
      if (updatedScheme.included_subsidies && updatedScheme.included_subsidies.length > 0) {
        // 计算包含的补贴项总金额
        const totalAmount = updatedScheme.included_subsidies.reduce((sum, subsidyId) => {
          const subsidy = subsidies.find(s => s.subsidy_item_id === subsidyId);
          return sum + (subsidy?.total_amount || 0);
        }, 0);

        // 更新方案总金额
        updatedScheme.total_amount = totalAmount;
      }
      return updatedScheme;
    });

    schemes = updatedSchemes; // 更新绑定的 prop
  }

  // 添加补贴项
  function addSubsidy() {
    if (!newSubsidyTypeId || !newSubsidyUnitId) return;

    // 确保总金额已计算
    calculateTotalAmount();

    // 查找补贴类型和单位的名称，用于显示
    const subsidyType = subsidyTypes.find(type => type.item_id === newSubsidyTypeId);
    const unit = units.find(unit => unit.item_id === newSubsidyUnitId);

    // 生成临时唯一ID
    const tempId = tempSubsidyIdCounter;
    tempSubsidyIdCounter--;

    // 创建新补贴项
    const subsidy: SubsidyWithDetails = {
      subsidy_item_id: tempId, // 使用临时ID
      project_id: project_id || '', // 使用 props 传入的 project_id
      subsidy_type_item_id: newSubsidyTypeId,
      unit_amount: newSubsidyUnitAmount,
      total_units: newSubsidyTotalUnits,
      unit_item_id: newSubsidyUnitId,
      total_amount: newSubsidyTotalAmount,
      // 添加字典项信息，方便前端显示
      subsidy_type: subsidyType ? {
        item_id: subsidyType.item_id || 0,
        dictionary_id: 0,
        item_key: '',
        item_value: subsidyType.item_value
      } : undefined,
      unit: unit ? {
        item_id: unit.item_id || 0,
        dictionary_id: 0,
        item_key: '',
        item_value: unit.item_value
      } : undefined
    };

    // 添加到列表并更新项目详情 (直接修改绑定的 prop)
    subsidies = [...subsidies, subsidy];

    // 更新所有方案的总金额
    updateSchemeAmounts();

    // 重置输入并关闭对话框
    newSubsidyTypeId = null;
    newSubsidyUnitAmount = 0;
    newSubsidyTotalUnits = 0;
    newSubsidyUnitId = null;
    newSubsidyTotalAmount = 0;
    subsidyDialogOpen = false;
  }

  // 打开删除补贴项确认对话框
  function openDeleteSubsidyConfirm(index: number) {
    confirmDeleteSubsidyIndex = index;
    confirmSubsidyDialogOpen = true;
  }

  // 删除补贴项
  function removeSubsidy() {
    if (!subsidies || confirmDeleteSubsidyIndex === null) return;

    // 获取要删除的补贴项ID
    const subsidyToDelete = subsidies[confirmDeleteSubsidyIndex];
    const subsidyId = subsidyToDelete?.subsidy_item_id;

    // 更新本地数组和项目详情 (直接修改绑定的 prop)
    subsidies = subsidies.filter((_: SubsidyWithDetails, i: number) => i !== confirmDeleteSubsidyIndex);

    // 如果补贴项已经有ID，从所有方案中移除该补贴项
    if (subsidyId) {
      const updatedSchemes = schemes.map(scheme => {
        if (scheme.included_subsidies && scheme.included_subsidies.includes(subsidyId)) {
          // 创建新的included_subsidies数组，排除被删除的补贴项ID
          const updatedIncludedSubsidies = scheme.included_subsidies.filter(id => id !== subsidyId);
          return {
            ...scheme,
            included_subsidies: updatedIncludedSubsidies
          };
        }
        return scheme;
      });
      schemes = updatedSchemes;
    }

    // 更新所有方案的总金额
    updateSchemeAmounts();

    // 关闭确认对话框
    confirmSubsidyDialogOpen = false;
    confirmDeleteSubsidyIndex = null;
  }

  // 处理CSV导入事件
  function handleCsvImport(event: CustomEvent) {
    const importedSubsidies = event.detail.subsidies as SubsidyWithDetails[];
    
    // 更新临时ID计数器，避免ID冲突
    let minTempId = Math.min(...subsidies.map(s => s.subsidy_item_id || 0), 0);
    
    // 为导入的补贴项分配临时ID
    const subsidiesWithTempIds = importedSubsidies.map((subsidy, index) => ({
      ...subsidy,
      subsidy_item_id: minTempId - index - 1
    }));

    // 合并到现有补贴列表
    subsidies = [...subsidies, ...subsidiesWithTempIds];

    // 更新所有补贴方案的总金额
    updateSchemeAmounts();

    console.log(`成功导入 ${subsidiesWithTempIds.length} 条补贴记录`);
  }

  // 组件挂载时加载字典项
  onMount(async () => {
    await loadDictionaryItems();
  });
</script>

<div class="space-y-10">
  <!-- 补贴方案 -->
  <div>
    <div class="flex justify-between items-center mb-6">
      <div>
        <h2 class="text-xl font-semibold">补贴方案</h2>
        <p class="text-sm text-gray-500 mt-1">添加项目的补贴方案信息</p>
      </div>

      <Button on:click={openAddSchemeDialog} variant="outline" class="gap-2">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus"><path d="M5 12h14"/><path d="M12 5v14"/></svg>
        添加补贴方案
      </Button>

      {#if schemeDialogOpen}
        <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div class="bg-white p-6 rounded-lg shadow-lg w-full max-w-md">
            <h3 class="text-lg font-semibold mb-4">{editingSchemeIndex !== null ? '编辑补贴方案' : '添加补贴方案'}</h3>
            <p class="text-gray-500 text-sm mb-4">请输入补贴方案的名称并选择包含的补贴项</p>

            <div class="py-4 space-y-4">
              <div>
                <Label forAttr="scheme_name">方案名称</Label>
                <Input
                  id="scheme_name"
                  bind:value={newSchemeName}
                  placeholder="请输入方案名称"
                />
                <p class="text-xs text-gray-500 mt-1">例如：标准补贴方案、高额补贴方案等</p>
              </div>

              {#if subsidies && subsidies.length > 0}
                <div>
                  <Label forAttr="included_subsidies">包含的补贴项</Label>
                  <div class="mt-2 border rounded-md p-2 max-h-40 overflow-y-auto">
                    {#each subsidies as subsidy}
                      {@const subsidyId = subsidy.subsidy_item_id || 0}
                      {#if subsidyId !== 0}
                        <div class="flex items-center py-1 border-b last:border-0">
                          <input
                            type="checkbox"
                            id="subsidy-{subsidyId}"
                            class="mr-2 h-4 w-4"
                            checked={selectedSubsidies.includes(subsidyId)}
                            onchange={(e) => {
                              const target = e.target as HTMLInputElement;
                              if (target.checked) {
                                // 添加到选中列表
                                if (!selectedSubsidies.includes(subsidyId)) {
                                  selectedSubsidies = [...selectedSubsidies, subsidyId];
                                }
                              } else {
                                // 从选中列表中移除
                                selectedSubsidies = selectedSubsidies.filter(id => id !== subsidyId);
                              }
                            }}
                          />
                          <label for="subsidy-{subsidyId}" class="flex-1 text-sm">
                            <span class="font-medium">{subsidy.subsidy_type?.item_value || '未知类型'}</span>
                            <span class="text-gray-500 ml-2">
                              ({subsidy.unit_amount} 元 × {subsidy.total_units} {subsidy.unit?.item_value || '单位'} = {subsidy.total_amount} 元)
                            </span>
                          </label>
                        </div>
                      {/if}
                    {/each}
                  </div>
                  <p class="text-xs text-gray-500 mt-1">选择要包含在此方案中的补贴项</p>
                </div>
              {/if}

              <div>
                <Label forAttr="total_amount">总金额</Label>
                <div class="relative">
                  <Input
                    id="total_amount"
                    type="number"
                    min="0"
                    bind:value={newSchemeAmount}
                    placeholder={selectedSubsidies.length > 0 ? `自动计算: ${calculateSelectedSubsidiesAmount()} 元` : "请输入总金额"}
                  />
                  <div class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">元</div>
                </div>
                <p class="text-xs text-gray-500 mt-1">
                  {#if selectedSubsidies.length > 0}
                    选中补贴项总金额: {calculateSelectedSubsidiesAmount()} 元 (可手动修改)
                  {:else}
                    该方案的总预算金额
                  {/if}
                </p>
              </div>
            </div>

            <div class="flex justify-end gap-2 pt-2 border-t">
              <Button on:click={() => schemeDialogOpen = false} variant="outline">取消</Button>
              <Button on:click={saveSubsidyScheme} disabled={!newSchemeName}>
                {editingSchemeIndex !== null ? '保存' : '添加'}
              </Button>
            </div>
          </div>
        </div>
      {/if}
    </div>

    {#if !schemes || schemes.length === 0}
      <div class="bg-gray-50 border border-gray-200 rounded-lg p-8 text-center">
        <div class="flex justify-center mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="text-gray-400"><path d="M12 2v6m0 12v2"></path><circle cx="12" cy="12" r="10"></circle><path d="M8 12h8"></path></svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">暂无补贴方案数据</h3>
        <p class="text-gray-500 mb-4">点击"添加补贴方案"按钮来添加项目的补贴方案</p>
        <Button on:click={openAddSchemeDialog} variant="outline" class="gap-2">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus"><path d="M5 12h14"/><path d="M12 5v14"/></svg>
          添加补贴方案
        </Button>
      </div>
    {:else}
      <div class="bg-white border rounded-lg overflow-hidden">
        <table class="w-full border-collapse">
          <thead>
            <tr class="bg-gray-50">
              <th class="text-left py-3 px-4 font-medium text-gray-700">方案名称</th>
              <th class="text-left py-3 px-4 font-medium text-gray-700">包含补贴项</th>
              <th class="text-left py-3 px-4 font-medium text-gray-700">总金额</th>
              <th class="w-[100px] text-right py-3 px-4 font-medium text-gray-700">操作</th>
            </tr>
          </thead>
          <tbody>
            {#each schemes as scheme, index}
              <tr class="border-t hover:bg-gray-50">
                <td class="py-3 px-4">{scheme.scheme_name}</td>
                <td class="py-3 px-4">
                  {#if scheme.included_subsidies && scheme.included_subsidies.length > 0}
                    <div class="flex flex-wrap gap-1">
                      {#each scheme.included_subsidies as subsidyId}
                        {@const subsidy = subsidies.find(s => s.subsidy_item_id === subsidyId)}
                        {#if subsidy}
                          <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                            {subsidy.subsidy_type?.item_value || '未知类型'}
                          </span>
                        {/if}
                      {/each}
                    </div>
                  {:else}
                    <span class="text-gray-500 text-sm">无</span>
                  {/if}
                </td>
                <td class="py-3 px-4">
                  <span class="font-medium text-green-600">{scheme.total_amount} 元</span>
                </td>
                <td class="text-right py-3 px-4">
                  <div class="flex justify-end gap-2">
                    <Button variant="ghost" size="sm" on:click={() => openEditSchemeDialog(index)} class="text-blue-500 hover:text-blue-700">
                      编辑
                    </Button>
                    <Button variant="ghost" size="sm" on:click={() => openDeleteSchemeConfirm(index)} class="text-red-500 hover:text-red-700">
                      删除
                    </Button>
                  </div>
                </td>
              </tr>
            {/each}
          </tbody>
        </table>
      </div>
    {/if}
  </div>

  <!-- 补贴项 -->
  <div>
    <div class="flex justify-between items-center mb-6">
      <div>
        <h2 class="text-xl font-semibold">补贴项</h2>
        <p class="text-sm text-gray-500 mt-1">添加项目的具体补贴项目</p>
      </div>

      <div class="flex gap-2">
        <SubsidyCsvImport project_id={project_id} on:import={handleCsvImport} />
        <Button on:click={() => subsidyDialogOpen = true} variant="outline" class="gap-2">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus"><path d="M5 12h14"/><path d="M12 5v14"/></svg>
          添加补贴项
        </Button>
      </div>

      {#if subsidyDialogOpen}
        <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div class="bg-white p-6 rounded-lg shadow-lg w-full max-w-md">
            <h3 class="text-lg font-semibold mb-4">添加补贴项</h3>
            <p class="text-gray-500 text-sm mb-4">请填写补贴项的详细信息</p>

            <div class="py-4 space-y-4">
              <div>
                <Label forAttr="subsidy_type">补贴类型</Label>
                <select
                  id="subsidy_type"
                  bind:value={newSubsidyTypeId}
                  class="h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                >
                  <option value={null}>请选择补贴类型</option>
                  {#each subsidyTypes as type}
                    <option value={type.item_id}>{type.item_value}</option>
                  {/each}
                </select>
                <p class="text-xs text-gray-500 mt-1">例如：交通补贴、餐饮补贴等</p>
              </div>

              <div class="grid grid-cols-2 gap-4">
                <div>
                  <Label forAttr="unit_amount">单位金额</Label>
                  <Input
                    id="unit_amount"
                    type="number"
                    min="0"
                    bind:value={newSubsidyUnitAmount}
                    placeholder="请输入单位金额"
                    on:input={() => {
                      calculateTotalAmount();
                    }}
                  />
                </div>
                <div>
                  <Label forAttr="total_units">总单位数</Label>
                  <Input
                    id="total_units"
                    type="number"
                    min="0"
                    bind:value={newSubsidyTotalUnits}
                    placeholder="请输入总单位数"
                    on:input={() => {
                      calculateTotalAmount();
                    }}
                  />
                </div>
              </div>

              <div>
                <Label forAttr="unit">单位</Label>
                <select
                  id="unit"
                  bind:value={newSubsidyUnitId}
                  class="h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                >
                  <option value={null}>请选择单位</option>
                  {#each units as unit}
                    <option value={unit.item_id}>{unit.item_value}</option>
                  {/each}
                </select>
                <p class="text-xs text-gray-500 mt-1">从"补贴的单位"字典中选择，例如：次、人、天等</p>
              </div>

              <div>
                <Label forAttr="total_amount">总金额</Label>
                <div class="relative">
                  <Input
                    id="total_amount"
                    type="number"
                    min="0"
                    bind:value={newSubsidyTotalAmount}
                    placeholder="自动计算总金额"
                    readonly
                    class="bg-gray-50"
                  />
                  <div class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">元</div>
                </div>
                <p class="text-xs text-gray-500 mt-1">单位金额 × 总单位数 = 总金额</p>
              </div>
            </div>

            <div class="flex justify-end gap-2 pt-2 border-t">
              <Button on:click={() => subsidyDialogOpen = false} variant="outline">取消</Button>
              <Button on:click={addSubsidy} disabled={!newSubsidyTypeId || !newSubsidyUnitId}>添加</Button>
            </div>
          </div>
        </div>
      {/if}
    </div>

    {#if !subsidies || subsidies.length === 0}
      <div class="bg-gray-50 border border-gray-200 rounded-lg p-8 text-center">
        <div class="flex justify-center mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="text-gray-400"><path d="M2 17a5 5 0 0 0 10 0c0-2.76-2.5-5-5-3-2.5-2-5 .24-5 3Z"></path><path d="M12 17a5 5 0 0 0 10 0c0-2.76-2.5-5-5-3-2.5-2-5 .24-5 3Z"></path><path d="M7 14c3.22-2.91 4.29-8.75 5-12 1.66 2.38 4.94 9 5 12"></path><path d="M22 9c-4.29 0-7.14-2.33-10-7 5.71 0 10 4.67 10 7Z"></path></svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">暂无补贴项数据</h3>
        <p class="text-gray-500 mb-4">可以通过CSV批量导入或手动添加补贴项目</p>
        <div class="flex gap-2 justify-center">
          <SubsidyCsvImport project_id={project_id} on:import={handleCsvImport} />
          <Button on:click={() => subsidyDialogOpen = true} variant="outline" class="gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus"><path d="M5 12h14"/><path d="M12 5v14"/></svg>
            添加补贴项
          </Button>
        </div>
      </div>
    {:else}
      <div class="bg-white border rounded-lg overflow-hidden">
        <table class="w-full border-collapse">
          <thead>
            <tr class="bg-gray-50">
              <th class="text-left py-3 px-4 font-medium text-gray-700">补贴类型</th>
              <th class="text-left py-3 px-4 font-medium text-gray-700">单位金额</th>
              <th class="text-left py-3 px-4 font-medium text-gray-700">总单位数</th>
              <th class="text-left py-3 px-4 font-medium text-gray-700">单位</th>
              <th class="text-left py-3 px-4 font-medium text-gray-700">总金额</th>
              <th class="w-[100px] text-right py-3 px-4 font-medium text-gray-700">操作</th>
            </tr>
          </thead>
          <tbody>
            {#each subsidies as subsidy, index}
              <tr class="border-t hover:bg-gray-50">
                <td class="py-3 px-4">
                  <span class="font-medium">{subsidy.subsidy_type?.item_value || '未知类型'}</span>
                </td>
                <td class="py-3 px-4">
                  <Input
                    type="number"
                    min="0"
                    bind:value={subsidy.unit_amount}
                    on:input={() => updateSubsidyTotalAmount(index)}
                    class="w-24 h-8 text-sm"
                  /> 元
                </td>
                <td class="py-3 px-4">
                  <Input
                    type="number"
                    min="0"
                    bind:value={subsidy.total_units}
                    on:input={() => updateSubsidyTotalAmount(index)}
                    class="w-24 h-8 text-sm"
                  />
                </td>
                <td class="py-3 px-4">{subsidy.unit?.item_value || '未知单位'}</td>
                <td class="py-3 px-4">
                  <span class="font-medium text-green-600">{subsidy.total_amount} 元</span>
                </td>
                <td class="text-right py-3 px-4">
                  <Button variant="ghost" size="sm" on:click={() => openDeleteSubsidyConfirm(index)} class="text-red-500 hover:text-red-700">
                    删除
                  </Button>
                </td>
              </tr>
            {/each}
          </tbody>
        </table>
      </div>
    {/if}
  </div>

  <!-- 帮助提示 -->
  <div class="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
    <h3 class="text-sm font-medium text-blue-800 mb-2">补贴信息说明</h3>
    <ul class="list-disc pl-5 text-sm text-blue-700 space-y-1">
      <li>补贴方案是对项目补贴的整体规划，可以创建多个不同的方案</li>
      <li>补贴项是具体的补贴内容，包括类型、金额和单位等信息</li>
      <li>单位金额 × 总单位数 = 总金额，系统会自动计算总金额</li>
      <li>创建补贴方案时，可以多选已添加的补贴项，系统会自动计算总金额</li>
      <li>已创建的补贴方案可以随时编辑，修改方案名称或包含的补贴项</li>
      <li><strong>CSV批量导入：</strong>可以通过下载模板文件，批量导入多个补贴项，提高数据录入效率</li>
      <li>补贴信息为可选项，您可以稍后再添加</li>
    </ul>
  </div>

  <!-- 删除补贴方案确认对话框 -->
  {#if confirmSchemeDialogOpen}
    <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white p-6 rounded-lg shadow-lg w-full max-w-md">
        <h3 class="text-lg font-semibold mb-4">确认删除补贴方案</h3>
        <p class="text-gray-700 mb-4">
          您确定要删除这个补贴方案吗？此操作无法撤销。
        </p>
        <div class="flex justify-end gap-2">
          <Button on:click={() => confirmSchemeDialogOpen = false} variant="outline">取消</Button>
          <Button on:click={removeSubsidyScheme} variant="destructive">确认删除</Button>
        </div>
      </div>
    </div>
  {/if}

  <!-- 删除补贴项确认对话框 -->
  {#if confirmSubsidyDialogOpen}
    <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white p-6 rounded-lg shadow-lg w-full max-w-md">
        <h3 class="text-lg font-semibold mb-4">确认删除补贴项</h3>
        <p class="text-gray-700 mb-4">
          您确定要删除这个补贴项吗？此操作无法撤销，并且会自动从所有包含此补贴项的方案中移除该补贴项。
        </p>
        <div class="flex justify-end gap-2">
          <Button on:click={() => confirmSubsidyDialogOpen = false} variant="outline">取消</Button>
          <Button on:click={removeSubsidy} variant="destructive">确认删除</Button>
        </div>
      </div>
    </div>
  {/if}
</div>
