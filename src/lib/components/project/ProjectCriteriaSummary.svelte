<script lang="ts">
  import { onMount } from 'svelte';
  import { ruleDesignerService, type ProjectCriterionWithRule } from '$lib/services/ruleDesignerService';
  import { goto } from '$app/navigation';
  import { Button } from '$lib/components/ui/button';
  import { ClipboardList } from 'lucide-svelte';

  // Props
  const { projectId } = $props<{ projectId: string }>();

  // 状态管理
  let isLoading = $state(true);
  let error = $state<string | null>(null);
  let inclusionCriteria = $state<ProjectCriterionWithRule[]>([]);
  let exclusionCriteria = $state<ProjectCriterionWithRule[]>([]);

  // 加载项目标准
  async function loadProjectCriteria() {
    isLoading = true;
    error = null;
    console.log(`[ProjectCriteriaSummary] Loading criteria for project: ${projectId}`);

    try {
      // 加载入组标准
      const inclusionQuery = { project_id: projectId, criterion_type: 'inclusion' };
      const inclusionResult = await ruleDesignerService.getProjectCriteria(inclusionQuery);
      inclusionCriteria = [...inclusionResult];

      // 加载排除标准
      const exclusionQuery = { project_id: projectId, criterion_type: 'exclusion' };
      const exclusionResult = await ruleDesignerService.getProjectCriteria(exclusionQuery);
      exclusionCriteria = [...exclusionResult];
    } catch (err: any) {
      error = err.message || '加载项目标准失败';
      console.error('加载项目标准失败:', err);
    } finally {
      isLoading = false;
    }
  }

  // 格式化参数值显示
  function formatParameterValues(criterion: ProjectCriterionWithRule): string {
    try {
      const schema = ruleDesignerService.parseParameterSchema(criterion.rule_definition.parameter_schema);
      const values = ruleDesignerService.parseParameterValues(criterion.criterion.parameter_values);

      return schema.parameters.map(param => {
        const value = values[param.name];
        if (value === undefined || value === null) return null;

        let displayValue = value;
        if (param.unit) {
          displayValue = `${value} ${param.unit}`;
        }

        return `${param.label}: ${displayValue}`;
      }).filter(Boolean).join(', ');
    } catch (e) {
      console.error('格式化参数值失败:', e);
      return '参数格式错误';
    }
  }

  // 检查标准是否在组中
  function isInGroup(criterion: ProjectCriterionWithRule): boolean {
    return !!criterion.criterion.criteria_group_id;
  }

  // 将标准按组分组
  function groupCriteriaByGroup(criteria: ProjectCriterionWithRule[]): Array<{isGroup: boolean, criteria: ProjectCriterionWithRule[]}> {
    const result: Array<{isGroup: boolean, criteria: ProjectCriterionWithRule[]}> = [];
    const groupMap = new Map<string, ProjectCriterionWithRule[]>();

    // 先将标准按组ID分组
    criteria.forEach(criterion => {
      const groupId = criterion.criterion.criteria_group_id;
      if (groupId) {
        if (!groupMap.has(groupId)) {
          groupMap.set(groupId, []);
        }
        groupMap.get(groupId)!.push(criterion);
      }
    });

    // 将组添加到结果中
    groupMap.forEach(groupCriteria => {
      if (groupCriteria.length > 0) {
        result.push({
          isGroup: true,
          criteria: groupCriteria
        });
      }
    });

    // 添加未分组的标准
    const ungroupedCriteria = criteria.filter(c => !isInGroup(c));
    ungroupedCriteria.forEach(criterion => {
      result.push({
        isGroup: false,
        criteria: [criterion]
      });
    });

    return result;
  }

  // 组件挂载时初始化
  onMount(() => {
    loadProjectCriteria();
  });
</script>

<div class="h-full flex flex-col" style="height: 100%;">
  <div class="flex-grow overflow-auto" style="height: 100%;">

  {#if isLoading}
    <div class="flex justify-center items-center py-4">
      <div class="inline-block w-4 h-4 border-2 border-t-transparent border-blue-600 rounded-full animate-spin"></div>
      <span class="ml-2 text-slate-600 dark:text-slate-300 text-sm">加载中...</span>
    </div>
  {:else if error}
    <div class="text-red-500 text-sm py-2">{error}</div>
  {:else if inclusionCriteria.length === 0 && exclusionCriteria.length === 0}
    <div class="flex flex-col items-center justify-center h-full py-8">
      <div class="text-gray-400 text-sm mb-4">暂无入组/排除标准</div>
      <Button variant="outline" size="sm" class="bg-white shadow-sm hover:bg-cyan-50 text-cyan-700 border-cyan-200" onclick={() => goto(`/projects/${projectId}/criteria`)}>
        <ClipboardList class="h-3.5 w-3.5 mr-1" />
        配置入排标准
      </Button>
    </div>
  {:else}
    <div class="flex flex-col h-full">
        <!-- 有标准时的布局 -->
        <div class="flex flex-col md:flex-row h-full" style="height: 100%;">
          <!-- 入组标准 -->
          {#if inclusionCriteria.length > 0}
            <div class="mb-4 md:mb-0 md:mr-6 flex-1" style="height: 100%;">
              <h4 class="font-medium text-green-700 mb-2 border-b border-green-100 pb-1">入组标准 ({inclusionCriteria.length})</h4>
              <div class="overflow-y-auto pr-1" style="max-height: calc(100% - 30px);">
                <div class="space-y-2">
                  <!-- 显示前20个标准（分组后） -->
                  {#each groupCriteriaByGroup(inclusionCriteria) as group, groupIndex}
                    {#if groupIndex < 20}
                      {#if group.isGroup}
                        <!-- 或关系组 -->
                        <div class="relative mb-3 mt-5">
                          <div class="w-full text-left border border-cyan-300 rounded-lg p-2 pt-5 bg-cyan-50/30 relative shadow-sm">
                            <!-- 组标题 -->
                            <div class="absolute -top-3 left-3 z-10">
                              <span class="bg-white px-2.5 py-0.5 text-cyan-700 font-medium text-xs rounded-full border border-cyan-200 inline-block shadow-sm">
                                或关系
                              </span>
                            </div>
                            <!-- 组内标准 -->
                            <div class="pl-2">
                              <ul class="list-disc list-inside space-y-1">
                                {#each group.criteria as criterion}
                                  <li class="text-gray-700 text-sm mb-1">
                                    <span class="font-semibold text-gray-800">{criterion.rule_definition.rule_name}</span>
                                    <div class="mt-1 ml-5">
                                      <span class="bg-cyan-50 text-cyan-800 px-2 py-0.5 rounded-md text-xs font-medium border border-cyan-100 inline-block">
                                        {formatParameterValues(criterion)}
                                      </span>
                                    </div>
                                  </li>
                                {/each}
                              </ul>
                            </div>
                          </div>
                        </div>
                      {:else}
                        <!-- 单个标准 -->
                        {#each group.criteria as criterion}
                          <div class="pl-2 mb-2">
                            <ul class="list-disc list-inside">
                              <li class="text-gray-700 text-sm mb-1">
                                <span class="font-semibold text-gray-800">{criterion.rule_definition.rule_name}</span>
                                <div class="mt-1 ml-5">
                                  <span class="bg-cyan-50 text-cyan-800 px-2 py-0.5 rounded-md text-xs font-medium border border-cyan-100 inline-block">
                                    {formatParameterValues(criterion)}
                                  </span>
                                </div>
                              </li>
                            </ul>
                          </div>
                        {/each}
                      {/if}
                    {/if}
                  {/each}

                  <!-- 如果有超过20个标准（或组），显示"还有更多"提示 -->
                  {#if groupCriteriaByGroup(inclusionCriteria).length > 20}
                    <div class="text-blue-600 text-xs pl-2 mt-1">
                      还有更多入组标准...
                    </div>
                  {/if}
                </div>
              </div>
            </div>
          {/if}

          <!-- 排除标准 -->
          {#if exclusionCriteria.length > 0}
            <div class="flex-1 {inclusionCriteria.length > 0 ? 'md:border-l md:border-gray-200 md:pl-6' : ''}" style="height: 100%;">
              <h4 class="font-medium text-red-700 mb-2 border-b border-red-100 pb-1">排除标准 ({exclusionCriteria.length})</h4>
              <div class="overflow-y-auto pr-1" style="max-height: calc(100% - 30px);">
                <div class="space-y-2">
                  <!-- 显示前20个标准（分组后） -->
                  {#each groupCriteriaByGroup(exclusionCriteria) as group, groupIndex}
                    {#if groupIndex < 20}
                      {#if group.isGroup}
                        <!-- 或关系组 -->
                        <div class="relative mb-3 mt-5">
                          <div class="w-full text-left border border-cyan-300 rounded-lg p-2 pt-5 bg-cyan-50/30 relative shadow-sm">
                            <!-- 组标题 -->
                            <div class="absolute -top-3 left-3 z-10">
                              <span class="bg-white px-2.5 py-0.5 text-cyan-700 font-medium text-xs rounded-full border border-cyan-200 inline-block shadow-sm">
                                或关系
                              </span>
                            </div>
                            <!-- 组内标准 -->
                            <div class="pl-2">
                              <ul class="list-disc list-inside space-y-1">
                                {#each group.criteria as criterion}
                                  <li class="text-gray-700 text-sm mb-1">
                                    <span class="font-semibold text-gray-800">{criterion.rule_definition.rule_name}</span>
                                    <div class="mt-1 ml-5">
                                      <span class="bg-red-50 text-red-800 px-2 py-0.5 rounded-md text-xs font-medium border border-red-100 inline-block">
                                        {formatParameterValues(criterion)}
                                      </span>
                                    </div>
                                  </li>
                                {/each}
                              </ul>
                            </div>
                          </div>
                        </div>
                      {:else}
                        <!-- 单个标准 -->
                        {#each group.criteria as criterion}
                          <div class="pl-2 mb-2">
                            <ul class="list-disc list-inside">
                              <li class="text-gray-700 text-sm mb-1">
                                <span class="font-semibold text-gray-800">{criterion.rule_definition.rule_name}</span>
                                <div class="mt-1 ml-5">
                                  <span class="bg-red-50 text-red-800 px-2 py-0.5 rounded-md text-xs font-medium border border-red-100 inline-block">
                                    {formatParameterValues(criterion)}
                                  </span>
                                </div>
                              </li>
                            </ul>
                          </div>
                        {/each}
                      {/if}
                    {/if}
                  {/each}

                  <!-- 如果有超过20个标准（或组），显示"还有更多"提示 -->
                  {#if groupCriteriaByGroup(exclusionCriteria).length > 20}
                    <div class="text-blue-600 text-xs pl-2 mt-1">
                      还有更多排除标准...
                    </div>
                  {/if}
                </div>
              </div>
            </div>
          {/if}
        </div>
    </div>
  {/if}
  </div>
</div>
