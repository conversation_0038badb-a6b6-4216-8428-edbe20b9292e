<script lang="ts">
  import { onMount } from 'svelte';
  import Input from '$lib/components/ui/input.svelte';
  import Label from '$lib/components/ui/label.svelte';
  import { sqliteDictionaryService } from '$lib/services/sqliteDictionaryService';
  import type { ProjectWithDetails, Project } from '$lib/services/projectManagementService';
  import Button from '$lib/components/ui/button/button.svelte';
  import { open } from '@tauri-apps/plugin-dialog';
  import ProjectFileExplorer from './ProjectFileExplorer.svelte';

  // 组件属性
  const props = $props<{projectDetails: ProjectWithDetails}>();
  let projectDetailsData = $derived(props.projectDetails);

  // 处理项目数据安全访问的函数
  function getProject(): Project {
    if (!projectDetailsData || !projectDetailsData.project) {
      return {
        project_name: '',
        project_short_name: '',
        project_path: '',
        disease_item_id: undefined,
        project_stage_item_id: undefined,
        project_status_item_id: undefined,
        recruitment_status_item_id: undefined,
        contract_case_center: undefined,
        contract_case_total: undefined,
        project_start_date: undefined
      };
    }
    return projectDetailsData.project;
  }

  // 每次projectDetailsData变化时更新项目数据
  let project = $state(getProject());
  $effect(() => {
    project = getProject();
  });

  $effect(() => {
    console.log('ProjectBasicInfo组件接收到的项目详情:', projectDetailsData);
  });

  // 字典项
  let diseases = $state<{item_id: number | undefined, item_value: string}[]>([]);
  let stages = $state<{item_id: number | undefined, item_value: string}[]>([]);
  let statuses = $state<{item_id: number | undefined, item_value: string}[]>([]);
  let recruitmentStatuses = $state<{item_id: number | undefined, item_value: string}[]>([]);
  let sponsorItems = $state<{item_id: number | undefined, item_value: string}[]>([]);

  // 申办方相关状态
  let sponsors = $state<any[]>([]);
  let confirmDeleteIndex = $state<number | null>(null);
  let confirmDialogOpen = $state(false);
  let sponsorSearchQuery = $state('');
  let filteredSponsorItems = $state<{item_id: number | undefined, item_value: string}[]>([]);
  let selectedSponsorId = $state<number | null>(null);

  // 初始化sponsors并保持同步
  $effect(() => {
    sponsors = projectDetailsData.sponsors || [];
  });

  // 根据搜索关键词过滤申办方
  $effect(() => {
    if (!sponsorItems || sponsorItems.length === 0) {
      filteredSponsorItems = [];
      return;
    }

    if (!sponsorSearchQuery) {
      filteredSponsorItems = [...sponsorItems];
      return;
    }

    const query = sponsorSearchQuery.toLowerCase().trim();
    filteredSponsorItems = sponsorItems.filter(item =>
      item.item_value.toLowerCase().includes(query)
    );
  });

  // 加载字典项
  async function loadDictionaryItems() {
    try {
      // 加载疾病字典
      const diseasesDict = await sqliteDictionaryService.getDictByName('疾病');
      if (diseasesDict && diseasesDict.items) {
        diseases = diseasesDict.items.map(item => ({
          item_id: item.item_id || 0,
          item_value: item.value
        }));
      }

      // 加载项目阶段字典
      const stagesDict = await sqliteDictionaryService.getDictByName('研究分期');
      if (stagesDict && stagesDict.items) {
        stages = stagesDict.items.map(item => ({
          item_id: item.item_id || 0,
          item_value: item.value
        }));
      }

      // 加载项目状态字典
      const statusesDict = await sqliteDictionaryService.getDictByName('研究阶段');
      if (statusesDict && statusesDict.items) {
        statuses = statusesDict.items.map(item => ({
          item_id: item.item_id || 0,
          item_value: item.value
        }));
      } else {
        console.error('未找到研究阶段字典');
      }

      // 加载招募状态字典
      const recruitmentStatusesDict = await sqliteDictionaryService.getDictByName('招募状态');
      if (recruitmentStatusesDict && recruitmentStatusesDict.items) {
        recruitmentStatuses = recruitmentStatusesDict.items.map(item => ({
          item_id: item.item_id || 0,
          item_value: item.value
        }));
      }

      // 加载申办方字典
      const sponsorsDict = await sqliteDictionaryService.getDictByName('申办方');
      if (sponsorsDict && sponsorsDict.items) {
        sponsorItems = sponsorsDict.items.map(item => ({
          item_id: item.item_id || 0,
          item_value: item.value
        }));
      }
    } catch (err) {
      console.error('加载字典项失败:', err);
    }
  }

  // 添加申办方
  function addSponsor() {
    if (!selectedSponsorId) return;

    // 检查是否已存在
    const exists = sponsors.some((s) => s.sponsor_item_id === selectedSponsorId);
    if (exists) {
      alert('该申办方已添加');
      return;
    }

    // 获取申办方详情
    const sponsorItem = sponsorItems.find((item) => item.item_id === selectedSponsorId);

    // 创建新申办方
    const newSponsor = {
      project_id: projectDetailsData.project.project_id || '',
      sponsor_item_id: selectedSponsorId || 0,
      sponsor: sponsorItem ? {
        item_id: sponsorItem.item_id || 0,
        dictionary_id: 0, // 这里不重要，后端会忽略
        item_key: '',
        item_value: sponsorItem.item_value
      } : undefined
    };

    // 添加到列表
    sponsors = [...sponsors, newSponsor];
    projectDetailsData.sponsors = sponsors;

    // 重置选择
    selectedSponsorId = null;
    sponsorSearchQuery = '';
  }

  // 打开删除确认对话框
  function openDeleteConfirm(index: number) {
    confirmDeleteIndex = index;
    confirmDialogOpen = true;
  }

  // 删除申办方
  function removeSponsor() {
    if (confirmDeleteIndex === null) return;

    // 更新本地状态
    sponsors = sponsors.filter((_, i) => i !== confirmDeleteIndex);

    // 更新 projectDetails
    projectDetailsData.sponsors = sponsors;

    // 关闭确认对话框
    confirmDialogOpen = false;
    confirmDeleteIndex = null;
  }

  // 选择文件夹（Tauri 官方推荐方式）
  async function handleSelectFolder() {
    try {
      const selected = await open({
        directory: true,
        multiple: false,
        title: '选择项目文件夹'
      });
      if (selected) {
        project.project_path = Array.isArray(selected) ? selected[0] : selected;
      }
    } catch (err) {
      console.error('选择文件夹失败:', err);
    }
  }

  // 组件挂载时加载字典项
  onMount(async () => {
    await loadDictionaryItems();
  });

  // 处理值变更时更新父组件
  $effect(() => {
    // 只有当project中有实际的变更时才更新父组件
    if (
      projectDetailsData &&
      projectDetailsData.project &&
      JSON.stringify(project) !== JSON.stringify(projectDetailsData.project)
    ) {
      console.log('项目数据已更改，更新父组件');
      projectDetailsData.project = {...project};
    }
  });
</script>

<div>
  <h2 class="text-xl font-semibold mb-4">基本信息</h2>

  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <!-- 项目名称 -->
    <div class="space-y-2">
      <Label forAttr="project_name">
        项目名称 <span class="text-red-500">*</span>
      </Label>
      <Input
        id="project_name"
        bind:value={project.project_name}
        placeholder="请输入项目名称"
        class={!project.project_name ? "border-red-300 focus:border-red-500" : ""}
      />
      {#if !project.project_name}
        <p class="text-xs text-red-500 mt-1">项目名称为必填项</p>
      {/if}
    </div>

    <!-- 项目简称 -->
    <div class="space-y-2">
      <Label forAttr="project_short_name">
        项目简称 <span class="text-red-500">*</span>
      </Label>
      <Input
        id="project_short_name"
        bind:value={project.project_short_name}
        placeholder="请输入项目简称"
        class={!project.project_short_name ? "border-red-300 focus:border-red-500" : ""}
      />
      {#if !project.project_short_name}
        <p class="text-xs text-red-500 mt-1">项目简称为必填项</p>
      {/if}
    </div>

    <!-- 项目路径 -->
    <div class="space-y-2">
      <Label forAttr="project_path">项目路径</Label>
      <div class="flex gap-2">
        <Input
          id="project_path"
          bind:value={project.project_path}
          placeholder="请选择项目文件夹"
          readonly
          class="flex-1 cursor-pointer bg-gray-100"
        />
        <Button type="button" on:click={handleSelectFolder} variant="outline">选择文件夹</Button>
      </div>
      <p class="text-xs text-gray-500">项目文件存储的路径（可选）</p>
    </div>

    <!-- 疾病 -->
    <div class="space-y-2">
      <Label forAttr="disease">
        疾病 <span class="text-red-500">*</span>
      </Label>
      <select
        id="disease"
        bind:value={project.disease_item_id}
        class="h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm {!project.disease_item_id ? 'border-red-300' : ''}"
      >
        <option value={undefined}>请选择疾病类型</option>
        {#each diseases as disease}
          <option value={disease.item_id}>{disease.item_value}</option>
        {/each}
      </select>
      {#if !project.disease_item_id}
        <p class="text-xs text-red-500 mt-1">疾病类型为必填项</p>
      {/if}
    </div>

    <!-- 项目阶段 -->
    <div class="space-y-2">
      <Label forAttr="project_stage">项目阶段</Label>
      <select
        id="project_stage"
        bind:value={project.project_stage_item_id}
        class="h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
      >
        <option value={undefined}>请选择项目阶段</option>
        {#each stages as stage}
          <option value={stage.item_id}>{stage.item_value}</option>
        {/each}
      </select>
      <p class="text-xs text-gray-500">项目当前所处的研究阶段（可选）</p>
    </div>

    <!-- 项目状态 -->
    <div class="space-y-2">
      <Label forAttr="project_status">项目状态</Label>
      <select
        id="project_status"
        bind:value={project.project_status_item_id}
        class="h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
      >
        <option value={undefined}>请选择项目状态</option>
        {#each statuses as status}
          <option value={status.item_id}>{status.item_value}</option>
        {/each}
      </select>
      <p class="text-xs text-gray-500">项目当前的运行状态（可选）</p>
    </div>

    <!-- 招募状态 -->
    <div class="space-y-2">
      <Label forAttr="recruitment_status">招募状态</Label>
      <select
        id="recruitment_status"
        bind:value={project.recruitment_status_item_id}
        class="h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
      >
        <option value={undefined}>请选择招募状态</option>
        {#each recruitmentStatuses as status}
          <option value={status.item_id}>{status.item_value}</option>
        {/each}
      </select>
      <p class="text-xs text-gray-500">项目当前的招募状态（可选）</p>
    </div>

    <!-- 合同中心数 -->
    <div class="space-y-2">
      <Label forAttr="contract_case_center">合同中心数</Label>
      <Input
        id="contract_case_center"
        type="number"
        min="0"
        bind:value={project.contract_case_center}
        placeholder="请输入合同中心数"
      />
      <p class="text-xs text-gray-500">合同约定的研究中心数量（可选）</p>
    </div>

    <!-- 合同例数 -->
    <div class="space-y-2">
      <Label forAttr="contract_case_total">合同例数</Label>
      <Input
        id="contract_case_total"
        type="number"
        min="0"
        bind:value={project.contract_case_total}
        placeholder="请输入合同例数"
      />
      <p class="text-xs text-gray-500">合同约定的病例总数（可选）</p>
    </div>

    <!-- 项目启动日期 -->
    <div class="space-y-2">
      <Label forAttr="project_start_date">项目启动日期</Label>
      <Input
        id="project_start_date"
        type="date"
        bind:value={project.project_start_date}
        placeholder="请选择项目启动日期"
      />
      <p class="text-xs text-gray-500">项目正式启动的日期（可选）</p>
    </div>

    <!-- 申办方选择 -->
    <div class="space-y-2">
      <Label forAttr="sponsor">申办方</Label>
      <div class="flex gap-2">
        <div class="relative flex-grow">
          <input
            type="text"
            bind:value={sponsorSearchQuery}
            placeholder="搜索申办方..."
            class="h-10 w-full rounded-md border border-input bg-background pl-9 pr-3 py-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
          />
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"
          >
            <circle cx="11" cy="11" r="8"></circle>
            <path d="m21 21-4.3-4.3"></path>
          </svg>
        </div>
        <button
          onclick={addSponsor}
          disabled={!selectedSponsorId}
          class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
        >
          添加
        </button>
      </div>
      <select
        bind:value={selectedSponsorId}
        class="h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm mt-2"
      >
        <option value={null}>请选择申办方</option>
        {#each filteredSponsorItems as sponsor}
          <option value={sponsor.item_id}>{sponsor.item_value}</option>
        {/each}
      </select>
      {#if filteredSponsorItems.length === 0 && sponsorSearchQuery}
        <p class="text-xs text-gray-500 mt-1">没有找到匹配的申办方</p>
      {/if}
      <p class="text-xs text-gray-500">选择申办方并点击"添加"按钮将其添加到项目中</p>
    </div>
  </div>

  <!-- 申办方列表部分 -->
  <div class="mt-8">
    <div class="flex justify-between items-center mb-6">
      <div>
        <h3 class="text-lg font-semibold">已添加的申办方</h3>
        <p class="text-sm text-gray-500 mt-1">项目的申办方信息列表</p>
      </div>
    </div>

    {#if !sponsors || sponsors.length === 0}
      <div class="bg-gray-50 border border-gray-200 rounded-lg p-8 text-center">
        <div class="flex justify-center mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="text-gray-400"><path d="M18 21a8 8 0 0 0-16 0"></path><circle cx="10" cy="8" r="5"></circle><path d="M22 20c0-3.37-2-6.5-4-8a5 5 0 0 0-.45-8.3"></path></svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">暂无申办方数据</h3>
        <p class="text-gray-500 mb-4">请在上方的申办方选择框中选择并添加申办方</p>
      </div>
    {:else}
      <div class="bg-white border rounded-lg overflow-hidden">
        <table class="w-full border-collapse">
          <thead>
            <tr class="bg-gray-50">
              <th class="text-left py-3 px-4 font-medium text-gray-700">申办方名称</th>
              <th class="w-[100px] text-right py-3 px-4 font-medium text-gray-700">操作</th>
            </tr>
          </thead>
          <tbody>
            {#each sponsors as sponsor, index}
              <tr class="border-t hover:bg-gray-50">
                <td class="py-3 px-4">{sponsor.sponsor?.item_value || '未知申办方'}</td>
                <td class="text-right py-3 px-4">
                  <button
                    onclick={() => openDeleteConfirm(index)}
                    class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-9 rounded-md px-3 text-red-500 hover:text-red-700"
                  >
                    删除
                  </button>
                </td>
              </tr>
            {/each}
          </tbody>
        </table>
      </div>
    {/if}
  </div>

  <!-- 表单验证提示 -->
  <div class="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
    <h3 class="text-sm font-medium text-blue-800 mb-2">填写说明</h3>
    <ul class="list-disc pl-5 text-sm text-blue-700 space-y-1">
      <li>带 <span class="text-red-500">*</span> 的字段为必填项</li>
      <li>项目名称、项目简称和疾病类型为必填项，其他字段为可选项</li>
      <li>申办方是指负责发起、管理或资助该项目的组织或机构</li>
      <li>一个项目可以有多个申办方</li>
      <li>填写完必填项后，您可以继续填写其他信息或直接保存项目</li>
    </ul>
  </div>

  <!-- 项目文件浏览器 -->
  {#if project.project_path}
    <div class="mt-4">
      <p class="text-sm text-gray-500 mb-2">项目路径: {project.project_path}</p>
      <ProjectFileExplorer projectPath={project.project_path} />
    </div>
  {:else}
    <div class="mt-4 p-4 bg-amber-50 border border-amber-200 rounded-lg">
      <p class="text-amber-800">请先选择项目文件夹路径，以查看项目文件</p>
    </div>
  {/if}

  <!-- 删除确认对话框 -->
  {#if confirmDialogOpen}
    <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white p-6 rounded-lg shadow-lg w-full max-w-md">
        <h3 class="text-lg font-semibold mb-4">确认删除</h3>
        <p class="text-gray-700 mb-4">
          您确定要删除这个申办方吗？此操作无法撤销。
        </p>
        <div class="flex justify-end gap-2">
          <button
            onclick={() => confirmDialogOpen = false}
            class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2"
          >
            取消
          </button>
          <button
            onclick={removeSponsor}
            class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-destructive text-destructive-foreground hover:bg-destructive/90 h-10 px-4 py-2"
          >
            确认删除
          </button>
        </div>
      </div>
    </div>
  {/if}
</div>
