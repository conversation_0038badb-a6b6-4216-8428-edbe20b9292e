<script lang="ts">
  import { onMount } from 'svelte';
  import { staffService, type Staff } from '$lib/services/staffService';
  import { Button } from '$lib/components/ui/button';

  // Props
  const { staffId } = $props<{ staffId: number }>();

  // 状态管理
  let staff = $state<Staff | null>(null);
  let isLoading = $state(true);
  let error = $state<string | null>(null);
  let positions = $state<{id: number, key: string, value: string}[]>([]);

  // 确认对话框状态
  let showConfirmDialog = $state(false);
  let confirmDialogTitle = $state('');
  let confirmDialogMessage = $state('');
  let confirmDialogCallback = $state<(() => void) | null>(null);

  // 显示确认对话框
  function showConfirm(title: string, message: string, callback: () => void) {
    confirmDialogTitle = title;
    confirmDialogMessage = message;
    confirmDialogCallback = callback;
    showConfirmDialog = true;
  }

  // 加载人员数据
  async function loadStaff() {
    isLoading = true;
    error = null;

    try {
      staff = await staffService.getStaffById(staffId);

      // 加载职位信息
      await loadPositions();

      // 将职位ID映射到职位名称
      if (staff) {
        const position = positions.find(p => p.id === staff.position_item_id);
        staff = {
          ...staff,
          position_name: position ? position.value : '未知职位'
        };
      }
    } catch (err: any) {
      error = err.message || '加载人员失败';
      console.error('加载人员失败:', err);
    } finally {
      isLoading = false;
    }
  }

  // 加载用户角色信息
  async function loadPositions() {
    try {
      positions = await staffService.getPositions();
    } catch (err: any) {
      console.error('加载用户角色失败:', err);
    }
  }

  // 删除人员
  async function deleteStaff() {
    if (!staff || !staff.id) return;

    showConfirm(
      '删除人员',
      '确定要删除此人员信息吗？此操作不可撤销。',
      async () => {
        try {
          const success = await staffService.deleteStaff(staff!.id!);

          if (success) {
            // 跳转到列表页
            window.location.href = '/staff';
          } else {
            error = '删除人员失败';
          }
        } catch (err: any) {
          error = err.message || '删除人员失败';
          console.error('删除人员失败:', err);
        }
      }
    );
  }

  // 组件挂载时加载数据
  onMount(() => {
    loadStaff();
  });
</script>

<div class="w-full">
  <!-- 工具栏 -->
  <div class="mb-4 flex justify-between items-center">
    <a href="/staff" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 flex items-center gap-1">
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-left"><path d="m12 19-7-7 7-7"/><path d="M19 12H5"/></svg>
      返回人员列表
    </a>

    {#if staff}
      <div class="flex gap-2">
        <Button variant="outline" size="sm" href={`/staff/${staffId}/edit`} class="flex items-center gap-1">
          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-pencil"><path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"/><path d="m15 5 4 4"/></svg>
          编辑人员
        </Button>
        <button
          type="button"
          class="inline-flex items-center gap-1 h-9 px-3 py-2 text-sm font-medium rounded-md bg-red-600 text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:bg-red-800 dark:hover:bg-red-700 dark:focus:ring-red-600"
          onclick={deleteStaff}
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trash-2"><path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/><path d="M10 11v6"/><path d="M14 11v6"/></svg>
          删除人员
        </button>
      </div>
    {/if}
  </div>

  <!-- 错误提示 -->
  {#if error}
    <div class="mb-4 bg-red-50 dark:bg-red-900/20 border border-red-100 dark:border-red-800 rounded-md p-4 text-red-800 dark:text-red-200">
      <p>{error}</p>
    </div>
  {/if}

  <!-- 内容区域 -->
  <div class="bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700 overflow-hidden">
    {#if isLoading}
      <div class="flex justify-center items-center p-8">
        <div class="inline-block w-6 h-6 border-2 border-t-transparent border-blue-600 dark:border-blue-400 rounded-full animate-spin mr-2"></div>
        <p>加载中...</p>
      </div>
    {:else if !staff}
      <div class="p-8 text-center text-slate-500 dark:text-slate-400">
        <p>人员不存在或已被删除</p>
      </div>
    {:else}
      <div class="p-6">
        <div class="flex flex-col md:flex-row md:items-start gap-6">
          <!-- 基本信息 -->
          <div class="flex-1">
            <h2 class="text-xl font-semibold text-slate-800 dark:text-slate-200 mb-4">基本信息</h2>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 class="text-sm font-medium text-slate-500 dark:text-slate-400">ID</h3>
                <p class="mt-1 text-slate-800 dark:text-slate-200">{staff.id}</p>
              </div>

              <div>
                <h3 class="text-sm font-medium text-slate-500 dark:text-slate-400">姓名</h3>
                <div class="mt-1 flex items-center">
                  <p class="text-slate-800 dark:text-slate-200 font-medium">{staff.name}</p>
                  {#if staff.isPI}
                    <span class="ml-2 px-2 py-0.5 text-xs rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900/40 dark:text-blue-300">
                      PI
                    </span>
                  {/if}
                </div>
              </div>

              <div>
                <h3 class="text-sm font-medium text-slate-500 dark:text-slate-400">性别</h3>
                <p class="mt-1 text-slate-800 dark:text-slate-200">{staff.gender}</p>
              </div>

              <div>
                <h3 class="text-sm font-medium text-slate-500 dark:text-slate-400">生日</h3>
                <p class="mt-1 text-slate-800 dark:text-slate-200">{staff.birthday}</p>
              </div>

              <div>
                <h3 class="text-sm font-medium text-slate-500 dark:text-slate-400">用户角色</h3>
                <p class="mt-1 text-slate-800 dark:text-slate-200">{staff.position_name}</p>
              </div>

              <div>
                <h3 class="text-sm font-medium text-slate-500 dark:text-slate-400">组织</h3>
                <p class="mt-1 text-slate-800 dark:text-slate-200">{staff.organization}</p>
              </div>
            </div>

            <h2 class="text-xl font-semibold text-slate-800 dark:text-slate-200 mt-6 mb-4">联系方式</h2>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 class="text-sm font-medium text-slate-500 dark:text-slate-400">电话</h3>
                <p class="mt-1 text-slate-800 dark:text-slate-200">{staff.phone}</p>
              </div>

              <div>
                <h3 class="text-sm font-medium text-slate-500 dark:text-slate-400">邮箱</h3>
                <p class="mt-1 text-slate-800 dark:text-slate-200">{staff.email}</p>
              </div>
            </div>

            <h2 class="text-xl font-semibold text-slate-800 dark:text-slate-200 mt-6 mb-4">系统信息</h2>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 class="text-sm font-medium text-slate-500 dark:text-slate-400">创建时间</h3>
                <p class="mt-1 text-slate-800 dark:text-slate-200">
                  {staff.created_at ? new Date(staff.created_at).toLocaleString() : '-'}
                </p>
              </div>

              <div>
                <h3 class="text-sm font-medium text-slate-500 dark:text-slate-400">更新时间</h3>
                <p class="mt-1 text-slate-800 dark:text-slate-200">
                  {staff.updated_at ? new Date(staff.updated_at).toLocaleString() : '-'}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    {/if}
  </div>

  <!-- 自定义确认对话框 -->
  {#if showConfirmDialog}
    <div class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div class="bg-white dark:bg-slate-800 rounded-lg shadow-lg max-w-md w-full p-6">
        <h3 class="text-lg font-semibold mb-2">{confirmDialogTitle}</h3>
        <p class="text-slate-600 dark:text-slate-300 mb-6">{confirmDialogMessage}</p>
        <div class="flex justify-end gap-3">
          <button
            type="button"
            class="px-4 py-2 text-sm font-medium rounded-md bg-slate-200 text-slate-800 hover:bg-slate-300 dark:bg-slate-700 dark:text-slate-200 dark:hover:bg-slate-600"
            onclick={() => showConfirmDialog = false}
          >
            取消
          </button>
          <button
            type="button"
            class="px-4 py-2 text-sm font-medium rounded-md bg-red-600 text-white hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-600"
            onclick={() => {
              showConfirmDialog = false;
              if (confirmDialogCallback) {
                confirmDialogCallback();
              }
            }}
          >
            确定
          </button>
        </div>
      </div>
    </div>
  {/if}
</div>
