<script lang="ts">
  import { configService } from '$lib/services/configService';
  import { Button } from '$lib/components/ui/button';
  import { onMount } from 'svelte';

  // 属性
  let { configName, title, description } = $props<{
    configName: string;
    title: string;
    description?: string;
  }>();

  // 状态管理
  let items = $state<string[]>([]);
  let newItem = $state('');
  let isLoading = $state(false);
  let isSaving = $state(false);
  let error = $state<string | null>(null);
  let success = $state<string | null>(null);

  // 加载配置列表
  async function loadItems() {
    isLoading = true;
    error = null;

    try {
      items = await configService.getConfigList(configName);
    } catch (err: any) {
      error = err.message || `加载${title}失败`;
      console.error(`加载${title}失败:`, err);
    } finally {
      isLoading = false;
    }
  }

  // 保存配置列表
  async function saveItems() {
    isSaving = true;
    error = null;
    success = null;

    try {
      const result = await configService.saveConfigList(configName, items);

      if (result.success) {
        success = `${title}保存成功`;
      } else {
        error = result.error || `保存${title}失败`;
      }
    } catch (err: any) {
      error = err.message || `保存${title}失败`;
      console.error(`保存${title}失败:`, err);
    } finally {
      isSaving = false;
    }
  }

  // 添加项目
  function addItem() {
    if (!newItem.trim()) return;

    // 检查是否已存在
    if (items.includes(newItem.trim())) {
      error = `${newItem} 已存在`;
      return;
    }

    items = [...items, newItem.trim()];
    newItem = '';
    error = null;
  }

  // 删除项目
  function removeItem(index: number) {
    items = items.filter((_, i) => i !== index);
  }

  // 组件挂载时加载配置
  onMount(() => {
    loadItems();
  });
</script>

<div class="config-list">
  <div class="bg-white dark:bg-slate-800 rounded-lg shadow-md p-6">
    <h2 class="text-xl font-semibold mb-2">{title}</h2>

    {#if description}
      <p class="text-sm text-slate-500 dark:text-slate-400 mb-6">{description}</p>
    {/if}

    <!-- 错误提示 -->
    {#if error}
      <div class="bg-red-50 dark:bg-red-900/20 border border-red-100 dark:border-red-800 rounded-md p-4 text-red-800 dark:text-red-200 mb-6">
        <p>{error}</p>
      </div>
    {/if}

    <!-- 成功提示 -->
    {#if success}
      <div class="bg-green-50 dark:bg-green-900/20 border border-green-100 dark:border-green-800 rounded-md p-4 text-green-800 dark:text-green-200 mb-6">
        <p>{success}</p>
      </div>
    {/if}

    <!-- 加载中 -->
    {#if isLoading}
      <div class="flex justify-center items-center py-12">
        <div class="inline-block w-6 h-6 border-2 border-t-transparent border-blue-600 dark:border-blue-400 rounded-full animate-spin mr-2"></div>
        <p>加载中...</p>
      </div>
    {:else}
      <!-- 添加表单 -->
      <div class="flex gap-2 mb-6">
        <input
          type="text"
          bind:value={newItem}
          placeholder={`添加新的${title}项`}
          class="flex-1 h-10 rounded-md border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 px-3 py-2 text-sm text-slate-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          onkeydown={(e) => e.key === 'Enter' && addItem()}
        />
        <Button onclick={addItem}>添加</Button>
      </div>

      <!-- 项目列表 -->
      {#if items.length === 0}
        <div class="text-center py-8 text-slate-500 dark:text-slate-400">
          <p>暂无{title}数据</p>
        </div>
      {:else}
        <div class="border border-slate-200 dark:border-slate-700 rounded-md overflow-hidden mb-6">
          <ul class="divide-y divide-slate-200 dark:divide-slate-700">
            {#each items as item, index}
              <li class="flex justify-between items-center p-3 hover:bg-slate-50 dark:hover:bg-slate-700/50">
                <span class="text-slate-800 dark:text-slate-200">{item}</span>
                <button
                  class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                  onclick={() => removeItem(index)}
                  aria-label="删除项目"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trash-2"><path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/><line x1="10" x2="10" y1="11" y2="17"/><line x1="14" x2="14" y1="11" y2="17"/></svg>
                </button>
              </li>
            {/each}
          </ul>
        </div>
      {/if}

      <!-- 保存按钮 -->
      <div class="flex justify-end">
        <Button onclick={saveItems} disabled={isSaving} class="flex items-center gap-2">
          {#if isSaving}
            <span class="inline-block w-4 h-4 border-2 border-t-transparent border-current rounded-full animate-spin"></span>
          {/if}
          {isSaving ? '保存中...' : '保存更改'}
        </Button>
      </div>
    {/if}
  </div>
</div>
