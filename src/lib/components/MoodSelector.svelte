<script lang="ts">
  import { Button } from "$lib/components/ui/button";
  import * as Dialog from "$lib/components/ui/dialog";

  // 定义心情类型
  type Mood = {
    id: string;
    emoji: string;
    name: string;
    greetings: string[];
  };

  // 心情数据
  const moods: Mood[] = [
    {
      id: "happy",
      emoji: "😊",
      name: "开心",
      greetings: [
        "你的笑容真美，继续保持这份好心情！",
        "看到你这么开心，我也感到很高兴！",
        "积极的心态会让一切变得更美好！",
        "愿你的每一天都充满欢笑和阳光！"
      ]
    },
    {
      id: "excited",
      emoji: "🎉",
      name: "兴奋",
      greetings: [
        "你的热情真是令人鼓舞！",
        "带着这份激情，你一定能创造奇迹！",
        "兴奋是创造力的源泉，期待你的精彩表现！",
        "让这份热情点燃你的创造力吧！"
      ]
    },
    {
      id: "calm",
      emoji: "😌",
      name: "平静",
      greetings: [
        "平静的心态是最大的智慧",
        "保持内心的平静，你会看到更多美好",
        "平静的心灵能感受到生活中的每一个细节",
        "在平静中蕴藏着无限的力量"
      ]
    },
    {
      id: "tired",
      emoji: "😴",
      name: "疲惫",
      greetings: [
        "适当的休息是为了走更长远的路",
        "照顾好自己，给自己一些放松的时间",
        "休息是为了更好的前行，别忘了给自己充电",
        "每个人都需要休息，这是新开始的准备"
      ]
    },
    {
      id: "focused",
      emoji: "🧠",
      name: "专注",
      greetings: [
        "专注是成功的关键，你正走在正确的道路上",
        "全神贯注时，你的潜力是无限的",
        "专注的时刻往往能创造最大的价值",
        "保持这份专注，你将收获丰厚的成果"
      ]
    },
    {
      id: "creative",
      emoji: "💡",
      name: "创意",
      greetings: [
        "你的创意思维令人赞叹！",
        "创意的火花正在你的脑海中闪烁，捕捉它们！",
        "伟大的想法往往来自于不同寻常的思考方式",
        "让你的创意之光照亮前行的道路"
      ]
    }
  ];

  // 组件状态
  let selectedMood: Mood | null = $state(null);
  let greeting: string = $state("");
  let dialogOpen = $state(false);

  // 选择心情
  function selectMood(mood: Mood) {
    selectedMood = mood;
    // 随机选择一条问候语
    const randomIndex = Math.floor(Math.random() * mood.greetings.length);
    greeting = mood.greetings[randomIndex];
    dialogOpen = true;
  }
</script>

<div class="w-full">
  <div class="grid grid-cols-3 gap-4 md:grid-cols-6">
    {#each moods as mood, i}
      <button
        class="mood-button flex flex-col items-center justify-center p-4 rounded-xl border border-slate-200 dark:border-slate-700 hover:bg-slate-50 dark:hover:bg-slate-700/50 transition-all"
        style="animation-delay: {i * 50}ms"
        onclick={() => selectMood(mood)}
      >
        <div class="relative">
          <span class="text-4xl mb-3 transform transition-all duration-300 hover:scale-110">{mood.emoji}</span>
          <!-- 装饰效果 -->
          <span class="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-8 h-1 bg-gradient-to-r from-transparent via-slate-200 dark:via-slate-600 to-transparent rounded-full opacity-70"></span>
        </div>
        <span class="text-sm font-medium text-slate-700 dark:text-slate-300">{mood.name}</span>
      </button>
    {/each}
  </div>

  <!-- 问候对话框 -->
  <Dialog.Root bind:open={dialogOpen}>
    <Dialog.Content class="max-w-md animate-fadeIn">
      <Dialog.Header>
        <Dialog.Title class="flex items-center gap-2">
          {#if selectedMood}
            <div class="flex items-center gap-3">
              <span class="text-3xl animate-pulse">{selectedMood.emoji}</span>
              <span class="text-xl font-semibold">你的心情：{selectedMood.name}</span>
            </div>
          {/if}
        </Dialog.Title>
      </Dialog.Header>

      <div class="py-6 px-2">
        <p class="text-lg leading-relaxed text-slate-700 dark:text-slate-300 italic">“{greeting}”</p>
      </div>

      <Dialog.Footer>
        <Button variant="outline" onclick={() => dialogOpen = false} class="px-6">关闭</Button>
      </Dialog.Footer>
    </Dialog.Content>
  </Dialog.Root>
</div>
