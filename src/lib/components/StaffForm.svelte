<script lang="ts">
  import { onMount } from 'svelte';
  import { staffService, type Staff } from '$lib/services/staffService';
  import { Button } from '$lib/components/ui/button';

  // Props
  const { staff = null } = $props<{ staff?: Staff | null }>();

  // 状态管理
  let isSubmitting = $state(false);
  let error = $state<string | null>(null);
  let success = $state<string | null>(null);
  let positions = $state<{id: number, key: string, value: string}[]>([]);

  // 表单数据
  let formData = $state<Staff>({
    name: '',
    gender: '男',
    birthday: '',
    phone: '',
    email: '',
    position_item_id: 0,
    isPI: false,
    organization: ''
  });

  // 表单验证
  let errors = $state<Record<string, string>>({});

  // 加载用户角色信息
  async function loadPositions() {
    try {
      positions = await staffService.getPositions();

      // 如果没有选择用户角色且有用户角色数据，默认选择第一个
      if (formData.position_item_id === 0 && positions.length > 0) {
        formData.position_item_id = positions[0].id;
      }
    } catch (err: any) {
      console.error('加载用户角色失败:', err);
      error = err.message || '加载用户角色失败';
    }
  }

  // 验证表单
  function validateForm(): boolean {
    errors = {};

    if (!formData.name.trim()) {
      errors.name = '姓名不能为空';
    }

    if (!formData.gender) {
      errors.gender = '请选择性别';
    }

    if (!formData.birthday) {
      errors.birthday = '生日不能为空';
    }

    if (!formData.phone.trim()) {
      errors.phone = '电话不能为空';
    } else if (!/^1[3-9]\d{9}$/.test(formData.phone)) {
      errors.phone = '请输入有效的手机号码';
    }

    if (!formData.email.trim()) {
      errors.email = '邮箱不能为空';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = '请输入有效的邮箱地址';
    }

    if (!formData.position_item_id) {
      errors.position_item_id = '请选择职位';
    }

    if (!formData.organization.trim()) {
      errors.organization = '组织不能为空';
    }

    return Object.keys(errors).length === 0;
  }

  // 提交表单
  async function handleSubmit() {
    if (!validateForm()) {
      return;
    }

    isSubmitting = true;
    error = null;
    success = null;

    try {
      if (staff && staff.id) {
        // 更新
        await staffService.updateStaff(staff.id, formData);
        success = '人员信息更新成功';
      } else {
        // 创建
        const id = await staffService.createStaff(formData);
        success = '人员创建成功';

        // 跳转到详情页
        setTimeout(() => {
          window.location.href = `/staff/${id}`;
        }, 1500);
      }
    } catch (err: any) {
      error = err.message || (staff ? '更新人员失败' : '创建人员失败');
      console.error(staff ? '更新人员失败:' : '创建人员失败:', err);
    } finally {
      isSubmitting = false;
    }
  }

  // 组件挂载时初始化
  onMount(() => {
    // 如果是编辑模式，使用传入的数据初始化表单
    if (staff) {
      formData = { ...staff };
    }

    // 加载职位信息
    loadPositions();
  });
</script>

<div class="w-full">
  <!-- 错误提示 -->
  {#if error}
    <div class="mb-4 bg-red-50 dark:bg-red-900/20 border border-red-100 dark:border-red-800 rounded-md p-4 text-red-800 dark:text-red-200">
      <p>{error}</p>
    </div>
  {/if}

  <!-- 成功提示 -->
  {#if success}
    <div class="mb-4 bg-green-50 dark:bg-green-900/20 border border-green-100 dark:border-green-800 rounded-md p-4 text-green-800 dark:text-green-200">
      <p>{success}</p>
    </div>
  {/if}

  <!-- 表单 -->
  <form onsubmit={(e) => { e.preventDefault(); handleSubmit(); }} class="bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700 p-6">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- 姓名 -->
      <div>
        <label for="name" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">姓名 <span class="text-red-500">*</span></label>
        <input
          type="text"
          id="name"
          bind:value={formData.name}
          class="w-full px-3 py-2 border {errors.name ? 'border-red-300 dark:border-red-600' : 'border-slate-300 dark:border-slate-600'} rounded-md text-sm text-slate-900 dark:text-slate-100 bg-white dark:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600"
          placeholder="请输入姓名"
        />
        {#if errors.name}
          <p class="mt-1 text-sm text-red-600 dark:text-red-400">{errors.name}</p>
        {/if}
      </div>

      <!-- 性别 -->
      <div>
        <label for="gender" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">性别 <span class="text-red-500">*</span></label>
        <select
          id="gender"
          bind:value={formData.gender}
          class="w-full px-3 py-2 border {errors.gender ? 'border-red-300 dark:border-red-600' : 'border-slate-300 dark:border-slate-600'} rounded-md text-sm text-slate-900 dark:text-slate-100 bg-white dark:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600"
        >
          <option value="男">男</option>
          <option value="女">女</option>
          <option value="其他">其他</option>
        </select>
        {#if errors.gender}
          <p class="mt-1 text-sm text-red-600 dark:text-red-400">{errors.gender}</p>
        {/if}
      </div>

      <!-- 生日 -->
      <div>
        <label for="birthday" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">生日 <span class="text-red-500">*</span></label>
        <input
          type="date"
          id="birthday"
          bind:value={formData.birthday}
          class="w-full px-3 py-2 border {errors.birthday ? 'border-red-300 dark:border-red-600' : 'border-slate-300 dark:border-slate-600'} rounded-md text-sm text-slate-900 dark:text-slate-100 bg-white dark:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600"
        />
        {#if errors.birthday}
          <p class="mt-1 text-sm text-red-600 dark:text-red-400">{errors.birthday}</p>
        {/if}
      </div>

      <!-- 电话 -->
      <div>
        <label for="phone" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">电话 <span class="text-red-500">*</span></label>
        <input
          type="tel"
          id="phone"
          bind:value={formData.phone}
          class="w-full px-3 py-2 border {errors.phone ? 'border-red-300 dark:border-red-600' : 'border-slate-300 dark:border-slate-600'} rounded-md text-sm text-slate-900 dark:text-slate-100 bg-white dark:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600"
          placeholder="请输入电话号码"
        />
        {#if errors.phone}
          <p class="mt-1 text-sm text-red-600 dark:text-red-400">{errors.phone}</p>
        {/if}
      </div>

      <!-- 邮箱 -->
      <div>
        <label for="email" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">邮箱 <span class="text-red-500">*</span></label>
        <input
          type="email"
          id="email"
          bind:value={formData.email}
          class="w-full px-3 py-2 border {errors.email ? 'border-red-300 dark:border-red-600' : 'border-slate-300 dark:border-slate-600'} rounded-md text-sm text-slate-900 dark:text-slate-100 bg-white dark:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600"
          placeholder="请输入邮箱地址"
        />
        {#if errors.email}
          <p class="mt-1 text-sm text-red-600 dark:text-red-400">{errors.email}</p>
        {/if}
      </div>

      <!-- 职位 -->
      <div>
        <label for="position" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">用户角色 <span class="text-red-500">*</span></label>
        <select
          id="position"
          bind:value={formData.position_item_id}
          class="w-full px-3 py-2 border {errors.position_item_id ? 'border-red-300 dark:border-red-600' : 'border-slate-300 dark:border-slate-600'} rounded-md text-sm text-slate-900 dark:text-slate-100 bg-white dark:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600"
        >
          <option value={0} disabled>请选择用户角色</option>
          {#each positions as position}
            <option value={position.id}>{position.value}</option>
          {/each}
        </select>
        {#if errors.position_item_id}
          <p class="mt-1 text-sm text-red-600 dark:text-red-400">{errors.position_item_id}</p>
        {/if}
      </div>

      <!-- 组织 -->
      <div>
        <label for="organization" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">组织 <span class="text-red-500">*</span></label>
        <input
          type="text"
          id="organization"
          bind:value={formData.organization}
          class="w-full px-3 py-2 border {errors.organization ? 'border-red-300 dark:border-red-600' : 'border-slate-300 dark:border-slate-600'} rounded-md text-sm text-slate-900 dark:text-slate-100 bg-white dark:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600"
          placeholder="请输入组织名称"
        />
        {#if errors.organization}
          <p class="mt-1 text-sm text-red-600 dark:text-red-400">{errors.organization}</p>
        {/if}
      </div>

      <!-- 是否为PI -->
      <div class="flex items-center">
        <input
          type="checkbox"
          id="isPI"
          bind:checked={formData.isPI}
          class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-300 rounded"
        />
        <label for="isPI" class="ml-2 block text-sm text-slate-700 dark:text-slate-300">
          是否为PI
        </label>
      </div>
    </div>

    <div class="mt-8 flex justify-end gap-3">
      <a
        href={staff && staff.id ? `/staff/${staff.id}` : '/staff'}
        class="px-4 py-2 text-sm font-medium rounded-md bg-slate-200 text-slate-800 hover:bg-slate-300 dark:bg-slate-700 dark:text-slate-200 dark:hover:bg-slate-600"
      >
        取消
      </a>
      <button
        type="submit"
        class="px-4 py-2 text-sm font-medium rounded-md bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:bg-blue-700 dark:hover:bg-blue-600 dark:focus:ring-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
        disabled={isSubmitting}
      >
        {#if isSubmitting}
          <span class="inline-block w-4 h-4 border-2 border-t-transparent border-white rounded-full animate-spin mr-2"></span>
        {/if}
        {staff ? '更新' : '创建'}
      </button>
    </div>
  </form>
</div>
