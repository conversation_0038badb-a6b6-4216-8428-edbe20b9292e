<script lang="ts">
  import { onMount } from 'svelte';
  
  export let title: string;
  export let data: any[] = [];
  export let chartType: 'pie' | 'bar' | 'line' | 'table' = 'bar';
  export let isLoading: boolean = false;
  export let error: string | null = null;
  export let height: string = '300px';
  export let icon: any = null;

  let chartContainer: HTMLDivElement;
  let chart: any;

  // 简化版图表配置
  function getChartConfig() {
    const baseConfig = {
      tooltip: {
        trigger: chartType === 'pie' ? 'item' : 'axis',
        formatter: chartType === 'pie' ? '{b}: {c} ({d}%)' : '{b}: {c}'
      },
      grid: chartType !== 'pie' ? {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      } : undefined
    };

    switch (chartType) {
      case 'pie':
        return {
          ...baseConfig,
          series: [{
            type: 'pie',
            radius: ['40%', '70%'],
            data: data.map(item => ({
              name: item.name || item.label,
              value: item.value || item.count
            })),
            itemStyle: {
              borderRadius: 8,
              borderColor: '#fff',
              borderWidth: 2
            }
          }]
        };
      
      case 'bar':
        return {
          ...baseConfig,
          xAxis: {
            type: 'category',
            data: data.map(item => item.name || item.label)
          },
          yAxis: {
            type: 'value'
          },
          series: [{
            type: 'bar',
            data: data.map(item => item.value || item.count),
            itemStyle: {
              borderRadius: [4, 4, 0, 0]
            }
          }]
        };
      
      case 'line':
        return {
          ...baseConfig,
          xAxis: {
            type: 'category',
            data: data.map(item => item.name || item.label)
          },
          yAxis: {
            type: 'value'
          },
          series: [{
            type: 'line',
            data: data.map(item => item.value || item.count),
            smooth: true,
            lineStyle: {
              width: 3
            },
            symbol: 'circle',
            symbolSize: 8
          }]
        };
      
      default:
        return baseConfig;
    }
  }

  onMount(async () => {
    if (chartType !== 'table') {
      // 这里应该初始化图表库 (如 ECharts)
      // 暂时用占位符代替
      console.log('Chart config:', getChartConfig());
    }
  });

  $: if (data && chart && chartType !== 'table') {
    // 更新图表数据
    chart.setOption(getChartConfig());
  }
</script>

<div class="metric-card bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
  <div class="flex items-center justify-between mb-6">
    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{title}</h3>
    {#if icon}
      <svelte:component this={icon} class="w-5 h-5 text-gray-500" />
    {/if}
  </div>

  {#if error}
    <div class="flex items-center text-red-500 dark:text-red-400 text-sm mb-4">
      <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
      </svg>
      {error}
    </div>
  {/if}

  {#if isLoading}
    <div class="animate-pulse">
      <div class="h-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
    </div>
  {:else if chartType === 'table'}
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
        <thead class="bg-gray-50 dark:bg-gray-800">
          <tr>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              项目
            </th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              数值
            </th>
          </tr>
        </thead>
        <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
          {#each data as item}
            <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
              <td class="px-4 py-3 text-sm text-gray-900 dark:text-white">
                {item.name || item.label}
              </td>
              <td class="px-4 py-3 text-sm text-gray-900 dark:text-white">
                {item.value || item.count}
              </td>
            </tr>
          {/each}
        </tbody>
      </table>
    </div>
  {:else}
    <div bind:this={chartContainer} style="height: {height};" class="w-full">
      <!-- 图表容器 - 实际项目中这里会用 ECharts 或其他图表库 -->
      <div class="flex items-center justify-center h-full text-gray-500 dark:text-gray-400">
        <div class="text-center">
          <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
            📊
          </div>
          <p>图表数据: {data.length} 项</p>
        </div>
      </div>
    </div>
  {/if}
</div>

<style>
  .metric-card {
    transition: transform 0.2s, box-shadow 0.2s;
  }
  
  .metric-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 8px 25px -8px rgba(0, 0, 0, 0.15);
  }
</style> 