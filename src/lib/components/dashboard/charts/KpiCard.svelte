<script lang="ts">
  // 属性
  export let title: string;
  export let value: number;
  export let unit: string = '';
  export let subtitle: string = '';
  export let format: 'number' | 'currency' | 'percentage' | 'duration' = 'number';
  export let icon: any = null;
  export let color: 'blue' | 'green' | 'red' | 'yellow' | 'purple' | 'indigo' | 'pink' | 'gray' = 'blue';
  export let trend: 'up' | 'down' | 'neutral' | null = null;
  export let trendValue: number | null = null;
  export let trendLabel: string = '较上期';
  export let isLoading: boolean = false;

  // 格式化数值
  function formatValue(value: number, format: string): string {
    switch (format) {
      case 'currency':
        return new Intl.NumberFormat('zh-CN', {
          style: 'currency',
          currency: 'CNY',
          minimumFractionDigits: 0,
          maximumFractionDigits: 0
        }).format(value);
      case 'percentage':
        return `${value}%`;
      case 'duration':
        // 假设值是天数，转换为更友好的格式
        if (value < 30) {
          return `${value} 天`;
        } else if (value < 365) {
          const months = Math.round(value / 30);
          return `${months} 个月`;
        } else {
          const years = Math.round(value / 365);
          return `${years} 年`;
        }
      default:
        return new Intl.NumberFormat('zh-CN').format(value);
    }
  }

  // 颜色映射
  const colorMap = {
    blue: {
      bg: 'bg-blue-50 dark:bg-blue-900/20',
      text: 'text-blue-600 dark:text-blue-400',
      icon: 'text-blue-500 dark:text-blue-300',
      border: 'border-blue-100 dark:border-blue-800'
    },
    green: {
      bg: 'bg-green-50 dark:bg-green-900/20',
      text: 'text-green-600 dark:text-green-400',
      icon: 'text-green-500 dark:text-green-300',
      border: 'border-green-100 dark:border-green-800'
    },
    red: {
      bg: 'bg-red-50 dark:bg-red-900/20',
      text: 'text-red-600 dark:text-red-400',
      icon: 'text-red-500 dark:text-red-300',
      border: 'border-red-100 dark:border-red-800'
    },
    yellow: {
      bg: 'bg-yellow-50 dark:bg-yellow-900/20',
      text: 'text-yellow-600 dark:text-yellow-400',
      icon: 'text-yellow-500 dark:text-yellow-300',
      border: 'border-yellow-100 dark:border-yellow-800'
    },
    purple: {
      bg: 'bg-purple-50 dark:bg-purple-900/20',
      text: 'text-purple-600 dark:text-purple-400',
      icon: 'text-purple-500 dark:text-purple-300',
      border: 'border-purple-100 dark:border-purple-800'
    },
    indigo: {
      bg: 'bg-indigo-50 dark:bg-indigo-900/20',
      text: 'text-indigo-600 dark:text-indigo-400',
      icon: 'text-indigo-500 dark:text-indigo-300',
      border: 'border-indigo-100 dark:border-indigo-800'
    },
    pink: {
      bg: 'bg-pink-50 dark:bg-pink-900/20',
      text: 'text-pink-600 dark:text-pink-400',
      icon: 'text-pink-500 dark:text-pink-300',
      border: 'border-pink-100 dark:border-pink-800'
    },
    gray: {
      bg: 'bg-gray-50 dark:bg-gray-800/40',
      text: 'text-gray-600 dark:text-gray-300',
      icon: 'text-gray-500 dark:text-gray-400',
      border: 'border-gray-100 dark:border-gray-700'
    }
  };

  // 趋势颜色和图标
  const trendConfig = {
    up: {
      color: 'text-green-500 dark:text-green-400',
      icon: '↑'
    },
    down: {
      color: 'text-red-500 dark:text-red-400',
      icon: '↓'
    },
    neutral: {
      color: 'text-gray-500 dark:text-gray-400',
      icon: '→'
    }
  };
</script>

<div class="kpi-card p-5 rounded-lg border {colorMap[color].border} {colorMap[color].bg} h-full">
  <div class="flex justify-between items-start mb-3">
    <div class="flex-1">
      <h3 class="text-sm font-medium text-gray-600 dark:text-gray-300">{title}</h3>
      {#if subtitle}
        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">{subtitle}</p>
      {/if}
    </div>
    {#if icon}
      <div class="p-2 rounded-full {colorMap[color].bg} {colorMap[color].icon}">
        <svelte:component this={icon} size={18} />
      </div>
    {/if}
  </div>
  
  <div class="mb-2">
    {#if isLoading}
      <div class="h-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
    {:else}
      <div class="flex items-end">
        <span class="text-2xl font-bold {colorMap[color].text}">{formatValue(value, format)}</span>
        {#if unit}
          <span class="ml-1 text-sm text-gray-500 dark:text-gray-400">{unit}</span>
        {/if}
      </div>
    {/if}
  </div>
  
  {#if trend && trendValue !== null}
    <div class="flex items-center text-xs">
      <span class="{trendConfig[trend].color} font-medium mr-1">
        {trendConfig[trend].icon} {trendValue}%
      </span>
      <span class="text-gray-500 dark:text-gray-400">{trendLabel}</span>
    </div>
  {/if}
</div>

<style>
  .kpi-card {
    transition: transform 0.2s, box-shadow 0.2s;
  }
  
  .kpi-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }
</style>
