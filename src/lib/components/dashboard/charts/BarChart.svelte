<script lang="ts">
  import { onMount, onDestroy } from 'svelte';
  import * as echarts from 'echarts/core';
  import { BarChart as EChartsBar } from 'echarts/charts';
  import {
    TitleComponent,
    TooltipComponent,
    LegendComponent,
    GridComponent
  } from 'echarts/components';
  import { CanvasRenderer } from 'echarts/renderers';

  // 注册必要的组件
  echarts.use([
    TitleComponent,
    TooltipComponent,
    LegendComponent,
    GridComponent,
    EChartsBar,
    CanvasRenderer
  ]);

  // 属性
  const { options = {}, height = '300px', width = '100%', theme = 'light' } = $props<{
    options: any;
    height?: string;
    width?: string;
    theme?: string | object;
  }>();

  // 状态
  let chartDom = $state<HTMLDivElement | null>(null);
  let chart = $state<echarts.ECharts | null>(null);
  let resizeObserver = $state<ResizeObserver | null>(null);

  // 初始化图表
  function initChart() {
    if (!chartDom) return;

    chart = echarts.init(chartDom, theme);
    chart.setOption(options);
  }

  // 更新图表
  $effect(() => {
    if (chart && options) {
      chart.setOption(options, true);
    }
  });

  // 组件挂载时初始化图表
  onMount(() => {
    initChart();

    // 监听容器大小变化
    resizeObserver = new ResizeObserver(() => {
      chart?.resize();
    });

    if (chartDom) {
      resizeObserver.observe(chartDom);
    }
  });

  // 组件销毁时清理资源
  onDestroy(() => {
    if (resizeObserver && chartDom) {
      resizeObserver.unobserve(chartDom);
      resizeObserver.disconnect();
    }
    chart?.dispose();
  });
</script>

<div bind:this={chartDom} style="width: {width}; height: {height};" class="echarts-container"></div>

<style>
  .echarts-container {
    min-height: 200px;
  }
</style>
