<script lang="ts">
  // 属性
  export let data: any[] = [];
  export let columns: { key: string; label: string; formatter?: (value: any) => string }[] = [];
  export let isLoading: boolean = false;
  export let emptyMessage: string = '暂无数据';
  export let onRowClick: ((row: any) => void) | null = null;
</script>

<div class="overflow-x-auto">
  <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
    <thead class="bg-gray-50 dark:bg-gray-800">
      <tr>
        {#each columns as column}
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
            {column.label}
          </th>
        {/each}
      </tr>
    </thead>
    <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-800">
      {#if isLoading}
        {#each Array(5) as _}
          <tr>
            {#each columns as _}
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
              </td>
            {/each}
          </tr>
        {/each}
      {:else if data.length === 0}
        <tr>
          <td colspan={columns.length} class="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400">
            {emptyMessage}
          </td>
        </tr>
      {:else}
        {#each data as row}
          <tr 
            class={onRowClick ? 'cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800' : ''}
            on:click={() => onRowClick && onRowClick(row)}
          >
            {#each columns as column}
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                {#if column.formatter}
                  {@html column.formatter(row[column.key])}
                {:else}
                  {row[column.key]}
                {/if}
              </td>
            {/each}
          </tr>
        {/each}
      {/if}
    </tbody>
  </table>
</div>

<style>
  /* 添加一些响应式样式 */
  @media (max-width: 640px) {
    table {
      display: block;
      overflow-x: auto;
    }
  }
</style>
