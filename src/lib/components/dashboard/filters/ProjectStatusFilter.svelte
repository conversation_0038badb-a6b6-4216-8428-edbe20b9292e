<script lang="ts">
  import { onMount, createEventDispatcher } from 'svelte';
  import { mcp_sqlite_explorer_read_query } from '$lib/services/sqliteExplorerService';

  // 属性
  const { label = '项目状态' } = $props();
  let selectedStatusIds = $state<number[]>([]);

  // 状态
  let statusOptions = $state<{ item_id: number; item_value: string }[]>([]);
  let isLoading = $state(true);
  let error = $state<string | null>(null);

  // 事件处理
  const dispatch = createEventDispatcher<{change: {status_ids?: number[]};}>();

  // 加载项目状态选项
  async function loadStatusOptions() {
    try {
      isLoading = true;
      error = null;

      // 获取研究阶段字典项（使用字典名称查询）
      const results = await mcp_sqlite_explorer_read_query({
        query: `SELECT di.item_id, di.item_value 
                FROM dictionary_items di 
                JOIN dictionaries d ON di.dictionary_id = d.id 
                WHERE d.name = '研究阶段' AND di.status = 'active'
                ORDER BY di.item_id`,
        fetch_all: true
      });

      statusOptions = results.map((item: any) => ({
        item_id: item.item_id,
        item_value: item.item_value
      }));

      console.log('项目状态选项加载完成（研究阶段）:', statusOptions);

      // 默认全部选中
      selectedStatusIds = statusOptions.map(option => option.item_id);
      
      // 触发初始变更事件，全选时发送undefined表示无筛选
      dispatch('change', { status_ids: undefined });

    } catch (err: any) {
      error = err.message || '加载项目状态选项失败';
      console.error('加载项目状态选项失败:', err);
    } finally {
      isLoading = false;
    }
  }

  // 处理状态变更
  function handleStatusChange(statusId: number, checked: boolean) {
    if (checked) {
      selectedStatusIds = [...selectedStatusIds, statusId];
    } else {
      selectedStatusIds = selectedStatusIds.filter((id: number) => id !== statusId);
    }

    // 如果选中了所有选项，则发送undefined表示无筛选
    // 如果只选中了部分选项，则发送选中的ID数组
    dispatch('change', {
      status_ids: selectedStatusIds.length === statusOptions.length ? undefined : 
                  selectedStatusIds.length > 0 ? [...selectedStatusIds] : undefined
    });
  }

  // 全选功能
  function selectAll() {
    selectedStatusIds = [...statusOptions.map(option => option.item_id)];
    // 全选时发送undefined表示无筛选
    dispatch('change', { status_ids: undefined });
  }

  // 清除状态筛选
  function clearStatusFilter() {
    selectedStatusIds = [];
    dispatch('change', { status_ids: undefined });
  }

  // 组件挂载时加载状态选项
  onMount(() => {
    loadStatusOptions();
  });
</script>

<div class="project-status-filter">
  <!-- 标题和操作按钮 -->
  <div class="flex justify-between items-center mb-3">
    <h4 class="text-sm font-medium text-gray-900 dark:text-white">
      {label}
    </h4>
    <div class="flex gap-1">
      {#if selectedStatusIds.length === 0}
        <button
          type="button"
          onclick={() => selectAll()}
          class="text-xs px-2 py-1 bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-400 rounded hover:bg-green-200 dark:hover:bg-green-900/30 transition-colors"
        >
          全选
        </button>
      {/if}
      {#if selectedStatusIds.length > 0 && selectedStatusIds.length < statusOptions.length}
        <button
          type="button"
          onclick={() => clearStatusFilter()}
          class="text-xs px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
        >
          清除
        </button>
      {/if}
    </div>
  </div>

  <!-- 状态指示器 -->
  {#if selectedStatusIds.length > 0}
    <div class="mb-3 px-2 py-1 bg-blue-50 dark:bg-blue-900/20 rounded text-xs text-blue-700 dark:text-blue-300">
      已选择 <span class="font-medium">{selectedStatusIds.length}</span>/{statusOptions.length} 个状态
    </div>
  {/if}

  <!-- 内容区域 -->
  {#if isLoading}
    <div class="flex items-center justify-center py-4">
      <div class="animate-spin h-4 w-4 border-2 border-blue-500 rounded-full border-t-transparent"></div>
      <span class="ml-2 text-sm text-gray-500 dark:text-gray-400">加载中...</span>
    </div>
  {:else if error}
    <div class="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded text-sm text-red-700 dark:text-red-400">
      {error}
    </div>
  {:else}
    <div class="space-y-1">
      {#each statusOptions as status}
        <label class="flex items-center p-2 rounded hover:bg-white dark:hover:bg-gray-700 cursor-pointer transition-colors">
          <input
            type="checkbox"
            checked={selectedStatusIds.includes(status.item_id)}
            onchange={(e) => handleStatusChange(status.item_id, e.currentTarget.checked)}
            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded mr-3 flex-shrink-0"
          />
          <span class="text-sm text-gray-700 dark:text-gray-300 leading-tight">
            {status.item_value}
          </span>
        </label>
      {/each}
    </div>
  {/if}
</div>
