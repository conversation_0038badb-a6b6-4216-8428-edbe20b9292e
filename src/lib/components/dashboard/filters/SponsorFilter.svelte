<script lang="ts">
  import { onMount, createEventDispatcher } from 'svelte';
  import { mcp_sqlite_explorer_read_query } from '$lib/services/sqliteExplorerService';

  // 属性
  const { label = '申办方' } = $props();
  let selectedSponsorIds = $state<number[]>([]);

  // 状态
  let sponsorOptions = $state<{ item_id: number; item_value: string }[]>([]);
  let isLoading = $state(true);
  let error = $state<string | null>(null);
  let searchTerm = $state('');
  let filteredOptions = $state<typeof sponsorOptions>([]);

  // 事件处理
  const dispatch = createEventDispatcher<{change: {sponsor_ids?: number[]};}>();

  // 加载申办方选项
  async function loadSponsorOptions() {
    try {
      isLoading = true;
      error = null;

      // 获取申办方字典项（使用字典名称查询）
      const results = await mcp_sqlite_explorer_read_query({
        query: `SELECT di.item_id, di.item_value 
                FROM dictionary_items di 
                JOIN dictionaries d ON di.dictionary_id = d.id 
                WHERE d.name = '申办方' AND di.status = 'active'
                ORDER BY di.item_value`,
        fetch_all: true
      });

      sponsorOptions = results.map((item: any) => ({
        item_id: item.item_id,
        item_value: item.item_value
      }));

      filteredOptions = [...sponsorOptions];

      console.log('申办方选项加载完成:', sponsorOptions);

      // 默认全部选中
      selectedSponsorIds = sponsorOptions.map(option => option.item_id);
      
      // 触发初始变更事件，全选时发送undefined表示无筛选
      dispatch('change', { sponsor_ids: undefined });

    } catch (err: any) {
      error = err.message || '加载申办方选项失败';
      console.error('加载申办方选项失败:', err);
    } finally {
      isLoading = false;
    }
  }

  // 处理申办方变更
  function handleSponsorChange(sponsorId: number, checked: boolean) {
    if (checked) {
      selectedSponsorIds = [...selectedSponsorIds, sponsorId];
    } else {
      selectedSponsorIds = selectedSponsorIds.filter((id: number) => id !== sponsorId);
    }

    // 如果选中了所有选项，则发送undefined表示无筛选
    dispatch('change', {
      sponsor_ids: selectedSponsorIds.length === sponsorOptions.length ? undefined : 
                   selectedSponsorIds.length > 0 ? [...selectedSponsorIds] : undefined
    });
  }

  // 全选功能
  function selectAll() {
    selectedSponsorIds = [...sponsorOptions.map(option => option.item_id)];
    // 全选时发送undefined表示无筛选
    dispatch('change', { sponsor_ids: undefined });
  }

  // 清除申办方筛选
  function clearSponsorFilter() {
    selectedSponsorIds = [];
    dispatch('change', { sponsor_ids: undefined });
  }

  // 筛选申办方选项
  $effect(() => {
    if (searchTerm.trim() === '') {
      filteredOptions = sponsorOptions;
    } else {
      const term = searchTerm.toLowerCase();
      filteredOptions = sponsorOptions.filter(sponsor =>
        sponsor.item_value.toLowerCase().includes(term)
      );
    }
  });

  // 组件挂载时加载申办方选项
  onMount(() => {
    loadSponsorOptions();
  });
</script>

<div class="sponsor-filter">
  <!-- 标题和操作按钮 -->
  <div class="flex justify-between items-center mb-3">
    <h4 class="text-sm font-medium text-gray-900 dark:text-white">
      {label}
    </h4>
    <div class="flex gap-1">
      {#if selectedSponsorIds.length === 0}
        <button
          type="button"
          onclick={() => selectAll()}
          class="text-xs px-2 py-1 bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-400 rounded hover:bg-green-200 dark:hover:bg-green-900/30 transition-colors"
        >
          全选
        </button>
      {/if}
      {#if selectedSponsorIds.length > 0 && selectedSponsorIds.length < sponsorOptions.length}
        <button
          type="button"
          onclick={() => clearSponsorFilter()}
          class="text-xs px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
        >
          清除
        </button>
      {/if}
    </div>
  </div>

  <!-- 状态指示器 -->
  {#if selectedSponsorIds.length > 0}
    <div class="mb-3 px-2 py-1 bg-blue-50 dark:bg-blue-900/20 rounded text-xs text-blue-700 dark:text-blue-300">
      已选择 <span class="font-medium">{selectedSponsorIds.length}</span>/{sponsorOptions.length} 个申办方
    </div>
  {/if}

  <!-- 内容区域 -->
  {#if isLoading}
    <div class="flex items-center justify-center py-4">
      <div class="animate-spin h-4 w-4 border-2 border-blue-500 rounded-full border-t-transparent"></div>
      <span class="ml-2 text-sm text-gray-500 dark:text-gray-400">加载中...</span>
    </div>
  {:else if error}
    <div class="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded text-sm text-red-700 dark:text-red-400">
      {error}
    </div>
  {:else}
    <!-- 搜索框 -->
    <div class="mb-2">
      <input
        type="text"
        placeholder="搜索申办方..."
        bind:value={searchTerm}
        class="block w-full px-3 py-1.5 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-colors"
      />
    </div>

    <!-- 申办方统计信息 -->
    {#if searchTerm && filteredOptions.length < sponsorOptions.length}
      <div class="mb-2 text-xs text-gray-600 dark:text-gray-400">
        显示 {filteredOptions.length} / {sponsorOptions.length} 个申办方
      </div>
    {/if}

    <!-- 申办方列表 -->
    <div class="max-h-32 overflow-y-auto space-y-1 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600">
      {#if filteredOptions.length === 0}
        <div class="text-sm text-gray-500 dark:text-gray-400 text-center py-3">
          {searchTerm ? '未找到匹配的申办方' : '暂无申办方数据'}
        </div>
      {:else}
        {#each filteredOptions as sponsor}
          <label class="flex items-center px-2 py-1.5 rounded hover:bg-white dark:hover:bg-gray-700 cursor-pointer transition-colors">
            <input
              type="checkbox"
              checked={selectedSponsorIds.includes(sponsor.item_id)}
              onchange={(e) => handleSponsorChange(sponsor.item_id, e.currentTarget.checked)}
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded mr-2 flex-shrink-0"
            />
            <span class="text-sm text-gray-700 dark:text-gray-300 leading-tight truncate">
              {sponsor.item_value}
            </span>
          </label>
        {/each}
      {/if}
    </div>

    <!-- 快速选择提示 -->
    {#if filteredOptions.length > 10 && searchTerm === ''}
      <div class="mt-2 text-xs text-amber-600 dark:text-amber-400 bg-amber-50 dark:bg-amber-900/20 px-2 py-1 rounded">
        💡 申办方较多，建议使用搜索功能快速定位
      </div>
    {/if}
  {/if}
</div>

<style>
  /* 自定义滚动条样式 */
  .scrollbar-thin {
    scrollbar-width: thin;
  }
  
  .scrollbar-thumb-gray-300::-webkit-scrollbar {
    width: 6px;
  }
  
  .scrollbar-thumb-gray-300::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
    background-color: #d1d5db;
    border-radius: 3px;
  }
  
  .dark .scrollbar-thumb-gray-600::-webkit-scrollbar-thumb {
    background-color: #4b5563;
  }
</style>
