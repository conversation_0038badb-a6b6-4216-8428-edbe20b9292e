<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import type { DateRangeParams } from '$lib/services/dashboardService';

  // 属性
  const {
    startDate = $bindable<string | undefined>(undefined),
    endDate = $bindable<string | undefined>(undefined),
    startLabel = '开始日期',
    endLabel = '结束日期'
  } = $props();

  // 事件
  const dispatch = createEventDispatcher();

  // 处理日期变更
  function handleDateChange() {
    dispatch('change', {
      start_date: startDate,
      end_date: endDate
    });
  }

  // 清除日期筛选
  function clearDates() {
    startDate.set(undefined);
    endDate.set(undefined);
    handleDateChange();
  }
</script>

<div class="date-range-filter">
  <div class="flex flex-col sm:flex-row gap-4 items-end">
    <div class="flex-1">
      <label for="start-date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
        {startLabel}
      </label>
      <input
        type="date"
        id="start-date"
        value={startDate}
        onchange={(e) => { startDate.set(e.currentTarget.value); handleDateChange(); }}
        class="block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
      />
    </div>

    <div class="flex-1">
      <label for="end-date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
        {endLabel}
      </label>
      <input
        type="date"
        id="end-date"
        value={endDate}
        onchange={(e) => { endDate.set(e.currentTarget.value); handleDateChange(); }}
        class="block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
      />
    </div>

    <button
      type="button"
      onclick={() => clearDates()}
      class="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
    >
      清除
    </button>
  </div>
</div>
