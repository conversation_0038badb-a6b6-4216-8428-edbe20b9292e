<script lang="ts">
  import { onMount, createEventDispatcher } from 'svelte';
  import { mcp_sqlite_explorer_read_query } from '$lib/services/sqliteExplorerService';

  // 属性
  const { label = '疾病领域' } = $props();
  let selectedDiseaseIds = $state<number[]>([]);

  // 状态
  let diseaseOptions = $state<{ item_id: number; item_value: string }[]>([]);
  let isLoading = $state(true);
  let error = $state<string | null>(null);

  // 事件处理
  const dispatch = createEventDispatcher<{change: {disease_ids?: number[]};}>();

  // 加载疾病领域选项
  async function loadDiseaseOptions() {
    try {
      isLoading = true;
      error = null;

      // 获取疾病字典项（使用字典名称查询）
      const results = await mcp_sqlite_explorer_read_query({
        query: `SELECT di.item_id, di.item_value 
                FROM dictionary_items di 
                JOIN dictionaries d ON di.dictionary_id = d.id 
                WHERE d.name = '疾病' AND di.status = 'active'
                ORDER BY di.item_value`,
        fetch_all: true
      });

      diseaseOptions = results.map((item: any) => ({
        item_id: item.item_id,
        item_value: item.item_value
      }));

      console.log('疾病领域选项加载完成:', diseaseOptions);

      // 默认全部选中
      selectedDiseaseIds = diseaseOptions.map(option => option.item_id);
      
      // 触发初始变更事件，全选时发送undefined表示无筛选
      dispatch('change', { disease_ids: undefined });

    } catch (err: any) {
      error = err.message || '加载疾病领域选项失败';
      console.error('加载疾病领域选项失败:', err);
    } finally {
      isLoading = false;
    }
  }

  // 处理疾病变更
  function handleDiseaseChange(diseaseId: number, checked: boolean) {
    if (checked) {
      selectedDiseaseIds = [...selectedDiseaseIds, diseaseId];
    } else {
      selectedDiseaseIds = selectedDiseaseIds.filter((id: number) => id !== diseaseId);
    }

    // 如果选中了所有选项，则发送undefined表示无筛选
    dispatch('change', {
      disease_ids: selectedDiseaseIds.length === diseaseOptions.length ? undefined : 
                   selectedDiseaseIds.length > 0 ? [...selectedDiseaseIds] : undefined
    });
  }

  // 全选功能
  function selectAll() {
    selectedDiseaseIds = [...diseaseOptions.map(option => option.item_id)];
    // 全选时发送undefined表示无筛选
    dispatch('change', { disease_ids: undefined });
  }

  // 清除疾病筛选
  function clearDiseaseFilter() {
    selectedDiseaseIds = [];
    dispatch('change', { disease_ids: undefined });
  }

  // 组件挂载时加载疾病选项
  onMount(() => {
    loadDiseaseOptions();
  });
</script>

<div class="disease-filter">
  <!-- 标题和操作按钮 -->
  <div class="flex justify-between items-center mb-3">
    <h4 class="text-sm font-medium text-gray-900 dark:text-white">
      {label}
    </h4>
    <div class="flex gap-1">
      {#if selectedDiseaseIds.length === 0}
        <button
          type="button"
          onclick={() => selectAll()}
          class="text-xs px-2 py-1 bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-400 rounded hover:bg-green-200 dark:hover:bg-green-900/30 transition-colors"
        >
          全选
        </button>
      {/if}
      {#if selectedDiseaseIds.length > 0 && selectedDiseaseIds.length < diseaseOptions.length}
        <button
          type="button"
          onclick={() => clearDiseaseFilter()}
          class="text-xs px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
        >
          清除
        </button>
      {/if}
    </div>
  </div>

  <!-- 状态指示器 -->
  {#if selectedDiseaseIds.length > 0}
    <div class="mb-3 px-2 py-1 bg-blue-50 dark:bg-blue-900/20 rounded text-xs text-blue-700 dark:text-blue-300">
      已选择 <span class="font-medium">{selectedDiseaseIds.length}</span>/{diseaseOptions.length} 个领域
    </div>
  {/if}

  <!-- 内容区域 -->
  {#if isLoading}
    <div class="flex items-center justify-center py-4">
      <div class="animate-spin h-4 w-4 border-2 border-blue-500 rounded-full border-t-transparent"></div>
      <span class="ml-2 text-sm text-gray-500 dark:text-gray-400">加载中...</span>
    </div>
  {:else if error}
    <div class="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded text-sm text-red-700 dark:text-red-400">
      {error}
    </div>
  {:else}
    <div class="max-h-32 overflow-y-auto space-y-1 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600">
      {#each diseaseOptions as disease}
        <label class="flex items-center p-2 rounded hover:bg-white dark:hover:bg-gray-700 cursor-pointer transition-colors">
          <input
            type="checkbox"
            checked={selectedDiseaseIds.includes(disease.item_id)}
            onchange={(e) => handleDiseaseChange(disease.item_id, e.currentTarget.checked)}
            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded mr-3 flex-shrink-0"
          />
          <span class="text-sm text-gray-700 dark:text-gray-300 leading-tight">
            {disease.item_value}
          </span>
        </label>
      {/each}
    </div>
  {/if}
</div>

<style>
  /* 自定义滚动条样式 */
  .scrollbar-thin {
    scrollbar-width: thin;
  }
  
  .scrollbar-thumb-gray-300::-webkit-scrollbar {
    width: 6px;
  }
  
  .scrollbar-thumb-gray-300::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
    background-color: #d1d5db;
    border-radius: 3px;
  }
  
  .dark .scrollbar-thumb-gray-600::-webkit-scrollbar-thumb {
    background-color: #4b5563;
  }
</style>
