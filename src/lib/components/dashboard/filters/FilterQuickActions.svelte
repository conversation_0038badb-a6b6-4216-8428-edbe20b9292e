<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import { RotateCcw, Filter, Eye, EyeOff } from 'lucide-svelte';

  // 属性
  const { 
    activeFiltersCount = 0,
    totalFiltersCount = 0,
    isVisible = false
  } = $props<{
    activeFiltersCount?: number;
    totalFiltersCount?: number;
    isVisible?: boolean;
  }>();

  // 事件处理
  const dispatch = createEventDispatcher<{
    reset: void;
    toggle: void;
    preset: { type: string };
  }>();

  // 预设筛选方案
  const presets = [
    { id: 'active', label: '活动项目', description: '在研 + 招募中' },
    { id: 'recruiting', label: '招募项目', description: '招募中状态' },
    { id: 'completed', label: '已完成项目', description: '已结束状态' },
    { id: 'oncology', label: '肿瘤项目', description: '肿瘤学领域' }
  ];

  function handleReset() {
    dispatch('reset');
  }

  function handleToggle() {
    dispatch('toggle');
  }

  function handlePreset(type: string) {
    dispatch('preset', { type });
  }
</script>

<div class="filter-quick-actions bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
  <!-- 筛选器状态 -->
  <div class="flex items-center justify-between mb-4">
    <div class="flex items-center space-x-3">
      <Filter class="w-5 h-5 text-gray-500" />
      <div>
        <div class="text-sm font-medium text-gray-900 dark:text-white">
          筛选器状态
        </div>
        <div class="text-xs text-gray-500 dark:text-gray-400">
          {activeFiltersCount} / {totalFiltersCount} 个筛选器已激活
        </div>
      </div>
    </div>
    
    <div class="flex items-center space-x-2">
      <button
        type="button"
        onclick={handleToggle}
        class="inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-md transition-colors
               {isVisible 
                 ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300' 
                 : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'} 
               hover:bg-opacity-80"
        title={isVisible ? '隐藏筛选器' : '显示筛选器'}
      >
        {#if isVisible}
          <EyeOff class="w-3 h-3 mr-1" />
          隐藏
        {:else}
          <Eye class="w-3 h-3 mr-1" />
          显示
        {/if}
      </button>
      
      {#if activeFiltersCount > 0}
        <button
          type="button"
          onclick={handleReset}
          class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-red-700 dark:text-red-300 bg-red-100 dark:bg-red-900 rounded-md hover:bg-red-200 dark:hover:bg-red-800 transition-colors"
          title="重置所有筛选器"
        >
          <RotateCcw class="w-3 h-3 mr-1" />
          重置
        </button>
      {/if}
    </div>
  </div>

  <!-- 快速筛选预设 -->
  {#if isVisible}
    <div class="border-t border-gray-200 dark:border-gray-700 pt-4">
      <div class="text-sm font-medium text-gray-900 dark:text-white mb-3">
        快速筛选预设
      </div>
      <div class="grid grid-cols-2 gap-2">
        {#each presets as preset}
          <button
            type="button"
            onclick={() => handlePreset(preset.id)}
            class="text-left p-3 rounded-lg border border-gray-200 dark:border-gray-600 hover:border-blue-300 dark:hover:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors group"
          >
            <div class="text-sm font-medium text-gray-900 dark:text-white group-hover:text-blue-700 dark:group-hover:text-blue-300">
              {preset.label}
            </div>
            <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
              {preset.description}
            </div>
          </button>
        {/each}
      </div>
    </div>
  {/if}
</div> 