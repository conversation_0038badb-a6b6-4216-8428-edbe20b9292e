<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import { Filter, X, RotateCcw, Settings } from 'lucide-svelte';
  
  // 导入筛选组件
  import ProjectStatusFilter from './ProjectStatusFilter.svelte';
  import ProjectStageFilter from './ProjectStageFilter.svelte';
  import RecruitmentStatusFilter from './RecruitmentStatusFilter.svelte';
  import SponsorFilter from './SponsorFilter.svelte';
  import DiseaseFilter from './DiseaseFilter.svelte';
  
  import type { FlatDashboardFilterParams } from '$lib/services/dashboardService';
  
  // 属性定义（使用 Svelte 5 语法）
  const {
    currentFilters = $bindable<FlatDashboardFilterParams>({})
  } = $props();
  
  const dispatch = createEventDispatcher<{
    filterChange: FlatDashboardFilterParams;
    clearAll: void;
  }>();
  
  // 内部状态
  let isVisible = $state(false);
  let internalFilters = $state<FlatDashboardFilterParams>({});
  
  // 活跃筛选计数
  let activeFilterCount = $derived(countActiveFilters(internalFilters));
  
  function countActiveFilters(filters: FlatDashboardFilterParams): number {
    let count = 0;
    if (filters.start_date || filters.end_date) count++;
    if (filters.status_ids?.length) count++;
    if (filters.stage_ids?.length) count++;
    if (filters.recruitment_ids?.length) count++;
    if (filters.sponsor_ids?.length) count++;
    if (filters.disease_ids?.length) count++;
    return count;
  }
  
  function handleFilterChange(filterType: string, filterData: any) {
    console.log(`筛选面板: ${filterType} 变更`, filterData);
    
    // 根据筛选类型更新内部状态（使用扁平化结构）
    if (filterType === 'project_status') {
      internalFilters = { ...internalFilters, status_ids: filterData.status_ids };
    } else if (filterType === 'project_stage') {
      internalFilters = { ...internalFilters, stage_ids: filterData.stage_ids };
    } else if (filterType === 'recruitment_status') {
      internalFilters = { ...internalFilters, recruitment_ids: filterData.recruitment_ids };
    } else if (filterType === 'sponsor') {
      internalFilters = { ...internalFilters, sponsor_ids: filterData.sponsor_ids };
    } else if (filterType === 'disease') {
      internalFilters = { ...internalFilters, disease_ids: filterData.disease_ids };
    } else if (filterType === 'date_range') {
      internalFilters = { 
        ...internalFilters, 
        start_date: filterData.start_date,
        end_date: filterData.end_date
      };
    }
    
    console.log('筛选面板: 更新后的内部筛选状态', internalFilters);
    
    // 立即应用筛选
    dispatch('filterChange', internalFilters);
  }
  
  function clearAllFilters() {
    internalFilters = {};
    dispatch('clearAll');
  }
  
  function togglePanel() {
    isVisible = !isVisible;
  }
  
  function handleBackdropClick(e: MouseEvent) {
    if (e.target === e.currentTarget) {
      isVisible = false;
    }
  }
</script>

<!-- 筛选按钮 - 浮动在右侧 -->
<div class="fixed top-4 right-4 z-40">
  <button
    onclick={togglePanel}
    class="flex items-center gap-2 px-4 py-3 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-full shadow-lg hover:shadow-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200"
  >
    <Settings class="w-5 h-5 text-gray-600 dark:text-gray-400" />
    <span class="font-medium text-gray-700 dark:text-gray-300">筛选</span>
    {#if activeFilterCount > 0}
      <span class="bg-blue-600 text-white text-xs px-2 py-1 rounded-full min-w-[20px] text-center">
        {activeFilterCount}
      </span>
    {/if}
  </button>
</div>

<!-- 背景遮罩 -->
{#if isVisible}
  <div 
    class="fixed inset-0 bg-black bg-opacity-50 z-50 transition-opacity duration-300"
    onclick={handleBackdropClick}
  ></div>
{/if}

<!-- 侧边栏筛选面板 -->
<div class="fixed top-0 right-0 h-full w-96 bg-white dark:bg-gray-900 shadow-2xl z-50 transform transition-transform duration-300 {isVisible ? 'translate-x-0' : 'translate-x-full'} flex flex-col">
  <!-- 头部 -->
  <div class="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 flex-shrink-0">
    <div class="flex items-center gap-2">
      <Filter class="w-5 h-5 text-blue-600 dark:text-blue-400" />
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white">筛选条件</h3>
      {#if activeFilterCount > 0}
        <span class="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs px-2 py-1 rounded-full">
          {activeFilterCount}
        </span>
      {/if}
    </div>
    <button
      onclick={togglePanel}
      class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-lg transition-colors"
    >
      <X class="w-5 h-5" />
    </button>
  </div>
  
  <!-- 筛选内容区域 - 可滚动 -->
  <div class="flex-1 overflow-y-auto p-4 space-y-4 min-h-0 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-transparent">
    <!-- 项目状态筛选 -->
    <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
      <ProjectStatusFilter
        on:change={(e) => handleFilterChange('project_status', e.detail)}
      />
    </div>
    
    <!-- 项目阶段筛选 -->
    <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
      <ProjectStageFilter
        on:change={(e) => handleFilterChange('project_stage', e.detail)}
      />
    </div>
    
    <!-- 招募状态筛选 -->
    <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
      <RecruitmentStatusFilter
        on:change={(e) => handleFilterChange('recruitment_status', e.detail)}
      />
    </div>
    
    <!-- 申办方筛选 -->
    <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
      <SponsorFilter
        on:change={(e) => handleFilterChange('sponsor', e.detail)}
      />
    </div>
    
    <!-- 疾病领域筛选 -->
    <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
      <DiseaseFilter
        on:change={(e) => handleFilterChange('disease', e.detail)}
      />
    </div>
  </div>
  
  <!-- 底部操作区域 - 固定在底部 -->
  {#if activeFilterCount > 0}
    <div class="p-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 flex-shrink-0">
      <div class="flex items-center justify-between mb-3">
        <div class="text-sm text-gray-600 dark:text-gray-400">
          已应用 <span class="font-medium text-blue-600 dark:text-blue-400">{activeFilterCount}</span> 个筛选条件
        </div>
      </div>
      <button
        onclick={clearAllFilters}
        class="w-full flex items-center justify-center gap-2 px-4 py-2 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400 border border-red-200 dark:border-red-800 rounded-lg hover:bg-red-100 dark:hover:bg-red-900/30 transition-colors"
      >
        <RotateCcw class="w-4 h-4" />
        清除所有筛选
      </button>
    </div>
  {/if}
</div>

<style>
  /* 自定义滚动条样式 */
  .scrollbar-thin {
    scrollbar-width: thin;
  }
  
  .scrollbar-thumb-gray-300::-webkit-scrollbar {
    width: 8px;
  }
  
  .scrollbar-track-transparent::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
    background-color: #d1d5db;
    border-radius: 4px;
  }
  
  .scrollbar-thumb-gray-300::-webkit-scrollbar-thumb:hover {
    background-color: #9ca3af;
  }
  
  .dark .scrollbar-thumb-gray-600::-webkit-scrollbar-thumb {
    background-color: #4b5563;
  }
  
  .dark .scrollbar-thumb-gray-600::-webkit-scrollbar-thumb:hover {
    background-color: #6b7280;
  }
</style> 