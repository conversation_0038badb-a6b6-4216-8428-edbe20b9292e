<script lang="ts">
  import { cn } from "$lib/utils";
  import { createEventDispatcher } from "svelte";

  export let value: any = undefined;
  export let placeholder = "Select an option";
  export let disabled = false;
  
  let className = "";
  export { className as class };
  
  let isOpen = false;
  let selectedLabel = "";
  
  const dispatch = createEventDispatcher();
  
  function toggle() {
    if (!disabled) {
      isOpen = !isOpen;
    }
  }
  
  function select(val: any, label: string) {
    value = val;
    selectedLabel = label;
    isOpen = false;
    dispatch('change', { value });
  }
  
  function handleClickOutside(event: MouseEvent) {
    const target = event.target as HTMLElement;
    if (isOpen && !target.closest('.select-container')) {
      isOpen = false;
    }
  }
  
  // Add click outside listener
  if (typeof window !== 'undefined') {
    window.addEventListener('click', handleClickOutside);
  }
</script>

<svelte:window on:click={handleClickOutside} />

<div class={cn("select-container relative", className)}>
  <button
    type="button"
    class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
    on:click={toggle}
    disabled={disabled}
    aria-haspopup="listbox"
    aria-expanded={isOpen}
  >
    <span class="truncate">{selectedLabel || placeholder}</span>
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      stroke-width="2"
      stroke-linecap="round"
      stroke-linejoin="round"
      class="h-4 w-4 opacity-50"
      class:rotate-180={isOpen}
    >
      <polyline points="6 9 12 15 18 9"></polyline>
    </svg>
  </button>
  
  {#if isOpen}
    <div
      class="absolute z-50 mt-1 max-h-60 w-full overflow-auto rounded-md border bg-popover text-popover-foreground shadow-md"
      role="listbox"
    >
      <slot {select} />
    </div>
  {/if}
</div>

<script context="module">
  export const Root = (props) => {
    return {
      props,
      $$render: ($$result, $$props, $$bindings, slots) => {
        return `<div class="select-root">${slots.default ? slots.default({}) : ''}</div>`;
      }
    };
  };

  export const Trigger = (props) => {
    return {
      props,
      $$render: ($$result, $$props, $$bindings, slots) => {
        return `<div class="select-trigger">${slots.default ? slots.default({}) : ''}</div>`;
      }
    };
  };

  export const Value = (props) => {
    const { placeholder, ...rest } = props;
    return {
      props,
      $$render: ($$result, $$props, $$bindings, slots) => {
        return `<div class="select-value" placeholder="${placeholder || ''}" ${Object.entries(rest).map(([k, v]) => `${k}="${v}"`).join(' ')}>${slots.default ? slots.default({}) : ''}</div>`;
      }
    };
  };

  export const Content = (props) => {
    return {
      props,
      $$render: ($$result, $$props, $$bindings, slots) => {
        return `<div class="select-content">${slots.default ? slots.default({}) : ''}</div>`;
      }
    };
  };

  export const Item = (props) => {
    const { value, ...rest } = props;
    return {
      props,
      $$render: ($$result, $$props, $$bindings, slots) => {
        return `<div class="select-item px-2 py-1.5 text-sm rounded-sm hover:bg-accent hover:text-accent-foreground cursor-pointer" data-value="${value}" ${Object.entries(rest).map(([k, v]) => `${k}="${v}"`).join(' ')}>${slots.default ? slots.default({}) : ''}</div>`;
      }
    };
  };
</script>
