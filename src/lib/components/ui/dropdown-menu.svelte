<script lang="ts">
  import { createEventDispatcher, onMount } from 'svelte';
  import { cn } from "$lib/utils";
  
  export let buttonClass = "";
  export let menuClass = "";
  
  let isOpen = false;
  let menuRef: HTMLDivElement;
  let buttonRef: HTMLButtonElement;
  
  const dispatch = createEventDispatcher();
  
  function toggle() {
    isOpen = !isOpen;
    if (isOpen) {
      dispatch('open');
    } else {
      dispatch('close');
    }
  }
  
  function handleClickOutside(event: MouseEvent) {
    if (isOpen && menuRef && buttonRef && 
        !menuRef.contains(event.target as Node) && 
        !buttonRef.contains(event.target as Node)) {
      isOpen = false;
      dispatch('close');
    }
  }
  
  onMount(() => {
    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  });
</script>

<div class="relative inline-block">
  <button
    bind:this={buttonRef}
    type="button"
    class={cn("", buttonClass)}
    on:click|stopPropagation={toggle}
    aria-haspopup="true"
    aria-expanded={isOpen}
  >
    <slot name="trigger" />
  </button>
  
  {#if isOpen}
    <div
      bind:this={menuRef}
      class={cn("absolute right-0 z-50 mt-1 min-w-[8rem] overflow-hidden rounded-md border border-slate-200 bg-white p-1 shadow-md animate-in fade-in-80", menuClass)}
    >
      <slot />
    </div>
  {/if}
</div>
