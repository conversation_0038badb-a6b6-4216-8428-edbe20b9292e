<script lang="ts">
  import { cn } from "$lib/utils";
  import { createEventDispatcher } from "svelte";

  export let value: string = "";

  let className = "";
  export { className as class };

  const dispatch = createEventDispatcher();

  export function setValue(newValue: string) {
    value = newValue;
    dispatch('change', { value });
  }
</script>

<script context="module">
  export const Root = (props) => {
    return {
      props,
      $$render: ($$result, $$props, $$bindings, slots) => {
        const { value, class: className, ...rest } = $$props;
        return `<div class="${cn("tabs-root", className)}" ${Object.entries(rest).map(([k, v]) => `${k}="${v}"`).join(' ')}>${slots.default ? slots.default({}) : ''}</div>`;
      }
    };
  };

  export const List = (props) => {
    return {
      props,
      $$render: ($$result, $$props, $$bindings, slots) => {
        const { class: className, ...rest } = $$props;
        return `<div class="${cn("flex border-b", className)}" ${Object.entries(rest).map(([k, v]) => `${k}="${v}"`).join(' ')}>${slots.default ? slots.default({}) : ''}</div>`;
      }
    };
  };

  export const Trigger = (props) => {
    return {
      props,
      $$render: ($$result, $$props, $$bindings, slots) => {
        const { value, class: className, ...rest } = $$props;
        const active = $$bindings.value?.[0] === value;
        return `<button
          class="${cn(
            "px-4 py-2 border-b-2 text-center transition-colors",
            active ? "border-primary text-primary" : "border-transparent hover:text-primary hover:border-primary/40",
            className
          )}"
          ${Object.entries(rest).map(([k, v]) => `${k}="${v}"`).join(' ')}
          data-value="${value || ''}"
        >${slots.default ? slots.default({}) : ''}</button>`;
      }
    };
  };

  export const Content = (props) => {
    return {
      props,
      $$render: ($$result, $$props, $$bindings, slots) => {
        const { value, class: className, ...rest } = $$props;
        // 获取父组件的值
        const parentValue = $$bindings.value?.[0];
        // 修改活动标签页的检测逻辑，支持直接比较和从父组件获取值
        const active = parentValue === value;
        // 添加更详细的调试信息
        console.log('Tab content render:', {
          tabValue: value,
          parentValue,
          active,
          bindings: $$bindings,
          props: $$props
        });
        if (!active) return '';
        return `<div
          class="${cn("tab-content", className)}"
          ${Object.entries(rest).map(([k, v]) => `${k}="${v}"`).join(' ')}
          data-tab-value="${value || ''}"
        >${slots.default ? slots.default({}) : ''}</div>`;
      }
    };
  };
</script>
