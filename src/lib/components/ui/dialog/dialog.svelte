<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  
  export let open = false;
  
  const dispatch = createEventDispatcher();
  
  function handleChange(value: boolean) {
    open = value;
    dispatch('change', value);
  }
  
  function handleClose() {
    handleChange(false);
  }
</script>

{#if open}
  <div 
    class="fixed inset-0 z-50 bg-black/50" 
    on:click={handleClose}
    on:keydown={(e) => e.key === 'Escape' && handleClose()}
  >
    <div 
      class="fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 bg-white p-6 shadow-lg duration-200 rounded-lg"
      on:click|stopPropagation={() => {}}
    >
      <slot {handleClose} />
    </div>
  </div>
{/if} 