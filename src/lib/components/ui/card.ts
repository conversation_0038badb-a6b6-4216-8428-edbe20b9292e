import { cva } from 'class-variance-authority';
import { cn } from '$lib/utils';

export const cardVariants = cva(
  'rounded-lg border bg-card text-card-foreground shadow-sm',
  {
    variants: {
      variant: {
        default: 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700',
        destructive: 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800',
        success: 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800',
        warning: 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800',
        info: 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  }
);

export function Card({ className, variant, ...props }: any) {
  return {
    ...props,
    class: cn(cardVariants({ variant }), className),
  };
}

export function CardHeader({ className, ...props }: any) {
  return {
    ...props,
    class: cn('flex flex-col space-y-1.5 p-6', className),
  };
}

export function CardTitle({ className, ...props }: any) {
  return {
    ...props,
    class: cn('text-2xl font-semibold leading-none tracking-tight', className),
  };
}

export function CardDescription({ className, ...props }: any) {
  return {
    ...props,
    class: cn('text-sm text-muted-foreground', className),
  };
}

export function CardContent({ className, ...props }: any) {
  return {
    ...props,
    class: cn('p-6 pt-0', className),
  };
}

export function CardFooter({ className, ...props }: any) {
  return {
    ...props,
    class: cn('flex items-center p-6 pt-0', className),
  };
}
