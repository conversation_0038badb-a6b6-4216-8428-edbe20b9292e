<script lang="ts">
  import { cn } from "$lib/utils";

  let className = "";
  export { className as class };
</script>

<div class={cn("w-full overflow-auto", className)}>
  <table class="w-full caption-bottom text-sm">
    <slot />
  </table>
</div>

<script context="module">
  export const Header = (props) => {
    return {
      props,
      $$render: ($$result, $$props, $$bindings, slots) => {
        return `<thead class="[&_tr]:border-b">${slots.default ? slots.default({}) : ''}</thead>`;
      }
    };
  };

  export const Body = (props) => {
    return {
      props,
      $$render: ($$result, $$props, $$bindings, slots) => {
        return `<tbody class="[&_tr:last-child]:border-0">${slots.default ? slots.default({}) : ''}</tbody>`;
      }
    };
  };

  export const Row = (props) => {
    return {
      props,
      $$render: ($$result, $$props, $$bindings, slots) => {
        return `<tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">${slots.default ? slots.default({}) : ''}</tr>`;
      }
    };
  };

  export const Head = (props) => {
    const { class: className, ...rest } = props;
    return {
      props,
      $$render: ($$result, $$props, $$bindings, slots) => {
        return `<th class="${cn("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0", className)}" ${Object.entries(rest).map(([k, v]) => `${k}="${v}"`).join(' ')}>${slots.default ? slots.default({}) : ''}</th>`;
      }
    };
  };

  export const Cell = (props) => {
    const { class: className, ...rest } = props;
    return {
      props,
      $$render: ($$result, $$props, $$bindings, slots) => {
        return `<td class="${cn("p-4 align-middle [&:has([role=checkbox])]:pr-0", className)}" ${Object.entries(rest).map(([k, v]) => `${k}="${v}"`).join(' ')}>${slots.default ? slots.default({}) : ''}</td>`;
      }
    };
  };
</script>
