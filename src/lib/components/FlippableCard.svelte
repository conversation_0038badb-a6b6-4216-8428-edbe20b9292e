<script lang="ts">
  import { cn } from "$lib/utils";

  // Props
  export let isFlipped = false;
  export let frontClass = "";
  export let backClass = "";
  export let containerClass = "";

  // 现在使用专用按钮来触发翻转，不再需要onFlip属性
  // 但是为了保持与ProjectStatsCard.svelte的兼容性，我们保留这个属性
  export const onFlip = (_e?: MouseEvent) => {};
</script>

<div
  class={cn(
    "relative w-full h-full perspective-1000 border-0 p-0 bg-transparent",
    containerClass
  )}
  aria-pressed={isFlipped}
>
  <div
    class={cn(
      "relative w-full h-full flip-transition transform-style-3d",
      isFlipped ? "rotate-y-180" : ""
    )}
  >
    <!-- Front of card -->
    <div
      class={cn(
        "absolute w-full h-full backface-hidden",
        frontClass
      )}
      role="region"
      aria-label="卡片正面内容"
    >
      <slot name="front" />
    </div>

    <!-- Back of card -->
    <div
      class={cn(
        "absolute w-full h-full backface-hidden rotate-y-180",
        backClass
      )}
      role="region"
      aria-label="卡片背面内容"
    >
      <slot name="back" />
    </div>
  </div>
</div>
