<script lang="ts">
  import { sqliteDictionaryService, type SqliteDict } from '$lib/services/sqliteDictionaryService';
  import { Button } from '$lib/components/ui/button';
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';

  // 属性
  let { dictionary = null } = $props<{
    dictionary?: SqliteDict | null;
  }>();

  // 状态管理
  let isLoading = $state(false);
  let isSaving = $state(false);
  let error = $state<string | null>(null);
  let success = $state<string | null>(null);

  // 表单数据
  let formData = $state<SqliteDict>({
    name: '',
    description: '',
    type_: 'list',
    tags: []
  });

  // 标签输入
  let tagsInput = $state('');

  // 初始化表单数据
  onMount(() => {
    if (dictionary) {
      formData = { ...dictionary };
      tagsInput = dictionary.tags?.join(', ') || '';
    }
  });

  // 处理标签输入变化
  function handleTagsInput(e: Event) {
    const value = (e.target as HTMLInputElement).value;
    tagsInput = value;
    formData.tags = value ? value.split(',').map(tag => tag.trim()).filter(Boolean) : [];
  }

  // 验证字典名称是否符合规范（只允许英文、数字、下划线）
  function validateDictName(name: string): boolean {
    const regex = /^[a-zA-Z0-9_]+$/;
    return regex.test(name);
  }

  // 保存字典
  async function saveDict(e: Event) {
    e.preventDefault();

    if (!formData.name) {
      error = '字典名称不能为空';
      return;
    }

    if (!validateDictName(formData.name)) {
      error = '字典名称只能包含英文字母、数字和下划线';
      return;
    }

    isSaving = true;
    error = null;
    success = null;

    try {
      if (dictionary?.id) {
        // 更新现有字典
        const result = await sqliteDictionaryService.updateDict(dictionary.id, formData);
        if (result) {
          success = '字典更新成功';
          // 直接导航到字典详情页面
          goto(`/sqlite-dictionaries/${dictionary.id}`);
        } else {
          error = '字典更新失败';
        }
      } else {
        // 创建新字典
        const id = await sqliteDictionaryService.createDict(formData);
        if (id) {
          success = '字典创建成功';
          // 直接导航到字典详情页面
          goto(`/sqlite-dictionaries/${id}`);
        } else {
          error = '字典创建失败';
        }
      }
    } catch (err: any) {
      error = err.message || '保存字典失败';
      console.error('保存字典失败:', err);
    } finally {
      isSaving = false;
    }
  }
</script>

<div class="dictionary-form">
  <!-- 错误提示 -->
  {#if error}
    <div class="bg-red-50 dark:bg-red-900/20 border border-red-100 dark:border-red-800 rounded-md p-4 text-red-800 dark:text-red-200 mb-6">
      <p>{error}</p>
    </div>
  {/if}

  <!-- 成功提示 -->
  {#if success}
    <div class="bg-green-50 dark:bg-green-900/20 border border-green-100 dark:border-green-800 rounded-md p-4 text-green-800 dark:text-green-200 mb-6">
      <p>{success}</p>
    </div>
  {/if}

  <!-- 加载中 -->
  {#if isLoading}
    <div class="flex justify-center items-center py-12">
      <div class="inline-block w-6 h-6 border-2 border-t-transparent border-blue-600 dark:border-blue-400 rounded-full animate-spin mr-2"></div>
      <p>加载中...</p>
    </div>
  {:else}
    <div class="bg-white dark:bg-slate-800 rounded-lg shadow-md p-6">
      <h2 class="text-xl font-semibold mb-6">{dictionary ? '编辑字典' : '新建字典'}</h2>

      <form onsubmit={(e) => saveDict(e)} class="space-y-6">
        <!-- 字典名称 -->
        <div>
          <label for="dict-name" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
            字典名称 <span class="text-red-500">*</span>
          </label>
          <input
            id="dict-name"
            type="text"
            bind:value={formData.name}
            placeholder="请输入字典名称"
            class="w-full h-10 rounded-md border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 px-3 py-2 text-sm text-slate-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
          />
          <p class="mt-1 text-xs text-slate-500 dark:text-slate-400">
            字典名称必须唯一，只能包含英文字母、数字和下划线，例如：recruitment_companies、project_types
          </p>
        </div>

        <!-- 字典描述 -->
        <div>
          <label for="dict-description" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
            字典描述
          </label>
          <textarea
            id="dict-description"
            bind:value={formData.description}
            placeholder="请输入字典描述"
            class="w-full h-24 rounded-md border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 px-3 py-2 text-sm text-slate-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
          ></textarea>
        </div>

        <!-- 字典类型 -->
        <div>
          <label for="dict-type" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
            字典类型
          </label>
          <select
            id="dict-type"
            bind:value={formData.type_}
            class="w-full h-10 rounded-md border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 px-3 py-2 text-sm text-slate-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="list">列表型</option>
            <option value="tree">树形结构</option>
            <option value="key-value">键值对</option>
          </select>
        </div>

        <!-- 标签 -->
        <div>
          <label for="dict-tags" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
            标签
          </label>
          <input
            id="dict-tags"
            type="text"
            value={tagsInput}
            oninput={handleTagsInput}
            placeholder="输入标签，用逗号分隔"
            class="w-full h-10 rounded-md border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 px-3 py-2 text-sm text-slate-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <p class="mt-1 text-xs text-slate-500 dark:text-slate-400">
            多个标签请用逗号分隔，例如：招募,公司,列表
          </p>

          {#if formData.tags && formData.tags.length > 0}
            <div class="flex flex-wrap gap-1 mt-2">
              {#each formData.tags as tag}
                <span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900/40 dark:text-blue-300">
                  {tag}
                </span>
              {/each}
            </div>
          {/if}
        </div>

        <!-- 提交按钮 -->
        <div class="flex justify-end gap-2">
          <Button type="button" variant="outline" onclick={() => goto('/sqlite-dictionaries')}>
            取消
          </Button>
          <Button type="submit" disabled={isSaving} class="flex items-center gap-2">
            {#if isSaving}
              <span class="inline-block w-4 h-4 border-2 border-t-transparent border-current rounded-full animate-spin"></span>
            {/if}
            {isSaving ? '保存中...' : '保存字典'}
          </Button>
        </div>
      </form>
    </div>
  {/if}
</div>
