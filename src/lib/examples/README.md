# 入排标准批量导入功能说明

## 功能概述

入排标准批量导入功能允许用户通过上传JSON文件的方式，一次性导入多个入组标准和排除标准，并可以设置"或"关系组。这大大提高了配置效率，特别是对于具有大量标准的项目。

## JSON文件格式

导入的JSON文件需要遵循以下格式：

```json
{
  "inclusion": [
    {
      "rule_definition_id": 4,
      "parameter_values": {
        "Min_age": 18,
        "Max_age": 65
      },
      "display_order": 1
    },
    // 更多入组标准...
  ],
  "exclusion": [
    {
      "rule_definition_id": 14,
      "parameter_values": {
        "Type": "LABA/LAMA/ICS三联",
        "Maintenance_duration": 3,
        "Stable_dosage_duration": 1
      },
      "display_order": 1
    },
    // 更多排除标准...
  ],
  "or_groups": [
    {
      "type": "inclusion",
      "criteria_indices": [0, 1]
    },
    // 更多"或"关系组...
  ]
}
```

### 字段说明

#### 入组/排除标准

- `inclusion`: 入组标准数组
- `exclusion`: 排除标准数组
- 每个标准包含以下字段：
  - `rule_definition_id`: 规则定义ID（必填，数字类型）
  - `parameter_values`: 参数值对象（必填，对象类型）
  - `display_order`: 显示顺序（可选，数字类型）
  - `is_active`: 是否激活（可选，布尔类型，默认为true）

#### "或"关系组

- `or_groups`: "或"关系组数组（可选）
- 每个"或"关系组包含以下字段：
  - `type`: 标准类型，必须是"inclusion"或"exclusion"
  - `criteria_indices`: 标准索引数组，指向上面定义的入组或排除标准的索引（从0开始）

## 常见规则定义示例

以下是一些常见规则定义的参数示例：

### 1. 年龄要求 (rule_definition_id: 4)
```json
{
  "rule_definition_id": 4,
  "parameter_values": {
    "Min_age": 18,
    "Max_age": 65
  }
}
```

### 2. 确诊史 (rule_definition_id: 7)
```json
{
  "rule_definition_id": 7,
  "parameter_values": {
    "diagnose_month": 6
  }
}
```

### 3. 支气管舒张剂前FEV1 (rule_definition_id: 8)
```json
{
  "rule_definition_id": 8,
  "parameter_values": {
    "Pre_BD_FEV1_min": 30,
    "Pre_BD_FEV1_max": 80
  }
}
```

### 4. 支气管舒张试验阳性要求 (rule_definition_id: 9)
```json
{
  "rule_definition_id": 9,
  "parameter_values": {
    "If_need": true,
    "Effective_time_range": 12
  }
}
```

### 5. 血嗜酸粒细胞计数 (rule_definition_id: 11)
```json
{
  "rule_definition_id": 11,
  "parameter_values": {
    "Min_count": 300,
    "data_range": "筛选前30天内"
  }
}
```

### 6. mMRC评分 (rule_definition_id: 12)
```json
{
  "rule_definition_id": 12,
  "parameter_values": {
    "Min_number": 2
  }
}
```

### 7. FEV1/FVC (rule_definition_id: 13)
```json
{
  "rule_definition_id": 13,
  "parameter_values": {
    "Max_number": 0.7
  }
}
```

### 8. 慢阻肺吸入药物维持治疗 (rule_definition_id: 14)
```json
{
  "rule_definition_id": 14,
  "parameter_values": {
    "Type": "LABA/LAMA/ICS三联",
    "Maintenance_duration": 3,
    "Stable_dosage_duration": 1
  }
}
```

### 9. 吸烟史 (rule_definition_id: 15)
```json
{
  "rule_definition_id": 15,
  "parameter_values": {
    "smoking_history": 10
  }
}
```

### 10. 生物燃料烟雾环境暴露 (rule_definition_id: 16)
```json
{
  "rule_definition_id": 16,
  "parameter_values": {
    "Exposure_to_biofuel_smoke_environment": 5
  }
}
```

### 11. 慢阻肺病急性加重病史 (rule_definition_id: 17)
```json
{
  "rule_definition_id": 17,
  "parameter_values": {
    "date_number": 12,
    "Min_count": 2,
    "degree": "中度"
  }
}
```

## 使用步骤

1. 在项目入排标准配置页面，点击"导入JSON"按钮
2. 在弹出的对话框中，可以点击"下载模板"获取标准模板文件
3. 根据项目需求修改模板文件，填入正确的规则定义ID和参数值
4. 点击"选择JSON文件"或将文件拖放到指定区域
5. 系统会验证JSON格式并导入标准
6. 导入完成后，会显示导入结果，包括成功和失败的数量及详细信息

## 获取规则定义ID

要获取正确的规则定义ID，可以通过以下方式：

1. 在规则定义管理页面查看所有可用的规则定义及其ID
2. 或者在项目入排标准配置页面，添加一个标准并查看其规则定义ID
3. 使用数据库导出工具导出规则定义表，查看完整的规则定义列表

## 注意事项

1. 导入前请确保JSON格式正确，特别是规则定义ID和参数值
2. "或"关系组中的标准索引必须有效（不超出数组范围）
3. 导入操作不会删除现有标准，只会添加新标准
4. 如果导入过程中出现错误，系统会显示详细的错误信息
5. 建议先在测试项目中尝试导入，确认无误后再在正式项目中使用
