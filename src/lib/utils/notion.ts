/**
 * Notion API 工具函数
 */

import { writable } from 'svelte/store';
import { get } from 'svelte/store';
import settings from '$lib/stores/settings'; // 确保导入 settings store
import { invoke } from '@tauri-apps/api/core'; // 导入 invoke
// import { fetch, type FetchOptions } from '@tauri-apps/api/http';
// 使用浏览器原生fetch
// @ts-ignore 临时忽略类型错误，以便应用可以正常运行

// 将 NotionItem 重命名为 NotionDatabaseItem
export interface NotionDatabaseItem {
    id: string;
    title: string;
    status: string; // 注意：这里的类型可能需要更精确，例如是 status 对象还是 name 字符串
    dueDate: string | null; // 允许为 null
    // 根据 +page.svelte 的使用情况，可能还需要 priority 等字段
    priority?: string[]; // 添加可选的 priority 字段
}

// 保持 writable store 名称不变或根据需要调整
export const notionItems = writable<NotionDatabaseItem[]>([]);
export const notionLoading = writable<boolean>(false);
export const notionError = writable<string | null>(null);

const LOCAL_STORAGE_KEY = 'notion_database_items'; // 统一定义本地存储键名

/**
 * 从Notion获取数据库条目 (通过 Tauri 命令)
 * @returns Promise<NotionDatabaseItem[]>
 */
export async function fetchDatabaseItems(): Promise<NotionDatabaseItem[]> {
    console.log('开始获取Notion数据库项目 (通过 Tauri 命令)');
    notionLoading.set(true);
    notionError.set(null);

    let currentSettings;
    try {
        currentSettings = get(settings);
        console.log('当前设置 (来自 store):', {
            hasApiKey: !!currentSettings.notionApiKey,
            hasDatabaseId: !!currentSettings.notionDatabaseId
        });
    } catch (error) {
        console.error('读取 settings store 时出错:', error);
        notionLoading.set(false);
        notionError.set('无法读取应用设置');
        throw new Error('无法读取应用设置');
    }

    const { notionApiKey, notionDatabaseId } = currentSettings;

    if (!notionApiKey || !notionDatabaseId) {
        console.error('缺少 Notion API 密钥或数据库 ID');
        notionLoading.set(false);
        notionError.set('请先在设置中配置Notion API密钥和数据库ID');
        throw new Error('请先设置Notion API密钥和数据库ID');
    }

    try {
        console.log('调用 Tauri 命令: fetch_notion_database');
        // 调用 Tauri 命令，传递 API Key 和 Database ID
        const items: NotionDatabaseItem[] = await invoke('fetch_notion_database', {
            apiKey: notionApiKey,
            databaseId: notionDatabaseId
        });

        console.log(`Tauri 命令成功返回 ${items.length} 个项目`);
        notionItems.set(items); // 更新 Svelte store

        // 保存到本地存储
        saveDatabaseItemsToLocalStorage(items);

        notionLoading.set(false);
        return items;

    } catch (error: any) {
        // Tauri invoke 抛出的错误通常是字符串
        const errorMessage = typeof error === 'string' ? error : (error?.message || '调用 Tauri 命令失败');
        console.error('调用 Tauri 命令 fetch_notion_database 失败:', errorMessage);
        notionLoading.set(false);
        notionError.set(`获取Notion数据失败: ${errorMessage}`);
        // 重新抛出错误，以便调用者可以捕获
        throw new Error(errorMessage);
    }
}

// 保持这个函数，因为它使用的 key 与 saveDatabaseItemsToLocalStorage 一致
/**
 * 从本地存储获取数据库条目
 * @returns 数据库条目列表
 */
export function getDatabaseItemsFromLocalStorage(): NotionDatabaseItem[] {
  try {
    console.log(`尝试从本地存储获取数据 (Key: ${LOCAL_STORAGE_KEY})`);
    const items = localStorage.getItem(LOCAL_STORAGE_KEY);
    if (!items) {
        console.log('本地存储中没有找到项目');
        return [];
    }
    const parsedItems = JSON.parse(items);
    console.log(`从本地存储获取到 ${parsedItems.length} 个项目`);
    // 可以添加类型检查来确保 localStorage 的数据结构符合 NotionDatabaseItem[]
    return Array.isArray(parsedItems) ? parsedItems : [];
  } catch (error) {
    console.error('从本地存储获取数据库条目失败:', error);
    return [];
  }
}

/**
 * 保存数据库条目到本地存储
 * @param items 数据库条目列表
 */
export function saveDatabaseItemsToLocalStorage(items: NotionDatabaseItem[]) {
  try {
    console.log(`尝试保存数据到本地存储 (Key: ${LOCAL_STORAGE_KEY}), 数据项数量:`, items.length);
    localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(items));
    return true;
  } catch (error) {
    console.error('保存数据库条目到本地存储失败:', error);
    return false;
  }
}

// 导入类型定义
interface ClassificationResult {
  task: string;
  reason: string;
  confidence?: number;
}

/**
 * 将笔记同步到 Notion 页面
 * @param category 分类名称
 * @param noteContent 笔记内容
 * @returns 同步结果
 */
export async function syncNoteToNotion(category: string, noteContent: string): Promise<{ success: boolean; error?: string; pageId?: string }> {
  console.log(`尝试将笔记同步到 Notion 页面: ${category}`);
  notionError.set(null);

  const currentSettings = get(settings);
  const { notionApiKey, notionDatabaseId } = currentSettings;

  if (!notionApiKey || !notionDatabaseId) {
    const errorMsg = '请先在设置中配置Notion API密钥和数据库ID';
    notionError.set(errorMsg);
    return { success: false, error: errorMsg };
  }

  // 获取当前数据库条目
  const items = get(notionItems);

  // 查找匹配的页面
  const matchingPage = items.find(item => item.title.trim() === category.trim());

  if (!matchingPage) {
    const errorMsg = `未找到匹配的页面: ${category}`;
    console.error(errorMsg);
    notionError.set(errorMsg);
    return { success: false, error: errorMsg };
  }

  try {
    // 调用 Tauri 命令同步笔记
    const result = await invoke('sync_note_to_notion', {
      apiKey: notionApiKey,
      pageId: matchingPage.id,
      content: noteContent
    });

    console.log('同步笔记成功:', result);
    return { success: true, pageId: matchingPage.id };
  } catch (error: any) {
    const errorMsg = typeof error === 'string' ? error : (error?.message || '同步笔记失败');
    console.error('同步笔记失败:', errorMsg);
    notionError.set(`同步笔记失败: ${errorMsg}`);
    return { success: false, error: errorMsg };
  }
}

/**
 * 批量同步笔记到 Notion
 * @param notesWithCategories 笔记和分类列表
 * @returns 同步结果
 */
export async function batchSyncToNotion(notesWithCategories: Array<{ note: string; categories: ClassificationResult[] }>): Promise<Array<{ note: string; categories: string[]; success: boolean; error?: string }>> {
  if (!notesWithCategories || notesWithCategories.length === 0) {
    return [];
  }

  const results = [];

  for (const item of notesWithCategories) {
    const { note, categories } = item;
    const categoryNames = categories.map(c => c.task);

    if (!note || categoryNames.length === 0) {
      results.push({
        note,
        categories: categoryNames,
        success: false,
        error: '笔记内容或分类列表为空'
      });
      continue;
    }

    try {
      const syncResults = [];
      let anySuccess = false;

      // 将笔记同步到每个分类
      for (const category of categoryNames) {
        const result = await syncNoteToNotion(category, note);
        syncResults.push(result);
        if (result.success) {
          anySuccess = true;
        }
      }

      results.push({
        note,
        categories: categoryNames,
        success: anySuccess,
        error: anySuccess ? undefined : '所有同步操作均失败'
      });

      // 添加延迟以避免 API 限流
      await new Promise(resolve => setTimeout(resolve, 500));

    } catch (error: any) {
      console.error('同步笔记失败:', error);
      results.push({
        note,
        categories: categoryNames,
        success: false,
        error: error?.message || '同步笔记失败'
      });
    }
  }

  return results;
}