// 笔记分类器
import { get } from 'svelte/store';
import settings from '$lib/stores/settings';
import { notionItems } from '$lib/utils/notion';
import { ChatOpenAI } from "@langchain/openai"; // 使用 OpenAI 兼容的 Chat 类
import { PromptTemplate } from "@langchain/core/prompts";
import { JsonOutputParser } from "@langchain/core/output_parsers";
import type { Runnable } from "@langchain/core/runnables";

// 分类结果类型
export interface ClassificationResult {
  task: string;
  reason: string;
  confidence?: number;
}

// 分类响应类型 (模型应该输出的 JSON 结构)
interface ClassificationResponse {
  classifications: ClassificationResult[];
  error: string | null;
}

/**
 * 笔记分类器类 (使用 Langchain)
 */
export class NoteClassifier {
  private apiKey: string;
  private baseUrl: string;
  private modelId: string;
  private notionApiKey: string; // 保留以备将来可能使用
  private notionDatabaseId: string; // 保留以备将来可能使用
  private notionInspirationDbId: string; // 保留以备将来可能使用
  private tasks: string[] = [];

  private model: ChatOpenAI; // Langchain 模型实例
  private chain: Runnable | null = null; // Langchain 调用链

  /**
   * 构造函数
   */
  constructor() {
    const settingsData = get(settings);
    this.apiKey = settingsData.openrouterApiKey || '';
    this.baseUrl = settingsData.openrouterSiteUrl || 'https://openrouter.ai/api/v1'; // OpenRouter API 基础端点，不包含 /chat/completions
    this.modelId = settingsData.models && settingsData.models.length > 0 ? settingsData.models[0].id : 'google/gemini-2.0-flash-001';
    this.notionApiKey = settingsData.notionApiKey || '';
    this.notionDatabaseId = settingsData.notionDatabaseId || '';
    this.notionInspirationDbId = settingsData.notionInspirationDbId || '';

    // 确保 baseUrl 不包含 /chat/completions
    if (this.baseUrl.endsWith('/chat/completions')) {
        this.baseUrl = this.baseUrl.replace('/chat/completions', '');
        console.log('移除了 baseUrl 中的 /chat/completions 路径，现在是:', this.baseUrl);
    }

    // 初始化 Langchain 模型
    this.model = new ChatOpenAI({
        apiKey: this.apiKey,
        modelName: this.modelId,
        openAIApiKey: this.apiKey, // 兼容性字段
        configuration: {
            baseURL: this.baseUrl, // 使用清理后的 baseUrl
            // 添加必要的 OpenRouter header
            defaultHeaders: {
                'HTTP-Referer': 'https://peckbyte.app', // 应用地址
                'X-Title': 'PeckByte Note Classifier', // 应用名称
                'Content-Type': 'application/json'
            }
        },
        temperature: 0.1, // 降低随机性以获取更一致的 JSON 输出
        maxRetries: 2, // 内部重试
    });

    console.log('初始化 ChatOpenAI 模型，使用 baseURL:', this.baseUrl);

    // 从 notionItems 获取任务列表
    this.loadTasks();
    // 初始化调用链
    this.setupChain();
  }

  /**
   * 加载任务列表
   */
  private loadTasks() {
    const items = get(notionItems);
    console.log('从 notionItems 获取到原始项目:', items);

    // 过滤掉空标题
    this.tasks = items.map(item => item.title).filter((title): title is string => !!title && title.trim() !== '');

    // 如果任务列表为空，添加一些默认任务用于测试
    if (this.tasks.length === 0) {
      console.warn('任务列表为空，添加默认测试任务');
      this.tasks = [
        '项目管理文档',
        '临床研究协调员 (CRC) 工作指南',
        '数据管理计划',
        '研究方案设计',
        '患者招募策略',
        '质量控制流程',
        '伦理审查申请',
        '不良事件报告',
        '研究进度报告',
        '数据分析计划'
      ];
    }

    console.log('最终加载的任务列表:', this.tasks);

    // 任务列表变化后需要重新设置链
    this.setupChain();
  }

  /**
   * 设置或更新 Langchain 调用链
   */
  private async setupChain() {
    if (this.tasks.length === 0) {
        console.warn("任务列表为空，无法设置分类链。");
        this.chain = null;
        return;
    }

    // 准备任务列表字符串，用于提示模板中的 {tasks} 变量
    const tasksStr = this.tasks.map(task => `- ${task}`).join('\n'); // 使用 \n 作为换行符
    console.log('准备任务列表字符串:', tasksStr);

    // 准备系统提示模板
    // 使用模板字面量处理多行字符串和换行
    const systemPromptTemplate = `你是一个专业的笔记分类助手。你的任务是将用户的临时笔记分类到预定义的写作任务中。
现有的写作任务主题如下：
{tasks}

用户笔记内容：
{note}

请分析上述用户笔记最可能属于哪个写作任务主题。如果笔记可能属于多个主题，请列出最相关的1-3个主题。

【重要】输出格式要求：
你必须严格按照以下JSON格式返回结果。不要包含任何额外的介绍、解释或注释。
不要在JSON前后添加任何文字、符号或markdown标记。
只返回纯JSON，确保它可以被JSON.parse()正确解析。

{json_format}

【严格注意事项】：
1. task字段必须是上述任务列表中的一个主题名称
2. 如果笔记内容模糊或无法分类，将classifications设为空数组[]，并在error字段说明原因
3. 最多返回3个分类条目
4. 所有文本必须使用中文
5. reason字段需要简明扼要，一句话说明原因
6. confidence字段是可选的，表示置信度，范围0-1
7. 你的整个回复必须是一个有效的JSON对象，不要添加任何其他内容
8. 不要使用markdown代码块或其他格式，直接返回JSON`;

    // 显式定义输入变量，确保 Langchain 知道模板需要哪些输入
    const prompt = new PromptTemplate({
        template: systemPromptTemplate,
        inputVariables: ["tasks", "json_format", "note"] // 添加 note
    });

    // 测试 prompt 模板是否正确替换变量
    const testPrompt = await prompt.format({
        tasks: tasksStr,
        json_format: this.getClassificationFormatString(),
        note: "测试笔记内容"
    });
    console.log('测试 prompt 模板替换结果 (前200字符):', testPrompt.substring(0, 200) + '...');

    // 检查 prompt 模板是否包含 {note} 占位符
    if (!systemPromptTemplate.includes('{note}')) {
        console.error('警告: prompt 模板中没有 {note} 占位符，用户笔记内容将不会被传递给模型!');
    } else {
        console.log('prompt 模板包含 {note} 占位符，用户笔记内容将正确传递给模型');
    }

    // 创建 JSON 输出解析器，添加更多错误处理
    const parser = new JsonOutputParser<ClassificationResponse>();

    // 使用 LCEL 构建链
    this.chain = prompt.pipe(this.model).pipe(parser);
    console.log("Langchain 调用链已设置/更新");
  }


  /**
   * 设置模型 ID
   * @param modelId 模型 ID
   */
  setModelId(modelId: string): void {
    if (modelId && modelId.trim() !== '') {
      this.modelId = modelId;

      // 确保 baseUrl 不包含 /chat/completions
      let cleanBaseUrl = this.baseUrl;
      if (cleanBaseUrl.endsWith('/chat/completions')) {
          cleanBaseUrl = cleanBaseUrl.replace('/chat/completions', '');
          console.log('setModelId: 移除了 baseUrl 中的 /chat/completions 路径，现在是:', cleanBaseUrl);
      }

      // 重新创建模型实例，因为 modelName 已弃用
      this.model = new ChatOpenAI({
          apiKey: this.apiKey,
          modelName: modelId, // 使用新的模型 ID
          openAIApiKey: this.apiKey,
          configuration: {
              baseURL: cleanBaseUrl, // 使用清理后的 baseUrl
              defaultHeaders: {
                  'HTTP-Referer': 'https://peckbyte.app',
                  'X-Title': 'PeckByte Note Classifier',
                  'Content-Type': 'application/json'
              }
          },
          temperature: 0.1,
          maxRetries: 2,
      });

      console.log('setModelId: 重新创建了 ChatOpenAI 模型，使用 baseURL:', cleanBaseUrl);
      console.log('已设置模型 ID:', modelId);
       // 模型变化后需要重新设置链（因为模型是链的一部分）
      this.setupChain();
    } else {
      console.warn('尝试设置无效的模型 ID');
    }
  }

  /**
   * 分类笔记 (使用 Langchain)
   * @param note 笔记内容
   * @param modelId 可选的模型 ID，如果提供则使用该模型
   * @returns 分类结果
   */
  /**
   * 检查 API 连接状态
   * @returns 连接状态对象 {success: boolean, message: string}
   */
  async checkApiConnection(): Promise<{success: boolean, message: string}> {
    try {
      console.log('开始检查 API 连接状态...');
      console.log('当前配置:', {
        apiKey: this.apiKey ? '已设置 (隐藏)' : '未设置',
        baseUrl: this.baseUrl,
        modelId: this.modelId,
        tasksCount: this.tasks.length
      });

      // 检查 API 密钥是否存在
      if (!this.apiKey || this.apiKey.trim() === '') {
        console.error('API 连接检查失败: 未设置 API 密钥');
        return {
          success: false,
          message: '未设置 OpenRouter API 密钥。请在设置中配置 API 密钥。'
        };
      }

      // 检查 API URL 是否正确
      if (!this.baseUrl || !this.baseUrl.includes('openrouter.ai/api/v1')) {
        console.error('API 连接检查失败: API URL 格式不正确', this.baseUrl);
        return {
          success: false,
          message: `OpenRouter API URL 格式不正确: ${this.baseUrl}。正确格式应为 "https://openrouter.ai/api/v1"。请在设置中更正。`
        };
      }

      // 检查 API URL 是否包含 /chat/completions
      if (this.baseUrl.includes('/chat/completions')) {
        console.error('API 连接检查失败: API URL 包含 /chat/completions 路径', this.baseUrl);

        // 自动修复 URL
        const fixedUrl = this.baseUrl.replace(/\/chat\/completions/g, '');
        console.log('自动修复 API URL，从', this.baseUrl, '修改为', fixedUrl);
        this.baseUrl = fixedUrl;

        // 重新创建模型实例
        this.model = new ChatOpenAI({
            apiKey: this.apiKey,
            modelName: this.modelId,
            openAIApiKey: this.apiKey,
            configuration: {
                baseURL: fixedUrl,
                defaultHeaders: {
                    'HTTP-Referer': 'https://peckbyte.app',
                    'X-Title': 'PeckByte Note Classifier',
                    'Content-Type': 'application/json'
                }
            },
            temperature: 0.1,
            maxRetries: 2,
        });

        // 重新设置调用链
        this.setupChain();
        console.log('已重新创建 ChatOpenAI 实例并更新调用链，使用修复后的 URL:', fixedUrl);
      }

      // 检查任务列表是否已加载
      if (this.tasks.length === 0) {
        console.log('任务列表为空，尝试重新加载...');
        this.loadTasks(); // 尝试重新加载
        if (this.tasks.length === 0) {
          console.error('API 连接检查失败: 任务列表为空');
          return {
            success: false,
            message: '任务列表为空。请确保 Notion 数据库中有分类项目。'
          };
        }
      }

      // 检查模型实例是否正确初始化
      if (!this.model) {
        console.error('API 连接检查失败: ChatOpenAI 模型实例未初始化');
        return {
          success: false,
          message: 'AI 模型未正确初始化。请刷新页面或重新打开应用。'
        };
      }

      console.log('API 连接检查通过');
      return {
        success: true,
        message: 'API 连接正常'
      };
    } catch (error) {
      console.error('API 连接检查失败:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : '未知错误'
      };
    }
  }

  async classifyNote(note: string, modelId?: string): Promise<ClassificationResult[]> {
    // 如果提供了模型 ID，则更新模型
    if (modelId && modelId.trim() !== '' && modelId !== this.modelId) {
      this.setModelId(modelId);
    }

    // 首先检查 API 连接状态
    const connectionStatus = await this.checkApiConnection();
    if (!connectionStatus.success) {
      console.error('API 连接检查失败:', connectionStatus.message);
      throw new Error(`API 连接错误: ${connectionStatus.message}`);
    }

    // 确保任务列表已加载并且链已设置
    if (this.tasks.length === 0) {
        this.loadTasks(); // 尝试重新加载
    }
    if (!this.chain) {
        this.setupChain(); // 尝试重新设置链
        if (!this.chain) {
           throw new Error("分类链未初始化，请确保任务列表已加载且非空。");
        }
    }

    // 记录当前使用的 API 配置
    console.log('当前 API 配置:', {
      baseUrl: this.baseUrl,
      modelId: this.modelId,
      tasksCount: this.tasks.length
    });

    try {
      console.log('使用 Langchain 开始处理笔记分类...');
      console.log('使用模型:', this.modelId);
      console.log('使用的任务列表:', this.tasks);

      // 准备任务列表字符串
      const tasksStr = this.tasks.map(task => `- ${task}`).join('\n'); // 使用 \n

      // 准备调用参数
      const invokeParams = {
          tasks: tasksStr,
          json_format: this.getClassificationFormatString(), // 获取格式字符串
          note: note // 用户笔记内容
      };

      console.log('调用 Langchain 链，参数:', {
          tasks: tasksStr.substring(0, 100) + (tasksStr.length > 100 ? '...' : ''),
          note: note.length > 50 ? note.substring(0, 50) + '...' : note
      });

      // 记录完整的任务列表和笔记内容，用于调试
      console.log('完整任务列表:', this.tasks);
      console.log('完整笔记内容:', note);

      let result;
      try {
          // 调用 Langchain 链
          console.log('开始调用 Langchain 链，使用参数:', {
              tasks: '(任务列表)',
              note: invokeParams.note.length > 50 ? invokeParams.note.substring(0, 50) + '...' : invokeParams.note
          });

          // 确保 note 参数正确传递
          result = await this.chain.invoke({
              tasks: tasksStr,
              json_format: this.getClassificationFormatString(),
              note: note // 直接使用原始笔记内容，确保正确传递
          });

          console.log('成功获取 Langchain 原始响应:', result);
      } catch (parseError: any) {
          // 如果 JSON 解析失败，尝试手动解析
          console.error('Langchain JSON 解析失败，尝试手动解析:', parseError);

          // 尝试直接获取模型的原始输出
          // 注意：这是一个备用方案，可能需要根据实际的 Langchain 版本和实现进行调整
          try {
              // 重新调用模型，但不使用 JsonOutputParser
              const rawResponse = await this.model.invoke(
                  `你是一个专业的笔记分类助手。你的任务是将用户的临时笔记分类到预定义的写作任务中。
现有的写作任务主题如下：
${tasksStr}

用户笔记内容：
${note}

请分析上述用户笔记最可能属于哪个写作任务主题。如果笔记可能属于多个主题，请列出最相关的1-3个主题。

【重要】输出格式要求：
你必须严格按照以下JSON格式返回结果。不要包含任何额外的介绍、解释或注释。
不要在JSON前后添加任何文字、符号或markdown标记。
只返回纯JSON，确保它可以被JSON.parse()正确解析。

返回的数据格式举例如下：

${this.getClassificationFormatString()}

【严格注意事项】：
1. task字段必须是上述任务列表中的一个主题名称
2. 如果笔记内容模糊或无法分类，将classifications设为空数组[]，并在error字段说明原因
3. 最多返回3个分类条目
4. 所有文本必须使用中文
5. reason字段需要简明扼要，一句话说明原因
6. confidence字段是可选的，表示置信度，范围0-1
7. 你的整个回复必须是一个有效的JSON对象，不要添加任何其他内容
8. 不要使用markdown代码块或其他格式，直接返回JSON`
              );

              console.log('获取到模型原始响应:', rawResponse);

              // 尝试从原始响应中提取 JSON
              const jsonMatch = rawResponse.content.toString().match(/\{[\s\S]*\}/);
              if (jsonMatch) {
                  const jsonStr = jsonMatch[0];
                  console.log('从原始响应中提取的 JSON 字符串:', jsonStr);
                  result = JSON.parse(jsonStr);
                  console.log('成功解析提取的 JSON:', result);
              } else {
                  throw new Error('无法从模型响应中提取 JSON');
              }
          } catch (extractError) {
              console.error('尝试手动提取 JSON 失败:', extractError);
              throw new Error(`JSON 解析失败: ${parseError.message}。尝试手动解析也失败: ${extractError instanceof Error ? extractError.message : String(extractError)}`);
          }
      }

      // 验证结果格式
      if (!result || typeof result !== 'object') {
          console.error('模型返回的结果格式无效:', result);
          throw new Error('模型返回的结果格式无效，无法解析为 JSON 对象');
      }

      if (!Array.isArray(result.classifications) && !result.error) {
          console.error('模型返回的 classifications 不是数组:', result);
          throw new Error('模型返回的 classifications 不是数组，无法处理分类结果');
      }

      if (result.error) {
        console.warn(`分类API返回错误: ${result.error}`);
        return []; // 或者根据错误类型决定是否抛出异常
      }

      // 添加默认置信度（如果API没有返回）
      const classifications = result.classifications.map((item: ClassificationResult, index: number) => ({
        ...item,
        confidence: item.confidence != null ? item.confidence : (1 - index * 0.2) // 优先使用API返回的，否则给默认
      }));

      // 确保返回普通的 JavaScript 对象数组，而不是 Proxy
      console.log('原始分类结果:', JSON.stringify(classifications));

      // 使用 JSON 序列化和反序列化来创建一个新的普通对象
      const plainResults = JSON.parse(JSON.stringify(classifications));
      console.log('转换后的普通对象分类结果:', plainResults);

      return plainResults;

    } catch (error: unknown) {
        console.error('Langchain 分类过程中发生错误:', error);
        // 尝试提取 Langchain 可能包装的原始错误信息
        let errorMessage = '分类失败，请检查网络或API设置。';

        if (error instanceof Error) {
            errorMessage = error.message;

            // 检查是否是网络错误
            if (errorMessage.includes('Failed to fetch') ||
                errorMessage.includes('Network Error') ||
                errorMessage.includes('Connection error')) {
                errorMessage = `连接错误: 无法连接到 OpenRouter API。请检查您的网络连接和API设置。
原始错误: ${errorMessage}`;
            }

            // 检查是否是认证错误
            else if (errorMessage.includes('401') || errorMessage.includes('Unauthorized')) {
                errorMessage = `认证错误: API密钥可能无效。请在设置中检查您的 OpenRouter API 密钥。
原始错误: ${errorMessage}`;
            }

            // 检查是否是URL错误
            else if (errorMessage.includes('404') || errorMessage.includes('Not Found')) {
                errorMessage = `API端点错误: 无法找到指定的API端点。请在设置中检查 OpenRouter 站点URL。
原始错误: ${errorMessage}`;
            }

            // Langchain 可能会有更详细的错误信息
            if ('response' in error && typeof (error as any).response?.data === 'object') {
                errorMessage += `
API响应: ${JSON.stringify((error as any).response.data)}`;
            }

            // 检查是否有 cause 属性
            if ('cause' in error && error.cause) {
                errorMessage += `
错误原因: ${error.cause instanceof Error ? error.cause.message : String(error.cause)}`;
            }
        }

        console.log('格式化后的错误信息:', errorMessage);
        throw new Error(errorMessage);
    }
  }

  /**
   * 获取用于提示的 JSON 格式字符串
   * (这是一个辅助方法，从 setupChain 中提取，避免重复)
   */
  private getClassificationFormatString(): string {
      return `{
  "classifications": [
    {
      "task": "最相关的主题1",
      "reason": "解释为什么笔记属于这个主题（基于内容的具体分析，一句话）",
      "confidence": 0.9
    },
    {
      "task": "第二相关的主题2",
      "reason": "解释为什么笔记属于这个主题（基于内容的具体分析，一句话）",
      "confidence": 0.7
    }
    // ... 最多三个
  ],
  "error": null // 或者 "错误原因"
}`;
  }

  /**
   * 批量分类笔记 (使用重构后的 classifyNote)
   * @param notes 笔记列表
   * @param modelId 可选的模型 ID，如果提供则使用该模型
   * @returns 分类结果列表
   */
  async batchClassifyNotes(notes: string[], modelId?: string): Promise<Array<{
    content: string;
    classifications: ClassificationResult[];
    status: string;
    reason: string;
  }>> {
    if (!notes || notes.length === 0) {
      return [];
    }

    // 如果提供了模型 ID，确保在循环开始前设置好
    if (modelId && modelId.trim() !== '' && modelId !== this.modelId) {
      this.setModelId(modelId);
    }

    const results = [];
    const batchSize = 3; // 每批处理的笔记数量（降低并发请求）
    const batches = [];

    // 将笔记分批
    for (let i = 0; i < notes.length; i += batchSize) {
      batches.push(notes.slice(i, i + batchSize));
    }

    console.log(`开始 Langchain 批量处理 ${notes.length} 条笔记，分为 ${batches.length} 批`);

    for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
      const batch = batches[batchIndex];
      console.log(`处理第 ${batchIndex + 1}/${batches.length} 批，包含 ${batch.length} 条笔记`);

      // 使用 Promise.allSettled 并发处理批内笔记，但要注意 API 限流
      // 为简单起见，并且为了更好地控制延迟，我们依然串行处理批内笔记
      const batchResults = [];
      for (let noteIndex = 0; noteIndex < batch.length; noteIndex++) {
          const note = batch[noteIndex];
          console.log(`处理第 ${batchIndex * batchSize + noteIndex + 1}/${notes.length} 条笔记 (Langchain)`);

          try {
              // 调用重构后的 classifyNote
              const classifications = await this.classifyNote(note); // 不需要再传 modelId，已在循环外设置

              batchResults.push({
                  content: note,
                  classifications,
                  status: classifications.length > 0 ? '成功' : '警告',
                  reason: classifications.length > 0
                      ? `找到 ${classifications.length} 个匹配分类`
                      : '未找到匹配的分类'
              });

          } catch (error: unknown) {
              console.error(`Langchain 分类笔记失败:`, error);

              // 使用与 classifyNote 相同的错误处理逻辑
              let reason = '未知错误';
              if (error instanceof Error) {
                  reason = error.message;

                  // 添加更友好的错误消息
                  if (reason.includes('连接错误:')) {
                      // 已经是格式化过的错误消息
                  } else if (reason.includes('Failed to fetch') ||
                      reason.includes('Network Error') ||
                      reason.includes('Connection error')) {
                      reason = `连接错误: 无法连接到 OpenRouter API。请检查网络连接和API设置。`;
                  } else if (reason.includes('401') || reason.includes('Unauthorized')) {
                      reason = `认证错误: API密钥可能无效。请检查 OpenRouter API 密钥。`;
                  } else if (reason.includes('404') || reason.includes('Not Found')) {
                      reason = `API端点错误: 无法找到指定的API端点。请检查 OpenRouter 站点URL。`;
                  }
              }

              batchResults.push({
                  content: note,
                  classifications: [],
                  status: '错误',
                  reason: reason
              });
          }
          // 每条笔记处理后增加短暂延迟，避免API限流
          if (noteIndex < batch.length - 1) {
              await new Promise<void>(resolve => setTimeout(resolve, 1500)); // 稍长延迟
          }
      }


      results.push(...batchResults);

      // 如果不是最后一批，则等待一段时间，避免API限流
      if (batchIndex < batches.length - 1) {
        console.log(`等待 3 秒后处理下一批`);
        await new Promise<void>(resolve => setTimeout(resolve, 3000));
      }
    }

    console.log(`Langchain 批量处理完成，共处理 ${notes.length} 条笔记`);

    // 确保返回普通的 JavaScript 对象数组，而不是 Proxy
    console.log('原始批量分类结果:', JSON.stringify(results));

    // 使用 JSON 序列化和反序列化来创建一个新的普通对象
    const plainResults = JSON.parse(JSON.stringify(results));
    console.log('转换后的普通对象批量分类结果:', plainResults);

    return plainResults;
  }

  /**
   * 解析批量笔记
   * @param text 包含多条笔记的文本
   * @param separator 笔记之间的分隔符
   * @returns 笔记列表
   */
  parseBatchNotes(text: string, separator: string = '\n\n'): string[] { // 使用 \n\n
    if (!text) {
      return [];
    }

    // 按分隔符拆分文本
    return text.split(separator)
      .map((note: string) => note.trim()) // 添加类型注解
      .filter((note: string) => note.length > 0); // 添加类型注解
  }
}

// 创建单例实例
const noteClassifierInstance = new NoteClassifier();

// 订阅 settings 和 notionItems 变化
// 注意：直接在顶层订阅 store 可能在某些环境下（如 SSR）有问题，
// 但在 Tauri + SvelteKit 的客户端环境中通常是可行的。

settings.subscribe(updatedSettings => {
  // 当设置变化时，更新 API Key, Base URL, 和默认模型
  const currentInstance = noteClassifierInstance as any; // 临时允许访问私有属性
  // 跟踪设置变化
  if (updatedSettings.openrouterApiKey && currentInstance.apiKey !== updatedSettings.openrouterApiKey) {
      console.log('NoteClassifier: 检测到 API Key 变化');
      currentInstance.apiKey = updatedSettings.openrouterApiKey;

      // 重新创建 ChatOpenAI 实例，因为 API Key 变化后需要重新初始化
      currentInstance.model = new ChatOpenAI({
          apiKey: updatedSettings.openrouterApiKey,
          modelName: currentInstance.modelId,
          openAIApiKey: updatedSettings.openrouterApiKey,
          configuration: {
              baseURL: currentInstance.baseUrl,
              defaultHeaders: {
                  'HTTP-Referer': 'https://peckbyte.app',
                  'X-Title': 'PeckByte Note Classifier',
                  'Content-Type': 'application/json'
              }
          },
          temperature: 0.1,
          maxRetries: 2,
      });

      // 重新设置调用链
      currentInstance.setupChain();
      console.log('NoteClassifier: 已重新创建 ChatOpenAI 实例并更新调用链，使用新的 API Key');
  }
  if (updatedSettings.openrouterSiteUrl) {
      // 始终检查 URL 是否包含 /chat/completions，无论是否与当前 URL 相同
      let cleanUrl = updatedSettings.openrouterSiteUrl;
      let urlChanged = false;

      // 彻底清理 URL，移除所有可能的 /chat/completions 路径
      while (cleanUrl.includes('/chat/completions')) {
          cleanUrl = cleanUrl.replace('/chat/completions', '');
          console.log('NoteClassifier: 检测到 URL 包含 /chat/completions 路径，已自动移除');
          urlChanged = true;
      }

      // 如果 URL 发生了变化，或者与当前 URL 不同
      if (urlChanged || currentInstance.baseUrl !== cleanUrl) {
          console.log('NoteClassifier: OpenRouter URL 需要更新，从', currentInstance.baseUrl, '更新为', cleanUrl);
          currentInstance.baseUrl = cleanUrl;

          // 重新创建 ChatOpenAI 实例，因为 baseURL 变化后需要重新初始化
          currentInstance.model = new ChatOpenAI({
              apiKey: currentInstance.apiKey,
              modelName: currentInstance.modelId,
              openAIApiKey: currentInstance.apiKey,
              configuration: {
                  baseURL: cleanUrl, // 使用清理后的 URL
                  defaultHeaders: {
                      'HTTP-Referer': 'https://peckbyte.app',
                      'X-Title': 'PeckByte Note Classifier',
                      'Content-Type': 'application/json'
                  }
              },
              temperature: 0.1,
              maxRetries: 2,
          });

          // 重新设置调用链
          currentInstance.setupChain();
          console.log('NoteClassifier: 已重新创建 ChatOpenAI 实例并更新调用链，新的 URL:', cleanUrl);
      } else {
          console.log('NoteClassifier: OpenRouter URL 无需更新，当前 URL:', currentInstance.baseUrl);
      }
  }
  if (updatedSettings.models && updatedSettings.models.length > 0) {
      const newDefaultModelId = updatedSettings.models[0].id;
      if (currentInstance.modelId !== newDefaultModelId) {
          noteClassifierInstance.setModelId(newDefaultModelId); // 使用公共方法更新模型
          console.log('NoteClassifier: 从设置更新了默认模型:', newDefaultModelId);
      }
  }
  // 如果设置（如API Key, Base URL, 模型）发生变化，重新设置链可能是必要的
  // 这里我们已经在 setModelId 中处理了模型变化，其他变化可能需要更复杂的处理
  // 或许在每次 classifyNote 前检查并重新 setupChain 是更健壮的方式
});

notionItems.subscribe(() => {
  // 当 Notion 任务列表变化时，更新分类器的任务列表
  console.log("NoteClassifier: 检测到 Notion 任务列表变化，尝试更新...");
  noteClassifierInstance['loadTasks'](); // 调用私有方法更新任务并重设链
});


// 导出单例实例
export const noteClassifier = noteClassifierInstance;
