/**
 * Tauri API 调用封装
 */
import { invoke } from '@tauri-apps/api/core';
import { getDatabaseItemsFromLocalStorage, saveDatabaseItemsToLocalStorage, fetchDatabaseItems } from './notion';
import settings from '$lib/stores/settings';
import { get } from 'svelte/store';

/**
 * 获取Notion数据库条目 (已弃用 - 使用 notion.ts 中的 fetchDatabaseItems)
 * 优先从本地存储获取，如果失败则尝试从Notion API获取
 */
/* // 函数已注释掉，因为 +page.svelte 直接调用 notion.ts
export async function getNotionDatabaseItems(forceRefresh = false) {
  try {
    // 如果不是强制刷新，先尝试从本地获取
    if (!forceRefresh) {
      const localItems = getDatabaseItemsFromLocalStorage();
      if (localItems && localItems.length > 0) {
        return localItems;
      }
    }

    // 获取设置
    const settingsData = get(settings);
    
    // 检查是否有必要的Notion API配置
    if (!settingsData.notionApiKey || !settingsData.notionDatabaseId) {
      throw new Error('缺少Notion API密钥或数据库ID，请在设置中配置');
    }

    // 调用Tauri命令（如果在Tauri环境）
    // 此处需要在src-tauri/src/main.rs中添加对应的命令
    // 但由于没有直接修改Rust代码的权限，先用前端API调用
    try {
      // 尝试调用Tauri命令
      return await invoke('fetch_notion_database', {
        apiKey: settingsData.notionApiKey,
        databaseId: settingsData.notionDatabaseId
      });
    } catch (error) {
      console.log('Tauri命令调用失败，使用前端API:', error);
      
      // 如果Tauri命令调用失败，则使用前端API
      // 注意：原始调用有参数错误，fetchDatabaseItems 现在不需要参数
      const items = await fetchDatabaseItems(); 
      
      // 保存到本地存储
      if (items && items.length > 0) {
        saveDatabaseItemsToLocalStorage(items);
      }
      
      return items;
    }
  } catch (error) {
    console.error('获取Notion数据库条目失败:', error);
    throw error;
  }
} 
*/ 