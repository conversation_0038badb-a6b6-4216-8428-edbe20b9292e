import { writable } from 'svelte/store';

export interface Settings {
    notionApiKey: string;
    notionDatabaseId: string;
}

// 默认设置
const defaultSettings: Settings = {
    notionApiKey: '',
    notionDatabaseId: ''
};

// 创建设置存储
export const settings = writable<Settings>(defaultSettings);

// 从本地存储加载设置
export function loadSettings(): void {
    try {
        const savedSettings = localStorage.getItem('notionSettings');
        if (savedSettings) {
            settings.set(JSON.parse(savedSettings));
        }
    } catch (error) {
        console.error('加载设置失败:', error);
    }
}

// 保存设置到本地存储
export function saveSettings(newSettings: Settings): void {
    try {
        localStorage.setItem('notionSettings', JSON.stringify(newSettings));
        settings.set(newSettings);
    } catch (error) {
        console.error('保存设置失败:', error);
    }
}

// 初始化时加载设置
loadSettings(); 