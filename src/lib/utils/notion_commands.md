# Notion API Tauri命令实现指南

本文档描述了如何在Tauri应用程序的Rust后端中实现Notion API请求的命令。

## 必要依赖

在`Cargo.toml`文件的`[dependencies]`部分添加以下依赖：

```toml
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
reqwest = { version = "0.11", features = ["json"] }
tokio = { version = "1", features = ["full"] }
```

## 在main.rs中实现命令

在`src-tauri/src/main.rs`文件中添加以下代码：

```rust
use serde::{Deserialize, Serialize};
use tauri::command;
use std::error::Error;
use reqwest::header::{HeaderMap, HeaderValue, AUTHORIZATION, CONTENT_TYPE};

// 数据库条目结构体，用于返回到前端
#[derive(Serialize, Deserialize, Debug, Clone)]
struct NotionDatabaseItem {
    id: String,
    title: String,
    description: String,
    tags: Vec<String>,
    date: String,
    last_edited_time: String,
    url: String,
}

// 请求Notion数据库命令
#[command]
async fn fetch_notion_database(
    api_key: String,
    database_id: String,
) -> Result<Vec<NotionDatabaseItem>, String> {
    // 检查输入参数
    if api_key.is_empty() || database_id.is_empty() {
        return Err("API密钥或数据库ID不能为空".to_string());
    }

    // 确保数据库ID格式正确（移除可能的连字符）
    let formatted_database_id = database_id.replace("-", "");

    // 创建HTTP客户端
    let client = reqwest::Client::new();

    // 创建请求头
    let mut headers = HeaderMap::new();
    headers.insert(
        AUTHORIZATION,
        HeaderValue::from_str(&format!("Bearer {}", api_key))
            .map_err(|e| format!("无效的API密钥格式: {}", e))?,
    );
    headers.insert(
        "Notion-Version",
        HeaderValue::from_static("2022-06-28"),
    );
    headers.insert(
        CONTENT_TYPE,
        HeaderValue::from_static("application/json"),
    );

    // 发送请求到Notion API
    let response = client
        .post(&format!(
            "https://api.notion.com/v1/databases/{}/query",
            formatted_database_id
        ))
        .headers(headers)
        .json(&serde_json::json!({
            "page_size": 100
        }))
        .send()
        .await
        .map_err(|e| format!("请求Notion API失败: {}", e))?;

    // 检查API响应状态
    if !response.status().is_success() {
        let error_text = response
            .text()
            .await
            .unwrap_or_else(|_| "未能获取错误详情".to_string());
        return Err(format!("Notion API返回错误: {}", error_text));
    }

    // 解析API响应
    let response_json = response
        .json::<serde_json::Value>()
        .await
        .map_err(|e| format!("解析API响应失败: {}", e))?;

    // 将API响应处理为我们需要的格式
    let results = response_json["results"]
        .as_array()
        .ok_or_else(|| "无效的API响应格式".to_string())?;

    // 解析结果
    let mut database_items = Vec::new();
    for item in results {
        let properties = &item["properties"];
        
        // 提取常见属性
        let title = extract_text_property(properties.get("Name").or(properties.get("Title")).or(properties.get("名称")));
        let description = extract_text_property(properties.get("Description").or(properties.get("描述")));
        let tags = extract_multi_select_property(properties.get("Tags").or(properties.get("标签")));
        let date = extract_date_property(properties.get("Date").or(properties.get("日期")));
        
        database_items.push(NotionDatabaseItem {
            id: item["id"].as_str().unwrap_or("").to_string(),
            title,
            description,
            tags,
            date,
            last_edited_time: item["last_edited_time"].as_str().unwrap_or("").to_string(),
            url: item["url"].as_str().unwrap_or("").to_string(),
        });
    }

    Ok(database_items)
}

// 辅助函数：提取文本属性
fn extract_text_property(property: Option<&serde_json::Value>) -> String {
    match property {
        Some(prop) => {
            let text_array = prop.get("title").or(prop.get("rich_text"));
            match text_array {
                Some(array) if array.is_array() => {
                    let arr = array.as_array().unwrap();
                    arr.iter()
                        .filter_map(|text| text.get("plain_text").and_then(|t| t.as_str()))
                        .collect::<Vec<&str>>()
                        .join("")
                }
                _ => String::new(),
            }
        }
        None => String::new(),
    }
}

// 辅助函数：提取多选属性
fn extract_multi_select_property(property: Option<&serde_json::Value>) -> Vec<String> {
    match property {
        Some(prop) => {
            let multi_select = prop.get("multi_select");
            match multi_select {
                Some(array) if array.is_array() => {
                    let arr = array.as_array().unwrap();
                    arr.iter()
                        .filter_map(|item| item.get("name").and_then(|n| n.as_str()).map(|s| s.to_string()))
                        .collect()
                }
                _ => Vec::new(),
            }
        }
        None => Vec::new(),
    }
}

// 辅助函数：提取日期属性
fn extract_date_property(property: Option<&serde_json::Value>) -> String {
    match property {
        Some(prop) => {
            let date = prop.get("date");
            match date {
                Some(d) => d.get("start").and_then(|s| s.as_str()).unwrap_or("").to_string(),
                _ => String::new(),
            }
        }
        None => String::new(),
    }
}

// 在main函数中注册命令
fn main() {
    tauri::Builder::default()
        .invoke_handler(tauri::generate_handler![
            fetch_notion_database,
            // 其他命令...
        ])
        .run(tauri::generate_context!())
        .expect("运行Tauri应用程序时出错");
}
```

## 其他注意事项

1. 确保在Tauri的配置文件（通常是`tauri.conf.json`）中允许发出网络请求：

```json
{
  "tauri": {
    "allowlist": {
      "http": {
        "all": true,
        "request": true,
        "scope": ["https://api.notion.com/*"]
      }
    }
  }
}
```

2. 错误处理是重要的，上述代码包含了基本的错误处理逻辑，但在生产环境中可能需要更完善的处理。

3. 可以根据实际的Notion数据库结构调整属性提取逻辑，Notion API返回的数据结构可能因数据库结构而异。

4. 对于大型数据库，可能需要实现分页功能。

5. 考虑添加缓存机制以减少API调用次数，尤其是当数据库较大时。 
noteId: "dc4aecf01ac611f098c26d2fd51e22d4"
tags: []

---

 