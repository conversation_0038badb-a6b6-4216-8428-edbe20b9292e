/**
 * 入排标准文本处理工具
 */

/**
 * 将文本分割为段落
 * @param text 要分割的文本
 * @returns 分割后的段落数组
 */
export function segmentText(text: string): string[] {
  if (!text || text.trim() === '') {
    return [];
  }

  // 首先尝试按照明显的分隔符分割
  let segments: string[] = [];

  // 1. 按照数字编号分割（如 "1. ", "2. "）
  const numberPattern = /\d+\.\s+/g;
  if (text.match(numberPattern)) {
    segments = text.split(numberPattern).filter(s => s.trim().length > 0);
    if (segments.length > 1) {
      return segments.map(s => s.trim());
    }
  }

  // 2. 按照连续两个或更多换行符分割
  const multiNewlinePattern = /(?:\r?\n){2,}/;
  if (text.match(multiNewlinePattern)) {
    segments = text.split(multiNewlinePattern).filter(s => s.trim().length > 0);
    if (segments.length > 1) {
      return segments.map(s => s.trim());
    }
  }

  // 3. 按照分号分割（常见于中文入排标准）
  const semicolonPattern = /；|;/g;
  if (text.match(semicolonPattern)) {
    segments = text.split(semicolonPattern).filter(s => s.trim().length > 0);
    if (segments.length > 1) {
      return segments.map(s => s.trim());
    }
  }

  // 如果无法分割，返回原始文本作为单个段落
  return [text.trim()];
}

/**
 * 尝试识别段落是入选标准还是排除标准
 * @param segment 文本段落
 * @returns 'inclusion' 或 'exclusion' 或 null（无法确定）
 */
export function identifyCriterionType(segment: string): 'inclusion' | 'exclusion' | null {
  const lowerSegment = segment.toLowerCase();

  // 入选标准的常见关键词
  const inclusionKeywords = [
    '入选标准', '入组标准', '纳入标准', '入选条件', '入组条件', '纳入条件',
    '符合以下条件', '满足以下条件', '年龄在', '年龄介于', '年龄≥', '年龄≤'
  ];

  // 排除标准的常见关键词
  const exclusionKeywords = [
    '排除标准', '排除条件', '不符合以下', '不满足以下', '存在以下情况',
    '有下列情况', '有以下疾病', '患有', '过敏', '不能耐受', '禁忌症'
  ];

  // 检查是否包含入选关键词
  for (const keyword of inclusionKeywords) {
    if (lowerSegment.includes(keyword)) {
      return 'inclusion';
    }
  }

  // 检查是否包含排除关键词
  for (const keyword of exclusionKeywords) {
    if (lowerSegment.includes(keyword)) {
      return 'exclusion';
    }
  }

  // 无法确定
  return null;
}

/**
 * 验证生成的JSON是否符合预期格式
 * @param json 要验证的JSON对象
 * @returns 验证结果，包含是否有效和错误信息
 */
export function validateGeneratedJson(json: any): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  // 检查基本结构
  if (!json) {
    errors.push('JSON为空');
    return { valid: false, errors };
  }

  // 检查criteria数组
  if (!Array.isArray(json.criteria)) {
    errors.push('缺少criteria数组或格式不正确');
    return { valid: false, errors };
  }

  // 检查每个criterion
  for (let i = 0; i < json.criteria.length; i++) {
    const criterion = json.criteria[i];

    // 检查必要字段
    if (!criterion.rule_definition_id) {
      errors.push(`第${i+1}条标准缺少rule_definition_id`);
    }

    if (!criterion.parameter_values || typeof criterion.parameter_values !== 'object') {
      errors.push(`第${i+1}条标准缺少parameter_values或格式不正确`);
    }

    if (!criterion.display_order && criterion.display_order !== 0) {
      errors.push(`第${i+1}条标准缺少display_order`);
    }
  }

  // 检查or_groups（如果存在）
  if (json.or_groups && !Array.isArray(json.or_groups)) {
    errors.push('or_groups格式不正确，应为数组');
  }

  if (json.or_groups && Array.isArray(json.or_groups)) {
    for (let i = 0; i < json.or_groups.length; i++) {
      const group = json.or_groups[i];

      if (!Array.isArray(group.criteria_indices)) {
        errors.push(`第${i+1}个or_group的criteria_indices不是数组`);
      } else if (group.criteria_indices.length < 2) {
        errors.push(`第${i+1}个or_group至少需要包含2个标准索引`);
      }
    }
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * 格式化生成的JSON字符串
 * @param jsonString JSON字符串
 * @returns 格式化后的JSON字符串
 */
export function formatJsonString(jsonString: string): string {
  try {
    const parsed = JSON.parse(jsonString);
    return JSON.stringify(parsed, null, 2);
  } catch (error) {
    console.error('格式化JSON失败:', error);
    return jsonString; // 返回原始字符串
  }
}

/**
 * 合并多个处理结果
 * @param results 处理结果数组
 * @returns 合并后的JSON对象
 */
export function mergeResults(results: any[]): any {
  // 初始化结果
  const merged: {
    criteria: any[];
    or_groups: any[];
  } = {
    criteria: [],
    or_groups: []
  };

  // 合并criteria
  for (const result of results) {
    if (result.criteria && Array.isArray(result.criteria)) {
      merged.criteria.push(...result.criteria);
    }

    if (result.or_groups && Array.isArray(result.or_groups)) {
      merged.or_groups.push(...result.or_groups);
    }
  }

  // 重新排序display_order
  merged.criteria = merged.criteria.map((criterion: any, index: number) => ({
    ...criterion,
    display_order: index + 1
  }));

  return merged;
}
