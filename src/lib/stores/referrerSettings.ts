import { writable, get } from 'svelte/store';

// 定义推荐人管理设置类型
export type ReferrerSettings = {
  serverUrl: string;
  port: number;
  token: string;
  tokenExpiry: string | null;
  username: string;
  initialized: boolean;
};

// 默认设置
const defaultSettings: ReferrerSettings = {
  serverUrl: 'https://api.e3e4.club',
  port: 443, // HTTPS默认端口
  token: '',
  tokenExpiry: null,
  username: '',
  initialized: false
};

// 创建可写 store
const referrerSettings = writable<ReferrerSettings>(defaultSettings);

// 加载设置
export async function loadReferrerSettings() {
  try {
    console.log('开始加载推荐人管理设置');
    // 从本地存储加载设置
    let loadedSettings: Partial<ReferrerSettings> = {};

    try {
      const storedSettings = localStorage.getItem('referrer_settings');
      console.log('从本地存储读取的设置:', storedSettings);

      if (storedSettings) {
        loadedSettings = JSON.parse(storedSettings);
        console.log('解析后的设置:', loadedSettings);
      } else {
        console.log('本地存储中没有找到设置，将使用默认设置');
      }
    } catch (storageError) {
      console.warn('从本地存储加载推荐人管理设置失败:', storageError);
      loadedSettings = { ...defaultSettings };
    }

    // 确保所有必要的字段都有值
    const updatedSettings = {
      serverUrl: loadedSettings.serverUrl || defaultSettings.serverUrl,
      port: loadedSettings.port || defaultSettings.port,
      token: loadedSettings.token || defaultSettings.token,
      tokenExpiry: loadedSettings.tokenExpiry || defaultSettings.tokenExpiry,
      username: loadedSettings.username || defaultSettings.username,
      initialized: true
    };

    console.log('更新后的设置:', updatedSettings);

    referrerSettings.update(state => ({
      ...state,
      ...updatedSettings
    }));

    // 检查token是否有效
    const tokenValid = isTokenValid();
    console.log('Token有效性检查结果:', tokenValid);

    return true;
  } catch (err) {
    console.error('加载推荐人管理设置出错:', err);
    return false;
  }
}

// 保存设置
export async function saveReferrerSettings(newSettings: Partial<ReferrerSettings>) {
  try {
    // 更新本地 store
    referrerSettings.update(state => ({
      ...state,
      ...newSettings,
      initialized: true
    }));

    // 保存到本地存储
    try {
      const currentSettings = get(referrerSettings);
      localStorage.setItem('referrer_settings', JSON.stringify({
        serverUrl: currentSettings.serverUrl,
        port: currentSettings.port,
        token: currentSettings.token,
        tokenExpiry: currentSettings.tokenExpiry,
        username: currentSettings.username
      }));
    } catch (storageError) {
      console.warn('无法将推荐人管理设置保存到本地存储:', storageError);
    }

    return true;
  } catch (err) {
    console.error('保存推荐人管理设置出错:', err);
    return false;
  }
}

// 清除 Token
export function clearReferrerToken() {
  referrerSettings.update(state => ({
    ...state,
    token: '',
    tokenExpiry: null,
    username: ''
  }));

  // 更新本地存储
  try {
    const currentSettings = get(referrerSettings);
    localStorage.setItem('referrer_settings', JSON.stringify({
      serverUrl: currentSettings.serverUrl,
      port: currentSettings.port,
      token: '',
      tokenExpiry: null,
      username: ''
    }));
  } catch (storageError) {
    console.warn('无法将推荐人管理设置保存到本地存储:', storageError);
  }
}

// 设置 Token
export function setReferrerToken(token: string, username: string, expiryInHours = 24) {
  const expiryDate = new Date();
  expiryDate.setHours(expiryDate.getHours() + expiryInHours);

  referrerSettings.update(state => ({
    ...state,
    token,
    username,
    tokenExpiry: expiryDate.toISOString()
  }));

  // 更新本地存储
  try {
    const currentSettings = get(referrerSettings);
    localStorage.setItem('referrer_settings', JSON.stringify({
      serverUrl: currentSettings.serverUrl,
      port: currentSettings.port,
      token,
      tokenExpiry: expiryDate.toISOString(),
      username
    }));
  } catch (storageError) {
    console.warn('无法将推荐人管理设置保存到本地存储:', storageError);
  }
}

// 检查 Token 是否有效
export function isTokenValid(): boolean {
  try {
    const settings = get(referrerSettings);

    if (!settings.token || !settings.tokenExpiry) {
      console.log('Token不存在或过期时间未设置');
      return false;
    }

    const expiryDate = new Date(settings.tokenExpiry);
    const now = new Date();

    const isValid = expiryDate > now;
    if (!isValid) {
      console.log('Token已过期', { expiry: settings.tokenExpiry, now: now.toISOString() });
    }

    return isValid;
  } catch (error) {
    console.error('检查Token有效性时出错:', error);
    return false;
  }
}

export default referrerSettings;
