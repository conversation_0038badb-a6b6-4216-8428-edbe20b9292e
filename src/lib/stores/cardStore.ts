import { writable } from 'svelte/store';

// 创建一个 store 来跟踪当前翻转的卡片 ID
export const activeCardId = writable<string | null>(null);

// 创建一个 store 来跟踪选中的疾病
export const selectedDiseaseStore = writable<string | null>(null);

// 重置所有卡片的函数
export function resetAllCards() {
  activeCardId.set(null);
}

// 重置所有卡片和疾病筛选的函数
export function resetAllCardsAndFilters() {
  activeCardId.set(null);
  selectedDiseaseStore.set(null);
}

// 设置当前活动卡片的函数
export function setActiveCard(id: string) {
  activeCardId.set(id);
}

// 设置选中的疾病
export function setSelectedDisease(disease: string | null) {
  selectedDiseaseStore.set(disease);
}
