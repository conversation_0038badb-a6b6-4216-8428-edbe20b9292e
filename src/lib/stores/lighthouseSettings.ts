import { writable, get } from 'svelte/store';

// 定义 Lighthouse 设置类型
export type LighthouseSettings = {
  serverUrl: string;
  port: number;
  token: string;
  tokenExpiry: string | null;
  username: string;
  initialized: boolean;
};

// 默认设置
const defaultSettings: LighthouseSettings = {
  serverUrl: 'http://43.139.167.155',
  port: 3001,
  token: '',
  tokenExpiry: null,
  username: '',
  initialized: false
};

// 创建可写 store
const lighthouseSettings = writable<LighthouseSettings>(defaultSettings);

// 加载设置
export async function loadLighthouseSettings() {
  try {
    console.log('开始加载 Lighthouse 设置');
    // 从本地存储加载设置
    let loadedSettings: Partial<LighthouseSettings> = {};

    try {
      const storedSettings = localStorage.getItem('lighthouse_settings');
      console.log('从本地存储读取的设置:', storedSettings);

      if (storedSettings) {
        loadedSettings = JSON.parse(storedSettings);
        console.log('解析后的设置:', loadedSettings);
      } else {
        console.log('本地存储中没有找到设置，将使用默认设置');
      }
    } catch (storageError) {
      console.warn('从本地存储加载 Lighthouse 设置失败:', storageError);
      loadedSettings = { ...defaultSettings };
    }

    // 确保所有必要的字段都有值
    const updatedSettings = {
      serverUrl: loadedSettings.serverUrl || defaultSettings.serverUrl,
      port: loadedSettings.port || defaultSettings.port,
      token: loadedSettings.token || defaultSettings.token,
      tokenExpiry: loadedSettings.tokenExpiry || defaultSettings.tokenExpiry,
      username: loadedSettings.username || defaultSettings.username,
      initialized: true
    };

    console.log('更新后的设置:', updatedSettings);

    lighthouseSettings.update(state => ({
      ...state,
      ...updatedSettings
    }));

    // 检查token是否有效
    const tokenValid = isTokenValid();
    console.log('Token有效性检查结果:', tokenValid);

    return true;
  } catch (err) {
    console.error('加载 Lighthouse 设置出错:', err);
    return false;
  }
}

// 保存设置
export async function saveLighthouseSettings(newSettings: Partial<LighthouseSettings>) {
  try {
    // 更新本地 store
    lighthouseSettings.update(state => ({
      ...state,
      ...newSettings,
      initialized: true
    }));

    // 保存到本地存储
    try {
      const currentSettings = get(lighthouseSettings);
      localStorage.setItem('lighthouse_settings', JSON.stringify({
        serverUrl: currentSettings.serverUrl,
        port: currentSettings.port,
        token: currentSettings.token,
        tokenExpiry: currentSettings.tokenExpiry,
        username: currentSettings.username
      }));
    } catch (storageError) {
      console.warn('无法将 Lighthouse 设置保存到本地存储:', storageError);
    }

    return true;
  } catch (err) {
    console.error('保存 Lighthouse 设置出错:', err);
    return false;
  }
}

// 清除 Token
export function clearLighthouseToken() {
  lighthouseSettings.update(state => ({
    ...state,
    token: '',
    tokenExpiry: null,
    username: ''
  }));

  // 更新本地存储
  try {
    const currentSettings = get(lighthouseSettings);
    localStorage.setItem('lighthouse_settings', JSON.stringify({
      serverUrl: currentSettings.serverUrl,
      port: currentSettings.port,
      token: '',
      tokenExpiry: null,
      username: ''
    }));
  } catch (storageError) {
    console.warn('无法将 Lighthouse 设置保存到本地存储:', storageError);
  }
}

// 设置 Token
export function setLighthouseToken(token: string, username: string, expiryInHours = 24) {
  const expiryDate = new Date();
  expiryDate.setHours(expiryDate.getHours() + expiryInHours);

  lighthouseSettings.update(state => ({
    ...state,
    token,
    username,
    tokenExpiry: expiryDate.toISOString()
  }));

  // 更新本地存储
  try {
    const currentSettings = get(lighthouseSettings);
    localStorage.setItem('lighthouse_settings', JSON.stringify({
      serverUrl: currentSettings.serverUrl,
      port: currentSettings.port,
      token,
      tokenExpiry: expiryDate.toISOString(),
      username
    }));
  } catch (storageError) {
    console.warn('无法将 Lighthouse 设置保存到本地存储:', storageError);
  }
}

// 检查 Token 是否有效
export function isTokenValid(): boolean {
  try {
    const settings = get(lighthouseSettings);

    if (!settings.token || !settings.tokenExpiry) {
      console.log('Token不存在或过期时间未设置');
      return false;
    }

    const expiryDate = new Date(settings.tokenExpiry);
    const now = new Date();

    const isValid = expiryDate > now;
    if (!isValid) {
      console.log('Token已过期', { expiry: settings.tokenExpiry, now: now.toISOString() });
    }

    return isValid;
  } catch (error) {
    console.error('检查Token有效性时出错:', error);
    return false;
  }
}

export default lighthouseSettings;
