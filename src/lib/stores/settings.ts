import { writable, get } from 'svelte/store';
import { invoke } from '@tauri-apps/api/core';

// 定义设置类型
export type Settings = {
  openrouterApiKey: string;
  openrouterSiteUrl: string;
  notionApiKey: string;
  notionDatabaseId: string;
  notionInspirationDbId: string;
  models: { id: string; name: string }[];
  initialized: boolean;
};

// 默认设置
const defaultSettings: Settings = {
  openrouterApiKey: '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',
  openrouterSiteUrl: 'https://openrouter.ai/api/v1',
  notionApiKey: 'ntn_V40341044546PN6Fp3PvPWFVHa9qOvfuv2BAEQiuPddbhP',
  notionDatabaseId: '1ca0f0738c8680edb18cde821a4158a8',
  notionInspirationDbId: '1cd0f0738c868027b960dc8129fbd159',
  models: [
    { id: "google/gemini-2.0-flash-001", name: "Gemini 2" },
    { id: "google/gemini-2.5-pro-exp-03-25:free", name: "Gemini 2.5" },
    { id: "deepseek/deepseek-chat-v3-0324", name: "Deepseek Chat v3" },
    { id: "openrouter/optimus-alpha", name: "Quasar Alpha" }
  ],
  initialized: false
};

// 创建可写store
const settings = writable<Settings>(defaultSettings);

// 加载设置
export async function loadSettings() {
  try {
    // 先尝试从本地存储加载设置
    let loadedSettings: any = {};
    let loadedModels = defaultSettings.models;

    try {
      const storedSettings = localStorage.getItem('app_settings');
      if (storedSettings) {
        const parsedSettings = JSON.parse(storedSettings);
        loadedSettings = {
          openrouter_api_key: parsedSettings.openrouterApiKey,
          openrouter_site_url: parsedSettings.openrouterSiteUrl,
          notion_api_key: parsedSettings.notionApiKey,
          notion_database_id: parsedSettings.notionDatabaseId,
          notion_inspiration_db_id: parsedSettings.notionInspirationDbId
        };

        // 加载模型列表
        if (Array.isArray(parsedSettings.models) && parsedSettings.models.length > 0) {
          loadedModels = parsedSettings.models;
        }
      }
    } catch (storageError) {
      console.warn('从本地存储加载设置失败:', storageError);

      // 如果本地存储加载失败，使用默认值
      loadedSettings = {
        openrouter_api_key: defaultSettings.openrouterApiKey,
        openrouter_site_url: defaultSettings.openrouterSiteUrl,
        notion_api_key: defaultSettings.notionApiKey,
        notion_database_id: defaultSettings.notionDatabaseId,
        notion_inspiration_db_id: defaultSettings.notionInspirationDbId
      };
    }

    // 实际实现需要在src-tauri/src/main.rs中添加对应命令
    // const backendSettings = await invoke('load_settings');
    // if (backendSettings) {
    //   loadedSettings = backendSettings;
    // }

    settings.update(state => ({
      ...state,
      openrouterApiKey: loadedSettings.openrouter_api_key || defaultSettings.openrouterApiKey,
      openrouterSiteUrl: loadedSettings.openrouter_site_url || defaultSettings.openrouterSiteUrl,
      notionApiKey: loadedSettings.notion_api_key || defaultSettings.notionApiKey,
      notionDatabaseId: loadedSettings.notion_database_id || defaultSettings.notionDatabaseId,
      notionInspirationDbId: loadedSettings.notion_inspiration_db_id || defaultSettings.notionInspirationDbId,
      models: loadedModels,
      initialized: true
    }));

    return true;
  } catch (err) {
    console.error('加载设置出错:', err);
    return false;
  }
}

// 保存设置
export async function saveSettings(newSettings: Partial<Settings>) {
  try {
    // 更新本地store
    settings.update(state => ({
      ...state,
      ...newSettings,
      initialized: true
    }));

    // 实际实现需要在src-tauri/src/main.rs中添加对应命令
    // await invoke('save_settings', {
    //   openrouterApiKey: newSettings.openrouterApiKey,
    //   openrouterSiteUrl: newSettings.openrouterSiteUrl,
    //   notionApiKey: newSettings.notionApiKey,
    //   notionDatabaseId: newSettings.notionDatabaseId,
    //   notionInspirationDbId: newSettings.notionInspirationDbId,
    //   models: newSettings.models
    // });

    // 尝试将设置保存到本地存储
    try {
      const currentSettings = get(settings);
      localStorage.setItem('app_settings', JSON.stringify({
        openrouterApiKey: currentSettings.openrouterApiKey,
        openrouterSiteUrl: currentSettings.openrouterSiteUrl,
        notionApiKey: currentSettings.notionApiKey,
        notionDatabaseId: currentSettings.notionDatabaseId,
        notionInspirationDbId: currentSettings.notionInspirationDbId,
        models: currentSettings.models
      }));
    } catch (storageError) {
      console.warn('无法将设置保存到本地存储:', storageError);
    }

    // 临时模拟延迟
    await new Promise(resolve => setTimeout(resolve, 500));

    return true;
  } catch (err) {
    console.error('保存设置出错:', err);
    return false;
  }
}

export default settings;