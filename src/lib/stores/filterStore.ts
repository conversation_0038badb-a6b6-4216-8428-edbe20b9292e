import { writable } from 'svelte/store';

// Define the filter state interface
export interface ProjectFilterState {
  activeFilter: 'all' | 'ongoing' | 'finished' | 'recruiting';
  selectedDiseaseFilter: string | null;
  searchName: string;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
  currentPage: number;
  selectedDiseaseId: number | null;
  selectedStageId: number | null;
  selectedStatusId: number | null;
  selectedRecruitmentStatusId: number | null;
  // 卡片翻转状态
  activeCardId: string | null;
}

// Default filter state
const defaultFilterState: ProjectFilterState = {
  activeFilter: 'ongoing',
  selectedDiseaseFilter: null,
  searchName: '',
  sortBy: 'project_name',
  sortOrder: 'asc',
  currentPage: 1,
  selectedDiseaseId: null,
  selectedStageId: null,
  selectedStatusId: null,
  selectedRecruitmentStatusId: null,
  activeCardId: null
};

// Create a writable store with the default state
export const projectFilterStore = writable<ProjectFilterState>(defaultFilterState);

// Helper function to save the current filter state
export function saveFilterState(state: Partial<ProjectFilterState>) {
  projectFilterStore.update(currentState => ({
    ...currentState,
    ...state
  }));
}

// Helper function to reset the filter state to default
export function resetFilterState() {
  projectFilterStore.set(defaultFilterState);
}
