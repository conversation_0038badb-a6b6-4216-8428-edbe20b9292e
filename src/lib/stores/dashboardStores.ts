import { writable, derived } from 'svelte/store';
import type {
  ChartDataPoint,
  DashboardOverview,
  ProjectStatusDistribution
} from '$lib/services/dashboardService';

// 原始仪表盘数据
export const rawDashboardData = writable<{
  overview?: DashboardOverview;
  projectStatusDistribution?: ProjectStatusDistribution[];
  projectStatusChartData?: ChartDataPoint[];
  projectStageDistribution?: any[];
  projectStageChartData?: ChartDataPoint[];
  recruitmentStatusDistribution?: any[];
  recruitmentStatusChartData?: ChartDataPoint[];
  diseaseDistribution?: any[];
  diseaseChartData?: ChartDataPoint[];
  monthlyNewProjects?: any[];
  monthlyNewProjectsChartData?: ChartDataPoint[];
}>({
  projectStatusDistribution: [],
  projectStageDistribution: [],
  recruitmentStatusDistribution: [],
  diseaseDistribution: [],
  monthlyNewProjects: [],
  projectStatusChartData: [],
  projectStageChartData: [],
  recruitmentStatusChartData: [],
  diseaseChartData: [],
  monthlyNewProjectsChartData: [],
});

// 加载状态
export const isLoading = writable<{
  overview: boolean;
  projectStatus: boolean;
  projectStage: boolean;
  recruitmentStatus: boolean;
  disease: boolean;
  monthlyNewProjects: boolean;
  financial: boolean;
  personnel: boolean;
  timeline: boolean;
}>({
  overview: false,
  projectStatus: false,
  projectStage: false,
  recruitmentStatus: false,
  disease: false,
  monthlyNewProjects: false,
  financial: false,
  personnel: false,
  timeline: false
});

// 错误状态
export const errors = writable<{
  overview?: string;
  projectStatus?: string;
  projectStage?: string;
  recruitmentStatus?: string;
  disease?: string;
  monthlyNewProjects?: string;
  financial?: string;
  personnel?: string;
  timeline?: string;
}>({});

// 派生的项目状态图表配置
export const projectStatusChartOptions = derived(
  [rawDashboardData],
  ([$rawDashboardData]) => {
    if (!$rawDashboardData.projectStatusChartData) {
      return {};
    }

    return {
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        data: $rawDashboardData.projectStatusChartData.map(item => item.name)
      },
      series: [
        {
          name: '项目状态',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 16,
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: $rawDashboardData.projectStatusChartData.map(item => ({
            name: item.name,
            value: item.value
          }))
        }
      ]
    };
  }
);

// 派生的项目阶段图表配置
export const projectStageChartOptions = derived(
  [rawDashboardData],
  ([$rawDashboardData]) => {
    if (!$rawDashboardData.projectStageChartData) {
      return {};
    }

    return {
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        data: $rawDashboardData.projectStageChartData.map(item => item.name)
      },
      series: [
        {
          name: '项目阶段',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 16,
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: $rawDashboardData.projectStageChartData.map(item => ({
            name: item.name,
            value: item.value
          }))
        }
      ]
    };
  }
);

// 派生的招募状态图表配置
export const recruitmentStatusChartOptions = derived(
  [rawDashboardData],
  ([$rawDashboardData]) => {
    if (!$rawDashboardData.recruitmentStatusChartData) {
      return {};
    }

    return {
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        data: $rawDashboardData.recruitmentStatusChartData.map(item => item.name)
      },
      series: [
        {
          name: '招募状态',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 16,
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: $rawDashboardData.recruitmentStatusChartData.map(item => ({
            name: item.name,
            value: item.value
          }))
        }
      ]
    };
  }
);

// 派生的疾病领域图表配置
export const diseaseChartOptions = derived(
  [rawDashboardData],
  ([$rawDashboardData]) => {
    if (!$rawDashboardData.diseaseChartData) {
      return {};
    }

    return {
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c} 个项目'
      },
      series: [
        {
          name: '疾病领域',
          type: 'bar',
          data: $rawDashboardData.diseaseChartData.map(item => ({
            name: item.name,
            value: item.value
          })),
          itemStyle: {
            borderRadius: 5
          }
        }
      ],
      xAxis: {
        type: 'category',
        data: $rawDashboardData.diseaseChartData.map(item => item.name),
        axisLabel: {
          rotate: 45,
          interval: 0
        }
      },
      yAxis: {
        type: 'value'
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        containLabel: true
      }
    };
  }
);

// 派生的每月新启动项目数图表配置
export const monthlyNewProjectsChartOptions = derived(
  [rawDashboardData],
  ([$rawDashboardData]) => {
    if (!$rawDashboardData.monthlyNewProjectsChartData) {
      return {};
    }

    // 按月份排序
    const sortedData = [...$rawDashboardData.monthlyNewProjectsChartData].sort((a, b) => {
      return a.name.localeCompare(b.name);
    });

    return {
      tooltip: {
        trigger: 'axis',
        formatter: '{b}: {c} 个项目'
      },
      xAxis: {
        type: 'category',
        data: sortedData.map(item => item.name),
        axisLabel: {
          rotate: 45,
          interval: 0
        }
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '新项目数',
          type: 'line',
          data: sortedData.map(item => item.value),
          smooth: true,
          symbol: 'circle',
          symbolSize: 8,
          itemStyle: {
            color: '#5470c6'
          },
          lineStyle: {
            width: 3
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(84, 112, 198, 0.5)'
                },
                {
                  offset: 1,
                  color: 'rgba(84, 112, 198, 0.1)'
                }
              ]
            }
          }
        }
      ],
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        containLabel: true
      }
    };
  }
);
