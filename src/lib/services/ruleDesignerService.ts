import { invoke } from '@tauri-apps/api/core';

// 规则定义类型
export interface RuleDefinition {
  rule_definition_id?: number;
  rule_name: string;
  rule_description?: string;
  category?: string;
  parameter_schema: string; // JSON string
  created_at?: string;
  updated_at?: string;
}

// 项目标准类型
export interface ProjectCriterion {
  project_criterion_id?: number;
  project_id: string;
  rule_definition_id: number;
  criterion_type: string; // "inclusion" or "exclusion"
  parameter_values: string; // JSON string
  is_active?: boolean;
  display_order?: number;
  created_at?: string;
  updated_at?: string;
  criteria_group_id?: string;
  group_operator?: string; // "OR"
}

// 项目标准详情类型（包含规则定义信息）
export interface ProjectCriterionWithRule {
  criterion: ProjectCriterion;
  rule_definition: RuleDefinition;
}

// 规则定义查询参数
export interface RuleDefinitionQuery {
  category?: string;
}

// 项目标准查询参数
export interface ProjectCriterionQuery {
  project_id: string;
  criterion_type?: string; // "inclusion" or "exclusion"
}

// 创建规则定义请求
export interface CreateRuleDefinitionRequest {
  rule_name: string;
  rule_description?: string;
  category?: string;
  parameter_schema: string; // JSON string
}

// 更新规则定义请求
export interface UpdateRuleDefinitionRequest {
  rule_name?: string;
  rule_description?: string;
  category?: string;
  parameter_schema?: string; // JSON string
}

// 创建项目标准请求
export interface CreateProjectCriterionRequest {
  project_id: string;
  rule_definition_id: number;
  criterion_type: string; // "inclusion" or "exclusion"
  parameter_values: string; // JSON string
  is_active?: boolean;
  display_order?: number;
  criteria_group_id?: string;
  group_operator?: string; // "OR"
}

// 更新项目标准请求
export interface UpdateProjectCriterionRequest {
  rule_definition_id?: number;
  criterion_type?: string; // "inclusion" or "exclusion"
  parameter_values?: string; // JSON string
  is_active?: boolean;
  display_order?: number;
  criteria_group_id?: string | null;
  group_operator?: string | null; // "OR"
}

// 参数类型
export type ParameterType = 'string' | 'number' | 'integer' | 'boolean' | 'enum';

// 参数定义
export interface ParameterDefinition {
  name: string;
  label: string;
  type: ParameterType;
  required: boolean;
  default?: any;
  options?: string[]; // for enum type
  unit?: string;
  readonly?: boolean;
}

// 参数模式
export interface ParameterSchema {
  parameters: ParameterDefinition[];
}

// 规则设计器服务
class RuleDesignerService {
  private dbPath: string;

  constructor() {
    // 使用与项目管理相同的数据库路径，根据数据库文档中的路径
    this.dbPath = '/Users/<USER>/我的文档/sqlite/peckbyte.db';
    console.log('[RuleDesignerService] 初始化服务，数据库路径:', this.dbPath);
  }

  // 初始化规则设计器表
  async initTables(): Promise<boolean> {
    try {
      return await invoke('init_rule_designer_tables', { dbPath: this.dbPath });
    } catch (error) {
      console.error('初始化规则设计器表失败:', error);
      throw error;
    }
  }

  // 获取所有规则定义
  async getRuleDefinitions(query: RuleDefinitionQuery = {}): Promise<RuleDefinition[]> {
    try {
      return await invoke('get_rule_definitions', { query, dbPath: this.dbPath });
    } catch (error) {
      console.error('获取规则定义列表失败:', error);
      throw error;
    }
  }

  // 根据ID获取规则定义
  async getRuleDefinitionById(ruleDefinitionId: number): Promise<RuleDefinition | null> {
    try {
      return await invoke('get_rule_definition_by_id', { ruleDefinitionId, dbPath: this.dbPath });
    } catch (error) {
      console.error('获取规则定义失败:', error);
      throw error;
    }
  }

  // 创建规则定义
  async createRuleDefinition(request: CreateRuleDefinitionRequest): Promise<RuleDefinition> {
    try {
      return await invoke('create_rule_definition', { request, dbPath: this.dbPath });
    } catch (error) {
      console.error('创建规则定义失败:', error);
      throw error;
    }
  }

  // 更新规则定义
  async updateRuleDefinition(ruleDefinitionId: number, request: UpdateRuleDefinitionRequest): Promise<RuleDefinition> {
    try {
      return await invoke('update_rule_definition', { ruleDefinitionId, request, dbPath: this.dbPath });
    } catch (error) {
      console.error('更新规则定义失败:', error);
      throw error;
    }
  }

  // 查找使用特定规则定义的项目
  async findProjectsUsingRule(ruleDefinitionId: number): Promise<Array<[string, string, string]>> {
    try {
      return await invoke('find_projects_using_rule', { ruleDefinitionId, dbPath: this.dbPath });
    } catch (error) {
      console.error('查找使用规则定义的项目失败:', error);
      throw error;
    }
  }

  // 删除规则定义
  async deleteRuleDefinition(ruleDefinitionId: number): Promise<boolean> {
    try {
      return await invoke('delete_rule_definition', { ruleDefinitionId, dbPath: this.dbPath });
    } catch (error) {
      console.error('删除规则定义失败:', error);
      throw error;
    }
  }

  // 获取项目标准
  async getProjectCriteria(query: ProjectCriterionQuery): Promise<ProjectCriterionWithRule[]> {
    try {
      return await invoke('get_project_criteria', { query, dbPath: this.dbPath });
    } catch (error) {
      console.error('获取项目标准列表失败:', error);
      throw error;
    }
  }

  // 根据ID获取项目标准
  async getProjectCriterionById(projectCriterionId: number): Promise<ProjectCriterionWithRule | null> {
    try {
      const result = await invoke('get_project_criterion_by_id', { projectCriterionId, dbPath: this.dbPath });
      return result as ProjectCriterionWithRule | null;
    } catch (error) {
      // 如果是"找不到记录"的错误，返回null而不是抛出异常
      if (error && typeof error === 'string' && error.includes('not found')) {
        console.warn(`项目标准ID ${projectCriterionId} 不存在`);
        return null;
      }
      console.error('获取项目标准失败:', error);
      throw error;
    }
  }

  // 创建项目标准
  async createProjectCriterion(request: CreateProjectCriterionRequest): Promise<ProjectCriterionWithRule> {
    try {
      return await invoke('create_project_criterion', { request, dbPath: this.dbPath });
    } catch (error) {
      console.error('创建项目标准失败:', error);
      throw error;
    }
  }

  // 更新项目标准
  async updateProjectCriterion(projectCriterionId: number, request: UpdateProjectCriterionRequest): Promise<ProjectCriterionWithRule> {
    try {
      return await invoke('update_project_criterion', { projectCriterionId, request, dbPath: this.dbPath });
    } catch (error) {
      console.error('更新项目标准失败:', error);
      throw error;
    }
  }

  // 删除项目标准
  async deleteProjectCriterion(projectCriterionId: number): Promise<boolean> {
    try {
      return await invoke('delete_project_criterion', { projectCriterionId, dbPath: this.dbPath });
    } catch (error) {
      console.error('删除项目标准失败:', error);
      throw error;
    }
  }

  // 解析参数模式
  parseParameterSchema(schema: string): ParameterSchema {
    try {
      // 检查输入是否为空
      if (!schema) {
        console.warn('参数模式为null或undefined，返回空模式');
        return this.createEmptyParameterSchema();
      }

      // 检查输入是否为空字符串
      if (schema.trim() === '') {
        console.warn('参数模式为空字符串，返回空模式');
        return this.createEmptyParameterSchema();
      }

      // 尝试解析JSON
      let parsed;
      try {
        parsed = JSON.parse(schema);
      } catch (jsonError) {
        console.error('JSON解析失败:', jsonError, '原始值:', schema);
        throw new Error(`无效的JSON格式: ${jsonError instanceof Error ? jsonError.message : String(jsonError)}`);
      }

      // 检查解析结果是否为对象
      if (!parsed || typeof parsed !== 'object') {
        console.warn('解析结果不是对象:', parsed);
        throw new Error('参数模式必须是一个JSON对象');
      }

      console.log('解析参数模式成功:', parsed);

      // 确保返回的对象符合 ParameterSchema 接口
      if (!parsed.parameters) {
        console.warn('参数模式不包含 parameters 属性，创建默认模式');
        return this.createEmptyParameterSchema();
      }

      // 确保 parameters 是数组
      if (!Array.isArray(parsed.parameters)) {
        console.warn('parameters 不是数组:', parsed.parameters);
        throw new Error('parameters 必须是一个数组');
      }

      // 验证每个参数定义
      for (let i = 0; i < parsed.parameters.length; i++) {
        const param = parsed.parameters[i];
        if (!param.name) {
          console.warn(`参数 #${i+1} 缺少 name 属性:`, param);
          throw new Error(`参数 #${i+1} 缺少必要的 name 属性`);
        }

        if (!param.type) {
          console.warn(`参数 "${param.name}" 缺少 type 属性:`, param);
          throw new Error(`参数 "${param.name}" 缺少必要的 type 属性`);
        }
      }

      return parsed;
    } catch (error) {
      console.error('解析参数模式失败:', error, '原始值:', schema);
      // 抛出异常，让调用者处理错误
      throw error;
    }
  }

  // 解析参数值
  parseParameterValues(values: string | Record<string, any>): Record<string, any> {
    try {
      // 如果已经是对象，直接返回
      if (values && typeof values === 'object' && !Array.isArray(values)) {
        console.log('参数值已经是对象，无需解析:', values);
        return values as Record<string, any>;
      }

      // 检查输入是否为空
      if (!values) {
        console.warn('参数值为null或undefined，返回空对象');
        return {};
      }

      // 确保values是字符串类型
      if (typeof values !== 'string') {
        console.warn('参数值不是字符串也不是对象，尝试转换:', values);
        try {
          // 尝试将值转换为字符串
          values = String(values);
        } catch (e) {
          console.error('无法将参数值转换为字符串:', e);
          return {};
        }
      }

      // 检查输入是否为空字符串
      if (values.trim() === '') {
        console.warn('参数值为空字符串，返回空对象');
        return {};
      }

      // 尝试解析JSON
      let parsed;
      try {
        parsed = JSON.parse(values);
      } catch (jsonError) {
        console.error('JSON解析失败:', jsonError, '原始值:', values);
        throw new Error(`无效的JSON格式: ${jsonError instanceof Error ? jsonError.message : String(jsonError)}`);
      }

      // 检查解析结果是否为对象
      if (!parsed || typeof parsed !== 'object' || Array.isArray(parsed)) {
        console.warn('解析结果不是对象:', parsed);
        throw new Error('参数值必须是一个JSON对象');
      }

      console.log('解析参数值成功:', parsed);
      return parsed;
    } catch (error) {
      console.error('解析参数值失败:', error, '原始值:', values);
      // 抛出异常，让调用者处理错误
      throw error;
    }
  }

  // 创建空的参数模式
  createEmptyParameterSchema(): ParameterSchema {
    return {
      parameters: []
    };
  }

  // 创建空的参数值
  createEmptyParameterValues(): Record<string, any> {
    return {};
  }

  // 将参数模式转换为JSON字符串
  stringifyParameterSchema(schema: ParameterSchema): string {
    return JSON.stringify(schema, null, 2);
  }

  // 将参数值转换为JSON字符串
  stringifyParameterValues(values: Record<string, any>): string {
    try {
      // 检查输入是否为空
      if (!values) {
        console.warn('参数值为null或undefined，返回空对象字符串');
        return '{}';
      }

      // 检查输入是否为对象
      if (typeof values !== 'object' || Array.isArray(values)) {
        console.warn('参数值不是对象，尝试转换:', values);
        // 如果不是对象，尝试转换为对象
        try {
          if (typeof values === 'string') {
            // 如果是字符串，尝试解析为对象
            values = JSON.parse(values);
          } else {
            // 其他类型，转换为空对象
            values = {};
          }
        } catch (e) {
          console.error('无法将参数值转换为对象:', e);
          return '{}';
        }
      }

      // 处理可能的循环引用
      const seen = new WeakSet();
      const replacer = (key: string, value: any) => {
        // 处理循环引用
        if (typeof value === 'object' && value !== null) {
          if (seen.has(value)) {
            return '[Circular Reference]';
          }
          seen.add(value);
        }
        return value;
      };

      // 序列化为JSON字符串
      return JSON.stringify(values, replacer, 2);
    } catch (error) {
      console.error('序列化参数值失败:', error);
      throw new Error(`序列化参数值失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
}

// 导出单例实例
export const ruleDesignerService = new RuleDesignerService();
