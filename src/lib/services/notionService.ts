import { invoke } from '@tauri-apps/api/core';

/**
 * Notion 同步结果统计
 */
export interface SyncStats {
  total: number;
  created: number;
  updated: number;
  failed: number;
}

/**
 * Notion 同步响应
 */
export interface SyncResponse {
  success: boolean;
  data?: SyncStats;
  error?: string;
}

/**
 * Notion 服务类
 */
export class NotionService {
  /**
   * 将项目数据同步到 Notion
   * @returns 同步结果
   */
  async syncProjectsToNotion(): Promise<SyncResponse> {
    try {
      console.log('开始同步项目到 Notion...');
      const response = await invoke<SyncResponse>('sync_projects_to_notion');

      if (response.success) {
        console.log('同步项目到 Notion 成功:', response.data);
      } else {
        console.error('同步项目到 Notion 失败:', response.error);
      }

      return response;
    } catch (error: any) {
      console.error('同步项目到 Notion 时出错:', error);
      return {
        success: false,
        error: error.message || '未知错误'
      };
    }
  }
}

// 导出单例实例
export const notionService = new NotionService();
