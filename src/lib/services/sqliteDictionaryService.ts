import { invoke } from '@tauri-apps/api/core';

/**
 * 字典
 */
export interface SqliteDict {
  id?: number;
  mongo_oid?: string;
  name: string;
  description?: string;
  type_?: string;
  created_at?: string;
  updated_at?: string;
  version?: number;
  tags?: string[];
  items?: SqliteDictItem[];
}

/**
 * 字典项
 */
export interface SqliteDictItem {
  item_id?: number;
  dictionary_id?: number;
  key: string;
  value: string;
  description?: string;
  status?: string;
}

/**
 * 字典查询
 */
export interface SqliteDictQuery {
  name?: string;
  type_?: string;
  tags?: string[];
}

/**
 * 字典响应
 */
export interface SqliteDictResponse<T> {
  success: boolean;
  error?: string;
  data?: T;
}

/**
 * SQLite 字典服务类
 */
export class SqliteDictionaryService {
  /**
   * 获取所有字典
   * @returns 字典列表
   */
  async getAllDicts(): Promise<SqliteDict[]> {
    try {
      console.log('获取所有字典...');
      const response = await invoke<SqliteDictResponse<SqliteDict[]>>('sqlite_get_all_dicts');

      if (response.success && response.data) {
        return response.data;
      } else {
        console.error('获取所有字典失败:', response.error);
        throw new Error(response.error || '获取所有字典失败');
      }
    } catch (error: any) {
      console.error('获取所有字典失败:', error);
      throw new Error(`获取所有字典失败: ${error.message || '未知错误'}`);
    }
  }

  /**
   * 查询字典
   * @param query 查询条件
   * @returns 字典列表
   */
  async queryDicts(query: SqliteDictQuery): Promise<SqliteDict[]> {
    try {
      console.log('查询字典:', query);
      const response = await invoke<SqliteDictResponse<SqliteDict[]>>('sqlite_query_dicts', { query });

      if (response.success && response.data) {
        return response.data;
      } else {
        console.error('查询字典失败:', response.error);
        throw new Error(response.error || '查询字典失败');
      }
    } catch (error: any) {
      console.error('查询字典失败:', error);
      throw new Error(`查询字典失败: ${error.message || '未知错误'}`);
    }
  }

  /**
   * 根据 ID 获取字典
   * @param id 字典 ID
   * @returns 字典
   */
  async getDictById(id: number): Promise<SqliteDict> {
    try {
      console.log(`根据 ID 获取字典: ${id}`);
      const response = await invoke<SqliteDictResponse<SqliteDict>>('sqlite_get_dict_by_id', { id });

      if (response.success && response.data) {
        return response.data;
      } else {
        console.error('根据 ID 获取字典失败:', response.error);
        throw new Error(response.error || '根据 ID 获取字典失败');
      }
    } catch (error: any) {
      console.error('根据 ID 获取字典失败:', error);
      throw new Error(`根据 ID 获取字典失败: ${error.message || '未知错误'}`);
    }
  }

  /**
   * 根据名称获取字典
   * @param name 字典名称
   * @returns 字典
   */
  async getDictByName(name: string): Promise<SqliteDict> {
    try {
      console.log(`根据名称获取字典: ${name}`);
      const response = await invoke<SqliteDictResponse<SqliteDict>>('sqlite_get_dict_by_name', { name });

      if (response.success && response.data) {
        return response.data;
      } else {
        console.error('根据名称获取字典失败:', response.error);
        throw new Error(response.error || '根据名称获取字典失败');
      }
    } catch (error: any) {
      console.error('根据名称获取字典失败:', error);
      throw new Error(`根据名称获取字典失败: ${error.message || '未知错误'}`);
    }
  }

  /**
   * 创建字典
   * @param dictionary 字典
   * @returns 字典 ID
   */
  async createDict(dictionary: SqliteDict): Promise<number> {
    try {
      console.log('创建字典:', dictionary);
      const response = await invoke<SqliteDictResponse<number>>('sqlite_create_dict', { dictionary });

      if (response.success && response.data !== undefined) {
        return response.data;
      } else {
        console.error('创建字典失败:', response.error);
        throw new Error(response.error || '创建字典失败');
      }
    } catch (error: any) {
      console.error('创建字典失败:', error);
      throw new Error(`创建字典失败: ${error.message || '未知错误'}`);
    }
  }

  /**
   * 更新字典
   * @param id 字典 ID
   * @param dictionary 字典
   * @returns 是否成功
   */
  async updateDict(id: number, dictionary: SqliteDict): Promise<boolean> {
    try {
      console.log(`更新字典: ID = ${id}`, dictionary);
      const response = await invoke<SqliteDictResponse<boolean>>('sqlite_update_dict', { id, dictionary });

      if (response.success) {
        return true;
      } else {
        console.error('更新字典失败:', response.error);
        throw new Error(response.error || '更新字典失败');
      }
    } catch (error: any) {
      console.error('更新字典失败:', error);
      throw new Error(`更新字典失败: ${error.message || '未知错误'}`);
    }
  }

  /**
   * 删除字典
   * @param id 字典 ID
   * @returns 是否成功
   */
  async deleteDict(id: number): Promise<boolean> {
    try {
      console.log(`删除字典: ID = ${id}`);
      console.log(`参数类型: ${typeof id}`);

      // 打印调用参数
      const params = { id };
      console.log('调用参数:', JSON.stringify(params));

      const response = await invoke<SqliteDictResponse<boolean>>('sqlite_delete_dict', params);
      console.log('调用响应:', JSON.stringify(response));

      if (response.success) {
        console.log('删除字典成功');
        return true;
      } else {
        console.error('删除字典失败:', response.error);
        throw new Error(response.error || '删除字典失败');
      }
    } catch (error: any) {
      console.error('删除字典异常:', error);
      if (error instanceof Error) {
        console.error('错误消息:', error.message);
        console.error('错误堆栈:', error.stack);
      }
      throw new Error(`删除字典失败: ${error.message || '未知错误'}`);
    }
  }

  /**
   * 获取字典项
   * @param dictionaryId 字典 ID
   * @returns 字典项列表
   */
  async getDictItems(dictionaryId: number): Promise<SqliteDictItem[]> {
    try {
      console.log(`获取字典项: dictionaryId = ${dictionaryId}`);
      const response = await invoke<SqliteDictResponse<SqliteDictItem[]>>('sqlite_get_dict_items', { dictionaryId });

      if (response.success && response.data) {
        return response.data;
      } else {
        console.error('获取字典项失败:', response.error);
        throw new Error(response.error || '获取字典项失败');
      }
    } catch (error: any) {
      console.error('获取字典项失败:', error);
      throw new Error(`获取字典项失败: ${error.message || '未知错误'}`);
    }
  }

  /**
   * 添加字典项
   * @param dictionaryId 字典 ID
   * @param item 字典项
   * @returns 字典项 ID
   */
  async addDictItem(dictionaryId: number, item: SqliteDictItem): Promise<number> {
    try {
      console.log(`添加字典项: dictionaryId = ${dictionaryId}`, item);
      const response = await invoke<SqliteDictResponse<number>>('sqlite_add_dict_item', { dictionaryId, item });

      if (response.success && response.data !== undefined) {
        return response.data;
      } else {
        console.error('添加字典项失败:', response.error);
        throw new Error(response.error || '添加字典项失败');
      }
    } catch (error: any) {
      console.error('添加字典项失败:', error);
      throw new Error(`添加字典项失败: ${error.message || '未知错误'}`);
    }
  }

  /**
   * 更新字典项
   * @param dictionaryId 字典 ID
   * @param key 字典项键
   * @param item 字典项
   * @returns 是否成功
   */
  async updateDictItem(dictionaryId: number, key: string, item: SqliteDictItem): Promise<boolean> {
    try {
      console.log(`更新字典项: dictionaryId = ${dictionaryId}, key = ${key}`, item);
      const response = await invoke<SqliteDictResponse<boolean>>('sqlite_update_dict_item', { dictionaryId, key, item });

      if (response.success) {
        return true;
      } else {
        console.error('更新字典项失败:', response.error);
        throw new Error(response.error || '更新字典项失败');
      }
    } catch (error: any) {
      console.error('更新字典项失败:', error);
      throw new Error(`更新字典项失败: ${error.message || '未知错误'}`);
    }
  }

  /**
   * 删除字典项
   * @param dictionaryId 字典 ID
   * @param key 字典项键
   * @returns 是否成功
   */
  async deleteDictItem(dictionaryId: number, key: string): Promise<boolean> {
    try {
      console.log(`删除字典项: dictionaryId = ${dictionaryId}, key = ${key}`);
      const response = await invoke<SqliteDictResponse<boolean>>('sqlite_delete_dict_item', { dictionaryId, key });

      if (response.success) {
        return true;
      } else {
        console.error('删除字典项失败:', response.error);
        throw new Error(response.error || '删除字典项失败');
      }
    } catch (error: any) {
      console.error('删除字典项失败:', error);
      throw new Error(`删除字典项失败: ${error.message || '未知错误'}`);
    }
  }
}

// 导出单例实例
export const sqliteDictionaryService = new SqliteDictionaryService();
