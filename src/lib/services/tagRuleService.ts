import { invoke } from '@tauri-apps/api/core';
import { TagColor, type TagRule, type FileResponse } from './fileSystemService';

/**
 * 添加标签规则请求
 */
export interface AddTagRuleRequest {
  label: string;
  keywords: string[];
  tag_color: TagColor;
}

/**
 * 删除标签规则请求
 */
export interface DeleteTagRuleRequest {
  label: string;
}

/**
 * 更新标签规则请求
 */
export interface UpdateTagRuleRequest {
  old_label: string;
  new_label: string;
  keywords: string[];
  tag_color: TagColor;
}

/**
 * 标签规则服务类
 */
export class TagRuleService {
  /**
   * 获取所有标签规则
   * @returns 标签规则列表
   */
  async getTagRules(): Promise<TagRule[]> {
    try {
      console.log('获取标签规则...');
      const response = await invoke<FileResponse>('get_tag_rules');

      if (response.success && response.data) {
        return response.data as TagRule[];
      } else {
        console.error('获取标签规则失败:', response.error);
        throw new Error(response.error || '获取标签规则失败');
      }
    } catch (error: any) {
      console.error('获取标签规则失败:', error);
      throw new Error(`获取标签规则失败: ${error.message || '未知错误'}`);
    }
  }

  /**
   * 添加标签规则
   * @param request 添加标签规则请求
   * @returns 操作结果
   */
  async addTagRule(request: AddTagRuleRequest): Promise<FileResponse> {
    try {
      console.log('添加标签规则:', request);
      return await invoke<FileResponse>('add_tag_rule', { request });
    } catch (error: any) {
      console.error('添加标签规则失败:', error);
      return {
        success: false,
        error: `添加标签规则失败: ${error.message || '未知错误'}`
      };
    }
  }

  /**
   * 删除标签规则
   * @param label 标签名称
   * @returns 操作结果
   */
  async deleteTagRule(label: string): Promise<FileResponse> {
    try {
      console.log('删除标签规则:', label);
      const request: DeleteTagRuleRequest = { label };
      return await invoke<FileResponse>('delete_tag_rule', { request });
    } catch (error: any) {
      console.error('删除标签规则失败:', error);
      return {
        success: false,
        error: `删除标签规则失败: ${error.message || '未知错误'}`
      };
    }
  }

  /**
   * 更新标签规则
   * @param request 更新标签规则请求
   * @returns 操作结果
   */
  async updateTagRule(request: UpdateTagRuleRequest): Promise<FileResponse> {
    try {
      console.log('更新标签规则:', request);
      return await invoke<FileResponse>('update_tag_rule', { request });
    } catch (error: any) {
      console.error('更新标签规则失败:', error);
      return {
        success: false,
        error: `更新标签规则失败: ${error.message || '未知错误'}`
      };
    }
  }
}

// 导出单例实例
export const tagRuleService = new TagRuleService();
