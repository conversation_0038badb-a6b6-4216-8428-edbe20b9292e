import { invoke } from '@tauri-apps/api/core';

/**
 * 文件标签颜色
 */
export type TagColor =
  | 'blue'
  | 'purple'
  | 'cyan'
  | 'green'
  | 'magenta'
  | 'pink'
  | 'red'
  | 'orange'
  | 'yellow'
  | 'volcano'
  | 'geekblue'
  | 'lime'
  | 'gold'
  | 'default';

/**
 * 文件标签
 */
export interface FileTag {
  label: string;
  color: TagColor;
}

/**
 * 文件节点
 */
export interface FileNode {
  key: string;
  title: string;
  is_leaf?: boolean;
  children: FileNode[];
  tags?: FileTag[];
  path: string;
  missing_tags?: FileTag[];
}

/**
 * 文件信息
 */
export interface FileInfo {
  name: string;
  path: string;
  size: number;
  modified_time: string;
  tags?: FileTag[];
}

/**
 * 标签规则
 */
export interface TagRule {
  label: string;
  keywords: string[];
  tag_color: TagColor;
}

/**
 * 文件对话框选项
 */
export interface DialogOptions {
  title?: string;
  default_path?: string;
  multiple?: boolean;
  directory?: boolean;
  filters?: FileFilter[];
}

/**
 * 文件过滤器
 */
export interface FileFilter {
  name: string;
  extensions: string[];
}

/**
 * 文件对话框结果
 */
export interface DialogResult {
  canceled: boolean;
  paths: string[];
}

/**
 * 文件操作响应
 */
export interface FileResponse {
  success: boolean;
  error?: string;
  data?: any;
}

/**
 * 文件系统服务类
 */
export class FileSystemService {
  /**
   * 获取文件列表
   * @param dirPath 目录路径
   * @returns 文件列表
   */
  async getFiles(dirPath: string): Promise<FileInfo[]> {
    if (!dirPath || dirPath.trim() === '') {
      console.error('获取文件列表失败: 目录路径为空');
      throw new Error('目录路径不能为空');
    }

    try {
      console.log(`获取文件列表: ${dirPath}`);

      // 检查路径是否存在
      try {
        // 先尝试获取文件信息，确认路径存在
        await this.getFileInfo(dirPath);
      } catch (err) {
        console.error(`路径不存在或无法访问: ${dirPath}`, err);
        throw new Error(`路径不存在或无法访问: ${dirPath}`);
      }

      const response = await invoke<FileResponse>('get_files', { dirPath });

      if (response.success && response.data) {
        console.log(`成功获取文件列表，路径: ${dirPath}`);
        return response.data as FileInfo[];
      } else {
        console.error(`获取文件列表失败: ${response.error}`);
        throw new Error(response.error || '获取文件列表失败');
      }
    } catch (error: any) {
      console.error(`获取文件列表失败: ${error}`);
      throw new Error(`获取文件列表失败: ${error.message || '未知错误'}`);
    }
  }

  /**
   * 打开文件
   * @param filePath 文件路径
   * @returns 操作结果
   */
  async openFile(filePath: string): Promise<FileResponse> {
    try {
      console.log(`打开文件: ${filePath}`);
      return await invoke<FileResponse>('open_file', { filePath });
    } catch (error: any) {
      console.error(`打开文件失败: ${error}`);
      return {
        success: false,
        error: `打开文件失败: ${error.message || '未知错误'}`
      };
    }
  }

  /**
   * 打开文件夹
   * @param folderPath 文件夹路径
   * @returns 操作结果
   */
  async openFolder(folderPath: string): Promise<FileResponse> {
    try {
      console.log(`打开文件夹: ${folderPath}`);
      return await invoke<FileResponse>('open_folder', { folderPath });
    } catch (error: any) {
      console.error(`打开文件夹失败: ${error}`);
      return {
        success: false,
        error: `打开文件夹失败: ${error.message || '未知错误'}`
      };
    }
  }

  /**
   * 显示打开文件对话框
   * @param options 对话框选项
   * @returns 对话框结果
   */
  async showOpenDialog(options: DialogOptions = {}): Promise<DialogResult> {
    try {
      console.log('显示打开文件对话框');
      return await invoke<DialogResult>('show_open_dialog', { options });
    } catch (error: any) {
      console.error(`显示打开文件对话框失败: ${error}`);
      return {
        canceled: true,
        paths: []
      };
    }
  }

  /**
   * 显示保存文件对话框
   * @param options 对话框选项
   * @returns 对话框结果
   */
  async showSaveDialog(options: DialogOptions = {}): Promise<DialogResult> {
    try {
      console.log('显示保存文件对话框');
      return await invoke<DialogResult>('show_save_dialog', { options });
    } catch (error: any) {
      console.error(`显示保存文件对话框失败: ${error}`);
      return {
        canceled: true,
        paths: []
      };
    }
  }

  /**
   * 导出项目数据到用户选择的文件夹
   * @param content 文件内容
   * @param filename 文件名
   * @param format 文件格式 (csv, json)
   * @returns 导出结果
   */
  async exportProjectsToFolder(content: string, filename: string, format: string): Promise<FileResponse> {
    try {
      console.log(`导出项目数据: ${filename} (格式: ${format})`);
      return await invoke<FileResponse>('export_projects_to_folder', {
        content,
        filename,
        format
      });
    } catch (error: any) {
      console.error(`导出项目数据失败: ${error}`);
      return {
        success: false,
        error: `导出失败: ${error}`,
        data: null
      };
    }
  }

  /**
   * 创建文件
   * @param filePath 文件路径
   * @param content 文件内容
   * @returns 操作结果
   */
  async createFile(filePath: string, content: string): Promise<FileResponse> {
    try {
      console.log(`创建文件: ${filePath}`);
      return await invoke<FileResponse>('create_file', { filePath, content });
    } catch (error: any) {
      console.error(`创建文件失败: ${error}`);
      return {
        success: false,
        error: `创建文件失败: ${error.message || '未知错误'}`
      };
    }
  }

  /**
   * 读取文件
   * @param filePath 文件路径
   * @returns 文件内容
   */
  async readFile(filePath: string): Promise<string> {
    try {
      console.log(`读取文件: ${filePath}`);
      const response = await invoke<FileResponse>('read_file', { filePath });

      if (response.success && response.data) {
        return response.data as string;
      } else {
        console.error(`读取文件失败: ${response.error}`);
        throw new Error(response.error || '读取文件失败');
      }
    } catch (error: any) {
      console.error(`读取文件失败: ${error}`);
      throw new Error(`读取文件失败: ${error.message || '未知错误'}`);
    }
  }

  /**
   * 删除文件
   * @param filePath 文件路径
   * @returns 操作结果
   */
  async deleteFile(filePath: string): Promise<FileResponse> {
    try {
      console.log(`删除文件: ${filePath}`);
      return await invoke<FileResponse>('delete_file', { filePath });
    } catch (error: any) {
      console.error(`删除文件失败: ${error}`);
      return {
        success: false,
        error: `删除文件失败: ${error.message || '未知错误'}`
      };
    }
  }

  /**
   * 创建目录
   * @param dirPath 目录路径
   * @returns 操作结果
   */
  async createDirectory(dirPath: string): Promise<FileResponse> {
    try {
      console.log(`创建目录: ${dirPath}`);
      return await invoke<FileResponse>('create_directory', { dirPath });
    } catch (error: any) {
      console.error(`创建目录失败: ${error}`);
      return {
        success: false,
        error: `创建目录失败: ${error.message || '未知错误'}`
      };
    }
  }

  /**
   * 复制文件
   * @param srcPath 源文件路径
   * @param destPath 目标文件路径
   * @returns 操作结果
   */
  async copyFile(srcPath: string, destPath: string): Promise<FileResponse> {
    try {
      console.log(`复制文件: ${srcPath} -> ${destPath}`);
      return await invoke<FileResponse>('copy_file', { srcPath, destPath });
    } catch (error: any) {
      console.error(`复制文件失败: ${error}`);
      return {
        success: false,
        error: `复制文件失败: ${error.message || '未知错误'}`
      };
    }
  }

  /**
   * 移动文件
   * @param srcPath 源文件路径
   * @param destPath 目标文件路径
   * @returns 操作结果
   */
  async moveFile(srcPath: string, destPath: string): Promise<FileResponse> {
    try {
      console.log(`移动文件: ${srcPath} -> ${destPath}`);
      return await invoke<FileResponse>('move_file', { srcPath, destPath });
    } catch (error: any) {
      console.error(`移动文件失败: ${error}`);
      return {
        success: false,
        error: `移动文件失败: ${error.message || '未知错误'}`
      };
    }
  }

  /**
   * 重命名文件
   * @param filePath 文件路径
   * @param newName 新文件名
   * @returns 新文件路径
   */
  async renameFile(filePath: string, newName: string): Promise<string> {
    try {
      console.log(`重命名文件: ${filePath} -> ${newName}`);
      const response = await invoke<FileResponse>('rename_file', { filePath, newName });

      if (response.success && response.data) {
        return response.data as string;
      } else {
        console.error(`重命名文件失败: ${response.error}`);
        throw new Error(response.error || '重命名文件失败');
      }
    } catch (error: any) {
      console.error(`重命名文件失败: ${error}`);
      throw new Error(`重命名文件失败: ${error.message || '未知错误'}`);
    }
  }

  /**
   * 获取文件信息
   * @param filePath 文件路径
   * @returns 文件信息
   */
  async getFileInfo(filePath: string): Promise<FileInfo> {
    try {
      console.log(`获取文件信息: ${filePath}`);
      const response = await invoke<FileResponse>('get_file_info', { filePath });

      if (response.success && response.data) {
        return response.data as FileInfo;
      } else {
        console.error(`获取文件信息失败: ${response.error}`);
        throw new Error(response.error || '获取文件信息失败');
      }
    } catch (error: any) {
      console.error(`获取文件信息失败: ${error}`);
      throw new Error(`获取文件信息失败: ${error.message || '未知错误'}`);
    }
  }

  /**
   * 检查文件夹缺失标签
   * @param folderPath 文件夹路径
   * @param tagRules 标签规则
   * @returns 缺失标签列表
   */
  async checkMissingTags(folderPath: string, tagRules: TagRule[]): Promise<FileTag[]> {
    try {
      console.log(`检查文件夹缺失标签: ${folderPath}`);
      const response = await invoke<FileResponse>('check_missing_tags', { folderPath, tagRules });

      if (response.success && response.data) {
        return response.data as FileTag[];
      } else {
        console.error(`检查文件夹缺失标签失败: ${response.error}`);
        throw new Error(response.error || '检查文件夹缺失标签失败');
      }
    } catch (error: any) {
      console.error(`检查文件夹缺失标签失败: ${error}`);
      throw new Error(`检查文件夹缺失标签失败: ${error.message || '未知错误'}`);
    }
  }
}

// 导出单例实例
export const fileSystemService = new FileSystemService();
