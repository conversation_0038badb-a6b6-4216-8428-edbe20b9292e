import { get } from 'svelte/store';
import referrerSettings from '$lib/stores/referrerSettings';
import { fetch } from '@tauri-apps/plugin-http';

// 推荐人类型
export interface Referrer {
  id: string;
  name: string;
  phone: string;
  createdAt: string;
  updatedAt: string;
}

// 创建推荐人请求
export interface CreateReferrerRequest {
  name: string;
  phone: string;
}

// 更新推荐人请求
export interface UpdateReferrerRequest {
  name?: string;
  phone?: string;
}

// 推荐关联记录类型
export interface AssociationRecord {
  id: string;
  referrerId: string;
  referrerName: string;
  referrerPhone: string;
  patientId: string;
  patientName: string;
  patientPhone: string;
  createdAt: string;
  updatedAt: string;
}

// API 返回的嵌套结构关联记录类型
export interface ApiAssociationRecord {
  id: string;
  referrer: {
    id: string;
    name: string;
    phone: string;
  };
  patient: {
    gcpmId?: string;
    mbglId?: number;
    name: string;
    phone: string;
  };
  createdAt: string;
  updatedAt: string;
}

// 创建关联记录请求 (前端使用)
export interface CreateAssociationRequest {
  referrerId: string;
  patientId: string;  // 前端使用patientId，内部会转换为patientMBGLId或patientGCPMId
}

// 更新关联记录请求 (前端使用)
export interface UpdateAssociationRequest {
  referrerId?: string;
  patientId?: string;  // 前端使用patientId，内部会转换为patientMBGLId或patientGCPMId
}

// API需要的关联记录请求格式 (内部使用)
interface ApiAssociationRequest {
  referrerId: string;
  patientMBGLId?: number;
  patientGCPMId?: string;
}

// 无推荐人的患者类型
export interface PatientWithoutReferrer {
  id: string;
  name: string;
  phone: string;
}

// API 返回的无推荐人患者类型
export interface ApiPatientWithoutReferrer {
  mbglId?: string | number;        // 慢病管理系统中的患者ID
  patientName?: string;            // 患者姓名
  patientPhone?: string;           // 患者手机号
  createdAt?: string;              // 创建时间
  updatedAt?: string;              // 更新时间
  // 兼容其他可能的字段格式
  id?: string;
  user_id?: string;
  gcpmId?: string;
  name?: string;
  phone?: string;
}

// 分页响应类型
export interface CursorPaginatedResponse<T> {
  items: T[];
  nextCursor: string | null;
  previousCursor: string | null;
  sortBy: string;
  sortOrder: string;
}

// 登录响应类型
export interface LoginResponse {
  access_token: string;
  user: {
    id: number;
    username: string;
    email: string;
  };
}

/**
 * 推荐人管理 API 服务
 */
class ReferrerApiService {
  /**
   * 获取服务器信息
   */
  private getServerInfo() {
    const settings = get(referrerSettings);
    return {
      serverUrl: settings.serverUrl,
      port: settings.port,
      token: settings.token
    };
  }

  /**
   * 构建完整的 API URL
   */
  private buildUrl(endpoint: string): string {
    const { serverUrl, port } = this.getServerInfo();
    // 如果端口是标准端口，则不需要在URL中包含
    if (port === 80 || port === 443) {
      return `${serverUrl}${endpoint}`;
    }
    return `${serverUrl}:${port}${endpoint}`;
  }

  /**
   * 获取请求头
   */
  private getHeaders(includeAuth: boolean = true): HeadersInit {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    };

    if (includeAuth) {
      const { token } = this.getServerInfo();
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }
    }

    return headers;
  }

  /**
   * 处理 API 错误
   */
  private async handleApiError(response: Response): Promise<never> {
    let errorMessage = '请求失败';

    try {
      const errorData = await response.json();
      errorMessage = errorData.message || `请求失败，状态码: ${response.status}`;
    } catch (e) {
      errorMessage = `请求失败，状态码: ${response.status}`;
    }

    throw new Error(errorMessage);
  }

  /**
   * 测试连接
   */
  async testConnection(): Promise<boolean> {
    try {
      // 使用更可靠的健康检查端点或者尝试获取一个公共资源
      // 首先尝试 /health 端点
      let url = this.buildUrl('/health');

      try {
        const response = await fetch(url, {
          method: 'GET',
          headers: this.getHeaders(false)
        });

        if (response.ok) {
          return true;
        }
      } catch (e) {
        console.log('健康检查端点不可用，尝试其他端点');
      }

      // 如果健康检查端点不可用，尝试登录端点
      url = this.buildUrl('/auth/login');

      const response = await fetch(url, {
        method: 'GET',
        headers: this.getHeaders(false)
      });

      // 即使返回 401 未授权，也表示服务器是可访问的
      return response.status < 500;
    } catch (error) {
      console.error('测试连接失败:', error);
      return false;
    }
  }

  /**
   * 登录并获取 Token
   */
  async login(username: string, password: string): Promise<LoginResponse> {
    try {
      console.log('尝试登录，用户名:', username);

      // 尝试主要登录端点
      let url = this.buildUrl('/auth/login');
      console.log('尝试登录URL:', url);

      try {
        const response = await fetch(url, {
          method: 'POST',
          headers: this.getHeaders(false),
          body: JSON.stringify({ username, password })
        });

        if (response.ok) {
          const data = await response.json();
          console.log('登录成功，响应数据:', {
            hasToken: !!data.access_token,
            tokenLength: data.access_token ? data.access_token.length : 0,
            hasUser: !!data.user
          });

          // 验证响应格式
          if (!data.access_token) {
            throw new Error('服务器响应中缺少访问令牌');
          }

          if (!data.user || !data.user.username) {
            // 尝试构造一个合理的用户对象
            data.user = data.user || {};
            data.user.username = username;
            data.user.id = data.user.id || 1;
            data.user.email = data.user.email || `${username}@example.com`;
            console.log('响应中缺少用户信息，已构造默认值:', data.user);
          }

          return data;
        } else if (response.status === 404) {
          // 尝试备用登录端点
          console.log('主要登录端点不存在，尝试备用端点');
          url = this.buildUrl('/api/login');

          const altResponse = await fetch(url, {
            method: 'POST',
            headers: this.getHeaders(false),
            body: JSON.stringify({ username, password })
          });

          if (!altResponse.ok) {
            return this.handleApiError(altResponse);
          }

          const data = await altResponse.json();
          console.log('备用登录成功，响应数据:', {
            hasToken: !!data.access_token || !!data.token,
            tokenLength: (data.access_token || data.token) ? (data.access_token || data.token).length : 0,
            hasUser: !!data.user
          });

          // 适配不同的响应格式
          const result: LoginResponse = {
            access_token: data.access_token || data.token,
            user: data.user || {
              id: 1,
              username: username,
              email: `${username}@example.com`
            }
          };

          if (!result.access_token) {
            throw new Error('服务器响应中缺少访问令牌');
          }

          return result;
        } else {
          return this.handleApiError(response);
        }
      } catch (fetchError) {
        console.error('登录请求失败:', fetchError);
        throw new Error(`登录请求失败: ${fetchError.message || '网络错误'}`);
      }
    } catch (error) {
      console.error('登录失败:', error);
      throw error;
    }
  }

  /**
   * 获取推荐人列表
   */
  async getReferrers(params: {
    limit?: number;
    after?: string;
    before?: string;
    sortBy?: string;
    sortOrder?: string;
    referrerName?: string;
    referrerPhone?: string;
  } = {}): Promise<CursorPaginatedResponse<Referrer>> {
    try {
      const { token } = this.getServerInfo();
      if (!token) {
        throw new Error('未登录，请先登录获取 Token');
      }

      // 构建查询参数
      const queryParams = new URLSearchParams();
      if (params.limit) queryParams.append('limit', params.limit.toString());
      if (params.after) queryParams.append('after', params.after);
      if (params.before) queryParams.append('before', params.before);
      if (params.sortBy) queryParams.append('sortBy', params.sortBy);
      if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);
      if (params.referrerName) queryParams.append('referrerName', params.referrerName);
      if (params.referrerPhone) queryParams.append('referrerPhone', params.referrerPhone);

      // 尝试两种可能的API路径
      let url = this.buildUrl(`/referral-management/referrer?${queryParams.toString()}`);
      console.log('尝试获取推荐人列表，URL:', url);

      try {
        const response = await fetch(url, {
          method: 'GET',
          headers: this.getHeaders()
        });

        if (response.ok) {
          const data = await response.json();
          console.log('成功获取推荐人列表，数据:', data);

          // 验证返回的数据格式
          if (!data.items || !Array.isArray(data.items)) {
            console.warn('API返回的数据格式不符合预期:', data);
            return {
              items: [],
              nextCursor: null,
              previousCursor: null,
              sortBy: params.sortBy || 'createdAt',
              sortOrder: params.sortOrder || 'desc'
            };
          }

          return data;
        } else if (response.status === 404) {
          // 尝试备用路径
          console.log('API路径不存在，尝试备用路径');
          url = this.buildUrl(`/api/referrers?${queryParams.toString()}`);

          const altResponse = await fetch(url, {
            method: 'GET',
            headers: this.getHeaders()
          });

          if (!altResponse.ok) {
            return this.handleApiError(altResponse);
          }

          const data = await altResponse.json();
          console.log('成功从备用路径获取推荐人列表，数据:', data);

          // 验证并适配数据格式
          if (data.referrers && Array.isArray(data.referrers)) {
            return {
              items: data.referrers,
              nextCursor: data.nextCursor || null,
              previousCursor: data.previousCursor || null,
              sortBy: params.sortBy || 'createdAt',
              sortOrder: params.sortOrder || 'desc'
            };
          } else if (Array.isArray(data)) {
            return {
              items: data,
              nextCursor: null,
              previousCursor: null,
              sortBy: params.sortBy || 'createdAt',
              sortOrder: params.sortOrder || 'desc'
            };
          }

          return data;
        } else {
          return this.handleApiError(response);
        }
      } catch (fetchError) {
        console.error('获取推荐人列表请求失败:', fetchError);
        throw new Error(`获取推荐人列表请求失败: ${fetchError.message || '网络错误'}`);
      }
    } catch (error) {
      console.error('获取推荐人列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取单个推荐人
   */
  async getReferrerById(id: string): Promise<Referrer> {
    try {
      const { token } = this.getServerInfo();
      if (!token) {
        throw new Error('未登录，请先登录获取 Token');
      }

      const url = this.buildUrl(`/referral-management/referrer/${id}`);

      const response = await fetch(url, {
        method: 'GET',
        headers: this.getHeaders()
      });

      if (!response.ok) {
        return this.handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      console.error('获取推荐人详情失败:', error);
      throw error;
    }
  }

  /**
   * 创建推荐人
   */
  async createReferrer(data: CreateReferrerRequest): Promise<Referrer> {
    try {
      const { token } = this.getServerInfo();
      if (!token) {
        throw new Error('未登录，请先登录获取 Token');
      }

      const url = this.buildUrl('/referral-management/referrer');

      const response = await fetch(url, {
        method: 'POST',
        headers: this.getHeaders(),
        body: JSON.stringify(data)
      });

      if (!response.ok) {
        return this.handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      console.error('创建推荐人失败:', error);
      throw error;
    }
  }

  /**
   * 更新推荐人
   */
  async updateReferrer(id: string, data: UpdateReferrerRequest): Promise<Referrer> {
    try {
      const { token } = this.getServerInfo();
      if (!token) {
        throw new Error('未登录，请先登录获取 Token');
      }

      const url = this.buildUrl(`/referral-management/referrer/${id}`);

      const response = await fetch(url, {
        method: 'PUT',
        headers: this.getHeaders(),
        body: JSON.stringify(data)
      });

      if (!response.ok) {
        return this.handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      console.error('更新推荐人失败:', error);
      throw error;
    }
  }

  /**
   * 获取关联记录列表
   */
  async getAssociations(params: {
    limit?: number;
    after?: string;
    before?: string;
    sortBy?: string;
    sortOrder?: string;
    patientName?: string;
    patientPhone?: string;
    referrerName?: string;
    referrerPhone?: string;
  } = {}): Promise<CursorPaginatedResponse<ApiAssociationRecord>> {
    try {
      const { token } = this.getServerInfo();
      if (!token) {
        throw new Error('未登录，请先登录获取 Token');
      }

      // 构建查询参数
      const queryParams = new URLSearchParams();
      if (params.limit) queryParams.append('limit', params.limit.toString());
      if (params.after) queryParams.append('after', params.after);
      if (params.before) queryParams.append('before', params.before);
      if (params.sortBy) queryParams.append('sortBy', params.sortBy);
      if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);
      if (params.patientName) queryParams.append('patientName', params.patientName);
      if (params.patientPhone) queryParams.append('patientPhone', params.patientPhone);
      if (params.referrerName) queryParams.append('referrerName', params.referrerName);
      if (params.referrerPhone) queryParams.append('referrerPhone', params.referrerPhone);

      // 尝试两种可能的API路径
      let url = this.buildUrl(`/referral-management/association?${queryParams.toString()}`);
      console.log('尝试获取关联记录列表，URL:', url);

      try {
        const response = await fetch(url, {
          method: 'GET',
          headers: this.getHeaders()
        });

        if (response.ok) {
          const data = await response.json();
          console.log('成功获取关联记录列表，数据:', data);

          // 验证返回的数据格式
          if (!data.items || !Array.isArray(data.items)) {
            console.warn('API返回的数据格式不符合预期:', data);
            return {
              items: [],
              nextCursor: null,
              previousCursor: null,
              sortBy: params.sortBy || 'createdAt',
              sortOrder: params.sortOrder || 'desc'
            };
          }

          return data;
        } else if (response.status === 404) {
          // 尝试备用路径
          console.log('API路径不存在，尝试备用路径');
          url = this.buildUrl(`/api/associations?${queryParams.toString()}`);

          const altResponse = await fetch(url, {
            method: 'GET',
            headers: this.getHeaders()
          });

          if (!altResponse.ok) {
            return this.handleApiError(altResponse);
          }

          const data = await altResponse.json();
          console.log('成功从备用路径获取关联记录列表，数据:', data);

          // 验证并适配数据格式
          if (data.associations && Array.isArray(data.associations)) {
            return {
              items: data.associations,
              nextCursor: data.nextCursor || null,
              previousCursor: data.previousCursor || null,
              sortBy: params.sortBy || 'createdAt',
              sortOrder: params.sortOrder || 'desc'
            };
          } else if (Array.isArray(data)) {
            return {
              items: data,
              nextCursor: null,
              previousCursor: null,
              sortBy: params.sortBy || 'createdAt',
              sortOrder: params.sortOrder || 'desc'
            };
          }

          return data;
        } else {
          return this.handleApiError(response);
        }
      } catch (fetchError) {
        console.error('获取关联记录列表请求失败:', fetchError);
        throw new Error(`获取关联记录列表请求失败: ${fetchError.message || '网络错误'}`);
      }
    } catch (error) {
      console.error('获取关联记录列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取单个关联记录
   */
  async getAssociationById(id: string): Promise<AssociationRecord> {
    try {
      const { token } = this.getServerInfo();
      if (!token) {
        throw new Error('未登录，请先登录获取 Token');
      }

      const url = this.buildUrl(`/referral-management/association/${id}`);

      const response = await fetch(url, {
        method: 'GET',
        headers: this.getHeaders()
      });

      if (!response.ok) {
        return this.handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      console.error('获取关联记录详情失败:', error);
      throw error;
    }
  }

  /**
   * 创建关联记录
   */
  async createAssociation(data: CreateAssociationRequest): Promise<AssociationRecord> {
    try {
      const { token } = this.getServerInfo();
      if (!token) {
        throw new Error('未登录，请先登录获取 Token');
      }

      const url = this.buildUrl('/referral-management/association');

      // 转换参数格式以匹配API要求
      // API需要patientMBGLId而不是patientId
      const apiRequestData = {
        referrerId: data.referrerId,
        patientMBGLId: parseInt(data.patientId) || 0, // 尝试转换为数字
        patientGCPMId: isNaN(parseInt(data.patientId)) ? data.patientId : undefined // 如果不是数字，可能是GCPM ID
      };

      console.log('转换后的API请求数据:', apiRequestData);

      const response = await fetch(url, {
        method: 'POST',
        headers: this.getHeaders(),
        body: JSON.stringify(apiRequestData)
      });

      if (!response.ok) {
        return this.handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      console.error('创建关联记录失败:', error);
      throw error;
    }
  }

  /**
   * 更新关联记录
   */
  async updateAssociation(id: string, data: UpdateAssociationRequest): Promise<AssociationRecord> {
    try {
      const { token } = this.getServerInfo();
      if (!token) {
        throw new Error('未登录，请先登录获取 Token');
      }

      const url = this.buildUrl(`/referral-management/association/${id}`);

      // 转换参数格式以匹配API要求
      const apiRequestData: any = {
        referrerId: data.referrerId
      };

      // 根据API文档，patientMBGLId是必需的
      if (!data.patientId) {
        throw new Error('患者ID不能为空');
      }

      // 转换patientId为API需要的格式
      const patientIdNum = parseInt(data.patientId);
      if (!isNaN(patientIdNum)) {
        // 如果是数字，则作为MBGL ID
        apiRequestData.patientMBGLId = patientIdNum;
      } else {
        // 如果不是数字，则作为GCPM ID
        apiRequestData.patientGCPMId = data.patientId;
      }

      console.log('更新关联，转换后的API请求数据:', apiRequestData);

      const response = await fetch(url, {
        method: 'PUT',
        headers: this.getHeaders(),
        body: JSON.stringify(apiRequestData)
      });

      if (!response.ok) {
        return this.handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      console.error('更新关联记录失败:', error);
      throw error;
    }
  }

  /**
   * 获取没有推荐人的慢病管理患者
   * 注意：此API的默认排序字段是 user_id，而不是 createdAt
   */
  async getPatientsWithoutReferrer(params: {
    limit?: number;
    after?: string;
    before?: string;
    sortBy?: string;  // 默认值应为 'user_id'
    sortOrder?: string;
    patientName?: string;
    patientPhone?: string;
  } = {}): Promise<CursorPaginatedResponse<ApiPatientWithoutReferrer>> {
    try {
      const { token } = this.getServerInfo();
      if (!token) {
        throw new Error('未登录，请先登录获取 Token');
      }

      // 构建查询参数
      const queryParams = new URLSearchParams();
      if (params.limit) queryParams.append('limit', params.limit.toString());
      if (params.after) queryParams.append('after', params.after);
      if (params.before) queryParams.append('before', params.before);
      if (params.sortBy) queryParams.append('sortBy', params.sortBy);
      if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);
      if (params.patientName) queryParams.append('patientName', params.patientName);
      if (params.patientPhone) queryParams.append('patientPhone', params.patientPhone);

      // 尝试两种可能的API路径
      let url = this.buildUrl(`/referral-management/no-referrer-mbgl-patients?${queryParams.toString()}`);
      console.log('尝试获取无推荐人患者列表，URL:', url);

      try {
        const response = await fetch(url, {
          method: 'GET',
          headers: this.getHeaders()
        });

        if (response.ok) {
          const data = await response.json();
          console.log('成功获取无推荐人患者列表，数据:', data);

          // 验证返回的数据格式
          if (!data.items || !Array.isArray(data.items)) {
            console.warn('API返回的数据格式不符合预期:', data);
            return {
              items: [],
              nextCursor: null,
              previousCursor: null,
              sortBy: params.sortBy || 'user_id',
              sortOrder: params.sortOrder || 'desc'
            };
          }

          return data;
        } else if (response.status === 404) {
          // 尝试备用路径
          console.log('API路径不存在，尝试备用路径');
          url = this.buildUrl(`/api/patients/no-referrer?${queryParams.toString()}`);

          const altResponse = await fetch(url, {
            method: 'GET',
            headers: this.getHeaders()
          });

          if (!altResponse.ok) {
            return this.handleApiError(altResponse);
          }

          const data = await altResponse.json();
          console.log('成功从备用路径获取无推荐人患者列表，数据:', data);

          // 验证并适配数据格式
          if (data.patients && Array.isArray(data.patients)) {
            return {
              items: data.patients,
              nextCursor: data.nextCursor || null,
              previousCursor: data.previousCursor || null,
              sortBy: params.sortBy || 'user_id',
              sortOrder: params.sortOrder || 'desc'
            };
          } else if (Array.isArray(data)) {
            return {
              items: data,
              nextCursor: null,
              previousCursor: null,
              sortBy: params.sortBy || 'user_id',
              sortOrder: params.sortOrder || 'desc'
            };
          }

          return data;
        } else {
          return this.handleApiError(response);
        }
      } catch (fetchError) {
        console.error('获取无推荐人患者列表请求失败:', fetchError);
        throw new Error(`获取无推荐人患者列表请求失败: ${fetchError.message || '网络错误'}`);
      }
    } catch (error) {
      console.error('获取无推荐人患者列表失败:', error);
      throw error;
    }
  }
}

export default new ReferrerApiService();
