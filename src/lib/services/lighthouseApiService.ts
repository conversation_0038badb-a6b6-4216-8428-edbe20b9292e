import { get } from 'svelte/store';
import lighthouseSettings from '$lib/stores/lighthouseSettings';
import { fetch } from '@tauri-apps/plugin-http';

// 定义用户类型
export interface LighthouseUser {
  id: number;
  username: string;
  email: string;
  fullName?: string;
  role: string;
  phone?: string;
  address?: string;
  city?: string;
  country?: string;
  postalCode?: string;
  birthDate?: string;
  avatar?: string;
  bio?: string;
  createdAt?: string;
  updatedAt?: string;
}

// 创建用户请求类型
export interface CreateUserRequest {
  username: string;
  email: string;
  password: string;
  fullName?: string;
  role: string;
  phone?: string;
  address?: string;
  city?: string;
  country?: string;
  postalCode?: string;
  birthDate?: string;
  avatar?: string;
  bio?: string;
}

// 更新用户请求类型
export interface UpdateUserRequest {
  username?: string;
  email?: string;
  password?: string;
  fullName?: string;
  // 注意: role 属性在更新用户时不应该包含，API 会返回错误
  // role?: string; // 已移除，因为 API 不接受此字段
  phone?: string;
  address?: string;
  city?: string;
  country?: string;
  postalCode?: string;
  birthDate?: string;
  avatar?: string;
  bio?: string;
}

// 登录请求类型
export interface LoginRequest {
  username: string;
  password: string;
}

// 登录响应类型
export interface LoginResponse {
  access_token: string;
  user: {
    id: number;
    username: string;
    email: string;
    role: string;
  };
}

// 分页响应类型
export interface PaginatedResponse<T> {
  data: T[];
  total: number;
}

/**
 * Lighthouse API 服务
 * 用于与 Lighthouse 后端 API 进行交互
 * 使用 Tauri HTTP 插件解决跨域问题
 */
class LighthouseApiService {
  /**
   * 获取服务器 URL 和端口
   * @returns 服务器 URL 和端口
   */
  private getServerInfo() {
    const settings = get(lighthouseSettings);
    return {
      serverUrl: settings.serverUrl,
      port: settings.port,
      token: settings.token
    };
  }

  /**
   * 测试连接
   * @returns 如果连接成功，返回 true
   */
  async testConnection(): Promise<boolean> {
    try {
      const { serverUrl, port } = this.getServerInfo();

      // 尝试多个端点，提高测试连接的成功率
      const endpoints = [
        '/api/books',       // 不需要认证的 GET 端点
        '/api/auth/login',  // 登录端点（不发送实际请求，只检查可访问性）
        '/'                 // 根路径
      ];

      // 依次尝试每个端点
      for (const endpoint of endpoints) {
        try {
          const url = `${serverUrl}:${port}${endpoint}`;
          console.log(`尝试测试连接 URL: ${url}`);

          // 使用 HEAD 请求检查端点是否可访问，不获取实际内容
          const response = await fetch(url, {
            method: 'HEAD',
            headers: {
              'Accept': 'application/json'
            }
          });

          console.log(`端点 ${endpoint} 响应状态:`, response.status);

          // 2xx 或 3xx 状态码都表示服务器可访问
          if (response.ok || (response.status >= 200 && response.status < 400)) {
            console.log(`连接测试成功，使用端点: ${endpoint}`);
            return true;
          }
        } catch (endpointError) {
          console.warn(`端点 ${endpoint} 测试失败:`, endpointError);
          // 继续尝试下一个端点
        }
      }

      // 如果所有端点都失败，尝试一个简单的 GET 请求到服务器根路径
      try {
        const rootUrl = `${serverUrl}:${port}`;
        console.log(`最后尝试连接到服务器根路径: ${rootUrl}`);

        const response = await fetch(rootUrl, {
          method: 'GET',
          headers: {
            'Accept': 'application/json'
          }
        });

        console.log('根路径响应状态:', response.status);
        return response.status < 500; // 只要不是服务器错误，就认为连接成功
      } catch (rootError) {
        console.error('根路径连接测试失败:', rootError);
        return false;
      }
    } catch (error) {
      console.error('测试连接失败:', error);
      return false;
    }
  }

  /**
   * 登录并获取 Token
   * @param username 用户名
   * @param password 密码
   * @returns 登录响应，包含 token 和用户信息
   */
  async login(username: string, password: string): Promise<LoginResponse> {
    try {
      const { serverUrl, port } = this.getServerInfo();
      const url = `${serverUrl}:${port}/api/auth/login`;

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ username, password })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || '登录失败');
      }

      return await response.json();
    } catch (error) {
      console.error('登录失败:', error);
      throw error;
    }
  }

  /**
   * 获取用户列表
   * @param page 页码，从 1 开始
   * @param limit 每页数量
   * @param search 搜索关键词
   * @returns 分页用户列表
   */
  async getUsers(page = 1, limit = 10, search = ''): Promise<PaginatedResponse<LighthouseUser>> {
    try {
      const { serverUrl, port, token } = this.getServerInfo();

      if (!token) {
        throw new Error('未登录，请先登录获取 Token');
      }

      const skip = (page - 1) * limit;
      let url = `${serverUrl}:${port}/api/users?skip=${skip}&take=${limit}`;

      if (search) {
        url += `&username=${encodeURIComponent(search)}`;
      }

      console.log('Fetching users from URL:', url);

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || '获取用户列表失败');
      }

      const data = await response.json();
      console.log('API response:', data);

      // 确保返回的数据符合预期格式
      if (!data || typeof data !== 'object') {
        throw new Error('API返回的数据格式不符合预期');
      }

      // 如果API返回的不是预期的格式，进行适配
      if (!data.data || !Array.isArray(data.data)) {
        // 尝试适配不同的API响应格式
        if (Array.isArray(data)) {
          // 如果直接返回数组，则包装成预期格式
          return {
            data: data,
            total: data.length
          };
        } else {
          // 返回空结果
          return {
            data: [],
            total: 0
          };
        }
      }

      return data;
    } catch (error) {
      console.error('获取用户列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取单个用户详情
   * @param id 用户 ID
   * @returns 用户详情
   */
  async getUserById(id: number): Promise<LighthouseUser> {
    try {
      const { serverUrl, port, token } = this.getServerInfo();

      if (!token) {
        throw new Error('未登录，请先登录获取 Token');
      }

      const url = `${serverUrl}:${port}/api/users/${id}`;

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || '获取用户详情失败');
      }

      return await response.json();
    } catch (error) {
      console.error('获取用户详情失败:', error);
      throw error;
    }
  }

  /**
   * 创建用户
   * @param user 用户信息
   * @returns 创建的用户
   */
  async createUser(user: CreateUserRequest): Promise<LighthouseUser> {
    try {
      const { serverUrl, port, token } = this.getServerInfo();

      if (!token) {
        throw new Error('未登录，请先登录获取 Token');
      }

      const url = `${serverUrl}:${port}/api/users`;

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(user)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || '创建用户失败');
      }

      return await response.json();
    } catch (error) {
      console.error('创建用户失败:', error);
      throw error;
    }
  }

  /**
   * 更新用户
   * @param id 用户 ID
   * @param user 更新的用户信息
   * @returns 更新后的用户
   */
  async updateUser(id: number, user: UpdateUserRequest): Promise<LighthouseUser> {
    try {
      const { serverUrl, port, token } = this.getServerInfo();

      if (!token) {
        throw new Error('未登录，请先登录获取 Token');
      }

      // 确保不包含 role 属性，因为 API 不接受更新角色
      const userData = { ...user };
      if ('role' in userData) {
        console.warn('更新用户时移除了 role 属性，因为 API 不接受此字段');
        delete (userData as any).role;
      }

      const url = `${serverUrl}:${port}/api/users/${id}`;
      console.log(`更新用户 ID: ${id}, URL: ${url}`);
      console.log('发送的数据:', userData);

      const response = await fetch(url, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(userData)
      });

      if (!response.ok) {
        const errorText = await response.text();
        let errorMessage = '更新用户失败';

        try {
          const errorData = JSON.parse(errorText);
          errorMessage = errorData.message || errorMessage;
          console.error('API 错误响应:', errorData);
        } catch (parseError) {
          console.error('无法解析错误响应:', errorText);
        }

        throw new Error(errorMessage);
      }

      const responseData = await response.json();
      console.log('更新用户成功:', responseData);
      return responseData;
    } catch (error) {
      console.error('更新用户失败:', error);
      throw error;
    }
  }

  /**
   * 删除用户
   * @param id 用户 ID
   * @returns 是否删除成功
   */
  async deleteUser(id: number): Promise<boolean> {
    try {
      const { serverUrl, port, token } = this.getServerInfo();

      if (!token) {
        throw new Error('未登录，请先登录获取 Token');
      }

      const url = `${serverUrl}:${port}/api/users/${id}`;

      const response = await fetch(url, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || '删除用户失败');
      }

      return true;
    } catch (error) {
      console.error('删除用户失败:', error);
      throw error;
    }
  }
}

export default new LighthouseApiService();
