/**
 * SQLite Explorer Service
 * 封装 MCP SQLite Explorer 功能的服务层
 */

export interface QueryParams {
  query: string;
  params?: any[];
  fetch_all?: boolean;
  row_limit?: number;
}

export interface QueryResult {
  [key: string]: any;
}

/**
 * 执行 SQLite 查询
 */
export async function mcp_sqlite_explorer_read_query(params: QueryParams): Promise<QueryResult[]> {
  // 这是一个占位函数，实际应该调用对应的 MCP 功能
  // 在真实环境中，这里应该通过某种方式调用 MCP SQLite Explorer
  
  try {
    // 模拟调用，实际需要替换为真实的 MCP 调用
    console.log('模拟 SQLite 查询:', params);
    
    // 为了演示，返回真实的数据库数据
    
    // 研究分期数据（对应UI中的"项目阶段"）
    if (params.query.includes('dictionary_id = 7') || params.query.includes("d.name = '研究分期'")) {
      return [
        { item_id: 101, item_value: '其他' },
        { item_id: 136, item_value: 'I期' },
        { item_id: 137, item_value: 'II期' },
        { item_id: 138, item_value: 'III期' },
        { item_id: 139, item_value: 'IV期' },
        { item_id: 140, item_value: 'I/II期' },
        { item_id: 141, item_value: 'II/III期' },
        { item_id: 142, item_value: '真实世界研究' }
      ];
    }
    
    // 研究阶段数据（对应UI中的"项目状态"）
    if (params.query.includes('dictionary_id = 6') || params.query.includes("d.name = '研究阶段'")) {
      return [
        { item_id: 37, item_value: '未启动' },
        { item_id: 38, item_value: '在研' },
        { item_id: 39, item_value: '已结束' },
        { item_id: 40, item_value: '暂停中' }
      ];
    }
    
    // 招募状态数据（dictionary_id = 10）
    if (params.query.includes('dictionary_id = 10') || params.query.includes("d.name = '招募状态'")) {
      return [
        { item_id: 58, item_value: '招募中' },
        { item_id: 59, item_value: '暂停招募' },
        { item_id: 60, item_value: '结束招募' }
      ];
    }
    
    // 疾病数据（dictionary_id = 5）
    if (params.query.includes('dictionary_id = 5') || params.query.includes("d.name = '疾病'")) {
      return [
        { item_id: 27, item_value: '化痰' },
        { item_id: 28, item_value: '哮喘' },
        { item_id: 29, item_value: '慢阻肺病' },
        { item_id: 30, item_value: '支气管扩张' },
        { item_id: 31, item_value: '流感' },
        { item_id: 32, item_value: '睡眠呼吸暂停' },
        { item_id: 33, item_value: '社区获得性肺炎' },
        { item_id: 34, item_value: '肺癌' },
        { item_id: 35, item_value: '间质性肺疾病' },
        { item_id: 36, item_value: '院内获得性肺炎' },
        { item_id: 98, item_value: '哮喘' },
        { item_id: 108, item_value: '难治性咳嗽' }
      ];
    }
    
    // 申办方数据
    if (params.query.includes('dictionary_id = 4') || params.query.includes("d.name = '申办方'")) {
      return [
        { item_id: 7, item_value: 'Mylan Pharma UK Ltd' },
        { item_id: 8, item_value: '上海凯宝药业股份有限公司' },
        { item_id: 9, item_value: '优锐医药科技(上海)有限公司' },
        { item_id: 10, item_value: '四川普瑞特药业有限公司' },
        { item_id: 11, item_value: '平安盐野义有限公司' },
        { item_id: 12, item_value: '江苏恒瑞医药股份有限公司' },
        { item_id: 13, item_value: '江西青峰药业有限公司' },
        { item_id: 14, item_value: '浙江宝仁堂药业有限公司' },
        { item_id: 15, item_value: '海思科医药集团股份有限公司' },
        { item_id: 16, item_value: '石药集团中奇制药技术（石家庄）有限公司' },
        { item_id: 17, item_value: '苏州欧米尼医药有限公司' },
        { item_id: 18, item_value: '葛兰素史克' },
        { item_id: 19, item_value: '辉瑞投资有限公司' },
        { item_id: 20, item_value: '阿斯利康投资（中国）有限公司' },
        { item_id: 21, item_value: 'Insilico Medicine Hong Kong Limited' },
        { item_id: 22, item_value: '三生国药' },
        { item_id: 23, item_value: '正大天晴' },
        { item_id: 24, item_value: '赛诺菲' },
        { item_id: 25, item_value: '麦济生物' },
        { item_id: 26, item_value: 'Areteia Therapeutics' },
        { item_id: 102, item_value: '广州呼吸疾健康研究院' },
        { item_id: 103, item_value: '凯西制药公司' },
        { item_id: 104, item_value: '罗氏制药' },
        { item_id: 105, item_value: '艾力斯' },
        { item_id: 106, item_value: '塔吉瑞生物医药' },
        { item_id: 107, item_value: '安进公司' },
        { item_id: 109, item_value: '健康元药业集团股份有限公司' },
        { item_id: 110, item_value: '赛默罗' },
        { item_id: 111, item_value: '南新制药' },
        { item_id: 112, item_value: '朗来医药' },
        { item_id: 113, item_value: '先声药业' }
      ];
    }
    
    return [];
  } catch (error) {
    console.error('SQLite 查询失败:', error);
    throw new Error(`数据库查询失败: ${error}`);
  }
}

/**
 * 获取表列表
 */
export async function mcp_sqlite_explorer_list_tables(): Promise<string[]> {
  try {
    console.log('模拟获取表列表');
    return [
      'projects', 'dictionaries', 'dictionary_items', 'staff', 
      'project_personnel_roles', 'subsidies', 'project_sponsors'
    ];
  } catch (error) {
    console.error('获取表列表失败:', error);
    throw new Error(`获取表列表失败: ${error}`);
  }
}

/**
 * 描述表结构
 */
export async function mcp_sqlite_explorer_describe_table(tableName: string): Promise<any[]> {
  try {
    console.log('模拟描述表结构:', tableName);
    return [
      { name: 'id', type: 'INTEGER', notnull: 1, dflt_value: null, pk: 1 },
      { name: 'name', type: 'TEXT', notnull: 1, dflt_value: null, pk: 0 }
    ];
  } catch (error) {
    console.error('描述表结构失败:', error);
    throw new Error(`描述表结构失败: ${error}`);
  }
} 