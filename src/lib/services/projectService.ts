import { invoke } from '@tauri-apps/api/core';

/**
 * 项目模型
 */
export interface Project {
  id?: string;
  project_name: string;
  project_short_name: string;
  project_number?: string;
  sponsor?: string[];
  disease?: string[];
  project_stage?: string;
  project_status?: string;
  recruitment_status?: string;
  created_at?: string;
  updated_at?: string;
  tags?: string[];
  remarks?: string;
}

/**
 * 项目查询参数
 */
export interface ProjectQuery {
  project_name?: string;
  project_short_name?: string;
  project_number?: string;
  sponsor?: string;
  disease?: string;
  project_stage?: string;
  project_status?: string;
  recruitment_status?: string;
  tag?: string;
}

/**
 * 项目分页结果
 */
export interface ProjectPagination {
  items: Project[];
  total: number;
  page: number;
  page_size: number;
}

/**
 * 创建项目请求
 */
export interface CreateProjectRequest {
  project_name: string;
  project_short_name: string;
  project_number?: string;
  sponsor?: string[];
  disease?: string[];
  project_stage?: string;
  project_status?: string;
  recruitment_status?: string;
  remarks?: string;
}

/**
 * 创建项目响应
 */
export interface CreateProjectResponse {
  success: boolean;
  id?: string;
  error?: string;
}

/**
 * 更新项目请求
 */
export interface UpdateProjectRequest {
  id: string;
  project_name: string;
  project_short_name: string;
  project_number?: string;
  sponsor?: string[];
  disease?: string[];
  project_stage?: string;
  project_status?: string;
  recruitment_status?: string;
  remarks?: string;
}

/**
 * 更新项目响应
 */
export interface UpdateProjectResponse {
  success: boolean;
  error?: string;
}

/**
 * 删除项目响应
 */
export interface DeleteProjectResponse {
  success: boolean;
  error?: string;
}

/**
 * 项目服务类
 */
export class ProjectService {
  /**
   * 获取所有项目
   * @returns 项目列表
   */
  async getAllProjects(): Promise<Project[]> {
    try {
      console.log('获取所有项目...');
      return await invoke<Project[]>('get_all_projects');
    } catch (error: any) {
      console.error('获取项目失败:', error);
      throw new Error(`获取项目失败: ${error.message || '未知错误'}`);
    }
  }

  /**
   * 分页查询项目
   * @param query 查询参数
   * @param page 页码
   * @param pageSize 每页数量
   * @returns 分页结果
   */
  async getProjectsPaginated(
    query: ProjectQuery = {},
    page: number = 1,
    pageSize: number = 10
  ): Promise<ProjectPagination> {
    try {
      console.log(`分页查询项目: 页码=${page}, 每页数量=${pageSize}`);
      return await invoke<ProjectPagination>('get_projects_paginated', {
        query,
        page,
        pageSize
      });
    } catch (error: any) {
      console.error('查询项目失败:', error);
      throw new Error(`查询项目失败: ${error.message || '未知错误'}`);
    }
  }

  /**
   * 根据 ID 获取项目
   * @param id 项目 ID
   * @returns 项目信息
   */
  async getProjectById(id: string): Promise<Project | null> {
    try {
      console.log(`获取项目: ID=${id}`);
      const project = await invoke<Project | null>('get_project_by_id', { id });
      return project;
    } catch (error: any) {
      console.error(`获取项目 ${id} 失败:`, error);
      throw new Error(`获取项目失败: ${error.message || '未知错误'}`);
    }
  }

  /**
   * 创建项目
   * @param project 项目信息
   * @returns 创建结果
   */
  async createProject(project: CreateProjectRequest): Promise<CreateProjectResponse> {
    try {
      console.log('创建项目:', project.project_name);
      return await invoke<CreateProjectResponse>('create_project', { request: project });
    } catch (error: any) {
      console.error('创建项目失败:', error);
      return {
        success: false,
        error: `创建项目失败: ${error.message || '未知错误'}`
      };
    }
  }

  /**
   * 更新项目
   * @param project 项目信息
   * @returns 更新结果
   */
  async updateProject(project: UpdateProjectRequest): Promise<UpdateProjectResponse> {
    try {
      console.log(`更新项目: ID=${project.id}`);
      return await invoke<UpdateProjectResponse>('update_project', { request: project });
    } catch (error: any) {
      console.error(`更新项目 ${project.id} 失败:`, error);
      return {
        success: false,
        error: `更新项目失败: ${error.message || '未知错误'}`
      };
    }
  }

  /**
   * 删除项目
   * @param id 项目 ID
   * @returns 删除结果
   */
  async deleteProject(id: string): Promise<DeleteProjectResponse> {
    try {
      console.log(`删除项目: ID=${id}`);
      return await invoke<DeleteProjectResponse>('delete_project', { id });
    } catch (error: any) {
      console.error(`删除项目 ${id} 失败:`, error);
      return {
        success: false,
        error: `删除项目失败: ${error.message || '未知错误'}`
      };
    }
  }
}

// 导出单例实例
export const projectService = new ProjectService();
