/**
 * Clipboard Service
 * 
 * This service provides functions to interact with the system clipboard
 * using the Tauri clipboard-manager plugin.
 */

import { readText, writeText } from '@tauri-apps/plugin-clipboard-manager';

/**
 * Read text from the clipboard
 * @returns Promise<string> The text content from the clipboard
 */
export async function getClipboardText(): Promise<string> {
  try {
    return await readText();
  } catch (error) {
    console.error('Failed to read from clipboard:', error);
    throw error;
  }
}

/**
 * Write text to the clipboard
 * @param text The text to write to the clipboard
 * @returns Promise<void>
 */
export async function setClipboardText(text: string): Promise<void> {
  try {
    await writeText(text);
  } catch (error) {
    console.error('Failed to write to clipboard:', error);
    throw error;
  }
}

/**
 * Copy text to the clipboard and show a success message
 * @param text The text to copy
 * @param successCallback Optional callback to execute on success
 * @returns Promise<boolean> True if successful, false otherwise
 */
export async function copyToClipboard(
  text: string, 
  successCallback?: (message: string) => void
): Promise<boolean> {
  try {
    await writeText(text);
    if (successCallback) {
      successCallback('已复制到剪贴板');
    }
    return true;
  } catch (error) {
    console.error('复制到剪贴板失败:', error);
    return false;
  }
}
