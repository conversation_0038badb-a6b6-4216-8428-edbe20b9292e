import { invoke } from '@tauri-apps/api/core';

/**
 * 配置响应
 */
export interface ConfigResponse {
  success: boolean;
  error?: string;
  data?: any;
}

/**
 * 系统配置
 */
export interface SystemConfig {
  clinical_research_folder_path?: string;
  notion_api_key?: string;
  notion_database_id?: string;
  [key: string]: any;
}

/**
 * 配置列表
 */
export interface ConfigList {
  config_name: string;
  config: string[];
}

/**
 * 配置服务类
 */
export class ConfigService {
  /**
   * 获取系统配置
   * @returns 系统配置
   */
  async getSystemConfig(): Promise<SystemConfig> {
    try {
      console.log('获取系统配置...');
      const response = await invoke<ConfigResponse>('get_system_config');

      if (response.success && response.data) {
        return response.data as SystemConfig;
      } else {
        console.error('获取系统配置失败:', response.error);
        return {};
      }
    } catch (error: any) {
      console.error('获取系统配置失败:', error);
      throw new Error(`获取系统配置失败: ${error.message || '未知错误'}`);
    }
  }

  /**
   * 保存系统配置
   * @param config 系统配置
   * @returns 保存结果
   */
  async saveSystemConfig(config: SystemConfig): Promise<ConfigResponse> {
    try {
      console.log('保存系统配置:', config);
      return await invoke<ConfigResponse>('save_system_config', { config });
    } catch (error: any) {
      console.error('保存系统配置失败:', error);
      return {
        success: false,
        error: `保存系统配置失败: ${error.message || '未知错误'}`
      };
    }
  }

  /**
   * 获取配置列表
   * @param configName 配置名称
   * @returns 配置列表
   */
  async getConfigList(configName: string): Promise<string[]> {
    try {
      console.log(`获取配置列表: ${configName}`);
      const response = await invoke<ConfigResponse>('get_config_list', { configName });

      if (response.success && response.data) {
        const configList = response.data as ConfigList;
        return configList.config || [];
      } else {
        console.error(`获取配置列表 ${configName} 失败:`, response.error);
        return [];
      }
    } catch (error: any) {
      console.error(`获取配置列表 ${configName} 失败:`, error);
      throw new Error(`获取配置列表失败: ${error.message || '未知错误'}`);
    }
  }

  /**
   * 保存配置列表
   * @param configName 配置名称
   * @param items 配置项列表
   * @returns 保存结果
   */
  async saveConfigList(configName: string, items: string[]): Promise<ConfigResponse> {
    try {
      console.log(`保存配置列表: ${configName}`, items);
      return await invoke<ConfigResponse>('save_config_list', { configName, items });
    } catch (error: any) {
      console.error(`保存配置列表 ${configName} 失败:`, error);
      return {
        success: false,
        error: `保存配置列表失败: ${error.message || '未知错误'}`
      };
    }
  }

  /**
   * 获取所有配置
   * @returns 所有配置
   */
  async getAllConfigs(): Promise<any[]> {
    try {
      console.log('获取所有配置...');
      return await invoke<any[]>('get_all_configs');
    } catch (error: any) {
      console.error('获取所有配置失败:', error);
      throw new Error(`获取所有配置失败: ${error.message || '未知错误'}`);
    }
  }

  /**
   * 获取配置
   * @param configName 配置名称
   * @returns 配置
   */
  async getConfig(configName: string): Promise<any> {
    try {
      console.log(`获取配置: ${configName}`);
      const response = await invoke<ConfigResponse>('get_config', { configName });

      if (response.success) {
        return response.data;
      } else {
        console.error(`获取配置 ${configName} 失败:`, response.error);
        return null;
      }
    } catch (error: any) {
      console.error(`获取配置 ${configName} 失败:`, error);
      throw new Error(`获取配置失败: ${error.message || '未知错误'}`);
    }
  }

  /**
   * 保存配置
   * @param configName 配置名称
   * @param config 配置内容
   * @returns 保存结果
   */
  async saveConfig(configName: string, config: any): Promise<ConfigResponse> {
    try {
      console.log(`保存配置: ${configName}`, config);
      const request = {
        config_name: configName,
        config
      };
      return await invoke<ConfigResponse>('save_config', { request });
    } catch (error: any) {
      console.error(`保存配置 ${configName} 失败:`, error);
      return {
        success: false,
        error: `保存配置失败: ${error.message || '未知错误'}`
      };
    }
  }

  /**
   * 删除配置
   * @param configName 配置名称
   * @returns 删除结果
   */
  async deleteConfig(configName: string): Promise<ConfigResponse> {
    try {
      console.log(`删除配置: ${configName}`);
      return await invoke<ConfigResponse>('delete_config', { configName });
    } catch (error: any) {
      console.error(`删除配置 ${configName} 失败:`, error);
      return {
        success: false,
        error: `删除配置失败: ${error.message || '未知错误'}`
      };
    }
  }

  // 特定配置类型的便捷方法

  /**
   * 获取申办方列表
   * @returns 申办方列表
   */
  async getSponsorList(): Promise<string[]> {
    return this.getConfigList('project_sponsor');
  }

  /**
   * 保存申办方列表
   * @param sponsors 申办方列表
   * @returns 保存结果
   */
  async saveSponsorList(sponsors: string[]): Promise<ConfigResponse> {
    return this.saveConfigList('project_sponsor', sponsors);
  }

  /**
   * 获取疾病列表
   * @returns 疾病列表
   */
  async getDiseaseList(): Promise<string[]> {
    return this.getConfigList('disease_tags');
  }

  /**
   * 保存疾病列表
   * @param diseases 疾病列表
   * @returns 保存结果
   */
  async saveDiseaseList(diseases: string[]): Promise<ConfigResponse> {
    return this.saveConfigList('disease_tags', diseases);
  }

  /**
   * 获取项目阶段列表
   * @returns 项目阶段列表
   */
  async getProjectPhaseList(): Promise<string[]> {
    return this.getConfigList('project_phase');
  }

  /**
   * 保存项目阶段列表
   * @param phases 项目阶段列表
   * @returns 保存结果
   */
  async saveProjectPhaseList(phases: string[]): Promise<ConfigResponse> {
    return this.saveConfigList('project_phase', phases);
  }

  /**
   * 获取项目状态列表
   * @returns 项目状态列表
   */
  async getProjectStatusList(): Promise<string[]> {
    return this.getConfigList('project_status');
  }

  /**
   * 保存项目状态列表
   * @param statuses 项目状态列表
   * @returns 保存结果
   */
  async saveProjectStatusList(statuses: string[]): Promise<ConfigResponse> {
    return this.saveConfigList('project_status', statuses);
  }

  /**
   * 获取入组状态列表
   * @returns 入组状态列表
   */
  async getRecruitmentStatusList(): Promise<string[]> {
    return this.getConfigList('recruitment_status');
  }

  /**
   * 保存入组状态列表
   * @param statuses 入组状态列表
   * @returns 保存结果
   */
  async saveRecruitmentStatusList(statuses: string[]): Promise<ConfigResponse> {
    return this.saveConfigList('recruitment_status', statuses);
  }
}

// 导出单例实例
export const configService = new ConfigService();
