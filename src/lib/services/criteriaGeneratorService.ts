import { invoke } from '@tauri-apps/api/core';
import { get } from 'svelte/store';
import settings from '$lib/stores/settings';
import { ChatOpenAI } from "@langchain/openai";
import { PromptTemplate } from "@langchain/core/prompts";
import { JsonOutputParser } from "@langchain/core/output_parsers";
import type { Runnable } from "@langchain/core/runnables";
import { segmentText } from '$lib/utils/criteriaTextProcessor';

// 规则定义类型
export interface RuleDefinition {
  rule_definition_id?: number;
  rule_name: string;
  rule_description?: string;
  category?: string;
  parameter_schema: string; // JSON string
  created_at?: string;
  updated_at?: string;
}

// 生成的标准项类型
export interface GeneratedCriterion {
  rule_definition_id: number;
  parameter_values: Record<string, any>;
  display_order: number;
}

// 生成的标准组类型
export interface GeneratedOrGroup {
  group_id: number;
  criteria_ids: number[];
  operator: string;
}

// 最终生成的JSON结构
export interface GeneratedCriteriaJson {
  criteria: GeneratedCriterion[];
  or_groups: GeneratedOrGroup[];
}

// 处理结果类型
interface ProcessResult {
  success: boolean;
  criterion?: GeneratedCriterion;
  error?: string;
}

/**
 * 入排标准生成器类
 */
export class CriteriaGenerator {
  private apiKey: string;
  private baseUrl: string;
  private modelId: string;
  private ruleDefinitions: RuleDefinition[] = [];
  private model: ChatOpenAI;
  private chain: Runnable | null = null;
  private promptTemplate: string;

  /**
   * 构造函数
   */
  constructor() {
    console.log('初始化 CriteriaGenerator...');

    // 从设置存储中获取配置
    const settingsData = get(settings);
    console.log('从设置中获取配置:', {
      hasApiKey: !!settingsData.openrouterApiKey,
      hasModels: settingsData.models?.length > 0,
      baseUrl: settingsData.openrouterSiteUrl
    });

    // 设置 API 密钥
    this.apiKey = settingsData.openrouterApiKey || '';
    if (!this.apiKey) {
      console.warn('警告: API 密钥未设置');
    }

    // 设置 API 基础 URL
    this.baseUrl = settingsData.openrouterSiteUrl || 'https://openrouter.ai/api/v1';

    // 确保 baseUrl 不包含 /chat/completions
    if (this.baseUrl.endsWith('/chat/completions')) {
      this.baseUrl = this.baseUrl.replace('/chat/completions', '');
      console.log('已移除 baseUrl 中的 /chat/completions 路径，现在是:', this.baseUrl);
    }

    // 设置模型 ID
    this.modelId = settingsData.models && settingsData.models.length > 0
      ? settingsData.models[0].id
      : 'google/gemini-2.0-flash-001';
    console.log('使用模型 ID:', this.modelId);

    // 初始化 Langchain 模型
    try {
      this.model = new ChatOpenAI({
        apiKey: this.apiKey,
        modelName: this.modelId,
        openAIApiKey: this.apiKey, // 兼容性字段
        configuration: {
          baseURL: this.baseUrl,
          defaultHeaders: {
            'HTTP-Referer': 'https://peckbyte.app',
            'X-Title': 'PeckByte Criteria Generator',
            'Content-Type': 'application/json'
          }
        },
        temperature: 0.1, // 降低随机性以获取更一致的 JSON 输出
        maxRetries: 2,
      });
      console.log('成功初始化 ChatOpenAI 模型');
    } catch (error) {
      console.error('初始化 ChatOpenAI 模型失败:', error);
      throw new Error(`初始化模型失败: ${error instanceof Error ? error.message : String(error)}`);
    }

    // 设置默认提示模板
    this.promptTemplate = this.getDefaultPromptTemplate();
    console.log('CriteriaGenerator 构造函数完成');
  }

  /**
   * 初始化生成器
   */
  async initialize(): Promise<void> {
    try {
      // 加载规则定义
      await this.loadRuleDefinitions();

      // 设置调用链
      this.setupChain();

      console.log('CriteriaGenerator 初始化完成，已加载规则定义:', this.ruleDefinitions.length);
    } catch (error) {
      console.error('CriteriaGenerator 初始化失败:', error);
      throw new Error(`初始化失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 刷新规则定义
   * 用于在添加新规则后重新加载规则定义
   */
  async refreshRuleDefinitions(): Promise<void> {
    try {
      console.log('刷新规则定义...');
      await this.loadRuleDefinitions();
      console.log('规则定义已刷新，当前规则数量:', this.ruleDefinitions.length);
    } catch (error) {
      console.error('刷新规则定义失败:', error);
      throw new Error(`刷新规则定义失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 加载规则定义
   */
  private async loadRuleDefinitions(): Promise<void> {
    try {
      console.log('开始加载规则定义...');

      // 使用固定的数据库路径
      const dbPath = '/Users/<USER>/我的文档/sqlite/peckbyte.db';
      console.log('使用数据库路径:', dbPath);

      // 调用后端命令获取规则定义
      const query = {}; // 空查询获取所有规则定义

      try {
        // 注意：参数名必须与后端命令定义匹配
        this.ruleDefinitions = await invoke<RuleDefinition[]>('get_rule_definitions', {
          query,
          dbPath: dbPath
        });

        console.log('已加载规则定义:', this.ruleDefinitions.length);

        if (this.ruleDefinitions.length === 0) {
          console.warn('警告: 未找到任何规则定义');
        }
      } catch (invokeError) {
        console.error('调用 get_rule_definitions 失败:', invokeError);
        // 如果调用失败，使用空数组
        this.ruleDefinitions = [];
        throw new Error(`调用 get_rule_definitions 失败: ${invokeError instanceof Error ? invokeError.message : String(invokeError)}`);
      }
    } catch (error) {
      console.error('加载规则定义失败:', error);
      throw new Error(`加载规则定义失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 设置调用链
   */
  private setupChain(): void {
    // 创建提示模板
    const prompt = new PromptTemplate({
      template: this.promptTemplate,
      inputVariables: ["rules_list", "text_segment"]
    });

    // 创建 JSON 输出解析器
    const parser = new JsonOutputParser<ProcessResult>();

    // 使用 LCEL 构建链
    this.chain = prompt.pipe(this.model).pipe(parser);
    console.log("Langchain 调用链已设置");
  }

  /**
   * 获取默认提示模板
   */
  getDefaultPromptTemplate(): string {
    return `你是一个专业的临床研究入排标准解析助手。你的任务是将文本描述的入排标准解析为结构化的JSON格式。

可用的规则列表：
{rules_list}

待处理的入排标准文本段落：
{text_segment}

请分析上述入排标准文本，判断它是否符合规则列表中的某一条规则。如果符合，请选择最匹配的规则，提取相关参数值，并按照该规则的参数模式要求生成JSON。

【重要】检测"或"关系：
1. 如果文本中包含"或"关系（例如"≥2次中度或≥1次重度"），请判断这是否应该分为两条独立的标准
2. 如果应该分为两条，请只处理其中一条，并将success设为false，在error中说明"检测到OR关系，需要分割为多条标准"
3. 例如："队列一要求筛选前12个月内发生≥2次中度或≥1次重度COPD急性加重" 应该分为两条：
   - "队列一要求筛选前12个月内发生≥2次中度COPD急性加重"
   - "队列一要求筛选前12个月内发生≥1次重度COPD急性加重"
4. 这两条标准应该在前端被设置为一个"OR组"，表示它们之间是"或"的关系

【输出格式要求】：
你必须严格按照以下JSON格式返回结果：
{{
  "success": true/false,
  "criterion": {{
    "rule_definition_id": 数字,
    "parameter_values": {{
      "参数名1": 值1,
      "参数名2": 值2,
      ...
    }}
  }},
  "error": "如果success为false，这里提供错误原因"
}}

【注意事项】：
1. 你必须从规则列表中选择一个最匹配的规则，不要创建新规则
2. 参数值必须符合参数模式中定义的类型和约束
3. 数字参数应该提取为数字类型，而不是字符串
4. 如果文本不符合任何规则描述，或无法提取所需参数，请将success设为false并提供错误原因
5. 不要添加参数模式中未定义的参数
6. 如果检测到需要分割的"或"关系，请在error中明确指出
7. 你的整个回复必须是一个有效的JSON对象，不要添加任何其他内容`;
  }

  /**
   * 设置提示模板
   */
  setPromptTemplate(template: string): void {
    this.promptTemplate = template;
    this.setupChain();
  }

  /**
   * 设置模型ID
   */
  setModelId(modelId: string): void {
    if (modelId && modelId !== this.modelId) {
      this.modelId = modelId;

      // 重新创建模型实例
      this.model = new ChatOpenAI({
        apiKey: this.apiKey,
        modelName: modelId,
        openAIApiKey: this.apiKey,
        configuration: {
          baseURL: this.baseUrl,
          defaultHeaders: {
            'HTTP-Referer': 'https://peckbyte.app',
            'X-Title': 'PeckByte Criteria Generator',
            'Content-Type': 'application/json'
          }
        },
        temperature: 0.1,
        maxRetries: 2,
      });

      // 重新设置调用链
      this.setupChain();
      console.log('已更新模型ID:', modelId);
    }
  }

  /**
   * 准备规则列表文本
   */
  private prepareRulesList(): string {
    // 过滤掉没有描述的规则
    const validRules = this.ruleDefinitions.filter(rule => rule.rule_description);

    // 构建规则列表文本
    let rulesListText = '';

    validRules.forEach((rule, index) => {
      rulesListText += `规则 ${index + 1}:\n`;
      rulesListText += `ID: ${rule.rule_definition_id}\n`;
      rulesListText += `名称: ${rule.rule_name}\n`;
      rulesListText += `描述: ${rule.rule_description || '无描述'}\n`;
      rulesListText += `参数模式: ${rule.parameter_schema}\n\n`;
    });

    return rulesListText;
  }

  /**
   * 分割文本为段落
   */
  private segmentText(text: string): string[] {
    // 使用工具类中的分割函数
    return segmentText(text);
  }

  /**
   * 使用所有规则一次性处理段落
   */
  private async processSegmentWithAllRules(segment: string, rulesListText: string): Promise<ProcessResult> {
    if (!this.chain) {
      throw new Error("Langchain 调用链未初始化");
    }

    try {
      // 准备调用参数
      const params = {
        rules_list: rulesListText,
        text_segment: segment
      };

      // 调用 Langchain 链
      const result = await this.chain.invoke(params);

      return result;
    } catch (error) {
      console.error('处理段落失败:', error);
      return {
        success: false,
        error: `处理失败: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }

  /**
   * 分割包含OR关系的段落
   */
  private async splitOrRelationship(segment: string, _rulesListText: string): Promise<{
    success: boolean;
    segments: string[];
    error?: string;
  }> {
    if (!this.model) {
      throw new Error("Langchain 模型未初始化");
    }

    try {
      // 创建专门用于分割OR关系的提示模板
      const splitPrompt = new PromptTemplate({
        template: `你是一个专业的临床研究入排标准解析助手。你的任务是将包含"或"关系的入排标准文本分割为多个独立的标准。

待处理的入排标准文本段落：
{text_segment}

请分析上述入排标准文本，判断它是否包含"或"关系（例如"≥2次中度或≥1次重度"）。如果包含，请将其分割为多个独立的标准。

例如："队列一要求筛选前12个月内发生≥2次中度或≥1次重度COPD急性加重" 应该分为两条：
1. "队列一要求筛选前12个月内发生≥2次中度COPD急性加重"
2. "队列一要求筛选前12个月内发生≥1次重度COPD急性加重"

【输出格式要求】：
你必须严格按照以下JSON格式返回结果：
{
  "success": true/false,
  "segments": ["分割后的段落1", "分割后的段落2", ...],
  "error": "如果success为false，这里提供错误原因"
}

【注意事项】：
1. 只有当文本确实包含"或"关系且需要分割时，才将success设为true
2. 分割后的段落应该保持原始语义，只是将"或"关系拆分为独立的条件
3. 如果文本不包含"或"关系，或者虽然包含"或"但不应该分割（例如是术语的一部分），请将success设为false
4. 你的整个回复必须是一个有效的JSON对象，不要添加任何其他内容`,
        inputVariables: ["text_segment"]
      });

      // 创建 JSON 输出解析器
      const parser = new JsonOutputParser<{
        success: boolean;
        segments: string[];
        error?: string;
      }>();

      // 使用 LCEL 构建临时链
      const splitChain = splitPrompt.pipe(this.model).pipe(parser);

      // 调用链
      const result = await splitChain.invoke({
        text_segment: segment
      });

      return result;
    } catch (error) {
      console.error('分割OR关系失败:', error);
      return {
        success: false,
        segments: [],
        error: `分割失败: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }

  /**
   * 处理文本
   */
  async processText(
    text: string,
    modelId?: string,
    promptTemplate?: string,
    progressCallback?: (current: number, total: number, success: number, failed: number) => void
  ): Promise<{
    generatedJson: GeneratedCriteriaJson;
    processingDetails: {
      matchedSegments: Array<{
        segment: string;
        rule: RuleDefinition;
        criterion: GeneratedCriterion;
      }>;
      failedSegments: Array<{
        segment: string;
        errors: Array<{
          rule: RuleDefinition;
          error: string;
        }>;
      }>;
    };
  }> {
    console.log('开始处理文本...');

    // 如果提供了模型ID，更新模型
    if (modelId) {
      this.setModelId(modelId);
    }

    // 如果提供了提示模板，更新提示模板
    if (promptTemplate) {
      this.setPromptTemplate(promptTemplate);
    }

    // 准备规则列表文本
    const rulesListText = this.prepareRulesList();
    console.log(`已准备规则列表文本，包含 ${this.ruleDefinitions.length} 条规则`);

    // 分割文本为段落
    const segments = this.segmentText(text);
    console.log(`已将文本分割为 ${segments.length} 个段落`);

    // 初始化结果
    const generatedJson: GeneratedCriteriaJson = {
      criteria: [],
      or_groups: []
    };

    // 初始化处理详情
    const processingDetails = {
      matchedSegments: [] as Array<{
        segment: string;
        rule: RuleDefinition;
        criterion: GeneratedCriterion;
      }>,
      failedSegments: [] as Array<{
        segment: string;
        errors: Array<{
          rule: RuleDefinition;
          error: string;
        }>;
      }>
    };

    // 统计成功和失败的数量
    let successCount = 0;
    let failedCount = 0;
    let nextGroupId = 1; // 用于生成OR组ID

    // 处理每个段落
    for (let i = 0; i < segments.length; i++) {
      const segment = segments[i].trim();
      if (!segment) continue; // 跳过空段落

      console.log(`处理段落 ${i+1}/${segments.length}: ${segment.substring(0, 50)}...`);

      // 调用进度回调
      if (progressCallback) {
        progressCallback(i + 1, segments.length, successCount, failedCount);
      }

      // 处理段落
      const processResult = await this.processSegmentWithAllRules(segment, rulesListText);

      // 检查是否需要处理OR关系
      if (!processResult.success && processResult.error?.includes('检测到OR关系')) {
        console.log(`段落 ${i+1} 检测到OR关系，尝试分割...`);

        // 分割OR关系
        const splitResult = await this.splitOrRelationship(segment, rulesListText);

        if (splitResult.success && splitResult.segments.length > 0) {
          console.log(`成功分割为 ${splitResult.segments.length} 个子段落`);

          // 创建一个新的OR组
          const orGroupCriteriaIds: number[] = [];

          // 处理每个子段落
          for (const subSegment of splitResult.segments) {
            console.log(`处理子段落: ${subSegment.substring(0, 50)}...`);

            // 处理子段落
            const subProcessResult = await this.processSegmentWithAllRules(subSegment, rulesListText);

            if (subProcessResult.success && subProcessResult.criterion) {
              // 验证规则ID是否有效
              const ruleId = subProcessResult.criterion.rule_definition_id;

              // 检查规则ID是否存在于当前加载的规则定义中
              let matchedRule = this.ruleDefinitions.find(
                rule => rule.rule_definition_id === ruleId
              );

              if (!matchedRule) {
                console.error(`子段落匹配了不存在的规则ID: ${ruleId}`);

                // 尝试刷新规则定义，可能是新添加的规则
                try {
                  await this.refreshRuleDefinitions();
                  console.log(`已刷新规则定义，当前规则数量: ${this.ruleDefinitions.length}`);

                  // 再次检查规则ID是否存在
                  const refreshedMatchedRule = this.ruleDefinitions.find(
                    rule => rule.rule_definition_id === ruleId
                  );

                  if (!refreshedMatchedRule) {
                    // 规则ID仍然不存在，记录错误
                    processingDetails.failedSegments.push({
                      segment: subSegment,
                      errors: [{
                        rule: { rule_name: '未知规则', rule_definition_id: ruleId } as RuleDefinition,
                        error: `匹配了不存在的规则ID: ${ruleId}（即使刷新规则定义后仍未找到）`
                      }]
                    });
                    failedCount++;
                    continue;
                  } else {
                    // 找到了刷新后的规则
                    console.log(`刷新规则定义后找到了规则ID ${ruleId}: ${refreshedMatchedRule.rule_name}`);
                    matchedRule = refreshedMatchedRule;
                  }
                } catch (refreshError) {
                  console.error('刷新规则定义失败:', refreshError);
                  processingDetails.failedSegments.push({
                    segment: subSegment,
                    errors: [{
                      rule: { rule_name: '未知规则', rule_definition_id: ruleId } as RuleDefinition,
                      error: `匹配了不存在的规则ID: ${ruleId}，且刷新规则定义失败: ${refreshError instanceof Error ? refreshError.message : String(refreshError)}`
                    }]
                  });
                  failedCount++;
                  continue;
                }
              }

              // 确保参数值是对象而不是字符串
              let paramValues = subProcessResult.criterion.parameter_values;
              if (typeof paramValues === 'string') {
                try {
                  paramValues = JSON.parse(paramValues);
                  console.log('Successfully parsed OR segment parameter values from string:', paramValues);
                } catch (e) {
                  console.error('Failed to parse OR segment parameter values string:', e);
                  // 尝试处理可能的格式问题
                  const cleanedStr = String(paramValues)
                    .replace(/'/g, '"')  // 替换单引号为双引号
                    .replace(/(\w+):/g, '"$1":')  // 给键名添加双引号
                    .replace(/,\s*}/g, '}');  // 移除尾部逗号

                  try {
                    paramValues = JSON.parse(cleanedStr);
                    console.log('Successfully parsed OR segment parameter values after cleaning:', paramValues);
                  } catch (cleanParseError) {
                    console.error('Still failed to parse OR segment after cleaning:', cleanParseError);
                    paramValues = {}; // 解析失败时使用空对象
                  }
                }
              }

              // 添加到结果中
              const criterionWithOrder = {
                ...subProcessResult.criterion,
                parameter_values: paramValues, // 确保参数值是对象
                display_order: generatedJson.criteria.length + 1 // 设置显示顺序
              };

              // 添加到结果中
              generatedJson.criteria.push(criterionWithOrder);

              // 添加到OR组
              orGroupCriteriaIds.push(criterionWithOrder.display_order);

              // 记录匹配成功的详情
              processingDetails.matchedSegments.push({
                segment: subSegment,
                rule: matchedRule,
                criterion: criterionWithOrder
              });

              successCount++;
              console.log(`子段落匹配成功，规则: ${matchedRule.rule_name}`);
            } else {
              // 子段落处理失败
              console.log(`子段落未找到匹配的规则:`, subSegment);
              processingDetails.failedSegments.push({
                segment: subSegment,
                errors: [{
                  rule: { rule_name: '所有规则' } as RuleDefinition,
                  error: subProcessResult.error || '未找到匹配的规则'
                }]
              });
              failedCount++;
            }
          }

          // 如果有成功匹配的子段落，创建OR组
          if (orGroupCriteriaIds.length > 0) {
            generatedJson.or_groups.push({
              group_id: nextGroupId++,
              criteria_ids: orGroupCriteriaIds,
              operator: 'OR'
            });
            console.log(`创建了OR组，包含 ${orGroupCriteriaIds.length} 个标准`);
          }
        } else {
          // 分割失败，记录错误
          console.log(`分割OR关系失败:`, splitResult.error);
          processingDetails.failedSegments.push({
            segment,
            errors: [{
              rule: { rule_name: 'OR关系分割' } as RuleDefinition,
              error: splitResult.error || '分割OR关系失败'
            }]
          });
          failedCount++;
        }
      } else if (processResult.success && processResult.criterion) {
        // 验证规则ID是否有效
        const ruleId = processResult.criterion.rule_definition_id;

        // 检查规则ID是否存在于当前加载的规则定义中
        let matchedRule = this.ruleDefinitions.find(
          rule => rule.rule_definition_id === ruleId
        );

        if (!matchedRule) {
          console.error(`段落 ${i+1} 匹配了不存在的规则ID: ${ruleId}`);

          // 尝试刷新规则定义，可能是新添加的规则
          try {
            await this.refreshRuleDefinitions();
            console.log(`已刷新规则定义，当前规则数量: ${this.ruleDefinitions.length}`);

            // 再次检查规则ID是否存在
            const refreshedMatchedRule = this.ruleDefinitions.find(
              rule => rule.rule_definition_id === ruleId
            );

            if (!refreshedMatchedRule) {
              // 规则ID仍然不存在，记录错误
              processingDetails.failedSegments.push({
                segment,
                errors: [{
                  rule: { rule_name: '未知规则', rule_definition_id: ruleId } as RuleDefinition,
                  error: `匹配了不存在的规则ID: ${ruleId}（即使刷新规则定义后仍未找到）`
                }]
              });
              failedCount++;
              continue;
            } else {
              // 找到了刷新后的规则
              console.log(`刷新规则定义后找到了规则ID ${ruleId}: ${refreshedMatchedRule.rule_name}`);
              matchedRule = refreshedMatchedRule;
            }
          } catch (refreshError) {
            console.error('刷新规则定义失败:', refreshError);
            processingDetails.failedSegments.push({
              segment,
              errors: [{
                rule: { rule_name: '未知规则', rule_definition_id: ruleId } as RuleDefinition,
                error: `匹配了不存在的规则ID: ${ruleId}，且刷新规则定义失败: ${refreshError instanceof Error ? refreshError.message : String(refreshError)}`
              }]
            });
            failedCount++;
            continue;
          }
        }

        // 确保参数值是对象而不是字符串
        let paramValues = processResult.criterion.parameter_values;
        if (typeof paramValues === 'string') {
          try {
            paramValues = JSON.parse(paramValues);
            console.log('Successfully parsed parameter values from string:', paramValues);
          } catch (e) {
            console.error('Failed to parse parameter values string:', e);
            // 尝试处理可能的格式问题
            const cleanedStr = String(paramValues)
              .replace(/'/g, '"')  // 替换单引号为双引号
              .replace(/(\w+):/g, '"$1":')  // 给键名添加双引号
              .replace(/,\s*}/g, '}');  // 移除尾部逗号

            try {
              paramValues = JSON.parse(cleanedStr);
              console.log('Successfully parsed parameter values after cleaning:', paramValues);
            } catch (cleanParseError) {
              console.error('Still failed to parse after cleaning:', cleanParseError);
              paramValues = {}; // 解析失败时使用空对象
            }
          }
        }

        // 添加到结果中
        const criterionWithOrder = {
          ...processResult.criterion,
          parameter_values: paramValues, // 确保参数值是对象
          display_order: generatedJson.criteria.length + 1 // 设置显示顺序
        };

        generatedJson.criteria.push(criterionWithOrder);

        // 记录匹配成功的详情
        processingDetails.matchedSegments.push({
          segment,
          rule: matchedRule,
          criterion: criterionWithOrder
        });

        successCount++;
        console.log(`段落 ${i+1} 匹配成功，规则: ${matchedRule.rule_name}`);
      } else {
        // 处理失败
        console.log(`段落 ${i+1} 未找到匹配的规则:`, processResult.error);
        processingDetails.failedSegments.push({
          segment,
          errors: [{
            rule: { rule_name: '所有规则' } as RuleDefinition,
            error: processResult.error || '未找到匹配的规则'
          }]
        });
        failedCount++;
      }
    }

    console.log(`处理完成，成功: ${successCount}，失败: ${failedCount}`);
    console.log(`生成的JSON:`, generatedJson);

    return {
      generatedJson,
      processingDetails
    };
  }
}
