import { invoke } from '@tauri-apps/api/core';

// Staff member interface
export interface Staff {
  id?: number;
  name: string;
  gender: string;
  birthday: string;
  phone: string;
  email: string;
  position_item_id: number;
  isPI: boolean;
  organization: string;
  created_at?: string;
  updated_at?: string;

  // Additional fields for UI display
  position_name?: string;
}

// Query interface for filtering staff
export interface StaffQuery {
  name?: string;
  gender?: string;
  position_item_id?: number;
  organization?: string;
  isPI?: boolean;
}

// Response interface from backend
interface StaffResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

/**
 * Staff service class
 */
export class StaffService {
  /**
   * Get all staff members
   * @returns Staff list
   */
  async getAllStaff(): Promise<Staff[]> {
    try {
      console.log('获取所有人员...');
      const response = await invoke<StaffResponse<Staff[]>>('get_all_staff');

      if (response.success && response.data) {
        return response.data;
      } else {
        console.error('获取所有人员失败:', response.error);
        throw new Error(response.error || '获取所有人员失败');
      }
    } catch (error: any) {
      console.error('获取所有人员失败:', error);
      throw new Error(`获取所有人员失败: ${error.message || '未知错误'}`);
    }
  }

  /**
   * Query staff members
   * @param query Query parameters
   * @returns Staff list
   */
  async queryStaff(query: StaffQuery): Promise<Staff[]> {
    try {
      // 处理查询参数，确保所有字段都是正确的类型
      const cleanQuery: StaffQuery = {};

      // 只添加非空的字段
      if (query.name) cleanQuery.name = query.name;
      if (query.gender) cleanQuery.gender = query.gender;
      if (query.organization) cleanQuery.organization = query.organization;

      // 确保 position_item_id 是数字
      if (query.position_item_id) {
        const positionId = Number(query.position_item_id);
        if (!isNaN(positionId) && positionId > 0) {
          cleanQuery.position_item_id = positionId;
        }
      }

      // 确保 isPI 是布尔值
      if (query.isPI !== undefined) {
        cleanQuery.isPI = Boolean(query.isPI);
      }

      console.log('查询人员，原始参数:', query);
      console.log('查询人员，清理后的参数:', cleanQuery);

      const response = await invoke<StaffResponse<Staff[]>>('query_staff', { query: cleanQuery });

      if (response.success && response.data) {
        return response.data;
      } else {
        console.error('查询人员失败:', response.error);
        throw new Error(response.error || '查询人员失败');
      }
    } catch (error: any) {
      console.error('查询人员失败:', error);
      throw new Error(`查询人员失败: ${error.message || '未知错误'}`);
    }
  }

  /**
   * Get staff member by ID
   * @param id Staff ID
   * @returns Staff member
   */
  async getStaffById(id: number): Promise<Staff> {
    try {
      console.log(`获取人员: ID = ${id}`);
      const response = await invoke<StaffResponse<Staff>>('get_staff_by_id', { id });

      if (response.success && response.data) {
        return response.data;
      } else {
        console.error('获取人员失败:', response.error);
        throw new Error(response.error || '获取人员失败');
      }
    } catch (error: any) {
      console.error('获取人员失败:', error);
      throw new Error(`获取人员失败: ${error.message || '未知错误'}`);
    }
  }

  /**
   * Create staff member
   * @param staff Staff data
   * @returns Staff ID
   */
  async createStaff(staff: Staff): Promise<number> {
    try {
      console.log('创建人员:', staff);
      const response = await invoke<StaffResponse<number>>('create_staff', { staff });

      if (response.success && response.data !== undefined) {
        return response.data;
      } else {
        console.error('创建人员失败:', response.error);
        throw new Error(response.error || '创建人员失败');
      }
    } catch (error: any) {
      console.error('创建人员失败:', error);
      throw new Error(`创建人员失败: ${error.message || '未知错误'}`);
    }
  }

  /**
   * Update staff member
   * @param id Staff ID
   * @param staff Staff data
   * @returns Success flag
   */
  async updateStaff(id: number, staff: Staff): Promise<boolean> {
    try {
      console.log(`更新人员: ID = ${id}`, staff);
      const response = await invoke<StaffResponse<boolean>>('update_staff', { id, staff });

      if (response.success) {
        return true;
      } else {
        console.error('更新人员失败:', response.error);
        throw new Error(response.error || '更新人员失败');
      }
    } catch (error: any) {
      console.error('更新人员失败:', error);
      throw new Error(`更新人员失败: ${error.message || '未知错误'}`);
    }
  }

  /**
   * Delete staff member
   * @param id Staff ID
   * @returns Success flag
   */
  async deleteStaff(id: number): Promise<boolean> {
    try {
      console.log(`删除人员: ID = ${id}`);
      const response = await invoke<StaffResponse<boolean>>('delete_staff', { id });

      if (response.success) {
        return true;
      } else {
        console.error('删除人员失败:', response.error);
        throw new Error(response.error || '删除人员失败');
      }
    } catch (error: any) {
      console.error('删除人员失败:', error);
      throw new Error(`删除人员失败: ${error.message || '未知错误'}`);
    }
  }

  /**
   * Get all user roles (dictionary items)
   * @returns User roles list
   */
  async getPositions(): Promise<{id: number, key: string, value: string}[]> {
    try {
      console.log('获取所有用户角色...');
      const response = await invoke<StaffResponse<{id: number, key: string, value: string}[]>>('get_positions');

      if (response.success && response.data) {
        return response.data;
      } else {
        console.error('获取所有用户角色失败:', response.error);
        throw new Error(response.error || '获取所有用户角色失败');
      }
    } catch (error: any) {
      console.error('获取所有用户角色失败:', error);
      throw new Error(`获取所有用户角色失败: ${error.message || '未知错误'}`);
    }
  }
}

// Export a singleton instance
export const staffService = new StaffService();
