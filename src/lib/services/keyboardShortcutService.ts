/**
 * Keyboard Shortcut Service
 * 
 * This service provides functions to handle keyboard shortcuts in the application.
 * It works with the Tauri clipboard plugin to provide clipboard functionality.
 */

import { getClipboardText, setClipboardText } from './clipboardService';

/**
 * Register global keyboard shortcuts for the application
 * This function should be called once when the application starts
 */
export function registerGlobalShortcuts() {
  // Register document-level event listeners for keyboard shortcuts
  document.addEventListener('keydown', handleKeyDown);
}

/**
 * Handle keydown events for keyboard shortcuts
 * @param event The keyboard event
 */
function handleKeyDown(event: KeyboardEvent) {
  // Check if the event target is an input element
  const target = event.target as HTMLElement;
  const isInput = target.tagName === 'INPUT' || 
                  target.tagName === 'TEXTAREA' || 
                  target.isContentEditable;

  // Get the active element
  const activeElement = document.activeElement as HTMLElement;
  
  // Handle copy (Cmd+C on macOS, Ctrl+C on Windows/Linux)
  if ((event.metaKey || event.ctrlKey) && event.key === 'c') {
    // Only handle copy if we're not in an input element (those handle copy natively)
    if (!isInput) {
      handleCopy();
      // Don't prevent default for inputs so they can use the native copy
    }
  }
  
  // Handle paste (Cmd+V on macOS, Ctrl+V on Windows/Linux)
  if ((event.metaKey || event.ctrlKey) && event.key === 'v') {
    // For paste, we want to handle it even in inputs in some cases
    // Check if the element has a data-custom-paste attribute
    if (activeElement && activeElement.hasAttribute('data-custom-paste')) {
      handlePaste(activeElement);
      event.preventDefault(); // Prevent default paste behavior
    }
  }
}

/**
 * Handle copy action
 */
async function handleCopy() {
  // Get selected text
  const selection = window.getSelection();
  if (selection && selection.toString()) {
    try {
      await setClipboardText(selection.toString());
      console.log('Text copied to clipboard');
    } catch (error) {
      console.error('Failed to copy text to clipboard:', error);
    }
  }
}

/**
 * Handle paste action
 * @param targetElement The element to paste into
 */
async function handlePaste(targetElement: HTMLElement) {
  try {
    const text = await getClipboardText();
    
    // Handle different element types
    if (targetElement.tagName === 'INPUT') {
      const inputElement = targetElement as HTMLInputElement;
      inputElement.value = text;
      // Dispatch input event to trigger any listeners
      inputElement.dispatchEvent(new Event('input', { bubbles: true }));
    } else if (targetElement.tagName === 'TEXTAREA') {
      const textareaElement = targetElement as HTMLTextAreaElement;
      textareaElement.value = text;
      // Dispatch input event to trigger any listeners
      textareaElement.dispatchEvent(new Event('input', { bubbles: true }));
    } else if (targetElement.isContentEditable) {
      targetElement.textContent = text;
    }
    
    // Dispatch a custom event that components can listen for
    targetElement.dispatchEvent(new CustomEvent('custompaste', { 
      bubbles: true,
      detail: { text }
    }));
    
    console.log('Text pasted from clipboard');
  } catch (error) {
    console.error('Failed to paste text from clipboard:', error);
  }
}
