<script lang="ts">
  import { onMount } from 'svelte';
  import { Button } from '$lib/components/ui/button';
  import Input from '$lib/components/ui/input.svelte';
  import * as Dialog from '$lib/components/ui/dialog';
  import lighthouseApiService, {
    type LighthouseUser,
    type CreateUserRequest,
    type UpdateUserRequest
  } from '$lib/services/lighthouseApiService';
  import {
    loadLighthouseSettings,
    isTokenValid,
    setLighthouseToken
  } from '$lib/stores/lighthouseSettings';
  import { goto } from '$app/navigation';

  // 状态管理
  let users = $state<LighthouseUser[]>([]);
  let totalUsers = $state(0);
  let currentPage = $state(1);
  let pageSize = $state(10);
  let searchQuery = $state('');
  let isLoading = $state(false);
  let error = $state('');
  let success = $state('');

  // 用户表单状态
  let showUserDialog = $state(false);
  let isEditMode = $state(false);
  let currentUserId = $state<number | null>(null);
  let formUsername = $state('');
  let formEmail = $state('');
  let formPassword = $state('');
  let formFullName = $state('');
  let formRole = $state('USER');
  let formPhone = $state('');
  let formAddress = $state('');
  let formCity = $state('');
  let formCountry = $state('');
  let formPostalCode = $state('');
  let formBio = $state('');
  let formError = $state('');

  // 删除对话框状态
  let showDeleteDialog = $state(false);
  let userToDelete = $state<LighthouseUser | null>(null);

  // 登录对话框状态
  let showLoginDialog = $state(false);
  let loginUsername = $state('');
  let loginPassword = $state('');
  let loginError = $state('');
  let isLoggingIn = $state(false);

  // 初始化
  onMount(async () => {
    await loadLighthouseSettings();

    // 检查是否已登录
    if (!isTokenValid()) {
      showLoginDialog = true;
    } else {
      await loadUsers();
    }
  });

  // 加载用户列表
  async function loadUsers() {
    if (!isTokenValid()) {
      showLoginDialog = true;
      return;
    }

    isLoading = true;
    error = '';

    try {
      const response = await lighthouseApiService.getUsers(currentPage, pageSize, searchQuery);
      if (response && response.data) {
        users = response.data;
        totalUsers = response.total || 0;
      } else {
        users = [];
        totalUsers = 0;
        console.warn('API返回的数据格式不符合预期:', response);
      }
    } catch (err: any) {
      users = [];
      totalUsers = 0;
      error = `加载用户列表失败: ${err.message}`;
      console.error('加载用户列表失败:', err);
    } finally {
      isLoading = false;
    }
  }

  // 处理页码变化
  function handlePageChange(newPage: number) {
    currentPage = newPage;
    loadUsers();
  }

  // 处理搜索
  function handleSearch() {
    currentPage = 1; // 重置到第一页
    loadUsers();
  }

  // 打开新建用户对话框
  function openCreateUserDialog() {
    isEditMode = false;
    currentUserId = null;
    formUsername = '';
    formEmail = '';
    formPassword = '';
    formFullName = '';
    formRole = 'USER';
    formPhone = '';
    formAddress = '';
    formCity = '';
    formCountry = '';
    formPostalCode = '';
    formBio = '';
    formError = '';
    showUserDialog = true;
  }

  // 打开编辑用户对话框
  async function openEditUserDialog(user: LighthouseUser) {
    isEditMode = true;
    currentUserId = user.id;

    try {
      // 获取完整的用户信息
      const userDetails = await lighthouseApiService.getUserById(user.id);

      formUsername = userDetails.username;
      formEmail = userDetails.email;
      formPassword = ''; // 不显示密码
      formFullName = userDetails.fullName || '';
      formRole = userDetails.role;
      formPhone = userDetails.phone || '';
      formAddress = userDetails.address || '';
      formCity = userDetails.city || '';
      formCountry = userDetails.country || '';
      formPostalCode = userDetails.postalCode || '';
      formBio = userDetails.bio || '';
      formError = '';

      showUserDialog = true;
    } catch (err: any) {
      error = `获取用户详情失败: ${err.message}`;
    }
  }

  // 保存用户
  async function saveUser() {
    formError = '';

    // 表单验证
    if (!formUsername) {
      formError = '用户名不能为空';
      return;
    }

    if (!formEmail) {
      formError = '邮箱不能为空';
      return;
    }

    if (!isEditMode && !formPassword) {
      formError = '密码不能为空';
      return;
    }

    try {
      if (isEditMode && currentUserId) {
        // 更新用户 - 不包含 role 属性，因为 API 不接受更新角色
        const updateData: UpdateUserRequest = {
          username: formUsername,
          email: formEmail,
          fullName: formFullName || undefined,
          // role 属性在更新时会导致错误，所以不包含
          phone: formPhone || undefined,
          address: formAddress || undefined,
          city: formCity || undefined,
          country: formCountry || undefined,
          postalCode: formPostalCode || undefined,
          bio: formBio || undefined
        };

        // 只有在填写了密码的情况下才更新密码
        if (formPassword) {
          updateData.password = formPassword;
        }

        console.log('更新用户数据:', updateData);
        await lighthouseApiService.updateUser(currentUserId, updateData);
        success = '用户更新成功';
      } else {
        // 创建用户
        const createData: CreateUserRequest = {
          username: formUsername,
          email: formEmail,
          password: formPassword,
          fullName: formFullName || undefined,
          role: formRole,
          phone: formPhone || undefined,
          address: formAddress || undefined,
          city: formCity || undefined,
          country: formCountry || undefined,
          postalCode: formPostalCode || undefined,
          bio: formBio || undefined
        };

        await lighthouseApiService.createUser(createData);
        success = '用户创建成功';
      }

      // 关闭对话框并刷新用户列表
      showUserDialog = false;
      await loadUsers();

      // 3秒后清除成功消息
      setTimeout(() => {
        success = '';
      }, 3000);
    } catch (err: any) {
      formError = `保存用户失败: ${err.message}`;
    }
  }

  // 打开删除确认对话框
  function openDeleteDialog(user: LighthouseUser) {
    userToDelete = user;
    showDeleteDialog = true;
  }

  // 删除用户
  async function deleteUser() {
    if (!userToDelete) return;

    try {
      await lighthouseApiService.deleteUser(userToDelete.id);
      success = '用户删除成功';

      // 关闭对话框并刷新用户列表
      showDeleteDialog = false;
      userToDelete = null;
      await loadUsers();

      // 3秒后清除成功消息
      setTimeout(() => {
        success = '';
      }, 3000);
    } catch (err: any) {
      error = `删除用户失败: ${err.message}`;
      showDeleteDialog = false;
    }
  }

  // 处理登录
  async function handleLogin() {
    loginError = '';
    isLoggingIn = true;

    try {
      if (!loginUsername || !loginPassword) {
        loginError = '用户名和密码不能为空';
        isLoggingIn = false;
        return;
      }

      const response = await lighthouseApiService.login(loginUsername, loginPassword);

      // 保存 token 和用户信息
      setLighthouseToken(response.access_token, response.user.username);

      // 关闭登录对话框并加载用户列表
      showLoginDialog = false;
      loginUsername = '';
      loginPassword = '';
      await loadUsers();
    } catch (err: any) {
      loginError = `登录失败: ${err.message}`;
    } finally {
      isLoggingIn = false;
    }
  }

  // 打开设置页面
  function openSettings() {
    goto('/settings');
  }

  // 计算总页数
  let totalPages = $derived(Math.max(1, Math.ceil(totalUsers / pageSize)));
</script>

<div class="container mx-auto p-4 max-w-7xl">
  <div class="flex justify-between items-center mb-6">
    <div>
      <h1 class="text-2xl font-bold">Lighthouse 用户管理</h1>
      <p class="text-muted-foreground">管理后端用户账号</p>
    </div>
    <div class="flex gap-2">
      <Button variant="outline" onclick={openSettings}>
        配置连接
      </Button>
      <Button onclick={openCreateUserDialog}>
        添加用户
      </Button>
    </div>
  </div>

  <!-- 搜索栏 -->
  <div class="flex gap-2 mb-4">
    <Input
      type="text"
      placeholder="搜索用户名或邮箱..."
      bind:value={searchQuery}
      class="max-w-md"
    />
    <Button variant="secondary" onclick={handleSearch}>
      搜索
    </Button>
  </div>

  <!-- 错误和成功消息 -->
  {#if error}
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
      {error}
    </div>
  {/if}

  {#if success}
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
      {success}
    </div>
  {/if}

  <!-- 用户列表 -->
  <div class="border rounded-md overflow-hidden mb-6">
    <table class="min-w-full divide-y divide-slate-200">
      <thead class="bg-slate-50">
        <tr>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">ID</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">用户名</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">邮箱</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">角色</th>
          <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-slate-500 uppercase tracking-wider">操作</th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-slate-200">
        {#if isLoading}
          <tr>
            <td colspan="5" class="px-6 py-4 text-center">
              <div class="flex justify-center">
                <div class="w-6 h-6 border-2 border-t-transparent border-slate-500 rounded-full animate-spin"></div>
              </div>
            </td>
          </tr>
        {:else if !users || users.length === 0}
          <tr>
            <td colspan="5" class="px-6 py-4 text-center text-sm text-slate-500">
              {searchQuery ? '没有找到匹配的用户' : '暂无用户数据'}
            </td>
          </tr>
        {:else}
          {#each users as user}
            <tr class="hover:bg-slate-50">
              <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500">{user.id}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-slate-900">{user.username}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500">{user.email}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500">
                <span class={`px-2 py-1 text-xs rounded-full ${user.role === 'ADMIN' ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800'}`}>
                  {user.role}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <button
                  onclick={() => openEditUserDialog(user)}
                  class="text-blue-600 hover:text-blue-900 mr-3"
                >
                  编辑
                </button>
                <button
                  onclick={() => openDeleteDialog(user)}
                  class="text-red-600 hover:text-red-900"
                >
                  删除
                </button>
              </td>
            </tr>
          {/each}
        {/if}
      </tbody>
    </table>
  </div>

  <!-- 分页 -->
  {#if totalPages > 1}
    <div class="flex justify-between items-center">
      <div class="text-sm text-slate-500">
        共 {totalUsers} 条记录，第 {currentPage} / {totalPages} 页
      </div>
      <div class="flex gap-1">
        <Button
          variant="outline"
          size="sm"
          disabled={currentPage === 1}
          onclick={() => handlePageChange(currentPage - 1)}
        >
          上一页
        </Button>
        {#each Array(Math.min(totalPages, 5)) as _, i}
          {#if totalPages <= 5 || (i < 3 && currentPage <= 3) || (i >= totalPages - 5 && currentPage > totalPages - 3) || (i >= currentPage - 2 && i <= currentPage + 2)}
            <Button
              variant={currentPage === i + 1 ? "default" : "outline"}
              size="sm"
              onclick={() => handlePageChange(i + 1)}
            >
              {i + 1}
            </Button>
          {/if}
        {/each}
        <Button
          variant="outline"
          size="sm"
          disabled={currentPage === totalPages}
          onclick={() => handlePageChange(currentPage + 1)}
        >
          下一页
        </Button>
      </div>
    </div>
  {/if}
</div>

<!-- 用户表单对话框 -->
<Dialog.Root bind:open={showUserDialog}>
  <Dialog.Content class="sm:max-w-[600px]">
    <Dialog.Header>
      <Dialog.Title>{isEditMode ? '编辑用户' : '添加用户'}</Dialog.Title>
      <Dialog.Description>
        {isEditMode ? '修改用户信息' : '创建新的用户账号'}
      </Dialog.Description>
    </Dialog.Header>

    <div class="grid gap-4 py-4">
      <div class="grid grid-cols-4 items-center gap-4">
        <label for="username" class="text-right text-sm font-medium">
          用户名 *
        </label>
        <input
          id="username"
          type="text"
          bind:value={formUsername}
          class="col-span-3 h-10 w-full rounded-md border border-slate-300 bg-transparent px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-slate-400"
        />
      </div>

      <div class="grid grid-cols-4 items-center gap-4">
        <label for="email" class="text-right text-sm font-medium">
          邮箱 *
        </label>
        <input
          id="email"
          type="email"
          bind:value={formEmail}
          class="col-span-3 h-10 w-full rounded-md border border-slate-300 bg-transparent px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-slate-400"
        />
      </div>

      <div class="grid grid-cols-4 items-center gap-4">
        <label for="password" class="text-right text-sm font-medium">
          密码 {isEditMode ? '' : '*'}
        </label>
        <input
          id="password"
          type="password"
          bind:value={formPassword}
          placeholder={isEditMode ? "留空则不修改密码" : ""}
          class="col-span-3 h-10 w-full rounded-md border border-slate-300 bg-transparent px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-slate-400"
        />
      </div>

      <div class="grid grid-cols-4 items-center gap-4">
        <label for="fullName" class="text-right text-sm font-medium">
          姓名
        </label>
        <input
          id="fullName"
          type="text"
          bind:value={formFullName}
          class="col-span-3 h-10 w-full rounded-md border border-slate-300 bg-transparent px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-slate-400"
        />
      </div>

      <div class="grid grid-cols-4 items-center gap-4">
        <label for="role" class="text-right text-sm font-medium">
          角色 *
        </label>
        <select
          id="role"
          bind:value={formRole}
          class="col-span-3 h-10 w-full rounded-md border border-slate-300 bg-transparent px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-slate-400"
        >
          <option value="USER">普通用户 (USER)</option>
          <option value="ADMIN">管理员 (ADMIN)</option>
        </select>
      </div>

      <div class="grid grid-cols-4 items-center gap-4">
        <label for="phone" class="text-right text-sm font-medium">
          电话
        </label>
        <input
          id="phone"
          type="text"
          bind:value={formPhone}
          class="col-span-3 h-10 w-full rounded-md border border-slate-300 bg-transparent px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-slate-400"
        />
      </div>

      <div class="grid grid-cols-4 items-center gap-4">
        <label for="address" class="text-right text-sm font-medium">
          地址
        </label>
        <input
          id="address"
          type="text"
          bind:value={formAddress}
          class="col-span-3 h-10 w-full rounded-md border border-slate-300 bg-transparent px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-slate-400"
        />
      </div>

      <div class="grid grid-cols-4 items-center gap-4">
        <label for="city" class="text-right text-sm font-medium">
          城市
        </label>
        <input
          id="city"
          type="text"
          bind:value={formCity}
          class="col-span-3 h-10 w-full rounded-md border border-slate-300 bg-transparent px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-slate-400"
        />
      </div>

      <div class="grid grid-cols-4 items-center gap-4">
        <label for="country" class="text-right text-sm font-medium">
          国家
        </label>
        <input
          id="country"
          type="text"
          bind:value={formCountry}
          class="col-span-3 h-10 w-full rounded-md border border-slate-300 bg-transparent px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-slate-400"
        />
      </div>

      <div class="grid grid-cols-4 items-center gap-4">
        <label for="postalCode" class="text-right text-sm font-medium">
          邮编
        </label>
        <input
          id="postalCode"
          type="text"
          bind:value={formPostalCode}
          class="col-span-3 h-10 w-full rounded-md border border-slate-300 bg-transparent px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-slate-400"
        />
      </div>

      <div class="grid grid-cols-4 items-center gap-4">
        <label for="bio" class="text-right text-sm font-medium">
          简介
        </label>
        <textarea
          id="bio"
          bind:value={formBio}
          rows="3"
          class="col-span-3 w-full rounded-md border border-slate-300 bg-transparent px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-slate-400"
        ></textarea>
      </div>

      {#if formError}
        <div class="text-red-500 text-sm mt-2 col-span-4 text-center">
          {formError}
        </div>
      {/if}
    </div>

    <Dialog.Footer>
      <Button variant="outline" onclick={() => (showUserDialog = false)}>
        取消
      </Button>
      <Button onclick={saveUser}>
        保存
      </Button>
    </Dialog.Footer>
  </Dialog.Content>
</Dialog.Root>

<!-- 删除确认对话框 -->
<Dialog.Root bind:open={showDeleteDialog}>
  <Dialog.Content>
    <Dialog.Header>
      <Dialog.Title>确认删除</Dialog.Title>
      <Dialog.Description>
        您确定要删除用户 "{userToDelete?.username}" 吗？此操作不可撤销。
      </Dialog.Description>
    </Dialog.Header>
    <Dialog.Footer>
      <div class="flex justify-end gap-2">
        <Button variant="outline" onclick={() => (showDeleteDialog = false)}>取消</Button>
        <Button variant="destructive" onclick={deleteUser}>删除</Button>
      </div>
    </Dialog.Footer>
  </Dialog.Content>
</Dialog.Root>

<!-- 登录对话框 -->
<Dialog.Root bind:open={showLoginDialog}>
  <Dialog.Content class="sm:max-w-[425px]">
    <Dialog.Header>
      <Dialog.Title>登录到 Lighthouse</Dialog.Title>
      <Dialog.Description>
        请输入您的 Lighthouse 后端账号和密码
      </Dialog.Description>
    </Dialog.Header>

    <div class="grid gap-4 py-4">
      <div class="grid grid-cols-4 items-center gap-4">
        <label for="login-username" class="text-right text-sm font-medium">
          用户名
        </label>
        <input
          id="login-username"
          type="text"
          bind:value={loginUsername}
          class="col-span-3 h-10 w-full rounded-md border border-slate-300 bg-transparent px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-slate-400"
        />
      </div>

      <div class="grid grid-cols-4 items-center gap-4">
        <label for="login-password" class="text-right text-sm font-medium">
          密码
        </label>
        <input
          id="login-password"
          type="password"
          bind:value={loginPassword}
          class="col-span-3 h-10 w-full rounded-md border border-slate-300 bg-transparent px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-slate-400"
        />
      </div>

      {#if loginError}
        <div class="text-red-500 text-sm mt-2 col-span-4 text-center">
          {loginError}
        </div>
      {/if}
    </div>

    <Dialog.Footer>
      <Button variant="outline" onclick={openSettings}>
        配置连接
      </Button>
      <Button onclick={handleLogin} disabled={isLoggingIn}>
        {#if isLoggingIn}
          <span class="inline-block w-4 h-4 border-2 border-t-transparent border-white rounded-full animate-spin mr-2"></span>
        {/if}
        登录
      </Button>
    </Dialog.Footer>
  </Dialog.Content>
</Dialog.Root>
