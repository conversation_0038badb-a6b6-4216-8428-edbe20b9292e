<script lang="ts">
  import { onMount } from 'svelte';
  import { Button } from '$lib/components/ui/button';
  import settings, { loadSettings, saveSettings } from "$lib/stores/settings";
  import lighthouseSettings, {
    loadLighthouseSettings,
    saveLighthouseSettings,
    isTokenValid as isLighthouseTokenValid,
    clearLighthouseToken,
    setLighthouseToken
  } from "$lib/stores/lighthouseSettings";
  import referrerSettings, {
    loadReferrerSettings,
    saveReferrerSettings,
    isTokenValid as isReferrerTokenValid,
    clearReferrerToken,
    setReferrerToken
  } from "$lib/stores/referrerSettings";
  import lighthouseApiService from "$lib/services/lighthouseApiService";
  import referrerApiService from "$lib/services/referrerApiService";
  import { ChatOpenAI } from "@langchain/openai"; // 导入 ChatOpenAI

  // 状态管理
  let openrouterApiKey = $state("");
  let openrouterSiteUrl = $state("");
  let notionApiKey = $state("");
  let notionDatabaseId = $state("");
  let notionInspirationDbId = $state("");
  let models = $state<{id: string; name: string}[]>([]);
  let activeSection = $state("api");
  let isSaving = $state(false);
  let saveError = $state("");
  let saveSuccess = $state(false);

  // 模型管理
  let newModelId = $state("");
  let newModelName = $state("");
  let editingModelIndex = $state<number | null>(null);
  let modelError = $state("");

  // 模型测试状态
  let testingModelId = $state<string | null>(null);
  let testResults = $state<Record<string, {success: boolean; message: string}>>({});

  // Lighthouse 设置
  let lighthouseServerUrl = $state("http://43.139.167.155");
  let lighthousePort = $state(3001);
  let isTestingLighthouseConnection = $state(false);
  let lighthouseConnectionTestResult = $state<{success: boolean; message: string} | null>(null);
  let lighthouseLoginUsername = $state("");
  let lighthouseLoginPassword = $state("");
  let isLighthouseLoggingIn = $state(false);
  let lighthouseLoginError = $state("");

  // 推荐人管理设置
  let referrerServerUrl = $state("https://api.e3e4.club");
  let referrerPort = $state(443);
  let isTestingReferrerConnection = $state(false);
  let referrerConnectionTestResult = $state<{success: boolean; message: string} | null>(null);
  let referrerLoginUsername = $state("");
  let referrerLoginPassword = $state("");
  let isReferrerLoggingIn = $state(false);
  let referrerLoginError = $state("");

  // 监听设置变化
  $effect(() => {
    if ($settings) {
      openrouterApiKey = $settings.openrouterApiKey || "";
      openrouterSiteUrl = $settings.openrouterSiteUrl || "https://openrouter.ai/api/v1/chat/completions";
      notionApiKey = $settings.notionApiKey || "";
      notionDatabaseId = $settings.notionDatabaseId || "";
      notionInspirationDbId = $settings.notionInspirationDbId || "";
      models = [...($settings.models || [])];
    }
  });

  // 加载设置
  onMount(() => {
    loadSettings();
    loadLighthouseSettings();
    loadReferrerSettings();
  });

  // 监听 Lighthouse 设置变化
  $effect(() => {
    if ($lighthouseSettings) {
      lighthouseServerUrl = $lighthouseSettings.serverUrl || "http://43.139.167.155";
      lighthousePort = $lighthouseSettings.port || 3001;
    }
  });

  // 监听推荐人管理设置变化
  $effect(() => {
    if ($referrerSettings) {
      referrerServerUrl = $referrerSettings.serverUrl || "https://api.e3e4.club";
      referrerPort = $referrerSettings.port || 443;
    }
  });

  // 保存设置
  async function handleSaveSettings() {
    if (!openrouterApiKey || !notionApiKey || !notionDatabaseId) {
      saveError = "API密钥和主数据库ID是必填项";
      return;
    }

    saveError = "";
    isSaving = true;
    saveSuccess = false;

    try {
      // 保存 OpenRouter 和 Notion 设置
      const success = await saveSettings({
        openrouterApiKey,
        openrouterSiteUrl,
        notionApiKey,
        notionDatabaseId,
        notionInspirationDbId,
        models
      });

      // 保存 Lighthouse 设置
      const lighthouseSuccess = await saveLighthouseSettings({
        serverUrl: lighthouseServerUrl,
        port: lighthousePort
      });

      // 保存推荐人管理设置
      const referrerSuccess = await saveReferrerSettings({
        serverUrl: referrerServerUrl,
        port: referrerPort
      });

      if (success && lighthouseSuccess && referrerSuccess) {
        saveSuccess = true;

        // 3秒后关闭成功提示
        setTimeout(() => {
          saveSuccess = false;
        }, 3000);
      } else {
        saveError = "保存设置时出现错误";
      }
    } catch (err: unknown) {
      console.error("保存设置出错:", err);
      saveError = `保存失败: ${err instanceof Error ? err.message : '未知错误'}`;
    } finally {
      isSaving = false;
    }
  }

  // 测试 Lighthouse 连接
  async function testLighthouseConnection() {
    lighthouseConnectionTestResult = null;
    isTestingLighthouseConnection = true;

    try {
      // 验证输入
      if (!lighthouseServerUrl) {
        lighthouseConnectionTestResult = {
          success: false,
          message: "请输入服务器地址"
        };
        isTestingLighthouseConnection = false;
        return;
      }

      if (!lighthousePort || lighthousePort <= 0) {
        lighthouseConnectionTestResult = {
          success: false,
          message: "请输入有效的端口号"
        };
        isTestingLighthouseConnection = false;
        return;
      }

      // 先保存当前设置
      await saveLighthouseSettings({
        serverUrl: lighthouseServerUrl,
        port: lighthousePort
      });

      console.log(`开始测试连接到 ${lighthouseServerUrl}:${lighthousePort}`);

      // 测试连接
      const success = await lighthouseApiService.testConnection();

      if (success) {
        lighthouseConnectionTestResult = {
          success: true,
          message: "连接成功！服务器可访问。"
        };
      } else {
        lighthouseConnectionTestResult = {
          success: false,
          message: "连接失败，无法访问服务器。请检查服务器地址和端口是否正确，以及服务器是否在运行。"
        };
      }
    } catch (err: any) {
      console.error("连接测试出错:", err);
      lighthouseConnectionTestResult = {
        success: false,
        message: `连接错误: ${err.message || "未知错误"}`
      };
    } finally {
      isTestingLighthouseConnection = false;
    }
  }

  // 登录到 Lighthouse
  async function loginToLighthouse() {
    lighthouseLoginError = "";
    isLighthouseLoggingIn = true;

    try {
      if (!lighthouseLoginUsername || !lighthouseLoginPassword) {
        lighthouseLoginError = "用户名和密码不能为空";
        isLighthouseLoggingIn = false;
        return;
      }

      // 先保存当前设置
      await saveLighthouseSettings({
        serverUrl: lighthouseServerUrl,
        port: lighthousePort
      });

      // 登录
      const response = await lighthouseApiService.login(lighthouseLoginUsername, lighthouseLoginPassword);

      // 保存 token
      setLighthouseToken(response.access_token, response.user.username);

      lighthouseConnectionTestResult = {
        success: true,
        message: `登录成功！欢迎 ${response.user.username}`
      };

      // 清空登录表单
      lighthouseLoginUsername = "";
      lighthouseLoginPassword = "";
    } catch (err: any) {
      lighthouseLoginError = `登录失败: ${err.message || "未知错误"}`;
      lighthouseConnectionTestResult = {
        success: false,
        message: `登录失败: ${err.message || "未知错误"}`
      };
    } finally {
      isLighthouseLoggingIn = false;
    }
  }

  // 注销 Lighthouse
  function logoutLighthouse() {
    clearLighthouseToken();
    lighthouseConnectionTestResult = {
      success: true,
      message: "已注销登录"
    };
  }

  // 测试推荐人管理连接
  async function testReferrerConnection() {
    referrerConnectionTestResult = null;
    isTestingReferrerConnection = true;

    try {
      // 验证输入
      if (!referrerServerUrl) {
        referrerConnectionTestResult = {
          success: false,
          message: "请输入服务器地址"
        };
        isTestingReferrerConnection = false;
        return;
      }

      if (!referrerPort || referrerPort <= 0) {
        referrerConnectionTestResult = {
          success: false,
          message: "请输入有效的端口号"
        };
        isTestingReferrerConnection = false;
        return;
      }

      // 先保存当前设置
      await saveReferrerSettings({
        serverUrl: referrerServerUrl,
        port: referrerPort
      });

      console.log(`开始测试连接到 ${referrerServerUrl}:${referrerPort}`);

      // 测试连接
      const success = await referrerApiService.testConnection();

      if (success) {
        referrerConnectionTestResult = {
          success: true,
          message: "连接成功！服务器可访问。"
        };
      } else {
        referrerConnectionTestResult = {
          success: false,
          message: "连接失败，无法访问服务器。请检查服务器地址和端口是否正确，以及服务器是否在运行。"
        };
      }
    } catch (err: any) {
      console.error("连接测试出错:", err);
      referrerConnectionTestResult = {
        success: false,
        message: `连接错误: ${err.message || "未知错误"}`
      };
    } finally {
      isTestingReferrerConnection = false;
    }
  }

  // 登录到推荐人管理
  async function loginToReferrer() {
    referrerLoginError = "";
    isReferrerLoggingIn = true;

    try {
      if (!referrerLoginUsername || !referrerLoginPassword) {
        referrerLoginError = "用户名和密码不能为空";
        isReferrerLoggingIn = false;
        return;
      }

      // 先保存当前设置
      await saveReferrerSettings({
        serverUrl: referrerServerUrl,
        port: referrerPort
      });

      // 登录
      const response = await referrerApiService.login(referrerLoginUsername, referrerLoginPassword);

      // 保存 token
      setReferrerToken(response.access_token, response.user.username);

      referrerConnectionTestResult = {
        success: true,
        message: `登录成功！欢迎 ${response.user.username}`
      };

      // 清空登录表单
      referrerLoginUsername = "";
      referrerLoginPassword = "";
    } catch (err: any) {
      referrerLoginError = `登录失败: ${err.message || "未知错误"}`;
      referrerConnectionTestResult = {
        success: false,
        message: `登录失败: ${err.message || "未知错误"}`
      };
    } finally {
      isReferrerLoggingIn = false;
    }
  }

  // 注销推荐人管理
  function logoutReferrer() {
    clearReferrerToken();
    referrerConnectionTestResult = {
      success: true,
      message: "已注销登录"
    };
  }

  // 重置为默认设置
  function resetToDefaults() {
    openrouterApiKey = "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";
    openrouterSiteUrl = "https://openrouter.ai/api/v1/chat/completions";
    notionApiKey = "ntn_V40341044546PN6Fp3PvPWFVHa9qOvfuv2BAEQiuPddbhP";
    notionDatabaseId = "1ca0f0738c8680edb18cde821a4158a8";
    notionInspirationDbId = "1cd0f0738c868027b960dc8129fbd159";
    models = [
      { id: "google/gemini-2.0-flash-001", name: "Gemini 2" },
      { id: "google/gemini-2.5-pro-exp-03-25:free", name: "Gemini 2.5" },
      { id: "deepseek/deepseek-chat-v3-0324", name: "Deepseek Chat v3" },
      { id: "openrouter/optimus-alpha", name: "Quasar Alpha" }
    ];
  }

  // 添加模型
  function addModel() {
    modelError = "";

    if (!newModelId || !newModelName) {
      modelError = "模型 ID 和名称不能为空";
      return;
    }

    // 检查是否已存在相同 ID 的模型
    if (models.some(m => m.id === newModelId)) {
      modelError = `模型 ID "${newModelId}" 已存在`;
      return;
    }

    models = [...models, { id: newModelId, name: newModelName }];
    newModelId = "";
    newModelName = "";
  }

  // 删除模型
  function deleteModel(index: number) {
    models = models.filter((_, i) => i !== index);
    if (editingModelIndex === index) {
      editingModelIndex = null;
    }
  }

  // 开始编辑模型
  function startEditModel(index: number) {
    editingModelIndex = index;
    newModelId = models[index].id;
    newModelName = models[index].name;
  }

  // 保存编辑的模型
  function saveEditModel() {
    modelError = "";

    if (editingModelIndex === null) return;

    if (!newModelId || !newModelName) {
      modelError = "模型 ID 和名称不能为空";
      return;
    }

    // 检查是否与其他模型 ID 冲突
    if (models.some((m, i) => m.id === newModelId && i !== editingModelIndex)) {
      modelError = `模型 ID "${newModelId}" 已存在`;
      return;
    }

    models = models.map((model, i) => {
      if (i === editingModelIndex) {
        return { id: newModelId, name: newModelName };
      }
      return model;
    });

    editingModelIndex = null;
    newModelId = "";
    newModelName = "";
  }

  // 取消编辑
  function cancelEdit() {
    editingModelIndex = null;
    newModelId = "";
    newModelName = "";
    modelError = "";
  }

  // 测试模型连接
  async function testModel(modelId: string) {
    if (!openrouterApiKey) {
      saveError = "请先填写API密钥";
      return;
    }

    testingModelId = modelId;

    try {
      console.log(`开始测试模型: ${modelId}`);

      // 创建临时模型实例
      const model = new ChatOpenAI({
        apiKey: openrouterApiKey,
        modelName: modelId,
        openAIApiKey: openrouterApiKey, // 兼容性字段
        configuration: {
            baseURL: openrouterSiteUrl.endsWith('/chat/completions')
                ? openrouterSiteUrl.slice(0, -'/chat/completions'.length)
                : openrouterSiteUrl,
            // 添加必要的 OpenRouter header
            defaultHeaders: {
                'HTTP-Referer': 'http://localhost:1423', // 应用地址
                'X-Title': 'Note Classifier App' // 应用名称
            }
        },
        temperature: 0.1,
        maxRetries: 1,
        timeout: 10000, // 10秒超时
      });

      // 发送简单测试请求
      const response = await model.invoke([
        {
          role: "system",
          content: "你是一个助手。请简短回复。"
        },
        {
          role: "user",
          content: "请回复'测试成功'来验证连接"
        }
      ]);

      console.log("模型测试响应:", response);

      // 检查响应
      const responseText = typeof response.content === 'string'
        ? response.content
        : Array.isArray(response.content)
          ? response.content.map(c => typeof c === 'string' ? c : JSON.stringify(c)).join('')
          : '';

      if (responseText.includes("测试成功")) {
        testResults = {
          ...testResults,
          [modelId]: {
            success: true,
            message: "连接成功！模型可用。"
          }
        };
      } else {
        testResults = {
          ...testResults,
          [modelId]: {
            success: true,
            message: `模型响应: ${responseText.substring(0, 50)}...`
          }
        };
      }

    } catch (error: any) {
      console.error("模型测试失败:", error);
      testResults = {
        ...testResults,
        [modelId]: {
          success: false,
          message: `连接失败: ${error.message || "未知错误"}`
        }
      };
    } finally {
      testingModelId = null;
    }
  }
</script>

<div class="container mx-auto p-4 max-w-6xl">
  <div class="mb-6">
    <h1 class="text-2xl font-bold">系统配置</h1>
    <p class="text-muted-foreground">配置应用所需的API密钥、模型和系统参数</p>
  </div>

  <div class="flex h-[calc(100vh-200px)] overflow-hidden border rounded-lg">
    <!-- 左侧导航菜单 - VS Code 风格 -->
    <div class="w-48 bg-slate-100 dark:bg-slate-800 border-r border-slate-200 dark:border-slate-700 flex-shrink-0">
      <div class="py-2">
        <button
          class={`w-full text-left px-4 py-2 text-sm ${activeSection === 'api' ? 'bg-slate-200 dark:bg-slate-700 font-medium' : 'hover:bg-slate-200 dark:hover:bg-slate-700'}`}
          onclick={() => activeSection = 'api'}
        >
          API 配置
        </button>
        <button
          class={`w-full text-left px-4 py-2 text-sm ${activeSection === 'models' ? 'bg-slate-200 dark:bg-slate-700 font-medium' : 'hover:bg-slate-200 dark:hover:bg-slate-700'}`}
          onclick={() => activeSection = 'models'}
        >
          模型管理
        </button>
        <button
          class={`w-full text-left px-4 py-2 text-sm ${activeSection === 'lighthouse' ? 'bg-slate-200 dark:bg-slate-700 font-medium' : 'hover:bg-slate-200 dark:hover:bg-slate-700'}`}
          onclick={() => activeSection = 'lighthouse'}
        >
          Lighthouse
        </button>
        <button
          class={`w-full text-left px-4 py-2 text-sm ${activeSection === 'referrer' ? 'bg-slate-200 dark:bg-slate-700 font-medium' : 'hover:bg-slate-200 dark:hover:bg-slate-700'}`}
          onclick={() => activeSection = 'referrer'}
        >
          推荐人管理
        </button>
      </div>
    </div>

    <!-- 右侧内容区域 -->
    <div class="flex-1 overflow-y-auto p-6">
      {#if activeSection === 'api'}
        <!-- API 配置面板 -->
        <div class="space-y-6">
          <div class="border-b pb-4">
            <h3 class="font-medium text-lg mb-4">OpenRouter配置</h3>

            <div class="grid grid-cols-4 items-center gap-4 mb-4">
              <label for="openrouter-key" class="text-right text-sm font-medium">
                API密钥
              </label>
              <input
                id="openrouter-key"
                type="password"
                bind:value={openrouterApiKey}
                class="col-span-3 h-10 w-full rounded-md border border-slate-300 bg-transparent px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-slate-400"
              />
            </div>

            <div class="grid grid-cols-4 items-center gap-4">
              <label for="openrouter-url" class="text-right text-sm font-medium">
                API地址
              </label>
              <input
                id="openrouter-url"
                type="text"
                bind:value={openrouterSiteUrl}
                class="col-span-3 h-10 w-full rounded-md border border-slate-300 bg-transparent px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-slate-400"
              />
            </div>
          </div>

          <div class="border-b pb-4">
            <h3 class="font-medium text-lg mb-4">Notion配置</h3>

            <div class="grid grid-cols-4 items-center gap-4 mb-4">
              <label for="notion-key" class="text-right text-sm font-medium">
                API密钥
              </label>
              <input
                id="notion-key"
                type="password"
                bind:value={notionApiKey}
                class="col-span-3 h-10 w-full rounded-md border border-slate-300 bg-transparent px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-slate-400"
              />
            </div>

            <div class="grid grid-cols-4 items-center gap-4 mb-4">
              <label for="notion-db" class="text-right text-sm font-medium">
                主数据库ID
              </label>
              <input
                id="notion-db"
                type="text"
                bind:value={notionDatabaseId}
                class="col-span-3 h-10 w-full rounded-md border border-slate-300 bg-transparent px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-slate-400"
              />
            </div>

            <div class="grid grid-cols-4 items-center gap-4">
              <label for="notion-inspiration-db" class="text-right text-sm font-medium">
                灵感数据库ID
              </label>
              <input
                id="notion-inspiration-db"
                type="text"
                bind:value={notionInspirationDbId}
                class="col-span-3 h-10 w-full rounded-md border border-slate-300 bg-transparent px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-slate-400"
              />
            </div>
          </div>
        </div>
      {:else if activeSection === 'models'}
        <!-- 模型管理面板 -->
        <div>
          <h3 class="font-medium text-lg mb-4">大语言模型管理</h3>

          <!-- 模型列表 -->
          <div class="border rounded-md overflow-hidden mb-6">
            <table class="min-w-full divide-y divide-slate-200">
              <thead class="bg-slate-50">
                <tr>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">模型名称</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">模型 ID</th>
                  <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-slate-500 uppercase tracking-wider">操作</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-slate-200">
                {#if models.length === 0}
                  <tr>
                    <td colspan="3" class="px-6 py-4 text-center text-sm text-slate-500">暂无模型，请添加模型</td>
                  </tr>
                {:else}
                  {#each models as model, index}
                    <tr class="hover:bg-slate-50">
                      <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-slate-900">{model.name}</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500">{model.id}</td>
                      <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium flex justify-end items-center">
                        <button
                          onclick={() => testModel(model.id)}
                          class="text-indigo-600 hover:text-indigo-900 mr-3"
                          disabled={testingModelId === model.id}
                        >
                          {#if testingModelId === model.id}
                            <span class="inline-block w-3 h-3 border-2 border-t-transparent border-indigo-600 rounded-full animate-spin mr-1"></span>
                            测试中
                          {:else}
                            测试
                          {/if}
                        </button>
                        <button
                          onclick={() => startEditModel(index)}
                          class="text-blue-600 hover:text-blue-900 mr-3"
                        >
                          编辑
                        </button>
                        <button
                          onclick={() => deleteModel(index)}
                          class="text-red-600 hover:text-red-900"
                        >
                          删除
                        </button>
                      </td>
                    </tr>
                    {#if testResults[model.id]}
                      <tr>
                        <td colspan="3" class="px-6 py-3 text-sm">
                          <div class={`p-2 rounded ${testResults[model.id].success ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'}`}>
                            <div class="flex items-center">
                              {#if testResults[model.id].success}
                                <span class="w-2 h-2 rounded-full bg-green-500 mr-2"></span>
                              {:else}
                                <span class="w-2 h-2 rounded-full bg-red-500 mr-2"></span>
                              {/if}
                              <span>{testResults[model.id].message}</span>
                            </div>
                          </div>
                        </td>
                      </tr>
                    {/if}
                  {/each}
                {/if}
              </tbody>
            </table>
          </div>

          <!-- 添加/编辑模型表单 -->
          <div class="bg-slate-50 p-4 rounded-md">
            <h4 class="font-medium text-base mb-3">{editingModelIndex !== null ? '编辑模型' : '添加新模型'}</h4>

            <div class="grid grid-cols-1 gap-4 mb-4 sm:grid-cols-2">
              <div>
                <label for="model-name" class="block text-sm font-medium text-slate-700 mb-1">模型名称</label>
                <input
                  id="model-name"
                  type="text"
                  bind:value={newModelName}
                  placeholder="例如: Gemini 2"
                  class="w-full h-10 rounded-md border border-slate-300 bg-white px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label for="model-id" class="block text-sm font-medium text-slate-700 mb-1">模型 ID</label>
                <input
                  id="model-id"
                  type="text"
                  bind:value={newModelId}
                  placeholder="例如: google/gemini-2.0-flash-001"
                  class="w-full h-10 rounded-md border border-slate-300 bg-white px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            {#if modelError}
              <div class="text-red-500 text-sm mb-3">{modelError}</div>
            {/if}

            <div class="flex justify-end gap-2">
              {#if editingModelIndex !== null}
                <Button variant="outline" size="sm" onclick={cancelEdit}>取消</Button>
                <Button size="sm" onclick={saveEditModel}>保存更改</Button>
              {:else}
                <Button size="sm" onclick={addModel}>添加模型</Button>
              {/if}
            </div>
          </div>
        </div>
      {:else if activeSection === 'lighthouse'}
        <!-- Lighthouse 配置面板 -->
        <div>
          <h3 class="font-medium text-lg mb-4">Lighthouse 后端配置</h3>

          <div class="border-b pb-4 mb-4">
            <h4 class="font-medium text-base mb-3">服务器设置</h4>

            <div class="grid grid-cols-4 items-center gap-4 mb-3">
              <label for="lighthouse-url" class="text-right text-sm font-medium">
                服务器地址
              </label>
              <input
                id="lighthouse-url"
                type="text"
                bind:value={lighthouseServerUrl}
                placeholder="例如: http://43.139.167.155"
                class="col-span-3 h-10 w-full rounded-md border border-slate-300 bg-transparent px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-slate-400"
              />
            </div>

            <div class="grid grid-cols-4 items-center gap-4 mb-3">
              <label for="lighthouse-port" class="text-right text-sm font-medium">
                端口号
              </label>
              <input
                id="lighthouse-port"
                type="number"
                bind:value={lighthousePort}
                placeholder="例如: 3001"
                class="col-span-3 h-10 w-full rounded-md border border-slate-300 bg-transparent px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-slate-400"
              />
            </div>

            <div class="flex justify-end">
              <Button
                variant="outline"
                size="sm"
                onclick={testLighthouseConnection}
                disabled={isTestingLighthouseConnection}
              >
                {#if isTestingLighthouseConnection}
                  <span class="inline-block w-4 h-4 border-2 border-t-transparent border-slate-500 rounded-full animate-spin mr-2"></span>
                {/if}
                测试连接
              </Button>
            </div>

            {#if lighthouseConnectionTestResult}
              <div class={`mt-3 p-3 rounded-md ${lighthouseConnectionTestResult.success ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'}`}>
                <div class="flex items-center">
                  {#if lighthouseConnectionTestResult.success}
                    <span class="w-2 h-2 rounded-full bg-green-500 mr-2"></span>
                  {:else}
                    <span class="w-2 h-2 rounded-full bg-red-500 mr-2"></span>
                  {/if}
                  <span>{lighthouseConnectionTestResult.message}</span>
                </div>
              </div>
            {/if}
          </div>

          <div>
            <h4 class="font-medium text-base mb-3">用户认证</h4>

            {#if isLighthouseTokenValid()}
              <div class="bg-green-50 text-green-800 p-3 rounded-md mb-3">
                <div class="flex items-center">
                  <span class="w-2 h-2 rounded-full bg-green-500 mr-2"></span>
                  <span>已登录为: {$lighthouseSettings.username}</span>
                </div>
                <div class="mt-2 text-sm">
                  Token 有效期至: {new Date($lighthouseSettings.tokenExpiry || '').toLocaleString()}
                </div>
              </div>

              <div class="flex justify-end">
                <Button
                  variant="outline"
                  size="sm"
                  onclick={logoutLighthouse}
                >
                  注销登录
                </Button>
              </div>
            {:else}
              <div class="grid grid-cols-4 items-center gap-4 mb-3">
                <label for="lighthouse-username" class="text-right text-sm font-medium">
                  用户名
                </label>
                <input
                  id="lighthouse-username"
                  type="text"
                  bind:value={lighthouseLoginUsername}
                  class="col-span-3 h-10 w-full rounded-md border border-slate-300 bg-transparent px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-slate-400"
                />
              </div>

              <div class="grid grid-cols-4 items-center gap-4 mb-3">
                <label for="lighthouse-password" class="text-right text-sm font-medium">
                  密码
                </label>
                <input
                  id="lighthouse-password"
                  type="password"
                  bind:value={lighthouseLoginPassword}
                  class="col-span-3 h-10 w-full rounded-md border border-slate-300 bg-transparent px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-slate-400"
                />
              </div>

              {#if lighthouseLoginError}
                <div class="text-red-500 text-sm mb-3">{lighthouseLoginError}</div>
              {/if}

              <div class="flex justify-end">
                <Button
                  size="sm"
                  onclick={loginToLighthouse}
                  disabled={isLighthouseLoggingIn}
                >
                  {#if isLighthouseLoggingIn}
                    <span class="inline-block w-4 h-4 border-2 border-t-transparent border-white rounded-full animate-spin mr-2"></span>
                  {/if}
                  登录
                </Button>
              </div>
            {/if}
          </div>
        </div>
      {:else if activeSection === 'referrer'}
        <!-- 推荐人管理配置面板 -->
        <div>
          <h3 class="font-medium text-lg mb-4">推荐人管理后端配置</h3>

          <div class="border-b pb-4 mb-4">
            <h4 class="font-medium text-base mb-3">服务器设置</h4>

            <div class="grid grid-cols-4 items-center gap-4 mb-3">
              <label for="referrer-url" class="text-right text-sm font-medium">
                服务器地址
              </label>
              <input
                id="referrer-url"
                type="text"
                bind:value={referrerServerUrl}
                placeholder="例如: https://api.e3e4.club"
                class="col-span-3 h-10 w-full rounded-md border border-slate-300 bg-transparent px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-slate-400"
              />
            </div>

            <div class="grid grid-cols-4 items-center gap-4 mb-3">
              <label for="referrer-port" class="text-right text-sm font-medium">
                端口号
              </label>
              <input
                id="referrer-port"
                type="number"
                bind:value={referrerPort}
                placeholder="例如: 443"
                class="col-span-3 h-10 w-full rounded-md border border-slate-300 bg-transparent px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-slate-400"
              />
            </div>

            <div class="flex justify-end">
              <Button
                variant="outline"
                size="sm"
                onclick={testReferrerConnection}
                disabled={isTestingReferrerConnection}
              >
                {#if isTestingReferrerConnection}
                  <span class="inline-block w-4 h-4 border-2 border-t-transparent border-slate-500 rounded-full animate-spin mr-2"></span>
                {/if}
                测试连接
              </Button>
            </div>

            {#if referrerConnectionTestResult}
              <div class={`mt-3 p-3 rounded-md ${referrerConnectionTestResult.success ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'}`}>
                <div class="flex items-center">
                  {#if referrerConnectionTestResult.success}
                    <span class="w-2 h-2 rounded-full bg-green-500 mr-2"></span>
                  {:else}
                    <span class="w-2 h-2 rounded-full bg-red-500 mr-2"></span>
                  {/if}
                  <span>{referrerConnectionTestResult.message}</span>
                </div>
              </div>
            {/if}
          </div>

          <div>
            <h4 class="font-medium text-base mb-3">用户认证</h4>

            {#if isReferrerTokenValid()}
              <div class="bg-green-50 text-green-800 p-3 rounded-md mb-3">
                <div class="flex items-center">
                  <span class="w-2 h-2 rounded-full bg-green-500 mr-2"></span>
                  <span>已登录为: {$referrerSettings.username}</span>
                </div>
                <div class="mt-2 text-sm">
                  Token 有效期至: {new Date($referrerSettings.tokenExpiry || '').toLocaleString()}
                </div>
              </div>

              <div class="flex justify-end">
                <Button
                  variant="outline"
                  size="sm"
                  onclick={logoutReferrer}
                >
                  注销登录
                </Button>
              </div>
            {:else}
              <div class="grid grid-cols-4 items-center gap-4 mb-3">
                <label for="referrer-username" class="text-right text-sm font-medium">
                  用户名
                </label>
                <input
                  id="referrer-username"
                  type="text"
                  bind:value={referrerLoginUsername}
                  class="col-span-3 h-10 w-full rounded-md border border-slate-300 bg-transparent px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-slate-400"
                />
              </div>

              <div class="grid grid-cols-4 items-center gap-4 mb-3">
                <label for="referrer-password" class="text-right text-sm font-medium">
                  密码
                </label>
                <input
                  id="referrer-password"
                  type="password"
                  bind:value={referrerLoginPassword}
                  class="col-span-3 h-10 w-full rounded-md border border-slate-300 bg-transparent px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-slate-400"
                />
              </div>

              {#if referrerLoginError}
                <div class="text-red-500 text-sm mb-3">{referrerLoginError}</div>
              {/if}

              <div class="flex justify-end">
                <Button
                  size="sm"
                  onclick={loginToReferrer}
                  disabled={isReferrerLoggingIn}
                >
                  {#if isReferrerLoggingIn}
                    <span class="inline-block w-4 h-4 border-2 border-t-transparent border-white rounded-full animate-spin mr-2"></span>
                  {/if}
                  登录
                </Button>
              </div>
            {/if}
          </div>
        </div>
      {/if}

      {#if saveError}
        <div class="text-red-500 text-sm mt-4">
          {saveError}
        </div>
      {/if}

      {#if saveSuccess}
        <div class="text-green-500 text-sm mt-4">
          设置已保存成功！
        </div>
      {/if}
    </div>
  </div>

  <div class="flex justify-between mt-6">
    <Button variant="outline" size="sm" on:click={resetToDefaults} type="button">
      重置为默认值
    </Button>
    <Button on:click={handleSaveSettings} disabled={isSaving}>
      {#if isSaving}
        <span class="inline-block w-4 h-4 border-2 border-t-transparent border-white rounded-full animate-spin mr-2"></span>
      {/if}
      保存配置
    </Button>
  </div>
</div>
