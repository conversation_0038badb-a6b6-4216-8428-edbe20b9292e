<script lang="ts">
  import { invoke } from '@tauri-apps/api/core';
  import { Button } from '$lib/components/ui/button';

  let testResult = '';
  let error: string | null = null;
  let dictId = 0;

  async function testCommand() {
    try {
      error = null;
      testResult = '';

      const response = await invoke('sqlite_test_command', { param: 'test123' });
      testResult = JSON.stringify(response, null, 2);
    } catch (err: any) {
      error = err.message || '测试命令失败';
      console.error('测试命令失败:', err);
    }
  }

  async function testDeleteDict() {
    if (!dictId) {
      error = '请输入字典 ID';
      return;
    }

    try {
      error = null;
      testResult = '';

      console.log(`测试删除字典，ID = ${dictId}`);
      const response = await invoke('sqlite_delete_dict', { id: dictId });
      testResult = JSON.stringify(response, null, 2);
    } catch (err: any) {
      error = err.message || '删除字典失败';
      console.error('删除字典失败:', err);
    }
  }
</script>

<div class="container mx-auto px-4 py-8">
  <h1 class="text-2xl font-bold mb-6">Tauri 命令测试</h1>

  <!-- 错误提示 -->
  {#if error}
    <div class="bg-red-50 dark:bg-red-900/20 border border-red-100 dark:border-red-800 rounded-md p-4 text-red-800 dark:text-red-200 mb-6">
      <p>{error}</p>
    </div>
  {/if}

  <div class="bg-white dark:bg-slate-800 rounded-lg shadow-md p-6 mb-6">
    <h2 class="text-lg font-semibold mb-4">测试命令</h2>

    <div class="flex gap-4 mb-4">
      <Button onclick={testCommand}>测试命令</Button>
    </div>

    <div class="mt-6">
      <h3 class="text-md font-medium mb-2">删除字典测试</h3>
      <div class="flex gap-4 items-end mb-4">
        <div>
          <label for="dict-id" class="block text-xs font-medium text-slate-500 dark:text-slate-400 mb-1">字典 ID</label>
          <input
            id="dict-id"
            type="number"
            bind:value={dictId}
            placeholder="输入字典 ID"
            class="w-full h-9 rounded-md border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 px-3 py-1.5 text-sm text-slate-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <Button onclick={testDeleteDict}>删除字典</Button>
      </div>
    </div>

    {#if testResult}
      <div class="mt-4">
        <h3 class="text-md font-medium mb-2">测试结果</h3>
        <pre class="bg-slate-100 dark:bg-slate-700 p-4 rounded-md overflow-auto max-h-80">{testResult}</pre>
      </div>
    {/if}
  </div>

  <div class="flex justify-between">
    <a href="/sqlite-dictionaries" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 flex items-center gap-1">
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-left"><path d="m12 19-7-7 7-7"/><path d="M19 12H5"/></svg>
      返回字典列表
    </a>
  </div>
</div>
