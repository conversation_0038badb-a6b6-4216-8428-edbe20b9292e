<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import { Button } from '$lib/components/ui/button';
  import { ArrowLeft } from 'lucide-svelte';
  import ProjectCriteriaConfig from '$lib/components/rule-designer/ProjectCriteriaConfig.svelte';
  import { ruleDesignerService } from '$lib/services/ruleDesignerService';

  // 获取项目ID
  const projectId = $page.params.projectId;

  // 状态管理
  let isInitializing = $state(true);
  let error = $state<string | null>(null);

  // 初始化规则设计器表
  async function initRuleDesignerTables() {
    try {
      await ruleDesignerService.initTables();
    } catch (err: any) {
      error = err.message || '初始化规则设计器表失败';
      console.error('初始化规则设计器表失败:', err);
    } finally {
      isInitializing = false;
    }
  }

  // 组件挂载时初始化
  onMount(() => {
    initRuleDesignerTables();
  });
</script>

<div class="container mx-auto px-4 py-8">
  <div class="flex justify-between items-center mb-6">
    <div class="flex items-center gap-2">
      <Button variant="ghost" on:click={() => goto(`/projects/${projectId}`)}>
        <ArrowLeft class="h-4 w-4 mr-2" />
        返回项目详情
      </Button>
      <h1 class="text-2xl font-bold">项目入组/排除标准</h1>
    </div>
  </div>

  {#if error}
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
      <span class="block sm:inline">{error}</span>
    </div>
  {/if}



  {#if isInitializing}
    <div class="flex justify-center items-center py-12">
      <div class="inline-block w-6 h-6 border-2 border-t-transparent border-blue-600 rounded-full animate-spin"></div>
      <span class="ml-2 text-slate-600 dark:text-slate-300">初始化中...</span>
    </div>
  {:else}
    <ProjectCriteriaConfig {projectId} />
  {/if}
</div>
