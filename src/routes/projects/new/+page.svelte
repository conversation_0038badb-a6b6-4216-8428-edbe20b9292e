<script lang="ts">
  import { goto } from '$app/navigation';
  import { projectManagementService, type ProjectWithDetails } from '$lib/services/projectManagementService';
  import { Button } from '$lib/components/ui/button';
  import ProjectForm from '$lib/components/project/ProjectForm.svelte';
  import { ArrowLeft, Save } from 'lucide-svelte';
  import { sanitizeProjectData } from '$lib/utils/projectDataUtils';

  // 状态管理
  let projectDetails = $state<ProjectWithDetails>({
    project: {
      project_name: '',
      project_short_name: '',
    },
    sponsors: [],
    research_drugs: [],
    drug_groups: [],
    personnel: [],
    subsidy_schemes: [],
    subsidies: []
  });
  let isLoading = $state(false);
  let error = $state<string | null>(null);

  // 模板选择
  let showTemplateDialog = $state(true);
  let selectedTemplate = $state('empty');
  let templates = $state<{id: string, name: string, description: string}[]>([
    {
      id: 'empty',
      name: '空白项目',
      description: '从零开始创建新项目'
    },
    {
      id: 'clinical-trial',
      name: '临床试验',
      description: '包含标准临床试验所需的基本结构'
    },
    {
      id: 'observational',
      name: '观察性研究',
      description: '适用于观察性研究项目'
    }
  ]);

  // 检查基本信息是否有效（不修改状态）
  function isBasicInfoValid() {
    const { project } = projectDetails;
    return !!(
      project.project_name &&
      project.project_short_name &&
      project.disease_item_id
    );
  }

  // 设置模板
  function setSelectedTemplate(templateId: string) {
    console.log('选择模板:', templateId);
    selectedTemplate = templateId;
  }

  // 处理模板选择
  function handleTemplateSelect() {
    console.log('处理模板选择，当前模板：', selectedTemplate);

    // 先应用模板
    applyTemplate(selectedTemplate);

    // 关闭对话框
    showTemplateDialog = false;

    console.log('模板选择完成');
    console.log('项目详情：', projectDetails);
  }

  // 应用项目模板
  function applyTemplate(templateId: string) {
    console.log('应用模板:', templateId);

    // 根据选择的模板预填充项目数据
    if (templateId === 'clinical-trial') {
      projectDetails = {
        project: {
          project_name: '',
          project_short_name: '',
          project_status_item_id: 1, // 假设1是"准备中"状态
          recruitment_status_item_id: 1, // 假设1是"未开始"状态
        },
        sponsors: [],
        research_drugs: [],
        drug_groups: [],
        personnel: [],
        subsidy_schemes: [],
        subsidies: []
      };
      console.log('应用临床试验模板');
    } else if (templateId === 'observational') {
      projectDetails = {
        project: {
          project_name: '',
          project_short_name: '',
          project_status_item_id: 1, // 假设1是"准备中"状态
          project_stage_item_id: 4, // 假设4是"观察性研究"阶段
        },
        sponsors: [],
        research_drugs: [],
        drug_groups: [],
        personnel: [],
        subsidy_schemes: [],
        subsidies: []
      };
      console.log('应用观察性研究模板');
    } else {
      // 空白项目，保持默认值
      projectDetails = {
        project: {
          project_name: '',
          project_short_name: '',
        },
        sponsors: [],
        research_drugs: [],
        drug_groups: [],
        personnel: [],
        subsidy_schemes: [],
        subsidies: []
      };
      console.log('应用空白项目模板');
    }
  }

  // 保存项目草稿
  async function saveDraft() {
    if (!isBasicInfoValid()) {
      error = "请至少填写项目名称、项目简称和疾病类型";
      return;
    }

    isLoading = true;
    error = null;

    try {
      // 提取各部分数据
      const {
        project,
        sponsors = [],
        research_drugs = [],
        drug_groups = [],
        personnel = [],
        subsidy_schemes = [],
        subsidies = []
      } = projectDetails;

      // 转换数据格式
      const sponsorsList = sponsors.map((s: any) => ({
        id: s.id,
        project_id: s.project_id || '',
        sponsor_item_id: s.sponsor_item_id
      }));

      const personnelList = personnel.map((p: any) => ({
        assignment_id: p.assignment_id,
        project_id: p.project_id || '',
        personnel_id: p.personnel_id,
        role_item_id: p.role_item_id
      }));

      // 清理数据，确保数值字段为数字类型
      const sanitizedProject = sanitizeProjectData({
        project,
        sponsors: sponsorsList,
        research_drugs,
        drug_groups,
        personnel: personnelList,
        subsidy_schemes,
        subsidies
      });

      // 保存项目
      const projectId = await projectManagementService.saveProjectWithDetails(
        sanitizedProject.project,
        sanitizedProject.sponsors,
        sanitizedProject.research_drugs,
        sanitizedProject.drug_groups,
        sanitizedProject.personnel,
        sanitizedProject.subsidy_schemes,
        sanitizedProject.subsidies
      );

      // 跳转到项目编辑页
      goto(`/projects/${projectId}/edit`);
    } catch (err) {
      error = err instanceof Error ? err.message : String(err);
    } finally {
      isLoading = false;
    }
  }

  // 保存项目
  async function saveProject() {
    if (!isBasicInfoValid()) {
      error = "请至少填写项目名称、项目简称和疾病类型";
      return;
    }

    isLoading = true;
    error = null;

    try {
      // 提取各部分数据
      const {
        project,
        sponsors = [],
        research_drugs = [],
        drug_groups = [],
        personnel = [],
        subsidy_schemes = [],
        subsidies = []
      } = projectDetails;

      // 转换数据格式
      const sponsorsList = sponsors.map((s: any) => ({
        id: s.id,
        project_id: s.project_id || '',
        sponsor_item_id: s.sponsor_item_id
      }));

      const personnelList = personnel.map((p: any) => ({
        assignment_id: p.assignment_id,
        project_id: p.project_id || '',
        personnel_id: p.personnel_id,
        role_item_id: p.role_item_id
      }));

      // 清理数据，确保数值字段为数字类型
      const sanitizedProject = sanitizeProjectData({
        project,
        sponsors: sponsorsList,
        research_drugs,
        drug_groups,
        personnel: personnelList,
        subsidy_schemes,
        subsidies
      });

      // 保存项目
      const projectId = await projectManagementService.saveProjectWithDetails(
        sanitizedProject.project,
        sanitizedProject.sponsors,
        sanitizedProject.research_drugs,
        sanitizedProject.drug_groups,
        sanitizedProject.personnel,
        sanitizedProject.subsidy_schemes,
        sanitizedProject.subsidies
      );

      // 跳转到项目详情页
      goto(`/projects/${projectId}`);
    } catch (err) {
      error = err instanceof Error ? err.message : String(err);
    } finally {
      isLoading = false;
    }
  }
</script>

<div class="container mx-auto px-4 py-8">
  <!-- 模板选择对话框 -->
  {#if showTemplateDialog}
    <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 max-w-4xl w-full">
        <h2 class="text-2xl font-bold mb-4">创建新项目</h2>
        <p class="text-gray-600 mb-6">选择一个模板开始创建您的项目，或者从空白项目开始。</p>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {#each templates as template}
            <button
              type="button"
              class="border-2 rounded-lg p-6 text-left cursor-pointer hover:border-blue-500 hover:bg-blue-50 transition-colors {selectedTemplate === template.id ? 'border-blue-500 bg-blue-50' : ''}"
              onclick={() => setSelectedTemplate(template.id)}
              onkeydown={(e) => { if (e.key === 'Enter' || e.key === ' ') setSelectedTemplate(template.id); }}
            >
              <h3 class="text-lg font-semibold mb-3">{template.name}</h3>
              <p class="text-sm text-gray-600">{template.description}</p>
            </button>
          {/each}
        </div>

        <div class="flex justify-end gap-3">
          <Button
            variant="outline"
            size="lg"
            onclick={() => showTemplateDialog = false}
          >
            取消
          </Button>
          <Button
            size="lg"
            onclick={handleTemplateSelect}
          >
            使用此模板
          </Button>
        </div>
      </div>
    </div>
  {/if}

  <div class="flex justify-between items-center mb-8">
    <div class="flex items-center gap-3">
      <Button variant="ghost" onclick={() => goto('/projects')}>
        <ArrowLeft class="h-5 w-5 mr-2" />
        返回项目列表
      </Button>
      <h1 class="text-3xl font-bold">新建项目</h1>
    </div>
    <div class="flex gap-3">
      <Button
        variant="outline"
        size="lg"
        onclick={saveDraft}
        disabled={isLoading || !isBasicInfoValid()}
      >
        保存草稿
      </Button>
      <Button
        size="lg"
        onclick={saveProject}
        disabled={isLoading || !isBasicInfoValid()}
      >
        <Save class="h-5 w-5 mr-2" />
        保存项目
      </Button>
    </div>
  </div>

  <!-- 错误提示 -->
  {#if error}
    <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded mb-6">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <p class="text-sm font-medium">{error}</p>
        </div>
      </div>
    </div>
  {/if}

  <!-- 使用新的项目表单组件 -->
  {#if !showTemplateDialog}
    <ProjectForm
      projectDetails={projectDetails}
      onSave={saveProject}
      onSaveDraft={saveDraft}
      isLoading={isLoading}
      isBasicInfoValid={isBasicInfoValid}
    />
  {/if}

  <!-- 底部操作栏 -->
  <div class="fixed bottom-0 left-0 right-0 bg-white border-t shadow-md py-4 px-8">
    <div class="container mx-auto flex justify-between items-center">
      <div class="flex items-center">
        <span class="text-gray-600 mr-4">项目完成度: <span class="font-bold text-blue-600">0%</span></span>
        <div class="w-48 bg-gray-200 rounded-full h-2.5">
          <div class="bg-blue-600 h-2.5 rounded-full" style="width: 0%"></div>
        </div>
      </div>
      <div class="flex gap-3">
        <Button
          variant="outline"
          onclick={saveDraft}
          disabled={isLoading || !isBasicInfoValid()}
        >
          保存草稿
        </Button>
        <Button
          onclick={saveProject}
          disabled={isLoading || !isBasicInfoValid()}
        >
          <Save class="h-4 w-4 mr-2" />
          保存项目
        </Button>
      </div>
    </div>
  </div>

  <!-- 底部间距，防止内容被固定底栏遮挡 -->
  <div class="h-20"></div>
</div>
