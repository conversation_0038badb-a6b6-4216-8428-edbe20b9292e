<script lang="ts">
  import ProjectBasicInfo from '$lib/components/project/ProjectBasicInfo.svelte';
  import ProjectSponsors from '$lib/components/project/ProjectSponsors.svelte';
  import ProjectDrugs from '$lib/components/project/ProjectDrugs.svelte';
  import ProjectPersonnel from '$lib/components/project/ProjectPersonnel.svelte';
  import ProjectSubsidies from '$lib/components/project/ProjectSubsidies.svelte';
  
  // 状态管理
  let projectDetails = $state({
    project: {
      project_name: '',
      project_short_name: '',
    },
    sponsors: [],
    research_drugs: [],
    drug_groups: [],
    personnel: [],
    subsidy_schemes: [],
    subsidies: []
  });
  
  let activeTab = $state('basic-info');
</script>

<div class="container mx-auto px-4 py-8">
  <h1 class="text-3xl font-bold mb-8">测试组件</h1>
  
  <div class="bg-white p-8 rounded-lg shadow mb-8">
    <h2 class="text-2xl font-bold mb-6">基本信息组件</h2>
    <ProjectBasicInfo projectDetails={projectDetails} />
  </div>
  
  <div class="bg-white p-8 rounded-lg shadow mb-8">
    <h2 class="text-2xl font-bold mb-6">申办方组件</h2>
    <ProjectSponsors projectDetails={projectDetails} />
  </div>
  
  <div class="bg-white p-8 rounded-lg shadow mb-8">
    <h2 class="text-2xl font-bold mb-6">研究药物组件</h2>
    <ProjectDrugs projectDetails={projectDetails} />
  </div>
  
  <div class="bg-white p-8 rounded-lg shadow mb-8">
    <h2 class="text-2xl font-bold mb-6">研究人员组件</h2>
    <ProjectPersonnel projectDetails={projectDetails} />
  </div>
  
  <div class="bg-white p-8 rounded-lg shadow mb-8">
    <h2 class="text-2xl font-bold mb-6">补贴信息组件</h2>
    <ProjectSubsidies projectDetails={projectDetails} />
  </div>
</div>
