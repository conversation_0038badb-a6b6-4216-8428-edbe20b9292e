<script lang="ts">

  import { Button } from '$lib/components/ui/button';
  import QualityControlPanel from '$lib/components/csv-import/QualityControlPanel.svelte';
  import { goto } from '$app/navigation';
  import { ArrowLeft, Shield, AlertTriangle, CheckCircle } from 'lucide-svelte';

  // 页面标题和描述
  const pageTitle = "项目质量控制";
  const pageDescription = "检查项目人员配置是否完整，确保关键角色（PI、CRC、CRA）已正确分配";
</script>

<svelte:head>
  <title>{pageTitle} - 项目管理系统</title>
</svelte:head>

<div class="container mx-auto px-4 py-8">
  <!-- 页面标题栏 -->
  <div class="flex items-center justify-between mb-6 bg-white p-4 rounded-lg shadow">
    <div class="flex items-center">
      <Button
        variant="ghost"
        size="sm"
        onclick={() => goto('/projects')}
        class="mr-3 text-gray-600 hover:text-gray-800"
      >
        <ArrowLeft class="h-4 w-4 mr-1" />
        返回项目列表
      </Button>
      <div class="bg-orange-600 p-2 rounded-lg mr-3">
        <Shield class="h-6 w-6 text-white" />
      </div>
      <div>
        <h1 class="text-2xl font-bold text-gray-800">{pageTitle}</h1>
        <p class="text-sm text-gray-500">{pageDescription}</p>
      </div>
    </div>
  </div>

  <!-- 质量控制说明卡片 -->
  <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
    <!-- 检查项目说明 -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
      <div class="flex items-center mb-3">
        <div class="bg-blue-100 p-2 rounded-lg mr-3">
          <Shield class="h-5 w-5 text-blue-600" />
        </div>
        <h3 class="font-semibold text-blue-900">质量检查</h3>
      </div>
      <p class="text-sm text-blue-700 mb-3">
        系统会自动检查每个项目是否配置了必要的关键角色，确保项目团队结构完整。
      </p>
      <div class="text-xs text-blue-600">
        <div>• 主要研究者 (PI)</div>
        <div>• 临床研究协调员 (CRC)</div>
        <div>• 临床研究助理 (CRA)</div>
      </div>
    </div>

    <!-- 问题项目说明 -->
    <div class="bg-orange-50 border border-orange-200 rounded-lg p-6">
      <div class="flex items-center mb-3">
        <div class="bg-orange-100 p-2 rounded-lg mr-3">
          <AlertTriangle class="h-5 w-5 text-orange-600" />
        </div>
        <h3 class="font-semibold text-orange-900">问题识别</h3>
      </div>
      <p class="text-sm text-orange-700 mb-3">
        缺失关键角色的项目会被标记为"未通过检查"，并提供详细的缺失角色信息和改进建议。
      </p>
      <div class="text-xs text-orange-600">
        <div>• 缺失角色提醒</div>
        <div>• 改进建议</div>
        <div>• 快速修复指导</div>
      </div>
    </div>

    <!-- 合规项目说明 -->
    <div class="bg-green-50 border border-green-200 rounded-lg p-6">
      <div class="flex items-center mb-3">
        <div class="bg-green-100 p-2 rounded-lg mr-3">
          <CheckCircle class="h-5 w-5 text-green-600" />
        </div>
        <h3 class="font-semibold text-green-900">合规确认</h3>
      </div>
      <p class="text-sm text-green-700 mb-3">
        已正确配置所有关键角色的项目会显示为"通过检查"，确保项目可以正常进行。
      </p>
      <div class="text-xs text-green-600">
        <div>• 角色配置完整</div>
        <div>• 团队结构合规</div>
        <div>• 可正常开展研究</div>
      </div>
    </div>
  </div>

  <!-- 质量控制面板 -->
  <div class="bg-white rounded-lg shadow p-6">
    <QualityControlPanel />
  </div>

  <!-- 操作指南 -->
  <div class="mt-8 bg-gray-50 border border-gray-200 rounded-lg p-6">
    <h3 class="font-semibold text-gray-900 mb-4">操作指南</h3>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <h4 class="font-medium text-gray-800 mb-2">如何修复问题项目？</h4>
        <ol class="text-sm text-gray-600 space-y-1">
          <li>1. 点击项目列表中的"编辑"按钮</li>
          <li>2. 进入"研究人员"配置页面</li>
          <li>3. 添加缺失的关键角色人员</li>
          <li>4. 保存项目并重新检查</li>
        </ol>
      </div>
      <div>
        <h4 class="font-medium text-gray-800 mb-2">批量导入人员授权</h4>
        <ol class="text-sm text-gray-600 space-y-1">
          <li>1. 在项目列表页点击"批量导入"</li>
          <li>2. 下载CSV模板文件</li>
          <li>3. 填写人员授权信息</li>
          <li>4. 上传并执行批量导入</li>
        </ol>
      </div>
    </div>
  </div>

  <!-- 相关链接 -->
  <div class="mt-6 flex justify-center space-x-4">
    <Button
      variant="outline"
      onclick={() => goto('/projects')}
      class="border-gray-300 hover:bg-gray-50"
    >
      返回项目列表
    </Button>
    <Button
      onclick={() => goto('/projects/new')}
      class="bg-blue-600 hover:bg-blue-700"
    >
      新建项目
    </Button>
  </div>
</div>
