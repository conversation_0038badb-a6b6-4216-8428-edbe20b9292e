<script lang="ts">
  import { onMount } from 'svelte';
  import { Button } from '$lib/components/ui/button';
  import Input from '$lib/components/ui/input.svelte';
  import Label from '$lib/components/ui/label.svelte';
  import {
    loadReferrerSettings,
    isTokenValid,
    setReferrerToken,
    clearReferrerToken
  } from '$lib/stores/referrerSettings';
  import referrerApiService from '$lib/services/referrerApiService';
  import type {
    Referrer,
    AssociationRecord,
    ApiAssociationRecord,
    PatientWithoutReferrer,
    ApiPatientWithoutReferrer
  } from '$lib/services/referrerApiService';

  // 分页设置
  const PAGE_SIZE = 20; // 每页显示的记录数，从10条增加到20条

  // 状态管理
  let activeTab = $state('referrers');
  let isLoading = $state(false);
  let error = $state('');
  let success = $state('');
  let isAuthenticated = $state(false); // 新增：明确的认证状态标志

  // 注销函数
  function logout() {
    console.log('执行注销操作');
    clearReferrerToken();
    isAuthenticated = false;
    success = '已成功注销';
    referrers = [];
    associations = [];
    patientsWithoutReferrer = [];
  }

  // 登录状态
  let loginUsername = $state('');
  let loginPassword = $state('');
  let isLoggingIn = $state(false);
  let loginError = $state('');

  // 推荐人列表状态
  let referrers = $state<Referrer[]>([]);
  let referrerNextCursor = $state<string | null>(null);
  let referrerPrevCursor = $state<string | null>(null);
  let referrerSearchName = $state('');
  let referrerSearchPhone = $state('');

  // 关联记录列表状态
  let associations = $state<AssociationRecord[]>([]);
  let associationNextCursor = $state<string | null>(null);
  let associationPrevCursor = $state<string | null>(null);
  let associationSearchPatientName = $state('');
  let associationSearchPatientPhone = $state('');
  let associationSearchReferrerName = $state('');
  let associationSearchReferrerPhone = $state('');

  // 将API返回的嵌套结构转换为扁平结构
  function convertApiAssociationToFlat(item: ApiAssociationRecord | AssociationRecord): AssociationRecord {
    // 检查是否有嵌套的patient和referrer对象
    if ('patient' in item && 'referrer' in item) {
      // 是嵌套结构 (ApiAssociationRecord)
      const apiItem = item as ApiAssociationRecord;
      return {
        id: apiItem.id,
        referrerId: apiItem.referrer.id,
        referrerName: apiItem.referrer.name,
        referrerPhone: apiItem.referrer.phone,
        patientId: apiItem.patient.mbglId?.toString() || apiItem.patient.gcpmId || '',
        patientName: apiItem.patient.name,
        patientPhone: apiItem.patient.phone,
        createdAt: apiItem.createdAt,
        updatedAt: apiItem.updatedAt
      };
    } else {
      // 如果已经是扁平结构，直接返回
      return item as AssociationRecord;
    }
  }

  // 将API返回的无推荐人患者数据转换为标准格式
  function convertApiPatientToStandard(item: ApiPatientWithoutReferrer): PatientWithoutReferrer {
    return {
      // 按照优先级处理ID字段
      id: item.mbglId?.toString() || item.id || item.user_id || item.gcpmId || '',
      // 按照优先级处理姓名字段
      name: item.patientName || item.name || '未知姓名',
      // 按照优先级处理手机号字段
      phone: item.patientPhone || item.phone || '未知手机号'
    };
  }

  // 无推荐人患者列表状态
  let patientsWithoutReferrer = $state<PatientWithoutReferrer[]>([]);
  let patientNextCursor = $state<string | null>(null);
  let patientPrevCursor = $state<string | null>(null);
  let patientSearchName = $state('');
  let patientSearchPhone = $state('');

  // 新建/编辑推荐人对话框状态
  let isReferrerDialogOpen = $state(false);
  let editingReferrer = $state<Referrer | null>(null);
  let newReferrerName = $state('');
  let newReferrerPhone = $state('');

  // 新建关联记录对话框状态
  let isAssociationDialogOpen = $state(false);
  let selectedReferrerId = $state('');
  let selectedPatientId = $state('');

  // 编辑关联记录对话框状态
  let isEditAssociationDialogOpen = $state(false);
  let editingAssociation = $state<AssociationRecord | null>(null);

  // 初始化
  onMount(async () => {
    console.log('组件挂载，开始初始化');
    await loadReferrerSettings();

    console.log('设置加载完成，检查Token有效性');
    const tokenValid = isTokenValid();
    console.log('Token有效性检查结果:', tokenValid);

    // 更新认证状态
    isAuthenticated = tokenValid;

    if (tokenValid) {
      console.log('Token有效，开始加载数据');
      loadData();
    } else {
      console.log('Token无效，显示登录表单');
    }
  });

  // 加载数据
  async function loadData() {
    console.log('开始加载数据，检查Token有效性');
    if (!isTokenValid()) {
      error = '请先登录';
      isAuthenticated = false; // 确保认证状态与Token状态一致
      return;
    }

    // 确保认证状态与Token状态一致
    isAuthenticated = true;
    error = ''; // 清除之前的错误信息
    console.log('Token有效，开始加载数据');

    try {
      // 依次加载数据，而不是并行加载，以便更好地处理错误
      console.log('加载推荐人列表...');
      await loadReferrers();

      console.log('加载关联记录...');
      await loadAssociations();

      console.log('加载无推荐人患者列表...');
      await loadPatientsWithoutReferrer();

      console.log('所有数据加载完成');
      success = '数据加载成功';

      // 确保UI更新
      console.log('数据加载后状态:', {
        isAuthenticated,
        activeTab,
        referrersCount: referrers.length,
        associationsCount: associations.length,
        patientsWithoutReferrerCount: patientsWithoutReferrer.length
      });
    } catch (err: any) {
      console.error('加载数据时出错:', err);
      error = `加载数据失败: ${err.message || '未知错误'}`;
    }
  }

  // 登录
  async function login() {
    console.log('尝试登录，检查用户名和密码');
    if (!loginUsername || !loginPassword) {
      loginError = '用户名和密码不能为空';
      console.log('用户名或密码为空，中止登录');
      return;
    }

    isLoggingIn = true;
    loginError = '';
    console.log('开始登录流程');

    try {
      console.log('调用登录API');
      const response = await referrerApiService.login(loginUsername, loginPassword);
      console.log('登录API响应:', {
        hasToken: !!response.access_token,
        tokenLength: response.access_token ? response.access_token.length : 0,
        hasUser: !!response.user,
        username: response.user ? response.user.username : 'unknown'
      });

      console.log('设置Token');
      setReferrerToken(response.access_token, response.user.username);

      console.log('检查Token是否设置成功');
      const tokenValid = isTokenValid();
      console.log('Token设置后有效性检查:', tokenValid);

      // 更新认证状态
      isAuthenticated = tokenValid;

      success = `登录成功，欢迎 ${response.user.username}`;
      loginUsername = '';
      loginPassword = '';

      // 加载数据
      console.log('登录成功，开始加载数据');

      // 确保UI先更新为已登录状态，然后再加载数据
      // 使用setTimeout来确保状态更新后再加载数据
      setTimeout(async () => {
        await loadData();
      }, 0);

      // 检查登录后的状态
      console.log('数据加载完成，检查状态:', {
        referrersCount: referrers.length,
        associationsCount: associations.length,
        patientsWithoutReferrerCount: patientsWithoutReferrer.length,
        activeTab: activeTab
      });
    } catch (err: any) {
      console.error('登录失败:', err);
      loginError = `登录失败: ${err.message || '未知错误'}`;
    } finally {
      isLoggingIn = false;
    }
  }

  // 加载推荐人列表
  async function loadReferrers(cursor?: string | null) {
    isLoading = true;
    error = '';
    console.log('开始加载推荐人列表', { cursor });

    try {
      // 验证 token 有效性
      if (!isTokenValid()) {
        error = '登录已过期，请重新登录';
        isAuthenticated = false; // 确保认证状态与Token状态一致
        return;
      }

      // 确保认证状态与Token状态一致
      isAuthenticated = true;

      const params: any = {
        limit: PAGE_SIZE,
        sortBy: 'createdAt',
        sortOrder: 'desc'
      };

      if (cursor) {
        if (cursor === referrerNextCursor) {
          params.after = cursor;
        } else if (cursor === referrerPrevCursor) {
          params.before = cursor;
        }
      }

      if (referrerSearchName) {
        params.referrerName = referrerSearchName;
      }

      if (referrerSearchPhone) {
        params.referrerPhone = referrerSearchPhone;
      }

      console.log('请求推荐人列表参数:', params);
      const response = await referrerApiService.getReferrers(params);
      console.log('获取推荐人列表成功:', response);

      // 验证响应数据
      if (response && response.items) {
        referrers = response.items;
        referrerNextCursor = response.nextCursor;
        referrerPrevCursor = response.previousCursor;
        console.log(`成功加载 ${referrers.length} 条推荐人数据`);
      } else {
        console.warn('推荐人列表响应格式不符合预期:', response);
        referrers = [];
        referrerNextCursor = null;
        referrerPrevCursor = null;
      }
    } catch (err: any) {
      console.error('加载推荐人列表出错:', err);
      error = `加载推荐人列表失败: ${err.message || '未知错误'}`;
      referrers = [];
      referrerNextCursor = null;
      referrerPrevCursor = null;
    } finally {
      isLoading = false;
    }
  }

  // 加载关联记录列表
  async function loadAssociations(cursor?: string | null) {
    isLoading = true;
    error = '';
    console.log('开始加载关联记录列表', { cursor });

    try {
      // 验证 token 有效性
      if (!isTokenValid()) {
        error = '登录已过期，请重新登录';
        isAuthenticated = false; // 确保认证状态与Token状态一致
        return;
      }

      // 确保认证状态与Token状态一致
      isAuthenticated = true;

      const params: any = {
        limit: PAGE_SIZE,
        sortBy: 'createdAt',
        sortOrder: 'desc'
      };

      if (cursor) {
        if (cursor === associationNextCursor) {
          params.after = cursor;
        } else if (cursor === associationPrevCursor) {
          params.before = cursor;
        }
      }

      if (associationSearchPatientName) {
        params.patientName = associationSearchPatientName;
      }

      if (associationSearchPatientPhone) {
        params.patientPhone = associationSearchPatientPhone;
      }

      if (associationSearchReferrerName) {
        params.referrerName = associationSearchReferrerName;
      }

      if (associationSearchReferrerPhone) {
        params.referrerPhone = associationSearchReferrerPhone;
      }

      console.log('请求关联记录列表参数:', params);
      const response = await referrerApiService.getAssociations(params);
      console.log('获取关联记录列表成功:', response);

      // 验证响应数据
      if (response && response.items) {
        // 使用转换函数将API返回的嵌套结构转换为扁平结构
        associations = response.items.map(convertApiAssociationToFlat);

        associationNextCursor = response.nextCursor;
        associationPrevCursor = response.previousCursor;
        console.log(`成功加载 ${associations.length} 条关联记录数据，已转换格式:`, associations[0]);
      } else {
        console.warn('关联记录列表响应格式不符合预期:', response);
        associations = [];
        associationNextCursor = null;
        associationPrevCursor = null;
      }
    } catch (err: any) {
      console.error('加载关联记录列表出错:', err);
      error = `加载关联记录列表失败: ${err.message || '未知错误'}`;
      associations = [];
      associationNextCursor = null;
      associationPrevCursor = null;
    } finally {
      isLoading = false;
    }
  }

  // 加载无推荐人患者列表
  async function loadPatientsWithoutReferrer(cursor?: string | null) {
    isLoading = true;
    error = '';
    console.log('开始加载无推荐人患者列表', { cursor });

    try {
      // 验证 token 有效性
      if (!isTokenValid()) {
        error = '登录已过期，请重新登录';
        isAuthenticated = false; // 确保认证状态与Token状态一致
        return;
      }

      // 确保认证状态与Token状态一致
      isAuthenticated = true;

      const params: any = {
        limit: PAGE_SIZE,
        sortBy: 'user_id',  // 修改为正确的排序字段，根据API文档
        sortOrder: 'desc'
      };

      if (cursor) {
        if (cursor === patientNextCursor) {
          params.after = cursor;
        } else if (cursor === patientPrevCursor) {
          params.before = cursor;
        }
      }

      if (patientSearchName) {
        params.patientName = patientSearchName;
      }

      if (patientSearchPhone) {
        params.patientPhone = patientSearchPhone;
      }

      console.log('请求无推荐人患者列表参数:', params);
      const response = await referrerApiService.getPatientsWithoutReferrer(params);
      console.log('获取无推荐人患者列表成功:', response);

      // 详细记录第一条数据的结构，以便调试
      if (response.items && response.items.length > 0) {
        console.log('无推荐人患者第一条数据结构:', response.items[0]);
        console.log('字段检查:', {
          mbglId: response.items[0].mbglId,
          patientName: response.items[0].patientName,
          name: response.items[0].name,
          patientPhone: response.items[0].patientPhone,
          phone: response.items[0].phone,
          id: response.items[0].id,
          user_id: response.items[0].user_id
        });
      }

      // 验证响应数据
      if (response && response.items) {
        // 使用转换函数将API返回的数据转换为标准格式
        patientsWithoutReferrer = response.items.map(convertApiPatientToStandard);
        patientNextCursor = response.nextCursor;
        patientPrevCursor = response.previousCursor;
        console.log(`成功加载 ${patientsWithoutReferrer.length} 条无推荐人患者数据，已转换格式:`, patientsWithoutReferrer[0]);
      } else {
        console.warn('无推荐人患者列表响应格式不符合预期:', response);
        patientsWithoutReferrer = [];
        patientNextCursor = null;
        patientPrevCursor = null;
      }
    } catch (err: any) {
      console.error('加载无推荐人患者列表出错:', err);
      error = `加载无推荐人患者列表失败: ${err.message || '未知错误'}`;
      patientsWithoutReferrer = [];
      patientNextCursor = null;
      patientPrevCursor = null;
    } finally {
      isLoading = false;
    }
  }

  // 打开新建推荐人对话框
  function openNewReferrerDialog() {
    editingReferrer = null;
    newReferrerName = '';
    newReferrerPhone = '';
    isReferrerDialogOpen = true;
  }

  // 打开编辑推荐人对话框
  function openEditReferrerDialog(referrer: Referrer) {
    editingReferrer = referrer;
    newReferrerName = referrer.name;
    newReferrerPhone = referrer.phone;
    isReferrerDialogOpen = true;
  }

  // 打开编辑关联记录对话框
  function openEditAssociationDialog(association: AssociationRecord) {
    editingAssociation = association;
    selectedReferrerId = association.referrerId;
    isEditAssociationDialogOpen = true;
    console.log('打开编辑关联记录对话框:', association);

    // 确保我们有完整的关联记录信息
    if (!association.patientId) {
      console.error('警告: 关联记录缺少patientId', association);
      // 尝试从其他字段推断patientId
      if (association.id && association.id.includes('_')) {
        // 有些API可能在id中包含patientId，格式如 "referrerId_patientId"
        const parts = association.id.split('_');
        if (parts.length > 1) {
          console.log('从ID中提取patientId:', parts[1]);
          association.patientId = parts[1];
        }
      }
    }
  }

  // 保存推荐人
  async function saveReferrer() {
    if (!newReferrerName || !newReferrerPhone) {
      error = '姓名和手机号不能为空';
      return;
    }

    isLoading = true;
    error = '';

    try {
      if (editingReferrer) {
        // 更新推荐人
        await referrerApiService.updateReferrer(editingReferrer.id, {
          name: newReferrerName,
          phone: newReferrerPhone
        });
        success = '推荐人更新成功';
      } else {
        // 创建推荐人
        await referrerApiService.createReferrer({
          name: newReferrerName,
          phone: newReferrerPhone
        });
        success = '推荐人创建成功';
      }

      // 关闭对话框并重新加载数据
      isReferrerDialogOpen = false;
      await loadReferrers();
    } catch (err: any) {
      error = `${editingReferrer ? '更新' : '创建'}推荐人失败: ${err.message || '未知错误'}`;
    } finally {
      isLoading = false;
    }
  }

  // 更新关联记录
  async function updateAssociation() {
    if (!selectedReferrerId || !editingAssociation) {
      error = '请选择推荐人';
      return;
    }

    isLoading = true;
    error = '';

    try {
      console.log('更新关联记录，参数:', {
        id: editingAssociation.id,
        referrerId: selectedReferrerId,
        patientId: editingAssociation.patientId
      });

      // 更新推荐人ID，同时需要传递患者ID
      await referrerApiService.updateAssociation(editingAssociation.id, {
        referrerId: selectedReferrerId,
        patientId: editingAssociation.patientId // 必须传递患者ID
      });

      success = '关联记录更新成功';
      isEditAssociationDialogOpen = false;

      // 重新加载关联记录列表
      await loadAssociations();
    } catch (err: any) {
      // 获取当前关联的患者信息，用于错误消息中显示
      const patientName = editingAssociation.patientName || '未知患者';

      // 如果错误消息中包含"undefined"，替换为实际患者名称
      let errorMsg = err.message || '未知错误';
      if (errorMsg.includes('undefined')) {
        errorMsg = errorMsg.replace('undefined', patientName);
      }

      error = `更新关联记录失败: ${errorMsg}`;
      console.error('更新关联记录失败:', err);
    } finally {
      isLoading = false;
    }
  }
</script>

<div class="container mx-auto p-4 max-w-6xl">
  <div class="mb-6">
    <h1 class="text-2xl font-bold">推荐人管理</h1>
    <p class="text-muted-foreground">管理推荐人信息和患者关联关系</p>
    <p class="text-xs text-blue-500 mt-1">每页显示 {PAGE_SIZE} 条记录，减少翻页操作</p>
  </div>

  <!-- 调试信息 -->
  <div class="mb-4 p-3 bg-blue-50 text-blue-800 rounded-md">
    <div class="flex items-center justify-between">
      <div>
        <span class="w-2 h-2 rounded-full bg-blue-500 mr-2"></span>
        <span>认证状态: {isAuthenticated ? '已登录' : '未登录'} (Token: {isTokenValid() ? '有效' : '无效'})</span>
      </div>
      <div class="flex gap-2">
        {#if isAuthenticated}
          <Button variant="outline" size="sm" onclick={() => loadData()}>
            强制刷新数据
          </Button>
          <Button variant="outline" size="sm" onclick={logout}>
            注销
          </Button>
        {/if}
      </div>
    </div>
  </div>

  {#if !isAuthenticated}
    <!-- 登录表单 -->
    <div class="max-w-md mx-auto rounded-lg border bg-card text-card-foreground shadow-sm">
      <div class="flex flex-col space-y-1.5 p-6">
        <h3 class="text-2xl font-semibold leading-none tracking-tight">登录</h3>
        <p class="text-sm text-muted-foreground">请登录以访问推荐人管理功能</p>
      </div>
      <div class="p-6 pt-0">
        <div class="space-y-4">
          <div class="space-y-2">
            <Label for="username">用户名</Label>
            <Input id="username" type="text" bind:value={loginUsername} placeholder="请输入用户名" />
          </div>
          <div class="space-y-2">
            <Label for="password">密码</Label>
            <Input id="password" type="password" bind:value={loginPassword} placeholder="请输入密码" />
          </div>
          {#if loginError}
            <div class="text-red-500 text-sm">{loginError}</div>
          {/if}
        </div>
      </div>
      <div class="flex items-center p-6 pt-0 justify-end">
        <Button onclick={login} disabled={isLoggingIn}>
          {#if isLoggingIn}
            <span class="inline-block w-4 h-4 border-2 border-t-transparent border-white rounded-full animate-spin mr-2"></span>
          {/if}
          登录
        </Button>
      </div>
    </div>
  {:else}
    <!-- 已登录状态 - 显示主界面 -->
    <!-- 认证状态: {isAuthenticated} -->
    <div class="w-full">
      <!-- 简单的标签页导航 -->
      <div class="flex border-b mb-4">
        <button
          class={`px-4 py-2 border-b-2 text-center transition-colors ${activeTab === 'referrers' ? 'border-primary text-primary' : 'border-transparent hover:text-primary hover:border-primary/40'}`}
          onclick={() => activeTab = 'referrers'}
        >
          推荐人管理
        </button>
        <button
          class={`px-4 py-2 border-b-2 text-center transition-colors ${activeTab === 'associations' ? 'border-primary text-primary' : 'border-transparent hover:text-primary hover:border-primary/40'}`}
          onclick={() => activeTab = 'associations'}
        >
          关联记录
        </button>
        <button
          class={`px-4 py-2 border-b-2 text-center transition-colors ${activeTab === 'patients' ? 'border-primary text-primary' : 'border-transparent hover:text-primary hover:border-primary/40'}`}
          onclick={() => activeTab = 'patients'}
        >
          无推荐人患者
        </button>
      </div>

      <!-- 推荐人管理标签页 -->
      {#if activeTab === 'referrers'}
        <div class="space-y-4">
        <div class="flex justify-between items-center">
          <div class="flex gap-2">
            <Input
              placeholder="搜索姓名"
              class="w-48"
              bind:value={referrerSearchName}
            />
            <Input
              placeholder="搜索手机号"
              class="w-48"
              bind:value={referrerSearchPhone}
            />
            <Button variant="outline" onclick={() => loadReferrers()}>
              搜索
            </Button>
          </div>
          <Button onclick={openNewReferrerDialog}>
            新建推荐人
          </Button>
        </div>

        <div class="border rounded-md overflow-hidden" style="max-height: 600px; overflow-y: auto;">
          <table class="min-w-full divide-y divide-slate-200">
            <thead class="bg-slate-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">姓名</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">手机号</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">创建时间</th>
                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-slate-500 uppercase tracking-wider">操作</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-slate-200">
              {#if isLoading}
                <tr>
                  <td colspan="4" class="px-6 py-4 text-center">
                    <div class="flex justify-center">
                      <span class="inline-block w-6 h-6 border-4 border-t-transparent border-slate-500 rounded-full animate-spin"></span>
                    </div>
                  </td>
                </tr>
              {:else if referrers.length === 0}
                <tr>
                  <td colspan="4" class="px-6 py-4 text-center text-sm text-slate-500">暂无数据</td>
                </tr>
              {:else}
                {#each referrers as referrer}
                  <tr class="hover:bg-slate-50">
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-slate-900">{referrer.name}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500">{referrer.phone}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500">
                      {new Date(referrer.createdAt).toLocaleString()}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onclick={() => openEditReferrerDialog(referrer)}
                        class="text-blue-600 hover:text-blue-900 mr-3"
                      >
                        编辑
                      </button>
                    </td>
                  </tr>
                {/each}
              {/if}
            </tbody>
          </table>
        </div>

        <!-- 分页控制 -->
        <div class="flex justify-between items-center">
          <Button
            variant="outline"
            disabled={!referrerPrevCursor || isLoading}
            onclick={() => loadReferrers(referrerPrevCursor)}
          >
            上一页
          </Button>

          <div class="text-sm text-slate-500">
            {#if referrers.length > 0}
              显示 {referrers.length} 条记录 (每页最多 {PAGE_SIZE} 条)
            {/if}
          </div>

          <Button
            variant="outline"
            disabled={!referrerNextCursor || isLoading}
            onclick={() => loadReferrers(referrerNextCursor)}
          >
            下一页
          </Button>
        </div>
      </div>
      {/if}

      <!-- 关联记录标签页 -->
      {#if activeTab === 'associations'}
        <div class="space-y-4">
        <div class="flex justify-between items-center">
          <div class="flex gap-2 flex-wrap">
            <Input
              placeholder="患者姓名"
              class="w-32"
              bind:value={associationSearchPatientName}
            />
            <Input
              placeholder="患者手机号"
              class="w-32"
              bind:value={associationSearchPatientPhone}
            />
            <Input
              placeholder="推荐人姓名"
              class="w-32"
              bind:value={associationSearchReferrerName}
            />
            <Input
              placeholder="推荐人手机号"
              class="w-32"
              bind:value={associationSearchReferrerPhone}
            />
            <Button variant="outline" onclick={() => loadAssociations()}>
              搜索
            </Button>
          </div>
        </div>

        <div class="border rounded-md overflow-hidden" style="max-height: 600px; overflow-y: auto;">
          <table class="min-w-full divide-y divide-slate-200">
            <thead class="bg-slate-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">患者姓名</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">患者手机号</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">推荐人姓名</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">推荐人手机号</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">创建时间</th>
                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-slate-500 uppercase tracking-wider">操作</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-slate-200">
              {#if isLoading}
                <tr>
                  <td colspan="6" class="px-6 py-4 text-center">
                    <div class="flex justify-center">
                      <span class="inline-block w-6 h-6 border-4 border-t-transparent border-slate-500 rounded-full animate-spin"></span>
                    </div>
                  </td>
                </tr>
              {:else if associations.length === 0}
                <tr>
                  <td colspan="6" class="px-6 py-4 text-center text-sm text-slate-500">暂无数据</td>
                </tr>
              {:else}
                {#each associations as association}
                  <tr class="hover:bg-slate-50">
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-slate-900">{association.patientName}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500">{association.patientPhone}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-slate-900">{association.referrerName}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500">{association.referrerPhone}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500">
                      {new Date(association.createdAt).toLocaleString()}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onclick={() => openEditAssociationDialog(association)}
                        class="text-blue-600 hover:text-blue-900"
                      >
                        修改推荐人
                      </button>
                    </td>
                  </tr>
                {/each}
              {/if}
            </tbody>
          </table>
        </div>

        <!-- 分页控制 -->
        <div class="flex justify-between items-center">
          <Button
            variant="outline"
            disabled={!associationPrevCursor || isLoading}
            onclick={() => loadAssociations(associationPrevCursor)}
          >
            上一页
          </Button>

          <div class="text-sm text-slate-500">
            {#if associations.length > 0}
              显示 {associations.length} 条记录 (每页最多 {PAGE_SIZE} 条)
            {/if}
          </div>

          <Button
            variant="outline"
            disabled={!associationNextCursor || isLoading}
            onclick={() => loadAssociations(associationNextCursor)}
          >
            下一页
          </Button>
        </div>
      </div>
      {/if}

      <!-- 无推荐人患者标签页 -->
      {#if activeTab === 'patients'}
        <div class="space-y-4">
        <div class="flex justify-between items-center">
          <div class="flex gap-2">
            <Input
              placeholder="搜索姓名"
              class="w-48"
              bind:value={patientSearchName}
            />
            <Input
              placeholder="搜索手机号"
              class="w-48"
              bind:value={patientSearchPhone}
            />
            <Button variant="outline" onclick={() => loadPatientsWithoutReferrer()}>
              搜索
            </Button>
          </div>
        </div>

        <div class="border rounded-md overflow-hidden" style="max-height: 600px; overflow-y: auto;">
          <table class="min-w-full divide-y divide-slate-200">
            <thead class="bg-slate-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">姓名</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">手机号</th>
                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-slate-500 uppercase tracking-wider">操作</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-slate-200">
              {#if isLoading}
                <tr>
                  <td colspan="3" class="px-6 py-4 text-center">
                    <div class="flex justify-center">
                      <span class="inline-block w-6 h-6 border-4 border-t-transparent border-slate-500 rounded-full animate-spin"></span>
                    </div>
                  </td>
                </tr>
              {:else if patientsWithoutReferrer.length === 0}
                <tr>
                  <td colspan="3" class="px-6 py-4 text-center text-sm text-slate-500">暂无数据</td>
                </tr>
              {:else}
                {#each patientsWithoutReferrer as patient}
                  <tr class="hover:bg-slate-50">
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-slate-900">{patient.name}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500">{patient.phone}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onclick={() => {
                          // 打开关联对话框
                          selectedPatientId = patient.id;
                          console.log('选择关联患者ID:', patient.id);
                          isAssociationDialogOpen = true;
                        }}
                        class="text-blue-600 hover:text-blue-900"
                      >
                        关联推荐人
                      </button>
                    </td>
                  </tr>
                {/each}
              {/if}
            </tbody>
          </table>
        </div>

        <!-- 分页控制 -->
        <div class="flex justify-between items-center">
          <Button
            variant="outline"
            disabled={!patientPrevCursor || isLoading}
            onclick={() => loadPatientsWithoutReferrer(patientPrevCursor)}
          >
            上一页
          </Button>

          <div class="text-sm text-slate-500">
            {#if patientsWithoutReferrer.length > 0}
              显示 {patientsWithoutReferrer.length} 条记录 (每页最多 {PAGE_SIZE} 条)
            {/if}
          </div>

          <Button
            variant="outline"
            disabled={!patientNextCursor || isLoading}
            onclick={() => loadPatientsWithoutReferrer(patientNextCursor)}
          >
            下一页
          </Button>
        </div>
      </div>
      {/if}
    </div>
  {/if}

  {#if error}
    <div class="mt-4 p-3 bg-red-50 text-red-800 rounded-md">
      <div class="flex items-center">
        <span class="w-2 h-2 rounded-full bg-red-500 mr-2"></span>
        <span>{error}</span>
      </div>
    </div>
  {/if}

  {#if success}
    <div class="mt-4 p-3 bg-green-50 text-green-800 rounded-md">
      <div class="flex items-center">
        <span class="w-2 h-2 rounded-full bg-green-500 mr-2"></span>
        <span>{success}</span>
      </div>
    </div>
  {/if}
</div>

<!-- 推荐人编辑对话框 -->
{#if isReferrerDialogOpen}
  <div class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-lg w-full max-w-md">
      <div class="p-6">
        <h3 class="text-lg font-medium mb-2">{editingReferrer ? '编辑推荐人' : '新建推荐人'}</h3>
        <p class="text-sm text-gray-500 mb-4">
          {editingReferrer ? '修改推荐人信息' : '添加新的推荐人信息'}
        </p>

        <div class="grid gap-4 py-4">
          <div class="grid grid-cols-4 items-center gap-4">
            <Label for="name" class="text-right">姓名</Label>
            <Input id="name" class="col-span-3" bind:value={newReferrerName} />
          </div>
          <div class="grid grid-cols-4 items-center gap-4">
            <Label for="phone" class="text-right">手机号</Label>
            <Input id="phone" class="col-span-3" bind:value={newReferrerPhone} />
          </div>
        </div>

        <div class="flex justify-end gap-2 mt-4">
          <Button variant="outline" onclick={() => isReferrerDialogOpen = false}>取消</Button>
          <Button onclick={saveReferrer} disabled={isLoading}>
            {#if isLoading}
              <span class="inline-block w-4 h-4 border-2 border-t-transparent border-white rounded-full animate-spin mr-2"></span>
            {/if}
            保存
          </Button>
        </div>
      </div>
    </div>
  </div>
{/if}

<!-- 关联推荐人对话框 -->
{#if isAssociationDialogOpen}
  <div class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-lg w-full max-w-md">
      <div class="p-6">
        <h3 class="text-lg font-medium mb-2">关联推荐人</h3>
        <p class="text-sm text-gray-500 mb-4">
          为患者选择一个推荐人
        </p>

        <div class="grid gap-4 py-4">
          <div class="grid grid-cols-4 items-center gap-4">
            <Label for="referrer" class="text-right">推荐人</Label>
            <div class="col-span-3">
              <select
                id="referrer"
                class="w-full h-10 rounded-md border border-slate-300 bg-transparent px-3 py-2 text-sm"
                bind:value={selectedReferrerId}
              >
                <option value="">请选择推荐人</option>
                {#each referrers as referrer}
                  <option value={referrer.id}>{referrer.name} ({referrer.phone})</option>
                {/each}
              </select>
            </div>
          </div>
        </div>

        <div class="flex justify-end gap-2 mt-4">
          <Button variant="outline" onclick={() => isAssociationDialogOpen = false}>取消</Button>
          <Button
            onclick={async () => {
              if (!selectedReferrerId || !selectedPatientId) {
                error = '请选择推荐人';
                return;
              }

              isLoading = true;
              error = '';

              try {
                // 获取当前选中的患者信息，用于错误消息中显示
                const selectedPatient = patientsWithoutReferrer.find(p => p.id === selectedPatientId);
                const patientName = selectedPatient ? selectedPatient.name : '未知患者';

                console.log('创建关联，参数:', {
                  referrerId: selectedReferrerId,
                  patientId: selectedPatientId,
                  patientName
                });

                await referrerApiService.createAssociation({
                  referrerId: selectedReferrerId,
                  patientId: selectedPatientId
                });

                success = '关联创建成功';
                isAssociationDialogOpen = false;

                // 重新加载数据
                await Promise.all([
                  loadAssociations(),
                  loadPatientsWithoutReferrer()
                ]);
              } catch (err: any) {
                // 获取当前选中的患者信息，用于错误消息中显示
                const selectedPatient = patientsWithoutReferrer.find(p => p.id === selectedPatientId);
                const patientName = selectedPatient ? selectedPatient.name : '未知患者';

                // 如果错误消息中包含"undefined"，替换为实际患者名称
                let errorMsg = err.message || '未知错误';
                if (errorMsg.includes('undefined')) {
                  errorMsg = errorMsg.replace('undefined', patientName);
                }

                error = `创建关联失败: ${errorMsg}`;
              } finally {
                isLoading = false;
              }
            }}
            disabled={isLoading || !selectedReferrerId}
          >
            {#if isLoading}
              <span class="inline-block w-4 h-4 border-2 border-t-transparent border-white rounded-full animate-spin mr-2"></span>
            {/if}
            关联
          </Button>
        </div>
      </div>
    </div>
  </div>
{/if}

<!-- 编辑关联记录对话框 -->
{#if isEditAssociationDialogOpen}
  <div class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-lg w-full max-w-md">
      <div class="p-6">
        <h3 class="text-lg font-medium mb-2">修改关联推荐人</h3>
        <p class="text-sm text-gray-500 mb-4">
          为患者 <span class="font-medium">{editingAssociation?.patientName}</span> 选择新的推荐人
        </p>

        <div class="grid gap-4 py-4">
          <div class="grid grid-cols-4 items-center gap-4">
            <Label for="edit-referrer" class="text-right">推荐人</Label>
            <div class="col-span-3">
              <select
                id="edit-referrer"
                class="w-full h-10 rounded-md border border-slate-300 bg-transparent px-3 py-2 text-sm"
                bind:value={selectedReferrerId}
              >
                <option value="">请选择推荐人</option>
                {#each referrers as referrer}
                  <option value={referrer.id}>{referrer.name} ({referrer.phone})</option>
                {/each}
              </select>
            </div>
          </div>

          <div class="grid grid-cols-4 items-center gap-4">
            <Label class="text-right">当前推荐人</Label>
            <div class="col-span-3 text-sm text-slate-500">
              {editingAssociation?.referrerName} ({editingAssociation?.referrerPhone})
            </div>
          </div>
        </div>

        <div class="flex justify-end gap-2 mt-4">
          <Button variant="outline" onclick={() => isEditAssociationDialogOpen = false}>取消</Button>
          <Button
            onclick={updateAssociation}
            disabled={isLoading || !selectedReferrerId}
          >
            {#if isLoading}
              <span class="inline-block w-4 h-4 border-2 border-t-transparent border-white rounded-full animate-spin mr-2"></span>
            {/if}
            保存修改
          </Button>
        </div>
      </div>
    </div>
  </div>
{/if}

<!-- 编辑关联记录对话框 -->
{#if isEditAssociationDialogOpen}
  <div class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-lg w-full max-w-md">
      <div class="p-6">
        <h3 class="text-lg font-medium mb-2">修改关联推荐人</h3>
        <p class="text-sm text-gray-500 mb-4">
          为患者 <span class="font-medium">{editingAssociation?.patientName}</span> 选择新的推荐人
        </p>

        <div class="grid gap-4 py-4">
          <div class="grid grid-cols-4 items-center gap-4">
            <Label for="edit-referrer" class="text-right">推荐人</Label>
            <div class="col-span-3">
              <select
                id="edit-referrer"
                class="w-full h-10 rounded-md border border-slate-300 bg-transparent px-3 py-2 text-sm"
                bind:value={selectedReferrerId}
              >
                <option value="">请选择推荐人</option>
                {#each referrers as referrer}
                  <option value={referrer.id}>{referrer.name} ({referrer.phone})</option>
                {/each}
              </select>
            </div>
          </div>

          <div class="grid grid-cols-4 items-center gap-4">
            <Label class="text-right">当前推荐人</Label>
            <div class="col-span-3 text-sm text-slate-500">
              {editingAssociation?.referrerName} ({editingAssociation?.referrerPhone})
            </div>
          </div>
        </div>

        <div class="flex justify-end gap-2 mt-4">
          <Button variant="outline" onclick={() => isEditAssociationDialogOpen = false}>取消</Button>
          <Button
            onclick={updateAssociation}
            disabled={isLoading || !selectedReferrerId}
          >
            {#if isLoading}
              <span class="inline-block w-4 h-4 border-2 border-t-transparent border-white rounded-full animate-spin mr-2"></span>
            {/if}
            保存修改
          </Button>
        </div>
      </div>
    </div>
  </div>
{/if}