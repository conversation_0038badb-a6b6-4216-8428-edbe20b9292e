<script lang="ts">
  import { Button } from "$lib/components/ui/button";
  // 在模拟代码中暂时不需要 invoke，但实际实现时会需要
  // import { invoke } from "@tauri-apps/api/core";
  import SettingsDialog from "$lib/components/SettingsDialog.svelte";
  import settings, { loadSettings, saveSettings } from "$lib/stores/settings";
  import { onMount } from "svelte";
  // 移除 getNotionDatabaseItems 的导入，因为它在 api.ts 中被注释或删除
  // import { getNotionDatabaseItems } from "$lib/utils/api";
  import {
    fetchDatabaseItems,
    getDatabaseItemsFromLocalStorage,
    notionItems,
    notionLoading,
    notionError,
    syncNoteToNotion,
    batchSyncToNotion as batchSyncToNotionApi
  } from "$lib/utils/notion";
  import { noteClassifier, type ClassificationResult } from "$lib/utils/noteClassifier";

  // 定义类型
  // 使用导入的 ClassificationResult 类型，而不是自定义

  // 状态管理
  let noteContent = $state("");
  let selectedModel = $state(""); // 初始化为空字符串，稍后会从设置中加载
  let isProcessing = $state(false);
  let classificationResults = $state<ClassificationResult[]>([]); // 确保初始化为空数组
  let errorMessage = $state("");
  let isBatchMode = $state(false);
  let batchSeparator = $state("\n\n");
  let settingsOpen = $state(false);
  let batchResults = $state<Array<{content: string; classifications: ClassificationResult[]; status: string; reason: string}>>([]);
  let selectedCategories = $state<string[]>([]);
  let syncingToNotion = $state(false);
  let syncResults = $state<Record<string, {success: boolean; error?: string}>>({});

  // 监听设置变化，加载默认模型
  $effect(() => {
    if ($settings.models && $settings.models.length > 0 && !selectedModel) {
      // 如果模型列表已加载且当前未选择模型，使用第一个模型
      selectedModel = $settings.models[0].id;
      console.log('从设置中加载默认模型:', selectedModel);
    }
  });

  // 监听模型选择变化
  $effect(() => {
    if (selectedModel) {
      // 当用户选择了新模型，更新 noteClassifier 的模型
      noteClassifier.setModelId(selectedModel);
      console.log('已切换到模型:', selectedModel);
    }
  });
  // let databaseItems = $state<NotionDatabaseItem[]>([]); // 不再需要本地 state，直接使用 $notionItems
  // let isLoadingDatabase = $state(false); // 使用 $notionLoading
  // let databaseError = $state(""); // 使用 $notionError

  // 页面加载时初始化设置并尝试加载数据
  onMount(async () => {
    if (!$settings.initialized) {
      await loadSettings(); // 等待设置加载完成
    }
    // 尝试从本地存储加载初始数据
    const localItems = getDatabaseItemsFromLocalStorage();
    if (localItems.length > 0) {
        $notionItems = localItems; // 使用 $ 语法直接写入 store
    }
    // 触发一次数据获取（如果需要立即获取最新数据）
    // 或者让用户手动点击刷新按钮
    // await loadDatabaseItems(); // 可以在 onMount 中自动加载
  });

  // 从设置中监听选定模型
  $effect(() => {
    if ($settings.initialized && $settings.models && $settings.models.length > 0) {
      selectedModel = $settings.models[0].id; // 确保 $settings.models 存在
    }
  });

  // 调用 Notion 工具函数加载数据
  async function loadDatabaseItems(forceRefresh = false) {
    console.log('开始加载数据库条目, forceRefresh:', forceRefresh);

    // 检查设置是否已初始化
    if (!$settings.initialized) {
      console.log('设置尚未初始化，尝试加载设置...');
      await loadSettings();
    }

    // 从设置中获取Notion API密钥和数据库ID (用于检查，实际调用由 fetchDatabaseItems 处理)
    const notionApiKey = $settings.notionApiKey;
    const notionDatabaseId = $settings.notionDatabaseId;

    console.log('当前设置状态 (检查):', {
      initialized: $settings.initialized,
      hasApiKey: !!notionApiKey,
      hasDatabaseId: !!notionDatabaseId
    });

    if (!notionApiKey || !notionDatabaseId) {
      console.error('缺少Notion API密钥或数据库ID (检查)');
      $notionError = "请先在设置中配置Notion API密钥和数据库ID"; // 更新 store
      settingsOpen = true; // 自动打开设置对话框
      return;
    }

    try {
        // 如果不是强制刷新，并且 store 中已有数据，则不重新获取
        if (!forceRefresh && $notionItems.length > 0) {
            console.log('已有数据，跳过刷新');
            return;
        }

        console.log('调用 fetchDatabaseItems 从 Notion 获取数据...');
        // 直接调用 notion.ts 中的 fetchDatabaseItems 函数
        // 它内部会处理加载状态、错误并更新 notionItems store
        await fetchDatabaseItems();
        console.log('fetchDatabaseItems 调用完成');
        // 数据会自动更新到 $notionItems store

    } catch (err: unknown) {
      // fetchDatabaseItems 内部已经处理了错误并更新了 notionError store
      // 这里可以记录额外的错误信息或处理 UI 逻辑
      console.error("loadDatabaseItems 捕获到错误:", err);
      // $notionError 应该已经被 fetchDatabaseItems 设置了
    } finally {
      // 加载状态由 $notionLoading store 管理
      console.log('加载数据库条目流程结束');
    }
  }

  // 分类笔记
  async function classifyNote() {
    if (!noteContent.trim()) {
      errorMessage = "请输入笔记内容";
      return;
    }

    // 检查API密钥是否已设置
    if (!$settings.openrouterApiKey) {
      errorMessage = "请先在设置中配置 OpenRouter API 密钥";
      settingsOpen = true;
      return;
    }

    if (!$settings.notionApiKey) {
      errorMessage = "请先在设置中配置 Notion API 密钥";
      settingsOpen = true;
      return;
    }

    if (!$settings.notionDatabaseId) {
      errorMessage = "请先在设置中配置 Notion 数据库 ID";
      settingsOpen = true;
      return;
    }

    errorMessage = "";
    isProcessing = true;

    // 清除之前的分类选择和同步结果
    selectedCategories = [];
    syncResults = {};

    // 重置分类结果为空数组
    classificationResults = [];
    console.log('重置分类结果为空数组');

    try {
      console.log('开始分类笔记，使用模型:', selectedModel);

      if (isBatchMode) {
        // 批量处理模式
        const notes = noteClassifier.parseBatchNotes(noteContent, batchSeparator);

        if (notes.length === 0) {
          errorMessage = "未找到有效的笔记内容";
          isProcessing = false;
          return;
        }

        console.log(`解析出 ${notes.length} 条笔记，开始批量分类`);

        // 批量分类
        batchResults = await noteClassifier.batchClassifyNotes(notes, selectedModel);

        // 将所有分类结果合并
        const flattenedResults = batchResults.flatMap(r => r.classifications);
        console.log('批量分类完成，合并前结果:', batchResults);
        console.log('批量分类完成，合并后结果:', flattenedResults);

        // 确保结果是普通对象数组
        classificationResults = JSON.parse(JSON.stringify(flattenedResults));
        console.log('批量分类完成，最终结果 (普通对象):', classificationResults);
      } else {
        // 单条笔记处理模式
        console.log('单条笔记处理模式，开始分类');
        const results = await noteClassifier.classifyNote(noteContent, selectedModel);
        console.log('分类完成，原始结果:', results);

        // 确保结果是普通对象数组
        classificationResults = JSON.parse(JSON.stringify(results));
        console.log('分类完成，最终结果 (普通对象):', classificationResults);
      }

      // 检查结果是否为空数组
      if (classificationResults.length === 0) {
        console.warn('分类结果为空数组');
        errorMessage = "分类结果为空，可能是因为笔记内容无法匹配任何现有分类。请尝试修改笔记内容或添加更多分类项。";
      }
    } catch (error: any) {
      console.error("分类笔记时出错:", error);

      // 格式化错误消息，使其更友好
      let formattedError = error?.message || "未知错误";

      // 检查是否是 JSON 解析错误
      if (formattedError.includes('JSON Parse error') || formattedError.includes('SyntaxError')) {
        formattedError = `JSON 解析错误: AI 模型返回的数据格式不正确。这通常是因为模型没有严格按照要求返回 JSON 格式。

原始错误: ${formattedError}

请尝试以下解决方案:
1. 重新尝试分类（有时模型会在第二次尝试时返回正确格式）
2. 选择不同的 AI 模型
3. 点击"重新加载设置"按钮刷新配置`;
      }

      // 如果错误消息已经是格式化过的（包含换行符），直接使用
      if (formattedError.includes('\n')) {
        errorMessage = formattedError;
      } else {
        errorMessage = `分类失败: ${formattedError}`;
      }
    } finally {
      isProcessing = false;
    }
  }

  // 切换分类选择状态
  function toggleCategorySelection(category: string) {
    if (selectedCategories.includes(category)) {
      // 如果已选中，则取消选择
      selectedCategories = selectedCategories.filter(c => c !== category);
    } else {
      // 如果未选中，则添加到选中列表
      selectedCategories = [...selectedCategories, category];
    }
  }

  // 同步选中的分类到 Notion
  async function syncSelectedToNotion() {
    if (selectedCategories.length === 0) {
      errorMessage = "请先选择要同步的分类";
      return;
    }

    syncingToNotion = true;
    errorMessage = "";

    try {
      // 逐个同步选中的分类
      for (const category of selectedCategories) {
        try {
          const result = await syncNoteToNotion(category, noteContent);

          // 记录同步结果
          syncResults = {
            ...syncResults,
            [category]: {
              success: result.success,
              error: result.error
            }
          };

          // 如果有错误，显示在错误消息区
          if (!result.success) {
            errorMessage = `同步分类 "${category}" 失败: ${result.error || "未知错误"}`;
          }

          // 添加延迟，避免 API 限流
          await new Promise(resolve => setTimeout(resolve, 500));

        } catch (error: any) {
          console.error(`同步分类 "${category}" 失败:`, error);

          // 记录错误
          syncResults = {
            ...syncResults,
            [category]: {
              success: false,
              error: error?.message || "未知错误"
            }
          };

          errorMessage = `同步分类 "${category}" 失败: ${error?.message || "未知错误"}`;
        }
      }

      // 同步完成后重新加载数据库条目
      await loadDatabaseItems(true);

      // 检查是否所有同步都成功
      const allSuccess = selectedCategories.every(category =>
        syncResults[category] && syncResults[category].success
      );

      if (allSuccess) {
        // 如果所有同步都成功，清除错误消息
        errorMessage = "";
      }

    } catch (error: any) {
      console.error("同步到 Notion 失败:", error);
      errorMessage = `同步失败: ${error?.message || "未知错误"}`;
    } finally {
      syncingToNotion = false;
    }
  }

  // 批量同步到Notion
  async function batchSyncToNotion() {
    if (!batchResults || batchResults.length === 0) {
      errorMessage = "没有可同步的批量结果";
      return;
    }

    try {
      // 准备批量同步数据
      const notesWithCategories = batchResults.map(result => ({
        note: result.content,
        categories: result.classifications
      }));

      // 批量同步
      const results = await batchSyncToNotionApi(notesWithCategories);

      // 检查结果
      const successCount = results.filter((r: any) => r.success).length;
      if (successCount > 0) {
        console.log(`批量同步成功: ${successCount}/${results.length} 条笔记`);
        // 同步成功后重新加载数据库条目
        await loadDatabaseItems(true);
      } else {
        errorMessage = "所有同步操作均失败";
      }
    } catch (error: any) {
      console.error("批量同步到Notion失败:", error);
      errorMessage = `批量同步失败: ${error?.message || "未知错误"}`;
    }
  }

  // 处理标签展示 (如果需要展示标签时可以使用这个函数)
  // 目前没有使用到这个函数，但保留以便将来可能的扩展
  // function formatTags(tags: string[] | undefined) {
  //   if (!tags) return '';
  //   return tags.join(', ');
  // }

  // 格式化日期时间
  function formatDateTime(dateTimeStr: string | null) {
    if (!dateTimeStr) return '无日期';
    try {
      const date = new Date(dateTimeStr);
      // 检查日期是否有效
      if (isNaN(date.getTime())) {
           return '无效日期';
      }
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (e) {
      console.error("格式化日期错误:", e);
      return dateTimeStr || '日期解析错误';
    }
  }

  // 打开Notion页面
  function openNotionPage(id: string) {
    if (id) {
      // 使用Notion页面ID构建URL
      const url = `https://notion.so/${id.replace(/-/g, '')}`;
      // 考虑使用 Tauri API 打开链接，如果需要更好的桌面集成
      // import { open } from '@tauri-apps/api/shell';
      // open(url);
      window.open(url, '_blank');
    }
  }
</script>

<div class="min-h-screen bg-gradient-to-b from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 py-8">
  <div class="container mx-auto px-6 max-w-6xl">
    <!-- 页面标题区域 -->
    <div class="text-center mb-10">
      <div class="flex justify-between items-center mb-4">
        <div class="flex-1"></div>
        <h1 class="text-3xl md:text-4xl font-bold text-slate-800 dark:text-white flex-grow text-center flex items-center justify-center gap-3">
          <span class="text-3xl">✨</span>
          灵感笔记分类助手
        </h1>
        <div class="flex-1"></div>
      </div>
      <p class="text-slate-600 dark:text-slate-400 max-w-2xl mx-auto text-lg">输入你的笔记内容，AI 将帮你自动分类并同步到 Notion</p>
    </div>

    <!-- 主要内容区域 -->
    <div class="bg-white dark:bg-slate-800 rounded-xl shadow-lg p-8 mb-10 border border-slate-100 dark:border-slate-700 hover-card">
      <!-- 批量模式切换 -->
      <div class="flex flex-wrap items-center justify-between mb-6 bg-slate-50 dark:bg-slate-700/30 p-4 rounded-lg">
        <div class="flex items-center">
          <div class="mr-3 text-slate-500 dark:text-slate-400">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-layers"><path d="m12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83Z"/><path d="m22 12-8.6 3.91a2 2 0 0 1-1.66 0L3.1 12"/><path d="m22 17-8.6 3.91a2 2 0 0 1-1.66 0L3.1 17"/></svg>
          </div>
          <label class="flex items-center cursor-pointer">
            <input
              type="checkbox"
              bind:checked={isBatchMode}
              class="sr-only peer"
            />
            <div class="relative w-11 h-6 bg-slate-200 dark:bg-slate-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            <span class="ms-3 text-sm font-medium dark:text-white">批量处理模式</span>
          </label>
        </div>

        {#if isBatchMode}
          <div class="flex items-center mt-2 sm:mt-0">
            <span class="text-sm mr-2 text-slate-600 dark:text-slate-300">分隔符:</span>
            <input
              type="text"
              bind:value={batchSeparator}
              class="w-20 p-2 text-sm border border-slate-300 dark:border-slate-600 dark:bg-slate-700 dark:text-white rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        {/if}
      </div>

      <!-- 笔记输入区 -->
      <div class="mb-6">
        <div class="flex justify-between items-center mb-2">
          <label for="note-input" class="block text-sm font-medium text-slate-700 dark:text-slate-300">
            {isBatchMode ? '笔记内容 (使用分隔符分割多条笔记)' : '笔记内容'}
          </label>
          <span class="text-xs text-slate-500 dark:text-slate-400">{noteContent.length} 字符</span>
        </div>
        <div class="relative">
          <textarea
            id="note-input"
            bind:value={noteContent}
            class="w-full p-4 border border-slate-300 dark:border-slate-600 dark:bg-slate-700 dark:text-white rounded-lg h-40 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-y min-h-[10rem] font-medium"
            placeholder={isBatchMode ?
              `在此输入多条笔记，用 ${batchSeparator} 分隔...

例如:
今天学习了新的编程技巧，非常有用。
${batchSeparator}
需要记得买新的笔记本和钥匙扣。`
              :
              `在此输入你的笔记内容...

例如: 今天学习了如何使用 Tauri 构建跨平台应用，这是一个非常有趣的技术。`
            }
          ></textarea>
          {#if noteContent.length > 0}
            <button
              class="absolute top-3 right-3 text-slate-400 hover:text-slate-600 dark:hover:text-slate-200 p-1 rounded-full hover:bg-slate-100 dark:hover:bg-slate-600 transition-colors"
              onclick={() => noteContent = ""}
              aria-label="清空内容"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
            </button>
          {/if}
        </div>
        {#if isBatchMode}
          <p class="mt-2 text-xs text-slate-500 dark:text-slate-400">批量模式下，每条笔记将被分开处理并分类。</p>
        {/if}
      </div>

      <!-- 模型选择器 -->
      <div class="mb-8">
        <label for="model-selector" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">选择 AI 模型</label>
        <div class="relative">
          <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-slate-500 dark:text-slate-400">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-brain-circuit"><path d="M12 4.5a2.5 2.5 0 0 0-4.96-.46 2.5 2.5 0 0 0-1.98 3 2.5 2.5 0 0 0-1.32 4.24 3 3 0 0 0 .34 5.58 2.5 2.5 0 0 0 2.96 3.08 2.5 2.5 0 0 0 4.91.05L12 20V4.5Z"/><path d="M16 8V5c0-1.1.9-2 2-2"/><path d="M12 13h4"/><path d="M12 18h6a2 2 0 0 1 2 2v1"/><path d="M12 8h8"/><path d="M20.5 8a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0Z"/><path d="M16.5 13a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0Z"/><path d="M20.5 21a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0Z"/><path d="M18.5 3a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0Z"/></svg>
          </div>
          <select
            id="model-selector"
            bind:value={selectedModel}
            class="w-full p-3 pl-10 border border-slate-300 dark:border-slate-600 dark:bg-slate-700 dark:text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none bg-no-repeat bg-[right_0.75rem_center] bg-[length:1em_1em] bg-[url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20fill%3D%22none%22%20viewBox%3D%220%200%2020%2020%22%3E%3Cpath%20stroke%3D%22%236b7280%22%20stroke-linecap%3D%22round%22%20stroke-linejoin%3D%22round%22%20stroke-width%3D%221.5%22%20d%3D%22m6%208%204%204%204-4%22%2F%3E%3C%2Fsvg%3E')]"
          >
            {#each $settings.models as model}
              <option value={model.id}>{model.name}</option>
            {/each}
          </select>
        </div>
        <p class="mt-2 text-xs text-slate-500 dark:text-slate-400">不同的模型可能有不同的分类效果和响应速度</p>
      </div>

      <!-- 操作按钮 -->
      <div class="flex flex-wrap gap-4">
        <Button
          onclick={classifyNote}
          disabled={isProcessing || !noteContent.trim()}
          class="gap-2 px-6 py-2 h-auto text-base font-medium bg-blue-600 hover:bg-blue-700 transition-colors"
        >
          {#if isProcessing}
            <span class="inline-block w-5 h-5 border-2 border-t-transparent border-white rounded-full animate-spin"></span>
          {/if}
          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-sparkles"><path d="m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z"/><path d="M5 3v4"/><path d="M3 5h4"/><path d="M19 17v4"/><path d="M17 19h4"/></svg>
          {isBatchMode ? '批量分类笔记' : '分类笔记'}
        </Button>

        {#if isBatchMode && classificationResults.length > 0}
          <Button
            variant="outline"
            onclick={batchSyncToNotion}
            class="gap-2 px-6 py-2 h-auto text-base font-medium border-blue-600 text-blue-600 hover:bg-blue-50 dark:border-blue-400 dark:text-blue-400 dark:hover:bg-blue-900/20 transition-colors"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-upload-cloud"><path d="M4 14.899A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.242"/><path d="M12 12v9"/><path d="m16 16-4-4-4 4"/></svg>
            批量同步到 Notion
          </Button>
        {/if}
      </div>

      <!-- 错误提示 -->
      {#if errorMessage}
        <div class="mt-6 p-4 bg-red-50 dark:bg-red-900/30 text-red-600 dark:text-red-300 rounded-lg border border-red-100 dark:border-red-800 flex items-start gap-3 animate-fadeIn">
          <div class="text-red-500 dark:text-red-400 mt-0.5">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-alert-circle"><circle cx="12" cy="12" r="10"/><line x1="12" x2="12" y1="8" y2="12"/><line x1="12" x2="12.01" y1="16" y2="16"/></svg>
          </div>
          <div class="flex-1">
            <h4 class="font-medium text-red-700 dark:text-red-300 mb-1">操作出错</h4>
            <p class="whitespace-pre-line">{errorMessage}</p>

            {#if errorMessage.includes('连接错误') || errorMessage.includes('API') || errorMessage.includes('密钥')}
              <div class="mt-3 flex flex-wrap gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onclick={() => settingsOpen = true}
                  class="h-8 px-3 text-xs border-red-600 text-red-600 hover:bg-red-50 dark:border-red-400 dark:text-red-400 dark:hover:bg-red-900/20"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-settings mr-1"><path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"/><circle cx="12" cy="12" r="3"/></svg>
                  打开设置
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  onclick={async () => {
                    // 检查并修复 OpenRouter URL
                    if ($settings.openrouterSiteUrl) {
                      let fixedUrl = $settings.openrouterSiteUrl;
                      let urlChanged = false;

                      // 彻底清理 URL，移除所有可能的 /chat/completions 路径
                      while (fixedUrl.includes('/chat/completions')) {
                        const oldUrl = fixedUrl;
                        fixedUrl = fixedUrl.replace('/chat/completions', '');
                        console.log('修复 OpenRouter URL，从', oldUrl, '修改为', fixedUrl);
                        urlChanged = true;
                      }

                      if (urlChanged) {
                        // 保存修复后的 URL
                        console.log('保存修复后的 URL:', fixedUrl);
                        await saveSettings({ openrouterSiteUrl: fixedUrl });
                        errorMessage = `已修复 OpenRouter URL 并重新加载设置，请再次尝试分类。\n原URL: ${$settings.openrouterSiteUrl}\n新URL: ${fixedUrl}`;
                      } else {
                        // URL 没有问题，只需重新加载设置
                        await loadSettings();
                        errorMessage = "已重新加载设置，请再次尝试分类";
                      }
                    } else {
                      // 没有设置 URL，只需重新加载设置
                      await loadSettings();
                      errorMessage = "已重新加载设置，请再次尝试分类";
                    }
                  }}
                  class="h-8 px-3 text-xs border-green-600 text-green-600 hover:bg-green-50 dark:border-green-400 dark:text-green-400 dark:hover:bg-green-900/20"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-refresh-cw mr-1"><path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/><path d="M21 3v5h-5"/><path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/><path d="M3 21v-5h5"/></svg>
                  重新加载设置
                </Button>

                <a
                  href="https://openrouter.ai/keys"
                  target="_blank"
                  rel="noopener noreferrer"
                  class="inline-flex items-center h-8 px-3 text-xs rounded-md border border-blue-600 text-blue-600 hover:bg-blue-50 dark:border-blue-400 dark:text-blue-400 dark:hover:bg-blue-900/20"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link mr-1"><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"/><polyline points="15 3 21 3 21 9"/><line x1="10" x2="21" y1="14" y2="3"/></svg>
                  获取 OpenRouter API 密钥
                </a>
              </div>
            {/if}
          </div>
        </div>
      {/if}
    </div>
  </div>

  <!-- 分类结果展示 -->
  {#if classificationResults && classificationResults.length > 0}
    <div class="container mx-auto px-6 max-w-6xl">
      <div class="bg-white dark:bg-slate-800 rounded-xl shadow-lg p-8 mb-10 border border-slate-100 dark:border-slate-700 animate-fadeIn">
        <div class="flex items-center gap-2 mb-6">
          <div class="text-blue-600 dark:text-blue-400">
            <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-list-filter"><path d="M3 6h18"/><path d="M7 12h10"/><path d="M10 18h4"/></svg>
          </div>
          <h2 class="text-2xl font-bold text-slate-800 dark:text-white">分类结果</h2>
          <span class="text-xs text-slate-500 dark:text-slate-400 ml-2">({classificationResults.length} 个结果)</span>
        </div>

        <!-- 调试信息区域 (仅在开发模式下显示) -->
        {#if import.meta.env.DEV}
          <div class="mb-4 p-3 bg-gray-100 dark:bg-gray-800 rounded-md text-xs font-mono overflow-auto max-h-40">
            <div class="mb-1 font-semibold">调试信息:</div>
            <div>结果类型: {typeof classificationResults}</div>
            <div>是否数组: {Array.isArray(classificationResults) ? '是' : '否'}</div>
            <div>结果长度: {classificationResults.length}</div>
            <div>结果内容: {JSON.stringify(classificationResults, null, 2)}</div>
            <div class="mt-2 flex flex-wrap gap-2">
              <button
                class="px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200"
                onclick={() => {
                  noteContent = "我需要设计一个患者招募策略，提高临床试验的入组效率。";
                  classifyNote();
                }}
              >
                测试1: 患者招募
              </button>
              <button
                class="px-2 py-1 text-xs bg-green-100 text-green-700 rounded hover:bg-green-200"
                onclick={() => {
                  noteContent = "需要准备下周的研究进度报告，包括入组情况和数据收集进度。";
                  classifyNote();
                }}
              >
                测试2: 进度报告
              </button>
              <button
                class="px-2 py-1 text-xs bg-purple-100 text-purple-700 rounded hover:bg-purple-200"
                onclick={() => {
                  noteContent = "伦理审查委员会要求我们修改知情同意书，需要重新提交申请。";
                  classifyNote();
                }}
              >
                测试3: 伦理审查
              </button>
              <button
                class="px-2 py-1 text-xs bg-yellow-100 text-yellow-700 rounded hover:bg-yellow-200"
                onclick={() => {
                  noteContent = "今天天气真好，我想去公园散步。";
                  classifyNote();
                }}
              >
                测试4: 无关内容
              </button>
              <button
                class="px-2 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200"
                onclick={() => {
                  noteContent = "我们需要更新数据管理计划，确保数据质量和完整性。";
                  classifyNote();
                }}
              >
                测试5: 数据管理
              </button>
            </div>
          </div>
        {/if}

      <!-- 添加同步状态和选择状态 -->
      <div class="flex flex-wrap justify-between items-center mb-6 bg-slate-50 dark:bg-slate-700/30 p-4 rounded-lg">
        <div class="flex items-center gap-3">
          <div class="text-blue-600 dark:text-blue-400">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check-square"><polyline points="9 11 12 14 22 4"/><path d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11"/></svg>
          </div>
          <div>
            <span class="text-sm font-medium text-slate-700 dark:text-slate-300">选择要同步的分类</span>
            <div class="text-xs text-slate-500 dark:text-slate-400">已选择 {selectedCategories.length} 个分类</div>
          </div>
          {#if syncingToNotion}
            <span class="inline-flex items-center gap-1 text-sm text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 px-3 py-1 rounded-full">
              <span class="inline-block w-3 h-3 border-2 border-t-transparent border-blue-600 dark:border-blue-400 rounded-full animate-spin"></span>
              正在同步...
            </span>
          {/if}
        </div>
        <Button
          variant="outline"
          onclick={syncSelectedToNotion}
          disabled={selectedCategories.length === 0 || syncingToNotion}
          class="gap-2 border-blue-600 text-blue-600 hover:bg-blue-50 dark:border-blue-400 dark:text-blue-400 dark:hover:bg-blue-900/20 h-10 px-4 py-2 mt-2 sm:mt-0"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-up-to-line"><path d="M5 3h14"/><path d="m18 13-6-6-6 6"/><path d="M12 7v14"/></svg>
          同步到 Notion
        </Button>
      </div>

      <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3 mt-6">
        {#each classificationResults as result, i}
          <button
            type="button"
            class="border border-slate-200 dark:border-slate-700 rounded-lg p-4 bg-white dark:bg-slate-800 shadow-sm hover:shadow-md transition-all duration-200 hover-card cursor-pointer text-left w-full {selectedCategories.includes(result.task) ? 'ring-2 ring-blue-500 dark:ring-blue-400' : 'hover:border-blue-300 dark:hover:border-blue-600'}"
            style="animation-delay: {i * 50}ms"
            onclick={() => toggleCategorySelection(result.task)}
            aria-pressed={selectedCategories.includes(result.task)}
          >
            <div class="flex justify-between items-start gap-3">
              <div class="flex-1">
                <div class="flex items-center gap-2 mb-2">
                  <div class="flex-shrink-0 w-5 h-5 rounded border border-slate-300 dark:border-slate-600 flex items-center justify-center {selectedCategories.includes(result.task) ? 'bg-blue-500 border-blue-500 dark:bg-blue-600 dark:border-blue-600' : ''}">
                    {#if selectedCategories.includes(result.task)}
                      <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check"><path d="M20 6 9 17l-5-5"/></svg>
                    {/if}
                  </div>
                  <h3 class="font-medium text-base text-slate-800 dark:text-white">{result.task}</h3>
                </div>

                <div class="text-sm text-slate-600 dark:text-slate-300 line-clamp-2 hover:line-clamp-none mb-2 ml-7 group-hover:bg-slate-50 dark:group-hover:bg-slate-700/50 group-hover:p-2 group-hover:rounded-md group-hover:shadow-sm transition-all duration-200">{result.reason}</div>
              </div>

              <span class="text-xs px-2 py-1 rounded-full font-medium flex-shrink-0 {(result.confidence || 0) > 0.7 ? 'bg-green-100 text-green-800 dark:bg-green-900/40 dark:text-green-300' : 'bg-amber-100 text-amber-800 dark:bg-amber-900/40 dark:text-amber-300'}">
                {Math.round((result.confidence || 0) * 100)}%
              </span>
            </div>

            {#if syncResults[result.task]}
              <div class="mt-3 ml-7 text-sm flex items-center gap-2 p-2 rounded-md {syncResults[result.task].success ? 'bg-green-50 text-green-600 dark:bg-green-900/20 dark:text-green-400' : 'bg-red-50 text-red-600 dark:bg-red-900/20 dark:text-red-400'}">
                {#if syncResults[result.task].success}
                  <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check-circle"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"/><path d="m9 11 3 3L22 4"/></svg>
                  同步成功
                {:else}
                  <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-alert-circle"><circle cx="12" cy="12" r="10"/><line x1="12" x2="12" y1="8" y2="12"/><line x1="12" x2="12.01" y1="16" y2="16"/></svg>
                  同步失败: {syncResults[result.task].error}
                {/if}
              </div>
            {/if}
          </button>
        {/each}
      </div>
      </div>
    </div>
  {/if}

  <!-- Notion数据库条目展示 -->
  <div class="container mx-auto px-6 max-w-6xl mb-10">
    <div class="bg-white dark:bg-slate-800 rounded-xl shadow-lg p-8 border border-slate-100 dark:border-slate-700">
      <div class="flex justify-between items-center mb-6">
        <div class="flex items-center gap-2">
          <div class="text-indigo-600 dark:text-indigo-400">
            <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-database"><ellipse cx="12" cy="5" rx="9" ry="3"/><path d="M3 5v14c0 1.66 4 3 9 3s9-1.34 9-3V5"/><path d="M3 12c0 1.66 4 3 9 3s9-1.34 9-3"/></svg>
          </div>
          <h2 class="text-2xl font-bold text-slate-800 dark:text-white">Notion 数据库条目</h2>
          <span class="text-sm text-slate-500 dark:text-slate-400 ml-1">{$notionItems.length > 0 ? `(${$notionItems.length})` : ''}</span>
        </div>
        <Button
          variant="outline"
          size="sm"
          onclick={() => loadDatabaseItems(true)}
          disabled={$notionLoading}
          class="h-9 px-3 text-sm gap-1 border-indigo-600 text-indigo-600 hover:bg-indigo-50 dark:border-indigo-400 dark:text-indigo-400 dark:hover:bg-indigo-900/20"
        >
          {#if $notionLoading}
            <span class="inline-block w-3 h-3 border-2 border-t-transparent border-indigo-600 dark:border-indigo-400 rounded-full animate-spin"></span>
          {/if}
          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-refresh-cw"><path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/><path d="M21 3v5h-5"/><path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/><path d="M3 21v-5h5"/></svg>
          刷新
        </Button>
      </div>

    {#if $notionError}
      <div class="p-6 bg-white dark:bg-slate-800 text-red-600 dark:text-red-300 rounded-lg border border-red-100 dark:border-red-800 mb-6 animate-fadeIn shadow-sm">
        <div class="flex items-start gap-4">
          <div class="text-red-500 dark:text-red-400 mt-0.5 flex-shrink-0 bg-red-50 dark:bg-red-900/20 p-3 rounded-full">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-alert-triangle"><path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"/><path d="M12 9v4"/><path d="M12 17h.01"/></svg>
          </div>
          <div class="flex-1 min-w-0">
            <h3 class="text-lg font-medium text-red-700 dark:text-red-300 mb-1">连接错误</h3>
            <p class="text-base mb-4">{$notionError}</p>
            <div class="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                class="h-9 px-4 text-sm border-red-600 text-red-600 hover:bg-red-50 dark:border-red-400 dark:text-red-400 dark:hover:bg-red-900/20"
                onclick={() => settingsOpen = true}
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-settings mr-1"><path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"/><circle cx="12" cy="12" r="3"/></svg>
                打开设置
              </Button>
              <Button
                variant="outline"
                size="sm"
                onclick={() => loadDatabaseItems(true)}
                class="h-9 px-4 text-sm"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-refresh-cw mr-1"><path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/><path d="M21 3v5h-5"/><path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/><path d="M3 21v-5h5"/></svg>
                重试
              </Button>
            </div>
          </div>
        </div>
      </div>
    {/if}

    {#if $notionLoading && $notionItems.length === 0}
      <div class="flex justify-center items-center py-12 animate-pulse">
        <div class="flex flex-col items-center gap-4 bg-white dark:bg-slate-800 p-6 rounded-lg shadow-sm border border-slate-100 dark:border-slate-700">
          <div class="inline-block w-10 h-10 border-3 border-t-transparent border-indigo-600 dark:border-indigo-400 rounded-full animate-spin"></div>
          <p class="text-slate-600 dark:text-slate-400 text-base font-medium">正在从 Notion 加载数据...</p>
          <p class="text-slate-500 dark:text-slate-500 text-sm">请稍候片刻</p>
        </div>
      </div>
    {:else if $notionItems.length === 0 && !$notionError}
      <div class="rounded-lg p-8 bg-white dark:bg-slate-800/50 text-center mb-6 flex flex-col sm:flex-row items-center gap-5 shadow-sm border border-slate-100 dark:border-slate-700">
        <div class="text-indigo-400 dark:text-indigo-500 flex-shrink-0 bg-indigo-50 dark:bg-indigo-900/20 p-4 rounded-full">
          <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-question"><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/><path d="M10 10.3c.2-.4.5-.8.9-1a2.1 2.1 0 0 1 2.6.4c.3.4.5.8.5 1.3 0 1.3-2 2-2 2"/><path d="M12 17h.01"/></svg>
        </div>
        <div class="flex-1 text-left">
          <h3 class="text-lg font-medium text-slate-700 dark:text-slate-300 mb-1">暂无数据</h3>
          <p class="text-sm text-slate-500 dark:text-slate-400 mb-4">在 Notion 数据库中未找到任何条目，请先配置数据库或刷新数据</p>
          <div class="flex gap-3">
            <Button
              variant="outline"
              size="sm"
              onclick={() => loadDatabaseItems(true)}
              disabled={$notionLoading}
              class="h-9 px-4 text-sm border-indigo-600 text-indigo-600 hover:bg-indigo-50 dark:border-indigo-400 dark:text-indigo-400 dark:hover:bg-indigo-900/20"
            >
              {#if $notionLoading}
                <span class="inline-block w-3 h-3 border-2 border-t-transparent border-indigo-600 dark:border-indigo-400 rounded-full animate-spin mr-1"></span>
              {/if}
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-refresh-cw mr-1"><path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/><path d="M21 3v5h-5"/><path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/><path d="M3 21v-5h5"/></svg>
              刷新数据
            </Button>
            <Button
              variant="outline"
              size="sm"
              onclick={() => settingsOpen = true}
              class="h-9 px-4 text-sm"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-settings mr-1"><path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"/><circle cx="12" cy="12" r="3"/></svg>
              打开设置
            </Button>
          </div>
        </div>
      </div>
    {:else if $notionItems.length > 0}
      <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3 mt-6">
        {#each $notionItems as item (item.id)}
          <button
            class="border border-slate-200 dark:border-slate-700 rounded-lg p-4 bg-white dark:bg-slate-800 hover:bg-slate-50 dark:hover:bg-slate-700/50 transition-colors cursor-pointer text-left w-full group shadow-sm hover:shadow-md"
            onclick={() => openNotionPage(item.id)}
            aria-label={`打开笔记 ${item.title}`}
          >
            <div class="flex items-start gap-3">
              <div class="flex-1 min-w-0">
                <h3 class="font-medium text-base mb-2 line-clamp-1 text-slate-800 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">{item.title || '无标题'}</h3>

                <div class="flex flex-wrap gap-2 mb-1">
                  {#if item.status}
                    <span class="text-xs px-2 py-0.5 bg-slate-100 dark:bg-slate-700 text-slate-700 dark:text-slate-300 rounded-full">{item.status}</span>
                  {/if}

                  {#if item.priority && item.priority.length > 0}
                    {#each item.priority as priorityItem}
                      <span class="text-xs px-2 py-0.5 bg-amber-100 dark:bg-amber-900/40 text-amber-800 dark:text-amber-300 rounded-full">{priorityItem}</span>
                    {/each}
                  {/if}

                  {#if item.dueDate}
                    <span class="text-xs px-2 py-0.5 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 rounded-full flex items-center gap-1">
                      <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar"><path d="M8 2v4"/><path d="M16 2v4"/><rect width="18" height="18" x="3" y="4" rx="2"/><path d="M3 10h18"/></svg>
                      {formatDateTime(item.dueDate)}
                    </span>
                  {/if}
                </div>
              </div>

              <div class="text-slate-400 dark:text-slate-500 opacity-0 group-hover:opacity-100 transition-opacity">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link"><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"/><polyline points="15 3 21 3 21 9"/><line x1="10" x2="21" y1="14" y2="3"/></svg>
              </div>
            </div>
          </button>
        {/each}
      </div>
    {/if}
    </div>
  </div>
</div>

<!-- 添加动画样式 -->
<style>
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }

  :global(.animate-fadeIn) {
    animation: fadeIn 0.3s ease-out forwards;
  }

  :global(.hover-card) {
    animation: fadeIn 0.5s ease-out forwards;
    opacity: 0;
  }
</style>

<!-- 设置对话框 -->
<SettingsDialog bind:open={settingsOpen} />