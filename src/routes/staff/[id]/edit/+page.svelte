<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/state';
  import StaffForm from '$lib/components/StaffForm.svelte';
  import { staffService, type Staff } from '$lib/services/staffService';

  // 获取人员 ID
  const staffId = $derived(parseInt(page.params.id));

  // 状态管理
  let staff = $state<Staff | null>(null);
  let isLoading = $state(true);
  let error = $state<string | null>(null);

  // 加载人员数据
  async function loadStaff() {
    isLoading = true;
    error = null;

    try {
      staff = await staffService.getStaffById(staffId);
    } catch (err: any) {
      error = err.message || '加载人员失败';
      console.error('加载人员失败:', err);
    } finally {
      isLoading = false;
    }
  }

  // 组件挂载时加载数据
  onMount(() => {
    loadStaff();
  });
</script>

<div class="container mx-auto px-4 py-8">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold">编辑人员</h1>
    <a href="/staff" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 flex items-center gap-1">
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-left"><path d="m12 19-7-7 7-7"/><path d="M19 12H5"/></svg>
      返回人员列表
    </a>
  </div>

  {#if isLoading}
    <div class="flex justify-center items-center py-12">
      <div class="inline-block w-6 h-6 border-2 border-t-transparent border-blue-600 dark:border-blue-400 rounded-full animate-spin mr-2"></div>
      <p>加载中...</p>
    </div>
  {:else if error}
    <div class="bg-red-50 dark:bg-red-900/20 border border-red-100 dark:border-red-800 rounded-md p-4 text-red-800 dark:text-red-200 mb-6">
      <p>{error}</p>
    </div>
  {:else if !staff}
    <div class="bg-amber-50 dark:bg-amber-900/20 border border-amber-100 dark:border-amber-800 rounded-md p-4 text-amber-800 dark:text-amber-200 mb-6">
      <p>人员不存在或已被删除</p>
    </div>
  {:else}
    <StaffForm staff={staff} />
  {/if}
</div>
