<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/state';
  import SqliteDictionaryForm from '$lib/components/SqliteDictionaryForm.svelte';
  import { sqliteDictionaryService, type SqliteDict } from '$lib/services/sqliteDictionaryService';

  // 获取字典 ID
  let dictId = $derived(parseInt(page.params.id));

  // 状态管理
  let dictionary = $state<SqliteDict | null>(null);
  let isLoading = $state(true);
  let error = $state<string | null>(null);

  // 加载字典数据
  async function loadDict() {
    isLoading = true;
    error = null;

    try {
      dictionary = await sqliteDictionaryService.getDictById(dictId);
    } catch (err: any) {
      error = err.message || '加载字典失败';
      console.error('加载字典失败:', err);
    } finally {
      isLoading = false;
    }
  }

  // 组件挂载时加载字典
  onMount(() => {
    if (dictId) {
      loadDict();
    }
  });
</script>

<div class="container mx-auto px-4 py-8">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold">编辑 SQLite 字典</h1>
    <a href="/sqlite-dictionaries" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 flex items-center gap-1">
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-left"><path d="m12 19-7-7 7-7"/><path d="M19 12H5"/></svg>
      返回字典列表
    </a>
  </div>

  {#if isLoading}
    <div class="flex justify-center items-center py-12">
      <div class="inline-block w-6 h-6 border-2 border-t-transparent border-blue-600 dark:border-blue-400 rounded-full animate-spin mr-2"></div>
      <p>加载中...</p>
    </div>
  {:else if error}
    <div class="bg-red-50 dark:bg-red-900/20 border border-red-100 dark:border-red-800 rounded-md p-4 text-red-800 dark:text-red-200 mb-6">
      <p>{error}</p>
    </div>
  {:else if !dictionary}
    <div class="bg-amber-50 dark:bg-amber-900/20 border border-amber-100 dark:border-amber-800 rounded-md p-4 text-amber-800 dark:text-amber-200 mb-6">
      <p>字典不存在或已被删除</p>
    </div>
  {:else}
    <SqliteDictionaryForm dictionary={dictionary} />
  {/if}
</div>
