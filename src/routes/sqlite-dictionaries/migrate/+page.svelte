<script lang="ts">
  import { onMount } from 'svelte';
  import { invoke } from '@tauri-apps/api/core';
  import { Button } from '$lib/components/ui/button';

  // 定义响应类型
  interface DictionaryResponse<T> {
    success: boolean;
    error?: string;
    data?: T;
  }

  // 状态管理
  let isLoading = $state(false);
  let error = $state<string | null>(null);
  let success = $state<string | null>(null);
  let logs = $state<{message: string, type: 'info' | 'success' | 'error'}[]>([]);
  let dictionaries = $state<any[]>([]);
  let migrationStats = $state({
    total: 0,
    success: 0,
    failed: 0,
    itemsTotal: 0,
    itemsSuccess: 0,
    itemsFailed: 0
  });

  // 添加日志
  function addLog(message: string, type: 'info' | 'success' | 'error' = 'info') {
    logs = [...logs, { message, type }];
    // 滚动到底部
    setTimeout(() => {
      const logElement = document.getElementById('log-container');
      if (logElement) {
        logElement.scrollTop = logElement.scrollHeight;
      }
    }, 10);
  }

  // 从文件读取JSON数据
  function readFileAsText(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target?.result) {
          resolve(event.target.result as string);
        } else {
          reject(new Error('读取文件失败'));
        }
      };
      reader.onerror = (error) => reject(error);
      reader.readAsText(file);
    });
  }

  // 上传并加载 JSON 文件
  async function handleFileUpload(event: Event) {
    const input = event.target as HTMLInputElement;
    if (!input.files || input.files.length === 0) {
      return;
    }

    const file = input.files[0];
    if (!file.name.endsWith('.json')) {
      error = '请上传 JSON 格式的文件';
      addLog(error, 'error');
      return;
    }

    try {
      addLog(`正在读取文件: ${file.name}...`);
      const content = await readFileAsText(file);
      dictionaries = JSON.parse(content);
      addLog(`成功加载 ${dictionaries.length} 个字典`, 'success');
      migrationStats.total = dictionaries.length;
      error = null;
      return true;
    } catch (err: any) {
      error = `读取文件失败: ${err.message}`;
      addLog(error, 'error');
      return false;
    }
  }

  // 加载预设的 MongoDB 字典数据
  async function loadDefaultDictionaries() {
    try {
      addLog('加载预设字典数据...');
      // 从静态资源目录读取数据
      const response = await fetch('/mongodb-dictionaries.json');
      if (!response.ok) {
        throw new Error(`请求失败: ${response.status} ${response.statusText}`);
      }
      const data = await response.text();
      dictionaries = JSON.parse(data);
      addLog(`成功加载 ${dictionaries.length} 个预设字典`, 'success');
      migrationStats.total = dictionaries.length;
      error = null;
      return true;
    } catch (err: any) {
      error = `加载预设字典数据失败: ${err.message}`;
      addLog(error, 'error');
      return false;
    }
  }

  // 转换 MongoDB 字典为 SQLite 字典格式
  function convertToSqliteDict(mongoDict: any) {
    // 处理创建时间和更新时间
    let createdAt = mongoDict.created_at;
    if (typeof createdAt === 'object' && createdAt.$date) {
      createdAt = createdAt.$date;
    }

    let updatedAt = mongoDict.updated_at;
    if (typeof updatedAt === 'object' && updatedAt.$date) {
      updatedAt = updatedAt.$date;
    }

    return {
      name: mongoDict.name,
      description: mongoDict.description || '',
      type_: mongoDict.type || 'list',
      tags: mongoDict.tags || [],
      mongo_oid: mongoDict._id.$oid,
      created_at: createdAt,
      updated_at: updatedAt,
      version: mongoDict.version || 1
    };
  }

  // 转换 MongoDB 字典项为 SQLite 字典项格式
  function convertToSqliteDictItems(mongoDict: any) {
    if (!mongoDict.items || !Array.isArray(mongoDict.items)) {
      return [];
    }

    return mongoDict.items.map((item: any) => ({
      key: item.key,
      value: item.value,
      description: item.description || '',
      status: item.status || 'active'
    }));
  }

  // 开始迁移
  async function startMigration() {
    if (dictionaries.length === 0) {
      error = '请先上传字典数据文件或加载预设字典';
      return;
    }

    isLoading = true;
    error = null;
    success = null;
    logs = [];
    migrationStats = {
      total: dictionaries.length,
      success: 0,
      failed: 0,
      itemsTotal: 0,
      itemsSuccess: 0,
      itemsFailed: 0
    };

    try {
      addLog('开始迁移字典数据到 SQLite...');

      // 迁移每个字典
      for (const mongoDict of dictionaries) {
        addLog(`正在处理字典: ${mongoDict.name}...`);

        // 转换字典格式
        const sqliteDict = convertToSqliteDict(mongoDict);
        const sqliteDictItems = convertToSqliteDictItems(mongoDict);
        migrationStats.itemsTotal += sqliteDictItems.length;

        try {
          // 创建字典
          addLog(`创建字典: ${sqliteDict.name}...`);
          const dictResponse = await invoke<DictionaryResponse<number>>('sqlite_create_dict', { dictionary: sqliteDict });

          if (!dictResponse.success) {
            throw new Error(dictResponse.error || '创建字典失败');
          }

          const dictId = dictResponse.data;
          addLog(`字典创建成功，ID: ${dictId}`, 'success');

          // 添加字典项
          addLog(`添加 ${sqliteDictItems.length} 个字典项...`);
          let itemSuccessCount = 0;

          for (const item of sqliteDictItems) {
            try {
              const itemResponse = await invoke<DictionaryResponse<number>>('sqlite_add_dict_item', {
                dictionaryId: dictId,
                item: item
              });

              if (itemResponse.success) {
                itemSuccessCount++;
                migrationStats.itemsSuccess++;
              } else {
                addLog(`添加字典项 ${item.key} 失败: ${itemResponse.error}`, 'error');
                migrationStats.itemsFailed++;
              }
            } catch (itemError: any) {
              addLog(`添加字典项 ${item.key} 出错: ${itemError.message}`, 'error');
              migrationStats.itemsFailed++;
            }
          }

          addLog(`成功添加 ${itemSuccessCount}/${sqliteDictItems.length} 个字典项`, 'success');
          migrationStats.success++;
        } catch (dictError: any) {
          addLog(`处理字典 ${mongoDict.name} 失败: ${dictError.message}`, 'error');
          migrationStats.failed++;
        }
      }

      success = `迁移完成! 成功导入 ${migrationStats.success}/${migrationStats.total} 个字典`;
      addLog(success, 'success');
    } catch (err: any) {
      error = `迁移过程中发生错误: ${err.message}`;
      addLog(error, 'error');
    } finally {
      isLoading = false;
    }
  }

  onMount(() => {
    addLog('准备就绪，点击"开始迁移"按钮开始导入数据。');
  });
</script>

<div class="p-6">
  <div class="mb-6 flex justify-between items-center">
    <div>
      <h1 class="text-2xl font-bold mb-2">字典数据迁移到 SQLite</h1>
      <p class="text-slate-600 dark:text-slate-400">
        此工具将帮助您将字典数据迁移到 SQLite 数据库。您可以上传 JSON 格式的字典数据文件或使用预设数据。
      </p>
    </div>
    <a href="/sqlite-dictionaries" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 flex items-center gap-1">
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-left"><path d="m12 19-7-7 7-7"/><path d="M19 12H5"/></svg>
      返回字典列表
    </a>
  </div>

  <!-- 错误提示 -->
  {#if error}
    <div class="bg-red-50 dark:bg-red-900/20 border border-red-100 dark:border-red-800 rounded-md p-4 text-red-800 dark:text-red-200 mb-6">
      <p>{error}</p>
    </div>
  {/if}

  <!-- 成功提示 -->
  {#if success}
    <div class="bg-green-50 dark:bg-green-900/20 border border-green-100 dark:border-green-800 rounded-md p-4 text-green-800 dark:text-green-200 mb-6">
      <p>{success}</p>
    </div>
  {/if}

  <!-- 数据源选择 -->
  <div class="bg-white dark:bg-slate-800 rounded-lg shadow-md p-6 mb-6">
    <div class="flex justify-between items-center mb-4">
      <h2 class="text-lg font-semibold">数据源</h2>
      <div class="flex gap-2">
        <Button variant="outline" onclick={loadDefaultDictionaries} disabled={isLoading}>
          加载预设数据
        </Button>
        <label class="cursor-pointer">
          <Button variant="outline" disabled={isLoading}>
            上传JSON文件
          </Button>
          <input
            type="file"
            accept=".json"
            class="hidden"
            onchange={handleFileUpload}
            disabled={isLoading}
          />
        </label>
      </div>
    </div>

    <div class="bg-slate-50 dark:bg-slate-700/30 rounded-md p-4 mb-4">
      <div class="flex justify-between items-center">
        <div>
          <p class="text-sm font-medium">当前数据源</p>
          <p class="text-sm text-slate-500 dark:text-slate-400">
            {dictionaries.length > 0 ? `已加载 ${dictionaries.length} 个字典` : '未加载数据'}
          </p>
        </div>
        {#if dictionaries.length > 0}
          <Button size="sm" variant="ghost" onclick={() => dictionaries = []}>清除</Button>
        {/if}
      </div>
    </div>

    <div class="bg-slate-50 dark:bg-slate-700/30 rounded-md p-4">
      <h3 class="text-sm font-medium mb-2">JSON 文件格式说明</h3>
      <p class="text-sm text-slate-600 dark:text-slate-400 mb-2">
        上传的 JSON 文件应为字典数组，每个字典对象包含以下字段：
      </p>

      <div class="text-xs bg-slate-100 dark:bg-slate-800 p-3 rounded-md overflow-auto max-h-48 mb-2 whitespace-pre">
{`[
  {
    "_id": { "$oid": "67b6c712ee95641d86f26e37" },  // 可选，字典的原始 ID
    "name": "招募公司",              // 必选，字典名称
    "description": "招募公司列表",  // 可选，字典描述
    "type": "list",                      // 可选，字典类型
    "created_at": "2025-02-15T14:15:36.809Z", // 可选，创建时间
    "updated_at": "2025-02-20T07:40:55.507Z", // 可选，更新时间
    "version": 1,                        // 可选，版本号
    "tags": ["tag1", "tag2"],            // 可选，标签数组
    "items": [                           // 可选，字典项数组
      {
        "key": "item_key",                // 必选，字典项键
        "value": "字典项值",          // 必选，字典项值
        "description": "描述",        // 可选，字典项描述
        "status": "active"                // 可选，状态（active 或 inactive）
      }
    ]
  }
]`}
      </div>

      <p class="text-xs text-slate-500 dark:text-slate-400">
        注意：字典项的键（key）只能包含英文字母、数字和下划线。
      </p>
    </div>
  </div>

  <!-- 迁移控制 -->
  <div class="bg-white dark:bg-slate-800 rounded-lg shadow-md p-6 mb-6">
    <div class="flex justify-between items-center mb-4">
      <h2 class="text-lg font-semibold">迁移控制</h2>
      <Button onclick={startMigration} disabled={isLoading || dictionaries.length === 0}>
        {#if isLoading}
          <div class="inline-block w-4 h-4 border-2 border-t-transparent border-white rounded-full animate-spin mr-2"></div>
          迁移中...
        {:else}
          开始迁移
        {/if}
      </Button>
    </div>

    {#if migrationStats.total > 0}
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
        <div class="bg-slate-50 dark:bg-slate-700/30 rounded-md p-3">
          <p class="text-xs text-slate-500 dark:text-slate-400 mb-1">字典</p>
          <p class="text-base font-medium">
            成功: <span class="text-green-600 dark:text-green-400">{migrationStats.success}</span> /
            失败: <span class="text-red-600 dark:text-red-400">{migrationStats.failed}</span> /
            总计: {migrationStats.total}
          </p>
        </div>

        <div class="bg-slate-50 dark:bg-slate-700/30 rounded-md p-3">
          <p class="text-xs text-slate-500 dark:text-slate-400 mb-1">字典项</p>
          <p class="text-base font-medium">
            成功: <span class="text-green-600 dark:text-green-400">{migrationStats.itemsSuccess}</span> /
            失败: <span class="text-red-600 dark:text-red-400">{migrationStats.itemsFailed}</span> /
            总计: {migrationStats.itemsTotal}
          </p>
        </div>

        <div class="bg-slate-50 dark:bg-slate-700/30 rounded-md p-3">
          <p class="text-xs text-slate-500 dark:text-slate-400 mb-1">完成率</p>
          <p class="text-base font-medium">
            字典: <span class="text-blue-600 dark:text-blue-400">{migrationStats.total > 0 ? Math.round(migrationStats.success / migrationStats.total * 100) : 0}%</span> /
            字典项: <span class="text-blue-600 dark:text-blue-400">{migrationStats.itemsTotal > 0 ? Math.round(migrationStats.itemsSuccess / migrationStats.itemsTotal * 100) : 0}%</span>
          </p>
        </div>
      </div>
    {/if}
  </div>

  <!-- 日志面板 -->
  <div class="bg-white dark:bg-slate-800 rounded-lg shadow-md overflow-hidden">
    <div class="border-b border-slate-200 dark:border-slate-700 px-6 py-4">
      <h2 class="text-lg font-semibold">迁移日志</h2>
    </div>
    <div id="log-container" class="bg-slate-900 text-slate-200 p-4 h-[400px] overflow-y-auto font-mono text-sm">
      {#each logs as log}
        <div class={log.type === 'success' ? 'text-green-400' : log.type === 'error' ? 'text-red-400' : 'text-slate-300'}>
          [{new Date().toLocaleTimeString()}] {log.message}
        </div>
      {/each}
      {#if logs.length === 0}
        <div class="text-slate-400">等待开始迁移...</div>
      {/if}
    </div>
  </div>
</div>
