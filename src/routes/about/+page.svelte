<script>
    import { onMount } from 'svelte';
    import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '$lib/components/ui/card';
    import { Button } from '$lib/components/ui/button';
    import Separator from '$lib/components/ui/separator.svelte';

    let appVersion = '0.1.0';
    let appName = 'PeckByte';

    onMount(async () => {
        // You can add code here to fetch app version or other details if needed
    });
</script>

<div class="container mx-auto p-4 max-w-4xl">
    <Card class="w-full">
        <CardHeader>
            <CardTitle class="text-2xl font-bold">关于 {appName}</CardTitle>
            <CardDescription>版本 {appVersion}</CardDescription>
        </CardHeader>
        <CardContent>
            <div class="flex flex-col gap-4">
                <div class="text-center mb-6">
                    <img src="/app-logo.png" alt="App Logo" class="w-32 h-32 mx-auto mb-4" />
                    <h2 class="text-xl font-semibold">{appName}</h2>
                    <p class="text-muted-foreground">临床研究项目管理系统</p>
                </div>

                <Separator />

                <div class="mt-4">
                    <h3 class="text-lg font-semibold mb-2">系统功能</h3>
                    <ul class="list-disc pl-6 space-y-1">
                        <li>项目管理 - 创建和管理临床研究项目</li>
                        <li>入排标准规则 - 定义和管理项目入排标准</li>
                        <li>人员管理 - 管理研究人员和角色分配</li>
                        <li>补贴管理 - 跟踪和管理项目补贴</li>
                        <li>数据字典 - 维护系统中使用的各类字典数据</li>
                    </ul>
                </div>

                <Separator />

                <div class="mt-4">
                    <h3 class="text-lg font-semibold mb-2">技术信息</h3>
                    <p>本应用基于 Tauri + SvelteKit 开发，使用 SQLite 作为数据库。</p>
                    <p class="text-sm text-muted-foreground mt-2">© 2023-2024 PeckByte. 保留所有权利。</p>
                </div>
            </div>
        </CardContent>
        <CardFooter class="flex justify-end">
            <Button variant="outline" on:click={() => window.history.back()}>返回</Button>
        </CardFooter>
    </Card>
</div>
