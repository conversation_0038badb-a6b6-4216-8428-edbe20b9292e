<script lang="ts">
  import { invoke } from "@tauri-apps/api/core";
  import { Button } from "$lib/components/ui/button";
  import MoodSelector from "$lib/components/MoodSelector.svelte";

  let name = $state("");
  let greetMsg = $state("");
  let title = $state("");
  let description = $state("");
  let userId = $state("67"); // 默认值设为67
  let apiResponse = $state("");
  let apiBaseUrl = $state("http://127.0.0.1:8000"); // FastAPI 后端基础 URL
  let apiStatus = $state<'unknown' | 'connected' | 'error'>('unknown'); // API 连接状态
  let isTestingConnection = $state(false); // 测试连接中

  async function greet(event: Event) {
    event.preventDefault();
    // Learn more about Tauri commands at https://tauri.app/develop/calling-rust/
    greetMsg = await invoke("greet", { name });
  }

  // 测试 API 连接
  async function testApiConnection() {
    isTestingConnection = true;
    apiStatus = 'unknown';

    try {
      // 尝试访问 API 的根路径或健康检查端点
      const response = await fetch(`${apiBaseUrl}/`, {
        method: 'GET',
        headers: {
          'accept': 'application/json'
        }
      });

      if (response.ok) {
        apiStatus = 'connected';
      } else {
        apiStatus = 'error';
      }
    } catch (error) {
      console.error('测试 API 连接失败:', error);
      apiStatus = 'error';
    } finally {
      isTestingConnection = false;
    }
  }

  // 发送请求到 FastAPI
  async function sendToFastAPI(event: Event) {
    event.preventDefault();
    try {
      // 使用用户配置的 API 基础 URL
      const response = await fetch(`${apiBaseUrl}/items/?user_id=${userId}`, {
        method: 'POST',
        headers: {
          'accept': 'application/json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: title,
          description: description
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      apiResponse = JSON.stringify(data, null, 2);
      // 请求成功时更新 API 状态
      apiStatus = 'connected';
    } catch (error: any) {
      console.error('API请求错误:', error);
      apiResponse = `Error: ${error.message}`;
      apiStatus = 'error';
    }
  }
</script>

<main class="min-h-screen bg-gradient-to-b from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
  <div class="container mx-auto px-4 py-12">
    <!-- 顶部欢迎区域 -->
    <div class="text-center mb-12">
      <h1 class="text-4xl font-bold text-slate-800 dark:text-white mb-4">欢迎使用 Tauri 应用</h1>
      <p class="text-slate-600 dark:text-slate-300 max-w-2xl mx-auto">这是一个基于 Tauri 框架的跨平台应用，结合了 Svelte 前端和 Rust 后端的强大功能。</p>
    </div>

    <!-- 心情选择器区域 -->
    <div class="hover-card bg-white dark:bg-slate-800 rounded-xl shadow-lg p-8 mb-12 border border-slate-100 dark:border-slate-700">
      <h2 class="text-2xl font-semibold text-slate-800 dark:text-white mb-6 flex items-center">
        <span class="mr-2">今日心情</span>
        <span class="text-xl animate-pulse">✨</span>
      </h2>
      <MoodSelector />
    </div>

    <!-- 个人问候区域 -->
    <div class="hover-card bg-white dark:bg-slate-800 rounded-xl shadow-lg p-8 mb-12 border border-slate-100 dark:border-slate-700">
      <h2 class="text-2xl font-semibold text-slate-800 dark:text-white mb-6">个人问候</h2>
      <form class="flex flex-col sm:flex-row gap-4 mb-4" onsubmit={greet}>
        <input
          id="greet-input"
          class="flex-1 px-4 py-3 rounded-lg border border-slate-200 dark:border-slate-600 dark:bg-slate-700 dark:text-white focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent"
          placeholder="请输入你的名字..."
          bind:value={name}
        />
        <Button type="submit" class="px-6 py-3">问候</Button>
      </form>
      {#if greetMsg}
        <div class="mt-4 p-4 bg-blue-50 dark:bg-blue-900/30 rounded-lg text-blue-800 dark:text-blue-200 animate-fadeIn">
          {greetMsg}
        </div>
      {/if}
    </div>

    <!-- API测试区域 -->
    <div class="hover-card bg-white dark:bg-slate-800 rounded-xl shadow-lg p-8 border border-slate-100 dark:border-slate-700">
      <h2 class="text-2xl font-semibold text-slate-800 dark:text-white mb-6">FastAPI 请求测试</h2>

      <!-- API 基础 URL 设置 -->
      <div class="mb-6 p-4 bg-slate-50 dark:bg-slate-700/50 rounded-lg">
        <label for="api-base-url" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">API 基础 URL</label>
        <div class="flex gap-2">
          <input
            id="api-base-url"
            class="flex-1 px-4 py-2 rounded-lg border border-slate-200 dark:border-slate-600 dark:bg-slate-700 dark:text-white focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent text-sm"
            placeholder="输入 FastAPI 后端地址..."
            bind:value={apiBaseUrl}
          />
          <Button
            type="button"
            variant="outline"
            class="text-sm"
            onclick={() => apiBaseUrl = "http://127.0.0.1:8000"}
          >
            重置
          </Button>
        </div>

        <div class="flex items-center justify-between mt-3">
          <p class="text-xs text-slate-500 dark:text-slate-400">当前请求地址: {apiBaseUrl}/items/?user_id={userId}</p>

          <div class="flex items-center gap-2">
            <!-- API 状态指示器 -->
            {#if apiStatus === 'connected'}
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
                <span class="w-2 h-2 rounded-full bg-green-500 mr-1"></span>
                已连接
              </span>
            {:else if apiStatus === 'error'}
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400">
                <span class="w-2 h-2 rounded-full bg-red-500 mr-1"></span>
                连接失败
              </span>
            {:else}
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-slate-100 text-slate-800 dark:bg-slate-700 dark:text-slate-300">
                <span class="w-2 h-2 rounded-full bg-slate-400 mr-1"></span>
                未知状态
              </span>
            {/if}

            <!-- 测试连接按钮 -->
            <Button
              type="button"
              variant="ghost"
              class="text-xs h-7 px-2"
              onclick={testApiConnection}
              disabled={isTestingConnection}
            >
              {#if isTestingConnection}
                <span class="inline-block w-3 h-3 border-2 border-t-transparent border-slate-600 dark:border-slate-300 rounded-full animate-spin mr-1"></span>
              {/if}
              测试连接
            </Button>
          </div>
        </div>
      </div>

      <form onsubmit={sendToFastAPI} class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div class="space-y-2">
            <label for="user-id-input" class="text-sm font-medium text-slate-700 dark:text-slate-300">用户ID</label>
            <input
              id="user-id-input"
              class="w-full px-4 py-3 rounded-lg border border-slate-200 dark:border-slate-600 dark:bg-slate-700 dark:text-white focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent"
              placeholder="输入用户ID..."
              bind:value={userId}
            />
          </div>
          <div class="space-y-2">
            <label for="title-input" class="text-sm font-medium text-slate-700 dark:text-slate-300">标题</label>
            <input
              id="title-input"
              class="w-full px-4 py-3 rounded-lg border border-slate-200 dark:border-slate-600 dark:bg-slate-700 dark:text-white focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent"
              placeholder="输入标题..."
              bind:value={title}
            />
          </div>
          <div class="space-y-2">
            <label for="description-input" class="text-sm font-medium text-slate-700 dark:text-slate-300">描述</label>
            <input
              id="description-input"
              class="w-full px-4 py-3 rounded-lg border border-slate-200 dark:border-slate-600 dark:bg-slate-700 dark:text-white focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent"
              placeholder="输入描述..."
              bind:value={description}
            />
          </div>
        </div>
        <div class="flex justify-end">
          <Button type="submit" variant="secondary" class="px-6">发送 POST 请求</Button>
        </div>
      </form>
      {#if apiResponse}
        <div class="mt-6">
          <h3 class="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">响应结果</h3>
          <pre class="p-4 bg-slate-100 dark:bg-slate-700 rounded-lg overflow-x-auto text-sm text-slate-800 dark:text-slate-200">{apiResponse}</pre>
        </div>
      {/if}
    </div>

    <!-- 技术栈区域 -->
    <div class="mt-12 text-center">
      <h2 class="text-xl font-semibold text-slate-700 dark:text-slate-300 mb-6">技术支持</h2>
      <div class="flex justify-center items-center gap-8 flex-wrap">
        <a href="https://vitejs.dev" target="_blank" class="group">
          <img src="/vite.svg" class="h-10 transition-all group-hover:scale-110 group-hover:drop-shadow-md" alt="Vite Logo" />
        </a>
        <a href="https://tauri.app" target="_blank" class="group">
          <img src="/tauri.svg" class="h-10 transition-all group-hover:scale-110 group-hover:drop-shadow-md" alt="Tauri Logo" />
        </a>
        <a href="https://kit.svelte.dev" target="_blank" class="group">
          <img src="/svelte.svg" class="h-10 transition-all group-hover:scale-110 group-hover:drop-shadow-md" alt="SvelteKit Logo" />
        </a>
      </div>
    </div>
  </div>
</main>

<style>
  /* 自定义动画效果 */
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes scaleIn {
    from { opacity: 0; transform: scale(0.9); }
    to { opacity: 1; transform: scale(1); }
  }

  :global(.animate-fadeIn) {
    animation: fadeIn 0.3s ease-out forwards;
  }

  :global(.mood-button) {
    animation: scaleIn 0.5s ease-out forwards;
    opacity: 0;
  }

  /* 卡片悬停效果 */
  :global(.hover-card) {
    transition: all 0.3s ease;
  }

  :global(.hover-card:hover) {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  /* 心情选择器动画 */
  :global(.mood-button) {
    transition: all 0.2s ease;
  }

  :global(.mood-button:hover) {
    transform: scale(1.05);
  }

  :global(.mood-button:active) {
    transform: scale(0.95);
  }
</style>
