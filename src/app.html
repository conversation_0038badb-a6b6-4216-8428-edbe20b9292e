<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%sveltekit.assets%/favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Tauri + SvelteKit + Typescript App</title>
    %sveltekit.head%
    <script>
      // 确保复制/粘贴快捷键在整个应用程序中都能正常工作
      document.addEventListener('DOMContentLoaded', function() {
        // 这个函数不需要做任何事情，只需要确保事件监听器被添加
        // Tauri会自动处理复制/粘贴操作，但我们需要确保事件不被阻止
      });
    </script>
  </head>
  <body data-sveltekit-preload-data="hover">
    <div style="display: contents">%sveltekit.body%</div>
  </body>
</html>
