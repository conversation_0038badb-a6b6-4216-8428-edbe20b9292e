---
description: 
globs: 
alwaysApply: true
---
# Tauri 前后端通信指南

## 基本通信流程

### 前端到后端通信
前端通过 `@tauri-apps/api/core` 的 `invoke` 函数调用后端命令。主要服务文件：

- [src/lib/services/staffService.ts](mdc:src/lib/services/staffService.ts)
- [src/lib/services/configService.ts](mdc:src/lib/services/configService.ts)
- [src/lib/services/projectManagementService.ts](mdc:src/lib/services/projectManagementService.ts)

### 后端命令处理
后端使用 `#[tauri::command]` 宏处理前端请求。关键实现：

- [src-tauri/src/lib.rs](mdc:src-tauri/src/lib.rs)
- [src-tauri/src/commands/unified/project_commands.rs](mdc:src-tauri/src/commands/unified/project_commands.rs)

### 错误处理模式
- 前端：try-catch 捕获错误
- 后端：Result 类型处理错误
- 使用统一的错误响应格式

## IPC 与数据序列化常见问题

### 命名约定与数据传递问题

1. **Tauri 参数检查与命名风格:**
   - **问题：** Tauri 期望 JavaScript payload 使用 `camelCase` 命名，而非 Rust 的 `snake_case`
   - **解法：** 前端调用时使用 `camelCase` 键名

2. **`Option<Vec<T>>` 反序列化:**
   - **问题：** 类型不匹配时，`serde` 可能静默地将整个集合解析为 `None`
   - **解法：** 确保数据类型严格匹配；调试时可将参数改为非可选类型

3. **Svelte 状态更新与传递:**
   - **问题：** 嵌套组件中的状态更新未能同步到顶层组件
   - **解法：** 遵循 Svelte 数据流规则，使用 `bind:` 建立双向绑定，正确使用 Svelte 5 的 `$bindable()`

4. **新创建项的 ID 处理:**
   - **问题：** 新创建项没有数据库 ID，导致关联关系无法建立
   - **解法：** 使用临时 ID（如负数或 UUID），后端建立 ID 映射表

5. **`#[serde(flatten)]` 数据格式匹配:**
   - **问题：** 后端使用 `#[serde(flatten)]` 时期望扁平化JSON结构，前端发送嵌套结构导致数据无法正确解析
   - **解法：** 前端需要将嵌套筛选参数转换为扁平化格式
   - **示例：** 
     ```typescript
     // 前端嵌套结构
     { project_status: { status_ids: [37, 38] } }
     
     // 转换为后端期望的扁平化结构
     { status_ids: [37, 38] }
     
     // 解决方案：创建转换函数
     function flattenFilterParams(filters: any): any {
       const flattened: any = {};
       if (filters.project_status?.status_ids) {
         flattened.status_ids = filters.project_status.status_ids;
       }
       // ... 其他字段
       return flattened;
     }
     ```

## 前端响应式更新最佳实践

### Svelte 组件重新渲染问题

1. **数据结构相同但内容变化时组件不更新:**
   - **问题：** 当数据结构保持不变但内容发生变化时，Svelte组件可能不会重新渲染
   - **解法：** 使用 `{#key}` 块强制重新渲染
   - **技巧：**
     ```svelte
     <!-- 使用复合键值包含所有变化因子 -->
     {#key `${data}-${filterState}`}
       <Component {data} />
     {/key}
     
     <!-- 对于复杂对象使用JSON.stringify -->
     {#key `${JSON.stringify(data)}-${selectedIds.join(',')}`}
       <Table {data} />
     {/key}
     ```

2. **图表组件更新问题:**
   - **问题：** ECharts等图表组件在数据更新时不重新渲染
   - **解法：** 确保键值能反映所有影响渲染的状态变化
   - **示例：**
     ```svelte
     {#key `${chartData}-${filterOptions.join(',')}`}
       <BarChart options={chartOptions} />
     {/key}
     ```

## 筛选功能设计最佳实践

### 用户体验设计原则

1. **默认状态设计:**
   - **原则：** 默认应该是"全选"而不是"全不选"
   - **原因：** 用户首次进入页面就能看到完整数据
   - **实现：**
     ```typescript
     // 默认全部勾选
     let selectedIds: number[] = allOptions.map(option => option.id);
     ```

2. **交互按钮设计:**
   - 提供"全选"和"清除筛选"按钮
   - 根据当前选择状态智能显示按钮
   - **示例：**
     ```svelte
     {#if selectedIds.length > 0 && selectedIds.length < allOptions.length}
       <button onclick={clearSelection}>清除筛选</button>
     {/if}
     
     {#if selectedIds.length === 0}
       <button onclick={selectAll}>全选</button>
     {/if}
     ```

3. **状态反馈:**
   - 显示当前筛选状态（如"已选择 3/4 个选项"）
   - 提供视觉反馈让用户了解当前筛选情况

4. **初始化同步:**
   - 确保组件挂载时将默认筛选状态同步到全局状态管理
   - **示例：**
     ```typescript
     onMount(() => {
       // 初始化筛选条件
       $activeFilters = {
         ...$activeFilters,
         project_status: {
           status_ids: selectedProjectStatusIds
         }
       };
     });
     ```

## 调试策略

### 前端调试
- 调用前打印 payload 结构
- 捕获并记录详细错误

### 后端调试
- 命令入口处添加 `info!` 日志
- 关键步骤添加 `debug!` 日志
- 在入口文件显式初始化日志系统，设置最低级别为 `DEBUG`

### 系统化调试方法

1. **前后端数据对比:**
   - 前端添加 `console.log` 输出发送的参数格式
   - 后端添加日志输出接收到的参数
   - 对比两者确保格式匹配

2. **序列化属性检查:**
   - 检查后端模型定义，特别注意 `#[serde(flatten)]` 等序列化属性
   - 理解这些属性对JSON结构的影响

3. **网络请求验证:**
   - 使用浏览器开发者工具查看网络请求的实际payload
   - 确认发送的数据格式与后端期望一致

4. **详细调试信息:**
   - 在关键数据加载函数中添加详细的调试信息
   - 包括筛选条件、返回数据、错误信息等
   - **示例：**
     ```typescript
     async function loadData() {
       console.log('开始加载数据，筛选条件:', filters);
       try {
         const result = await service.getData(filters);
         console.log('获取到数据:', result);
         return result;
       } catch (error) {
         console.error('加载数据失败:', error);
         throw error;
       }
     }
     ```
