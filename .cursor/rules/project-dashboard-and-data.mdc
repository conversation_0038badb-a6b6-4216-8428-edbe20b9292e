---
description:
globs:
alwaysApply: false
---
# Project Dashboard: Data Flow and Status Management

This rule describes the project management dashboard, its data sources, and how project statuses are managed and displayed. For a detailed design of the dashboard KPIs, UI, and technical implementation, refer to [项目管理仪表盘.md](mdc:开发文档/项目管理仪表盘.md). For the complete database schema, refer to [database.md](mdc:开发文档/database.md).

## Dashboard Overview

The project management dashboard provides a visual overview of clinical research project operations. Key information includes project counts, status distributions, phase distributions, and recruitment statuses.

- **Main Frontend Page**: The dashboard is implemented as a Svelte component located at `[src/routes/dashboard/project-management/+page.svelte](mdc:src/routes/dashboard/project-management/+page.svelte)`.
- **Chart Components**: Reusable chart components are found in `[src/lib/components/dashboard/charts/](mdc:src/lib/components/dashboard/charts/)`.
- **Filter Components**: Filters for the dashboard are located in `[src/lib/components/dashboard/filters/](mdc:src/lib/components/dashboard/filters/)`.

## Data Fetching and Backend Interaction

Dashboard data is fetched from the backend through a series of services and commands:

1.  **Frontend Service**: The `[src/lib/services/dashboardService.ts](mdc:src/lib/services/dashboardService.ts)` is responsible for making API calls to the backend to retrieve dashboard data.
2.  **Backend Tauri Commands**: These commands are defined in `[src-tauri/src/commands/dashboard_commands.rs](mdc:src-tauri/src/commands/dashboard_commands.rs)` (this file might need to be created as per the design document). They handle requests from the frontend.
3.  **Backend Service Layer**: The business logic for data aggregation and processing resides in `[src-tauri/src/services/dashboard_service.rs](mdc:src-tauri/src/services/dashboard_service.rs)` (this file might also need to be created).
4.  **Repository Layer**: Database queries, especially for project data and aggregations, are handled by the repository layer, likely within `[src-tauri/src/repositories/project_management_repository.rs](mdc:src-tauri/src/repositories/project_management_repository.rs)` or a new `dashboard_repository.rs`.

## Project Data and Status Resolution

The primary source of project information is the `projects` table, as detailed in `[database.md](mdc:开发文档/database.md)`.

A critical aspect of the dashboard is displaying human-readable statuses for various project attributes (e.g., project status, project stage, recruitment status, disease type). These statuses are stored as IDs in the `projects` table (e.g., `project_status_item_id`, `project_stage_item_id`).

**To resolve these IDs into meaningful names, the system relies on the `dictionary_items` table, also described in `[database.md](mdc:开发文档/database.md)`.**

- The `dictionary_items` table contains key-value pairs where `item_id` is the identifier stored in other tables (like `projects`), and `item_value` (or similar field) provides the human-readable string.
- For example, `projects.project_status_item_id` would be joined with `dictionary_items.item_id` to retrieve the actual status name (e.g., "进行中", "已完成").

This lookup mechanism is fundamental for displaying data on the dashboard as described in `[项目管理仪表盘.md](mdc:开发文档/项目管理仪表盘.md)` (e.g., "项目状态分布", "项目阶段分布").

## Key Tables Referenced:

- `[projects](mdc:开发文档/database.md)`: Main table for project information. Contains foreign keys like `project_status_item_id`, `project_stage_item_id`, `recruitment_status_item_id`, `disease_item_id`.
- `[dictionary_items](mdc:开发文档/database.md)`: Stores mappings for various IDs to human-readable values. Used to look up statuses, types, and other categorical data.
- `[project_personnel_roles](mdc:开发文档/database.md)`: For data related to personnel allocation.
- `[subsidies](mdc:开发文档/database.md)`: For financial and subsidy information.
- `[project_criteria](mdc:开发文档/database.md)`: For inclusion/exclusion criteria.

By understanding these relationships, the AI can better assist with queries related to the project management dashboard, its data display, and underlying data sources.
