---
description:
globs:
alwaysApply: false
---
# 数据库集成

本项目支持多种数据库后端，主要包括 SQLite 和 MongoDB。

## 数据库配置
数据库配置在 [src-tauri/src/app.rs](mdc:src-tauri/src/app.rs) 中初始化：

```rust
let db_config = DatabaseConfig::new();
```

## 数据访问层
项目使用仓储模式（Repository Pattern）处理数据访问：

- SQLite 实现：[src-tauri/src/repositories/sqlite/](mdc:src-tauri/src/repositories/sqlite/)
- MongoDB 实现：[src-tauri/src/repositories/mongodb/](mdc:src-tauri/src/repositories/mongodb/)

## 前端数据服务
前端通过服务层访问数据：

- [src/lib/services/sqliteDictionaryService.ts](mdc:src/lib/services/sqliteDictionaryService.ts) - SQLite 字典服务
- [src/lib/components/SqliteDictionaryDetail.svelte](mdc:src/lib/components/SqliteDictionaryDetail.svelte) - 字典详情组件

## 数据模型
数据模型定义在各个相关模块中，确保前后端数据结构一致。
