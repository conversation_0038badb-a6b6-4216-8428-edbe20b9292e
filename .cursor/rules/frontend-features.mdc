---
description:
globs:
alwaysApply: false
---
# 前端功能与页面结构

本规则描述了应用程序前端的主要功能模块及其对应的 SvelteKit 页面结构，基于主导航菜单。

## 导航菜单项与功能

### 1. 首页

- **菜单项:** 首页
- **对应页面:** [`src/routes/+page.svelte`](mdc:src/routes/+page.svelte)
- **功能描述:** 应用程序的入口页面，可能包含概览信息或快捷入口。

### 2. 灵感笔记

- **菜单项:** 灵感笔记
- **对应目录:** [`src/routes/notes/`](mdc:src/routes/notes/)
- **功能描述:** 用于记录和管理用户的灵感、想法或笔记。

### 3. SQLite 字典

- **菜单项:** SQLite 字典
- **对应目录:** [`src/routes/sqlite-dictionaries/`](mdc:src/routes/sqlite-dictionaries/)
- **功能描述:** 管理和查阅存储在 SQLite 中的字典数据。可能还包含一些测试页面如 [`src/routes/test-dict/`](mdc:src/routes/test-dict/) 和 [`src/routes/test-dict-item/`](mdc:src/routes/test-dict-item/)。

### 4. 人员管理

- **菜单项:** 人员管理
- **对应目录:** [`src/routes/staff/`](mdc:src/routes/staff/)
- **功能描述:** 用于管理组织或项目中的人员信息。

### 5. 项目管理

- **菜单项:** 项目管理
- **对应目录:** [`src/routes/projects/`](mdc:src/routes/projects/)
- **功能描述:** 用于管理项目相关的信息、进度或任务。

### 6. 入排标准规则

- **菜单项:** 入排标准规则
- **对应目录:**
    - [`src/routes/rules/`](mdc:src/routes/rules/)
    - [`src/routes/criteria-designer/`](mdc:src/routes/criteria-designer/)
    - [`src/routes/criteria-designer-simple/`](mdc:src/routes/criteria-designer-simple/)
    - [`src/routes/criteria-designer-with-editor/`](mdc:src/routes/criteria-designer-with-editor/)
    - [`src/routes/criteria-test/`](mdc:src/routes/criteria-test/)
- **功能描述:** 用于定义、设计、测试和管理某种标准或规则，特别是与"入排"（Entry/Ranking）相关的规则。包含多个不同复杂度和用途的设计器及测试页面。

## 其他可能页面

- **Langchain 集成:** [`src/routes/langchain/`](mdc:src/routes/langchain/) - 可能与 AI 或 Langchain 功能相关。
- **测试页面:** 存在多个 `test-` 或 `debug` 前缀的目录，用于开发和测试。

## 布局

- **全局布局:** [`src/routes/+layout.svelte`](mdc:src/routes/+layout.svelte) 和 [`src/routes/+layout.ts`](mdc:src/routes/+layout.ts) 定义了所有页面的通用布局和加载逻辑。
