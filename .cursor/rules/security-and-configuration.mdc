---
description:
globs:
alwaysApply: false
---
# 安全性和配置

## Tauri 配置
主要配置文件：[src-tauri/tauri.conf.json](mdc:src-tauri/tauri.conf.json)

包含：
- 应用标识符
- 构建命令
- 窗口配置
- 安全策略

## 权限系统
使用 Tauri 的 capabilities 系统管理权限：

- [src-tauri/capabilities/default.json](mdc:src-tauri/capabilities/default.json) - 默认权限配置

## API 安全
- 所有 API 调用通过 Tauri 命令系统
- 统一的错误处理和响应格式
- CSP (Content Security Policy) 配置

## 配置管理
前端配置服务：[src/lib/services/configService.ts](mdc:src/lib/services/configService.ts)

功能：
- 配置读取
- 配置保存
- 配置验证

## Lighthouse 服务器配置
Lighthouse 服务器相关的配置在前端进行管理：
- **配置界面**: 用户通过 [src/lib/components/SettingsDialog.svelte](mdc:src/lib/components/SettingsDialog.svelte) 组件中的 "Lighthouse" 标签页进行服务器 URL 和端口的配置。
- **保存逻辑**: 当用户点击保存时，`handleSaveSettings` 函数会调用 `saveLighthouseSettings` 函数。
- **存储机制**: `saveLighthouseSettings` 函数位于 [src/lib/stores/lighthouseSettings.ts](mdc:src/lib/stores/lighthouseSettings.ts) 文件中。它会将 Lighthouse 的配置信息（包括服务器 URL, 端口, Token 等）存储在**浏览器的 Local Storage** 中，使用的键名是 `lighthouse_settings`。
- **状态管理**: 同时，这些设置也会被更新到 Svelte 的可写存储中，供应用在运行时实时访问。
