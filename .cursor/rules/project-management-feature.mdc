---
description: 
globs: 
alwaysApply: false
---
# 项目管理 (Project Management) 功能详解

本规则详细描述了应用程序中的"项目管理"功能模块，包括其前端实现、后端交互和数据库结构。

## 功能概述

项目管理模块允许用户创建、查看、编辑和删除临床研究项目及其相关信息。这包括项目的基本信息、申办方、研究药物、人员角色、入排标准、补贴方案等。

## 前端实现 (src/routes/projects/)

### 核心页面与组件

- **项目列表页:** [`src/routes/projects/+page.svelte`](mdc:src/routes/projects/+page.svelte)
  - 功能：显示项目列表，支持搜索、筛选和分页。提供创建新项目的入口，以及导航到项目详情/编辑页和**项目入排标准配置页**的入口。
  - **操作列:** 每行提供以下操作按钮：
    - **编辑:** 跳转到 [`src/routes/projects/[id]/edit/+page.svelte`](mdc:src/routes/projects/[id]/edit/+page.svelte)。
    - **配置:** 跳转到 [`src/routes/projects/[projectId]/criteria/+page.svelte`](mdc:src/routes/projects/[projectId]/criteria/+page.svelte) (参考 [@inclusion_exclusion_rules.mdc](mdc:.cursor/rules/inclusion_exclusion_rules.mdc))。
    - **文件夹:** 调用 `fileSystemService.openFolder` 打开项目路径。
    - **删除:** 调用 `projectManagementService.deleteProject` 并显示确认对话框。
- **新建项目页:** [`src/routes/projects/new/+page.svelte`](mdc:src/routes/projects/new/+page.svelte)
  - **功能:** 提供表单用于创建新的项目。
  - **实现细节:** (见下文 `ProjectForm.svelte` 及相关组件)
    - 页面加载时提供模板选择。
    - 使用核心表单组件 [`src/lib/components/project/ProjectForm.svelte`](mdc:src/lib/components/project/ProjectForm.svelte)。
    - 调用 [`src/lib/services/projectManagementService.ts`](mdc:src/lib/services/projectManagementService.ts) 的 `saveProjectWithDetails` 保存数据 (此时 `project_id` 为空)。
- **项目详情/编辑页:** (动态路由，例如 [`src/routes/projects/[id]/+page.svelte`](mdc:src/routes/projects/[id]/+page.svelte))
  - **功能:** 显示单个项目的详细信息，并允许用户编辑。
  - **加载流程:**
    - 页面加载时，从 URL 参数获取 `projectId`。
    - 调用前端服务 `projectManagementService.getProjectDetails(projectId)`。
    - 前端服务调用后端 Tauri 命令 `get_project_details` 获取项目完整数据。
    - 将获取到的 `projectDetails` 数据传递给核心表单组件 `ProjectForm.svelte` 进行渲染。
  - **编辑与保存流程:**
    - **复用表单组件:** 使用与"新建项目"页**相同**的核心表单组件 [`src/lib/components/project/ProjectForm.svelte`](mdc:src/lib/components/project/ProjectForm.svelte) 来显示和修改数据。
    - **触发保存:** 用户点击表单中的"保存项目"按钮，调用本页面 (`[id]/+page.svelte`) 中定义的保存处理函数 (`onSave` prop)。
    - **页面处理:** 保存处理函数获取更新后的 `projectDetails` (包含 `project_id`)，调用 `sanitizeProjectData` ([`src/lib/utils/projectDataUtils.ts`](mdc:src/lib/utils/projectDataUtils.ts))。
    - **复用保存服务:** 调用**相同**的前端服务方法 `projectManagementService.saveProjectWithDetails(updatedProjectDetails)`。
    - **复用后端命令:** 前端服务调用**相同**的后端 Tauri 命令 `save_project_with_details`。
    - **后端处理 (更新):** 后端仓储层 `project_management_repository.save_project_with_details` 方法检测到 `project_id` 存在，执行**更新**逻辑：在事务中删除旧的关联数据，更新 `projects` 主表记录，插入新的关联数据。
- **核心表单组件:** [`src/lib/components/project/ProjectForm.svelte`](mdc:src/lib/components/project/ProjectForm.svelte)
  - **功能:** 作为新建和编辑页面的核心 UI，负责展示和管理项目数据的输入。
  - **结构:** 通过 `activeSection` 状态控制显示以下四个子组件之一，并通过进度条和导航按钮控制流程：
      - **基本信息:** [`ProjectBasicInfo.svelte`](mdc:src/lib/components/project/ProjectBasicInfo.svelte)
        - 功能: 处理项目核心字段（名称、简称、状态等）和申办方列表 (`projectDetails.project`, `projectDetails.sponsors`)。
        - 交互: 标准输入、下拉菜单（疾病、分期、状态等）、申办方搜索与选择。
        - 数据获取: 使用 `sqliteDictionaryService` 获取下拉菜单所需的字典项。
      - **研究药物:** [`ProjectDrugs.svelte`](mdc:src/lib/components/project/ProjectDrugs.svelte)
        - 功能: 管理研究药物和药物分组 (`projectDetails.research_drugs`, `projectDetails.drug_groups`)。
        - 交互: 对话框添加/删除药物，对话框添加/删除分组。
        - 特殊逻辑: 新建项目时使用 `localStorage` 暂存数据。
      - **研究人员:** [`ProjectPersonnel.svelte`](mdc:src/lib/components/project/ProjectPersonnel.svelte)
        - 功能: 管理人员角色分配 (`projectDetails.personnel`)。
        - 交互: 单人/批量添加模式，员工搜索，角色选择。
        - 数据获取: `staffService` 获取员工，`sqliteDictionaryService` 获取角色字典。
      - **补贴信息:** [`ProjectSubsidies.svelte`](mdc:src/lib/components/project/ProjectSubsidies.svelte)
        - 功能: 管理补贴方案和具体补贴项 (`projectDetails.subsidy_schemes`, `projectDetails.subsidies`)。
        - 交互: 对话框添加/编辑/删除补贴项和方案，方案可关联补贴项。
        - 数据获取: `sqliteDictionaryService` 获取补贴类型和单位字典。

### 前端服务

前端服务遵循 [Tauri 前后端通信指南](mdc:.cursor/rules/tauri-frontend-backend-communication.mdc) 中描述的模式与后端进行交互。

- **项目管理服务:** [`src/lib/services/projectManagementService.ts`](mdc:src/lib/services/projectManagementService.ts)
  - **核心方法:**
    - `getProjectDetails(projectId)`: 调用后端 `get_project_details` 命令获取单个项目的完整信息（用于编辑页加载）。
    - `saveProjectWithDetails(projectDetails)`: 调用后端 `save_project_with_details` 命令，用于**新建** (无 `project_id`) 和**更新** (有 `project_id`) 项目。
    - `getProjectsList(...)`: 调用后端 `get_projects_list` 获取项目列表（用于列表页）。
    - `deleteProject(projectId)`: 调用后端 `pm_delete_project` 删除项目。
- **其他相关服务:**
  - [`src/lib/services/sqliteDictionaryService.ts`](mdc:src/lib/services/sqliteDictionaryService.ts): 获取各种下拉菜单所需的字典数据。
  - [`src/lib/services/staffService.ts`](mdc:src/lib/services/staffService.ts): 搜索和获取员工信息（用于人员分配）。

## 后端交互 (Tauri Commands)

后端 Tauri 命令的定义和调用遵循 [Tauri 前后端通信指南](mdc:.cursor/rules/tauri-frontend-backend-communication.mdc)。项目管理功能相关的命令主要定义在 [`src-tauri/src/commands/project_management_commands.rs`](mdc:src-tauri/src/commands/project_management_commands.rs) 中：

- `get_projects_list`: 获取项目列表（支持分页、过滤）。
- `get_project_details`: 获取单个项目及其所有关联数据 (用于编辑页加载)。
- `save_project_with_details`: **核心命令**，接收 `ProjectWithDetails` 对象，根据 `project_id` 是否存在智能处理**新建或更新**操作。调用仓储层的同名方法。
- `pm_delete_project`: 删除项目。
- `init_project_management_tables`, `check_database_tables`, `reset_database_tables`: 数据库管理相关命令。

## 后端核心逻辑 (仓储层)

- [`src-tauri/src/repositories/project_management_repository.rs`](mdc:src-tauri/src/repositories/project_management_repository.rs): 包含与数据库交互的具体实现。
  - `get_project_details(projectId)`: 查询 `projects` 表及所有关联表，组装 `ProjectWithDetails` 返回。
  - `save_project_with_details(projectDetails)`: 
    - **判断操作**: 检查 `projectDetails.project.project_id`。
    - **更新逻辑 (ID 存在)**: 启动事务 -> 删除所有旧关联记录 -> 更新 `projects` 表 -> 插入所有新关联记录 -> 提交事务。
    - **新建逻辑 (ID 不存在)**: 启动事务 -> 插入 `projects` 表 (生成新 ID) -> 插入所有关联记录 (使用新 ID) -> 提交事务。

## 数据库结构 (参考 ER 图)

sqlite数据地址："/Users/<USER>/我的文档/sqlite/peckbyte.db"

项目管理功能涉及以下主要数据库表（以 SQLite 为例）：

- **`projects`**: 存储核心项目信息。
  - `project_id` (PK), `project_name`, `project_short_name`, `project_path`, `disease_item_id` (FK), `project_stage_item_id` (FK), `project_status_item_id` (FK), `recruitment_status_item_id` (FK), `contract_case_center`, `contract_case_total`, `project_start_date`, `last_updated`.
- **`project_criteria`**: 项目入排标准规则。
  - `project_criterion_id` (PK), `project_id` (FK), `rule_definition_id`, `criterion_type`, `parameter_values`, `is_active`, `display_order`, `created_at`, `updated_at`.
- **`project_personnel_roles`**: 项目人员分配及角色。
  - `assignment_id` (PK), `project_id` (FK), `personnel_id` (FK), `role_item_id` (FK).
- **`project_sponsors`**: 项目申办方关联表。
  - `id` (PK), `project_id` (FK), `sponsor_item_id` (FK).
- **`subsidies`**: 项目补贴项明细。
  - `subsidy_item_id` (PK), `project_id` (FK), `subsidy_type_item_id` (FK), `unit_amount`, `total_units`, `unit_item_id` (FK), `total_amount`, `last_updated`.
- **`subsidy_schemes`**: 项目补贴方案。
  - `scheme_id` (PK), `project_id` (FK), `scheme_name`, `total_amount`.
- **`scheme_included_subsidies`**: 补贴方案与补贴项的关联表（多对多关系）。
  - `id` (PK), `scheme_id` (FK -> subsidy_schemes), `subsidy_item_id` (FK -> subsidies).
- **`research_drugs`**: 项目研究药物信息。
  - `drug_info_id` (PK), `project_id` (FK), `research_drug`.
- **`drug_groups`**: 项目药物分组信息。
  - `group_id` (PK), `project_id` (FK), `drug_name`, `share`.
- **`dictionary_items`**: 字典条目表，用于存储下拉选项或分类数据 (如项目阶段、状态、申办方、角色、补贴类型、单位等)。
  - `item_id` (PK), `dictionary_id` (FK), `item_key`, `item_value`, `item_description`, `status`.
- **`staff`**: (间接关联) 人员信息表，通过 `project_personnel_roles.personnel_id` 关联。

**注意:** 多个 `_item_id` 字段通常关联到 `dictionary_items` 表，用于表示分类或状态信息。
