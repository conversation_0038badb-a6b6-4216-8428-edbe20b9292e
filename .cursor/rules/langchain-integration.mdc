---
description: 
globs: 
alwaysApply: false
---
# Langchain 集成指南

## 简介
本指南总结了在 Tauri + SvelteKit 项目中整合 Langchain（JS/TS 版本）的关键步骤和最佳实践，以替代直接调用 LLM API。适用于项目中所有需要 LLM 的功能模块，包括但不限于"灵感笔记"。

## 核心实现步骤

### 1. 环境准备
- **安装依赖**: 
  ```bash
  npm install langchain @langchain/openai @langchain/core
  ```
- **导入相关模块**:
  ```typescript
  import { ChatOpenAI } from "@langchain/openai";
  import { PromptTemplate } from "@langchain/core/prompts";
  import { JsonOutputParser } from "@langchain/core/output_parsers";
  import type { Runnable } from "@langchain/core/runnables";
  ```

### 2. 实现模式
- **初始化模型**:
  ```typescript
  this.model = new ChatOpenAI({
      apiKey: this.apiKey,
      modelName: this.modelId,
      configuration: {
          baseURL: this.baseUrl, // 不需要包含 /chat/completions
          defaultHeaders: {
               'HTTP-Referer': 'http://localhost:1420',
               'X-Title': 'Your App Name'
          }
      },
      temperature: 0.1, // 调整以获得更确定性的输出
  });
  ```

- **定义提示模板**:
  ```typescript
  const prompt = new PromptTemplate({
      template: systemPromptTemplate,
      inputVariables: ["param1", "param2"], // 明确定义需要的输入变量
  });
  ```

- **构建链 (LCEL)**:
  ```typescript
  this.chain = prompt.pipe(this.model).pipe(parser);
  ```

- **调用链**:
  ```typescript
  const result = await this.chain.invoke({
      param1: value1,
      param2: value2,
  });
  ```

### 3. 动态更新与响应式设计
- **响应配置变化**: 订阅 `settings` store，当 API 密钥、URL、模型变化时更新模型实例。
- **响应上下文变化**: 当提示相关的上下文（如任务列表）变化时，重建提示和链。
- **保持接口一致**: 确保公共方法接口保持不变，这样前端组件无需修改。

## 示例实现

参考 [`src/lib/utils/noteClassifier.ts`](mdc:src/lib/utils/noteClassifier.ts) 作为项目内的实际案例，展示了如何：
- 使用 Langchain 替代直接 API 调用
- 处理用户配置（通过 Svelte store）
- 构建链式处理流程
- 适当处理错误

## 高级拓展

在基础实现之上，可以考虑以下 Langchain 高级功能扩展：

1. **RAG (检索增强生成)**: 结合项目数据（例如 Notion 数据库）作为上下文增强 LLM 输出。
2. **Agents**: 使 LLM 可以调用工具（如数据库查询），解决更复杂的任务。
3. **记忆**: 为对话添加短期或长期记忆，使交互更连贯。
4. **输出解析器**: 使用专门的解析器（如 `StructuredOutputParser`）以获取精确格式的输出。
5. **缓存**: 使用 `Runnable.withCache()` 实现结果缓存，优化性能和成本。

## 最佳实践

1. **关注点分离**: 将 LLM 调用逻辑与 UI、数据访问分离，便于维护和测试。
2. **错误处理**: 添加合理的错误捕获、重试和降级策略。
3. **安全性**: API 密钥等敏感信息最好在后端管理，或确保前端存储安全。
4. **离线备份**: 考虑本地持久化关键数据，以应对网络问题或 API 不可用。
5. **配置灵活**: 使模型、提示等可配置，便于调整或切换到其他提供商。