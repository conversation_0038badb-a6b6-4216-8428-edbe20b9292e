---
description: 
globs: 开发文档/**/*.md
alwaysApply: false
---
# 开发文档维护与参考指南

本规则确保开发过程中正确维护和使用开发文档，保持代码与文档的一致性。

## 开发文档结构

### 📚 核心开发文档
- [开发文档索引](mdc:开发文档/README.md) - 所有开发文档的入口和导航
- [项目管理开发文档](mdc:开发文档/项目管理开发文档.md) - 项目管理模块技术实现
- [项目管理仪表盘开发文档](mdc:开发文档/项目管理仪表盘.md) - 数据分析仪表盘技术设计
- [入排标准规则开发文档](mdc:开发文档/入组排除标准配置功能开发文档.md) - 规则引擎技术架构
- [自动化生成入排标准开发文档](mdc:开发文档/自动化生成入排标准JSON文件.md) - AI 功能集成实现
- [数据库结构文档](mdc:开发文档/database.md) - SQLite 数据库完整结构

### 🌐 API 文档
- [Lighthouse API 文档](mdc:开发文档/api-documentation.md) - 后端 API 接口规范
- [转诊管理 API 文档](mdc:开发文档/referral-management-api.md) - 转诊系统接口
- [GCPM API 调用实例](mdc:开发文档/GCPM%20API调用实例.md) - API 调用示例

## 🔄 开发过程中的文档维护要求

### 新增功能时
1. **创建功能开发文档**: 在 [开发文档目录](mdc:开发文档) 下创建对应的技术文档
2. **更新索引**: 在 [开发文档 README](mdc:开发文档/README.md) 中添加新文档的链接和描述
3. **关联功能手册**: 在 [功能手册](mdc:FUNCTIONAL_MANUAL.md) 中添加用户使用说明
4. **架构图更新**: 使用 mermaid 图描述新功能的架构和数据流

### 功能变更时
1. **同步更新文档**: 修改对应的开发文档以反映功能变更
2. **版本记录**: 在文档中记录变更历史和版本信息
3. **影响分析**: 更新相关模块的文档和架构图

### 文件结构变更时
1. **路径更新**: 更新文档中所有受影响的文件路径引用
2. **目录结构**: 在 [项目架构文档](mdc:开发文档/README.md) 中更新目录结构说明
3. **依赖关系**: 更新模块间的依赖关系图

## 📊 Mermaid 图表维护指南

### 推荐的图表类型

#### 系统架构图
```mermaid
graph TB
    A[前端 SvelteKit] --> B[Tauri Bridge]
    B --> C[Rust 后端]
    C --> D[SQLite 数据库]
    C --> E[外部 API]
```

#### 数据流图
```mermaid
sequenceDiagram
    participant F as 前端
    participant S as 服务层
    participant R as 仓储层
    participant D as 数据库
    
    F->>S: 调用服务方法
    S->>R: 调用仓储方法
    R->>D: 执行SQL查询
    D-->>R: 返回数据
    R-->>S: 返回结果
    S-->>F: 返回响应
```

#### 状态流转图
```mermaid
stateDiagram-v2
    [*] --> 创建中
    创建中 --> 进行中: 启动项目
    进行中 --> 暂停中: 暂停
    暂停中 --> 进行中: 恢复
    进行中 --> 已完成: 完成
    已完成 --> [*]
```

### 图表维护原则
1. **及时更新**: 代码变更时同步更新相关图表
2. **清晰标注**: 使用中文标注，保持一致的命名规范
3. **模块化**: 为复杂系统创建多个专门的图表
4. **版本控制**: 在文档中标记图表的版本和更新日期

## 🛠️ 开发流程中的文档检查点

### 代码审查时
- [ ] 检查是否有新增/修改的功能未更新文档
- [ ] 验证文档中的代码示例是否与实际代码一致
- [ ] 确认架构图反映了当前的系统设计

### 发布前
- [ ] 所有新功能都有对应的开发文档
- [ ] API 变更已在相关 API 文档中更新
- [ ] 数据库变更已在 [数据库文档](mdc:开发文档/database.md) 中记录
- [ ] Mermaid 图表与当前架构一致

## 🎯 文档质量标准

### 技术准确性
- 所有代码路径和文件引用必须正确
- API 接口定义与实际实现一致
- 数据库结构与实际表结构匹配

### 内容完整性
- 包含足够的技术细节供开发人员参考
- 提供清晰的实现指导和最佳实践
- 包含错误处理和边界情况说明

### 可维护性
- 使用一致的文档格式和结构
- 包含版本信息和更新日期
- 提供清晰的交叉引用和导航

## 🔗 文档间关联原则

### 避免重复
- 核心信息在单一文档中维护
- 其他文档通过链接引用，避免复制内容
- 工作区规则中的信息不在开发文档中重复

### 保持一致性
- 使用统一的术语和命名规范
- 保持文档结构和格式的一致性
- 确保交叉引用的准确性

### 分层组织
- 开发文档专注于技术实现细节
- 功能手册专注于用户使用说明
- API 文档专注于接口规范

## 📋 常用维护任务

### 定期检查
1. 验证所有文件路径引用的有效性
2. 检查代码示例的准确性
3. 更新过时的架构图和流程图
4. 确认外部依赖和 API 的版本信息

### 重构时的文档更新
1. 更新模块结构图
2. 修改相关的代码示例
3. 更新接口定义和数据模型
4. 调整文档间的交叉引用

通过遵循这些规则，确保开发文档始终与代码保持同步，为团队提供准确、及时、有用的技术参考。
