---
description: 
globs: 
alwaysApply: true
---
# 项目架构概览

这是一个使用 Tauri + SvelteKit 构建的桌面应用程序，结合了 Rust 后端的高性能和 SvelteKit 前端的现代化开发体验。

## 项目整体结构

### 前端部分 (src/)
#### 核心文件
- [app.html](mdc:src/app.html) - 主 HTML 模板，应用的基础页面结构
- [app.css](mdc:src/app.css) - 全局样式文件，包含 Tailwind CSS 的基础样式

#### 目录结构
- [src/routes/](mdc:src/routes) - SvelteKit 路由和页面组件
  - [+page.svelte](mdc:src/routes/+page.svelte) - 主页面
  - 遵循 SvelteKit 的文件路由约定
- [src/lib/](mdc:src/lib) - 前端共享代码
  - [services/](mdc:src/lib/services) - 前端服务层，处理与后端的通信
  - [components/](mdc:src/lib/components) - 可复用组件
  - 包含共享工具、类型定义和业务逻辑

#### 样式管理
- 使用 Tailwind CSS 进行样式设计
- 全局样式定义在 app.css 中
- 组件样式使用 Tailwind 类名内联方式
- 支持响应式设计和主题定制

### 后端部分 (src-tauri/)
#### 核心配置文件
- [src-tauri/Cargo.toml](mdc:src-tauri/Cargo.toml) - Rust 依赖和项目配置
- [src-tauri/tauri.conf.json](mdc:src-tauri/tauri.conf.json) - Tauri 配置
- [src-tauri/build.rs](mdc:src-tauri/build.rs) - 自定义构建脚本

#### 核心源码文件
- [src-tauri/src/main.rs](mdc:src-tauri/src/main.rs) - 应用程序入口点，调用 lib.rs 中的 run 函数
- [src-tauri/src/lib.rs](mdc:src-tauri/src/lib.rs) - 核心库文件，包含 Tauri 命令实现和模块导出
- [src-tauri/src/app.rs](mdc:src-tauri/src/app.rs) - 应用程序初始化，包括数据库连接和服务配置
- [src-tauri/src/error.rs](mdc:src-tauri/src/error.rs) - 统一错误处理
- [src-tauri/src/response.rs](mdc:src-tauri/src/response.rs) - API 响应结构定义
- [src-tauri/src/db.rs](mdc:src-tauri/src/db.rs) - 数据库操作核心实现
- [src-tauri/src/staff.rs](mdc:src-tauri/src/staff.rs) - 人员管理相关功能

#### 模块化结构
- [src-tauri/src/repositories/](mdc:src-tauri/src/repositories) - 数据访问层
  - sqlite/ - SQLite 数据库实现
  - mongodb/ - MongoDB 数据库实现
- [src-tauri/src/models/](mdc:src-tauri/src/models) - 数据模型定义
- [src-tauri/src/commands/](mdc:src-tauri/src/commands) - Tauri 命令实现
  - unified/ - 统一的命令处理接口
- [src-tauri/src/services/](mdc:src-tauri/src/services) - 业务逻辑服务层
  - unified/ - 统一的服务实现
- [src-tauri/src/config/](mdc:src-tauri/src/config) - 配置管理

#### 资源目录
- [src-tauri/capabilities/](mdc:src-tauri/capabilities) - Tauri 权限配置
- [src-tauri/.sqlite/](mdc:src-tauri/.sqlite) - SQLite 数据库文件
- [src-tauri/icons/](mdc:src-tauri/icons) - 应用程序图标

### 配置文件
- [svelte.config.js](mdc:svelte.config.js) - SvelteKit 配置
- [vite.config.js](mdc:vite.config.js) - Vite 构建配置
- [tailwind.config.ts](mdc:tailwind.config.ts) - Tailwind CSS 配置
- [package.json](mdc:package.json) - Node.js 依赖和脚本

### 构建输出
- [build/](mdc:build) - 编译后的前端资源
- [.svelte-kit/](mdc:.svelte-kit) - SvelteKit 生成的文件

## 开发说明

### 前端开发
#### 技术栈
- 使用 TypeScript 确保类型安全
- 使用 Tailwind CSS 进行样式设计
- SvelteKit 框架提供现代化的开发体验

#### 最佳实践
1. 组件开发
   - 保持组件小巧且功能专注
   - 可复用组件放在 `lib/components` 目录
   - 使用 TypeScript 类型定义
   
2. 路由管理
   - 遵循 SvelteKit 的文件路由约定
   - 在 `routes` 目录下组织页面结构
   
3. 代码组织
   - 共享逻辑放在 `lib` 目录
   - 服务层代码放在 `lib/services`
   - 工具函数放在 `lib/utils`

4. 样式管理
   - 优先使用 Tailwind 类名
   - 避免编写自定义 CSS
   - 遵循响应式设计原则

### 后端开发
#### 架构说明
后端使用 Tauri 和 Rust 构建，提供原生性能和功能：
- 使用仓储模式（Repository Pattern）处理数据访问，支持 SQLite 和 MongoDB
- 使用服务层（Service Layer）处理业务逻辑，通过 unified 模块提供统一接口
- 使用命令模式（Command Pattern）处理前端请求，所有命令在 lib.rs 中注册
- 统一的错误处理（error.rs）和响应格式（response.rs）
- 支持动态配置数据库类型和连接参数

#### 开发规范
1. 代码文档
   - 所有 Rust 代码都应该有适当的文档注释
   - 关键功能需要详细的使用说明

2. 最佳实践
   - 遵循 Rust 的编程范式和最佳实践
   - 使用 Tauri 命令进行前后端通信
   - 数据库操作应在专门的模块中隔离
   - 使用 Rust 的 Result 类型适当处理错误

3. 模块化原则
   - 保持模块职责单一
   - 通过统一接口提供服务
   - 确保错误处理的一致性
   - 维护良好的代码组织结构
