#!/bin/bash

echo "===== 手动图标替换指南 ====="
echo "由于未安装 ImageMagick，此脚本将指导您手动替换图标。"
echo ""

# 检查是否在项目根目录
if [ ! -f "src-tauri/tauri.conf.json" ]; then
    echo "错误: 未找到 src-tauri/tauri.conf.json 文件"
    echo "请确保您在项目根目录中运行此脚本"
    exit 1
fi

# 备份原始图标文件
echo "步骤 1: 备份原始图标文件..."
mkdir -p src-tauri/icons/original_backup
cp src-tauri/icons/32x32.png src-tauri/icons/original_backup/ 2>/dev/null || true
cp src-tauri/icons/128x128.png src-tauri/icons/original_backup/ 2>/dev/null || true
cp src-tauri/icons/<EMAIL> src-tauri/icons/original_backup/ 2>/dev/null || true
cp src-tauri/icons/icon.icns src-tauri/icons/original_backup/ 2>/dev/null || true
cp src-tauri/icons/icon.ico src-tauri/icons/original_backup/ 2>/dev/null || true
cp src-tauri/icons/icon.png src-tauri/icons/original_backup/ 2>/dev/null || true
cp static/favicon.png static/original_favicon.png 2>/dev/null || true
echo "原始图标已备份到 src-tauri/icons/original_backup/ 目录"
echo ""

# 创建必要的目录
echo "步骤 2: 创建必要的目录..."
mkdir -p src-tauri/icons/android/mipmap-mdpi
mkdir -p src-tauri/icons/android/mipmap-hdpi
mkdir -p src-tauri/icons/android/mipmap-xhdpi
mkdir -p src-tauri/icons/android/mipmap-xxhdpi
mkdir -p src-tauri/icons/android/mipmap-xxxhdpi
mkdir -p src-tauri/icons/ios
echo "目录创建完成"
echo ""

# 提供手动替换指南
echo "步骤 3: 手动替换图标"
echo "请使用在线工具将 SVG 转换为所需的图标格式："
echo "- Convertio: https://convertio.co/svg-png/"
echo "- CloudConvert: https://cloudconvert.com/svg-to-png"
echo "- SVG to PNG: https://svgtopng.com/"
echo "- RealFaviconGenerator: https://realfavicongenerator.net/ (推荐，可生成所有平台图标)"
echo ""

echo "需要替换的主要图标文件 (tauri.conf.json 中指定)："
echo "1. src-tauri/icons/32x32.png (32x32 像素)"
echo "2. src-tauri/icons/128x128.png (128x128 像素)"
echo "3. src-tauri/icons/<EMAIL> (256x256 像素)"
echo "4. src-tauri/icons/icon.ico (Windows 图标，多尺寸)"
echo "5. src-tauri/icons/icon.icns (macOS 图标)"
echo ""

echo "其他可选图标文件："
echo "- static/favicon.png (网页图标，32x32 像素)"
echo "- src-tauri/icons/icon.png (1024x1024 像素，高分辨率图标)"
echo ""

echo "平台特定图标 (可选)："
echo "- Android 图标: src-tauri/icons/android/mipmap-*/ic_launcher.png"
echo "- iOS 图标: src-tauri/icons/ios/AppIcon-*.png"
echo "- Windows 应用商店图标: src-tauri/icons/Square*.png, src-tauri/icons/StoreLogo.png"
echo ""

echo "===== 图标尺寸参考 ====="
echo "基本图标尺寸:"
echo "- 32x32.png: 32x32 像素"
echo "- 128x128.png: 128x128 像素"
echo "- <EMAIL>: 256x256 像素"
echo "- icon.png: 1024x1024 像素"
echo ""

echo "Android 图标尺寸:"
echo "- mipmap-mdpi/ic_launcher.png: 48x48 像素"
echo "- mipmap-hdpi/ic_launcher.png: 72x72 像素"
echo "- mipmap-xhdpi/ic_launcher.png: 96x96 像素"
echo "- mipmap-xxhdpi/ic_launcher.png: 144x144 像素"
echo "- mipmap-xxxhdpi/ic_launcher.png: 192x192 像素"
echo ""

echo "iOS 图标尺寸 (主要):"
echo "- <EMAIL>: 20x20 像素"
echo "- <EMAIL>: 40x40 像素"
echo "- <EMAIL>: 60x60 像素"
echo "- <EMAIL>: 29x29 像素"
echo "- <EMAIL>: 80x80 像素"
echo "- <EMAIL>: 120x120 像素"
echo "- <EMAIL>: 180x180 像素"
echo "- <EMAIL>: 76x76 像素"
echo "- <EMAIL>: 152x152 像素"
echo "- <EMAIL>: 167x167 像素"
echo "- <EMAIL>: 1024x1024 像素"
echo ""

echo "Windows 应用商店图标尺寸 (主要):"
echo "- Square44x44Logo.png: 44x44 像素"
echo "- Square71x71Logo.png: 71x71 像素"
echo "- Square150x150Logo.png: 150x150 像素"
echo "- Square310x310Logo.png: 310x310 像素"
echo "- StoreLogo.png: 50x50 像素"
echo ""

echo "===== 完成 ====="
echo "原始图标已备份，您可以开始手动替换图标文件。"
echo "请记住，只有 tauri.conf.json 中指定的图标文件是必需的，其他图标文件是可选的。"
echo ""
echo "提示: 您可以使用 RealFaviconGenerator 网站一次性生成所有平台的图标。"
