use rusqlite::Connection;
use std::path::Path;

fn main() {
    // 连接到数据库
    let db_path = Path::new("/Users/<USER>/我的文档/sqlite/peckbyte.db");
    let conn = Connection::open(db_path).expect("无法连接到数据库");
    
    // 执行简单查询
    let mut stmt = conn.prepare("SELECT id, name, gender, birthday, phone, email, position_item_id, isPI, organization, created_at, updated_at FROM staff LIMIT 5").expect("准备查询失败");
    
    let staff_iter = stmt.query_map([], |row| {
        Ok((
            row.get::<_, i64>(0)?,
            row.get::<_, String>(1)?,
            row.get::<_, String>(2)?,
            row.get::<_, String>(3)?,
            row.get::<_, String>(4)?,
            row.get::<_, String>(5)?,
            row.get::<_, i64>(6)?,
            row.get::<_, bool>(7)?,
            row.get::<_, String>(8)?,
            row.get::<_, Option<String>>(9)?,
            row.get::<_, Option<String>>(10)?
        ))
    }).expect("执行查询失败");
    
    println!("查询结果:");
    for staff in staff_iter {
        match staff {
            Ok((id, name, gender, birthday, phone, email, position_item_id, isPI, organization, created_at, updated_at)) => {
                println!("ID: {}, 姓名: {}, 性别: {}, 生日: {}, 电话: {}, 邮箱: {}, 职位ID: {}, 是否PI: {}, 组织: {}, 创建时间: {:?}, 更新时间: {:?}",
                    id, name, gender, birthday, phone, email, position_item_id, isPI, organization, created_at, updated_at);
            },
            Err(e) => println!("获取记录失败: {}", e),
        }
    }
    
    // 测试条件查询
    println!("\n测试条件查询 - 按组织查询:");
    let org = "江西省人民医院";
    let mut stmt = conn.prepare("SELECT id, name FROM staff WHERE organization LIKE ?").expect("准备条件查询失败");
    
    let staff_iter = stmt.query_map([format!("%{}%", org)], |row| {
        Ok((
            row.get::<_, i64>(0)?,
            row.get::<_, String>(1)?
        ))
    }).expect("执行条件查询失败");
    
    for staff in staff_iter {
        match staff {
            Ok((id, name)) => println!("ID: {}, 姓名: {}", id, name),
            Err(e) => println!("获取记录失败: {}", e),
        }
    }
    
    // 测试多条件查询
    println!("\n测试多条件查询 - 按组织和职位查询:");
    let position_item_id = 63; // 医生
    let mut stmt = conn.prepare("SELECT id, name FROM staff WHERE organization LIKE ? AND position_item_id = ?").expect("准备多条件查询失败");
    
    let staff_iter = stmt.query_map([format!("%{}%", org), position_item_id.to_string()], |row| {
        Ok((
            row.get::<_, i64>(0)?,
            row.get::<_, String>(1)?
        ))
    }).expect("执行多条件查询失败");
    
    for staff in staff_iter {
        match staff {
            Ok((id, name)) => println!("ID: {}, 姓名: {}", id, name),
            Err(e) => println!("获取记录失败: {}", e),
        }
    }
}
