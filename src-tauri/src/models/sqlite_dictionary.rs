use serde::{Deserialize, Serialize};
// 导入

/// 字典
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Dictionary {
    /// ID
    #[serde(skip_serializing_if = "Option::is_none")]
    pub id: Option<i64>,

    /// MongoDB ID
    #[serde(skip_serializing_if = "Option::is_none")]
    pub mongo_oid: Option<String>,

    /// 名称
    pub name: String,

    /// 描述
    #[serde(skip_serializing_if = "Option::is_none")]
    pub description: Option<String>,

    /// 类型
    #[serde(default = "default_type")]
    pub type_: String,

    /// 创建时间
    #[serde(skip_serializing_if = "Option::is_none")]
    pub created_at: Option<String>,

    /// 更新时间
    #[serde(skip_serializing_if = "Option::is_none")]
    pub updated_at: Option<String>,

    /// 版本
    #[serde(default = "default_version")]
    pub version: i32,

    /// 标签
    #[serde(skip_serializing_if = "Option::is_none")]
    pub tags: Option<Vec<String>>,

    /// 字典项
    #[serde(skip_serializing_if = "Option::is_none")]
    pub items: Option<Vec<DictionaryItem>>,
}

/// 字典项
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DictionaryItem {
    /// ID
    #[serde(skip_serializing_if = "Option::is_none")]
    pub item_id: Option<i64>,

    /// 字典 ID
    #[serde(skip_serializing_if = "Option::is_none")]
    pub dictionary_id: Option<i64>,

    /// 键
    pub key: String,

    /// 值
    pub value: String,

    /// 描述
    #[serde(skip_serializing_if = "Option::is_none")]
    pub description: Option<String>,

    /// 状态
    #[serde(default = "default_status")]
    pub status: String,
}

/// 字典查询
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DictionaryQuery {
    /// 名称
    #[serde(skip_serializing_if = "Option::is_none")]
    pub name: Option<String>,

    /// 类型
    #[serde(skip_serializing_if = "Option::is_none")]
    pub type_: Option<String>,

    /// 标签
    #[serde(skip_serializing_if = "Option::is_none")]
    pub tags: Option<Vec<String>>,
}

/// 默认类型
fn default_type() -> String {
    "list".to_string()
}

/// 默认版本
fn default_version() -> i32 {
    1
}

/// 默认状态
fn default_status() -> String {
    "active".to_string()
}

impl Dictionary {
    /// 创建新字典
    pub fn new(name: String, description: Option<String>) -> Self {
        let now = chrono::Utc::now().to_rfc3339();
        Self {
            id: None,
            mongo_oid: None,
            name,
            description,
            type_: default_type(),
            created_at: Some(now.clone()),
            updated_at: Some(now),
            version: default_version(),
            tags: None,
            items: None,
        }
    }
}

impl DictionaryItem {
    /// 创建新字典项
    pub fn new(key: String, value: String, description: Option<String>) -> Self {
        Self {
            item_id: None,
            dictionary_id: None,
            key,
            value,
            description,
            status: default_status(),
        }
    }
}
