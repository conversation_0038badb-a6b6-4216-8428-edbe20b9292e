use serde::{Deserialize, Serialize};

/// 图表数据点
#[derive(Serialize, Deserialize, Debug, <PERSON>lone)]
pub struct ChartDataPoint {
    pub name: String,
    pub value: f64,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub series: Option<String>,
}

/// 项目状态分布
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct ProjectStatusDistribution {
    pub status_name: String,
    pub project_count: i64,
}

/// 项目阶段分布
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct ProjectStageDistribution {
    pub stage_name: String,
    pub project_count: i64,
}

/// 招募状态分布
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct RecruitmentStatusDistribution {
    pub status_name: String,
    pub project_count: i64,
}

/// 疾病领域分布
#[derive(Serialize, Deserialize, Debug, <PERSON>lone)]
pub struct DiseaseDistribution {
    pub disease_name: String,
    pub project_count: i64,
}

/// 申办方项目分布
#[derive(Serialize, Deserialize, Debug, <PERSON><PERSON>)]
pub struct SponsorDistribution {
    pub sponsor_name: String,
    pub project_count: i64,
}

/// 月度新项目
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct MonthlyNewProjects {
    pub month: String,
    pub project_count: i64,
}

/// 角色分布
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct RoleDistribution {
    pub role_name: String,
    pub project_count: i64,
}

/// 人员工作负荷
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct PersonnelWorkload {
    pub personnel_id: i64,
    pub personnel_name: String,
    pub project_count: i64,
}

/// 项目补贴总览
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct ProjectSubsidyOverview {
    pub project_id: String,
    pub project_name: String,
    pub total_amount: f64,
}

/// 补贴类型分布
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct SubsidyTypeDistribution {
    pub subsidy_type_name: String,
    pub total_amount: f64,
}

/// 补贴方案数量
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct SubsidySchemeCount {
    pub project_id: String,
    pub project_name: String,
    pub scheme_count: i64,
}

/// 项目研究药物数
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct ProjectDrugCount {
    pub project_id: String,
    pub project_name: String,
    pub drug_count: i64,
}

/// 药物分组概览
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct DrugGroupOverview {
    pub project_id: String,
    pub project_name: String,
    pub group_name: String,
    pub share: i64,
}

/// 项目标准数量
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct ProjectCriteriaCount {
    pub project_id: String,
    pub project_name: String,
    pub criteria_count: i64,
}

/// 标准类型分布
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct CriteriaTypeDistribution {
    pub project_id: String,
    pub project_name: String,
    pub inclusion_count: i64,
    pub exclusion_count: i64,
}

/// 活动标准占比
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct ActiveCriteriaRatio {
    pub project_id: String,
    pub project_name: String,
    pub active_count: i64,
    pub total_count: i64,
    pub active_ratio: f64,
}

/// 仪表盘概览数据
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct DashboardOverview {
    pub active_project_count: i64,
    pub total_project_count: i64,
    pub recruiting_project_count: i64,
}

/// 日期范围参数
#[derive(Deserialize, Debug, Clone)]
pub struct DateRangeParams {
    pub start_date: Option<String>,
    pub end_date: Option<String>,
}

/// 项目状态筛选参数
#[derive(Deserialize, Debug, Clone)]
pub struct ProjectStatusFilterParams {
    pub status_ids: Option<Vec<i64>>,
}

/// 申办方筛选参数
#[derive(Deserialize, Debug, Clone)]
pub struct SponsorFilterParams {
    pub sponsor_ids: Option<Vec<i64>>,
}

/// 疾病领域筛选参数
#[derive(Deserialize, Debug, Clone)]
pub struct DiseaseFilterParams {
    pub disease_ids: Option<Vec<i64>>,
}

/// 项目阶段筛选参数
#[derive(Deserialize, Debug, Clone)]
pub struct ProjectStageFilterParams {
    pub stage_ids: Option<Vec<i64>>,
}

/// 招募状态筛选参数
#[derive(Deserialize, Debug, Clone)]
pub struct RecruitmentStatusFilterParams {
    pub recruitment_ids: Option<Vec<i64>>,
}

/// 仪表盘筛选参数
#[derive(Deserialize, Debug, Clone)]
pub struct DashboardFilterParams {
    // 扁平化的筛选参数
    pub start_date: Option<String>,
    pub end_date: Option<String>,
    pub status_ids: Option<Vec<i64>>,
    pub stage_ids: Option<Vec<i64>>,
    pub recruitment_ids: Option<Vec<i64>>,
    pub sponsor_ids: Option<Vec<i64>>,
    pub disease_ids: Option<Vec<i64>>,
}

/// 财务指标数据
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct FinancialMetrics {
    pub total_subsidy_amount: f64,
    pub average_subsidy_per_project: f64,
    pub subsidy_by_type: Vec<ChartDataPoint>,
    pub top_subsidized_projects: Vec<ProjectSubsidyOverview>,
}

/// 人员指标数据
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct PersonnelMetrics {
    pub total_personnel: i64,
    pub personnel_by_role: Vec<ChartDataPoint>,
    pub personnel_utilization: Vec<PersonnelWorkload>,
    pub pi_distribution: Vec<PIDistribution>,
}

/// PI 分布
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct PIDistribution {
    pub personnel_id: i64,
    pub personnel_name: String,
    pub is_pi: bool,
    pub project_count: i64,
}

/// 时间线指标数据
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct TimelineMetrics {
    pub projects_by_start_month: Vec<ChartDataPoint>,
    pub average_project_duration: f64,
    pub upcoming_deadlines: Vec<UpcomingDeadline>,
    pub project_phase_transitions: Vec<PhaseTransition>,
}

/// 即将到期项目
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct UpcomingDeadline {
    pub project_id: String,
    pub project_name: String,
    pub deadline_date: String,
    pub days_remaining: i64,
}

/// 项目阶段转换
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct PhaseTransition {
    pub project_id: String,
    pub project_name: String,
    pub from_stage: String,
    pub to_stage: String,
    pub transition_date: String,
}
