use serde::{Deserialize, Serialize};
// 导入

/// 文件标签颜色
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum TagColor {
    <PERSON>,
    <PERSON>,
    <PERSON><PERSON>,
    <PERSON>,
    <PERSON>genta,
    Pink,
    Red,
    Orange,
    Yellow,
    Volcano,
    Geekblue,
    Lime,
    Gold,
    #[serde(rename = "default")]
    Default,
}

/// 文件标签
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileTag {
    /// 标签名称
    pub label: String,

    /// 标签颜色
    pub color: TagColor,
}

/// 文件节点
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileNode {
    /// 节点键
    pub key: String,

    /// 节点标题
    pub title: String,

    /// 是否为叶子节点
    #[serde(skip_serializing_if = "Option::is_none")]
    pub is_leaf: Option<bool>,

    /// 子节点
    pub children: Vec<FileNode>,

    /// 标签
    #[serde(skip_serializing_if = "Option::is_none")]
    pub tags: Option<Vec<FileTag>>,

    /// 文件路径
    pub path: String,

    /// 缺失的标签
    #[serde(skip_serializing_if = "Option::is_none")]
    pub missing_tags: Option<Vec<FileTag>>,
}

/// 文件信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileInfo {
    /// 文件名
    pub name: String,

    /// 文件路径
    pub path: String,

    /// 文件大小（字节）
    pub size: u64,

    /// 修改时间
    pub modified_time: String,

    /// 标签
    #[serde(skip_serializing_if = "Option::is_none")]
    pub tags: Option<Vec<FileTag>>,
}

/// 标签规则
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TagRule {
    /// 标签名称
    pub label: String,

    /// 关键词
    pub keywords: Vec<String>,

    /// 标签颜色
    pub tag_color: TagColor,
}

/// 文件对话框选项
#[derive(Debug, Serialize, Deserialize)]
pub struct DialogOptions {
    /// 标题
    #[serde(skip_serializing_if = "Option::is_none")]
    pub title: Option<String>,

    /// 默认路径
    #[serde(skip_serializing_if = "Option::is_none")]
    pub default_path: Option<String>,

    /// 是否可以选择多个文件
    #[serde(skip_serializing_if = "Option::is_none")]
    pub multiple: Option<bool>,

    /// 是否可以选择目录
    #[serde(skip_serializing_if = "Option::is_none")]
    pub directory: Option<bool>,

    /// 文件过滤器
    #[serde(skip_serializing_if = "Option::is_none")]
    pub filters: Option<Vec<FileFilter>>,
}

/// 文件过滤器
#[derive(Debug, Serialize, Deserialize)]
pub struct FileFilter {
    /// 名称
    pub name: String,

    /// 扩展名
    pub extensions: Vec<String>,
}

/// 文件对话框结果
#[derive(Debug, Serialize, Deserialize)]
pub struct DialogResult {
    /// 是否取消
    pub canceled: bool,

    /// 选择的文件路径
    pub paths: Vec<String>,
}

/// 文件操作响应
#[derive(Debug, Serialize, Deserialize)]
pub struct FileResponse {
    /// 是否成功
    pub success: bool,

    /// 错误信息
    #[serde(skip_serializing_if = "Option::is_none")]
    pub error: Option<String>,

    /// 数据
    #[serde(skip_serializing_if = "Option::is_none")]
    pub data: Option<serde_json::Value>,
}
