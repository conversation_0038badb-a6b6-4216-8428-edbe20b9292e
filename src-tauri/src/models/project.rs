use mongodb::bson::{oid::ObjectId, DateTime, Document};
use serde::{Deserialize, Serialize};

/// 项目信息模型
#[derive(Debug, Serialize, Deserialize)]
pub struct Project {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,

    /// 项目名称
    pub project_name: String,

    /// 项目简称
    pub project_short_name: String,

    /// 项目编号
    #[serde(skip_serializing_if = "Option::is_none")]
    pub project_number: Option<String>,

    /// 申办方
    #[serde(skip_serializing_if = "Option::is_none")]
    pub sponsor: Option<Vec<String>>,

    /// 适应症
    #[serde(skip_serializing_if = "Option::is_none")]
    pub disease: Option<Vec<String>>,

    /// 研究分期
    #[serde(skip_serializing_if = "Option::is_none")]
    pub project_stage: Option<String>,

    /// 项目状态
    #[serde(skip_serializing_if = "Option::is_none")]
    pub project_status: Option<String>,

    /// 招募状态
    #[serde(skip_serializing_if = "Option::is_none")]
    pub recruitment_status: Option<String>,

    /// 创建时间
    #[serde(skip_serializing_if = "Option::is_none")]
    pub created_at: Option<DateTime>,

    /// 更新时间
    #[serde(skip_serializing_if = "Option::is_none")]
    pub updated_at: Option<DateTime>,

    /// 标签
    #[serde(skip_serializing_if = "Option::is_none")]
    pub tags: Option<Vec<String>>,

    /// 备注
    #[serde(skip_serializing_if = "Option::is_none")]
    pub remarks: Option<String>,
}

impl Project {
    /// 创建新项目
    pub fn new(
        project_name: String,
        project_short_name: String,
        project_number: Option<String>,
        sponsor: Option<Vec<String>>,
        disease: Option<Vec<String>>,
        project_stage: Option<String>,
        project_status: Option<String>,
        recruitment_status: Option<String>,
    ) -> Self {
        let now = DateTime::now();

        Self {
            id: None,
            project_name,
            project_short_name,
            project_number,
            sponsor,
            disease,
            project_stage,
            project_status,
            recruitment_status,
            created_at: Some(now),
            updated_at: Some(now),
            tags: None,
            remarks: None,
        }
    }

    /// 从 Document 转换为 Project
    pub fn from_document(doc: Document) -> Result<Self, mongodb::bson::de::Error> {
        mongodb::bson::from_document(doc)
    }

    /// 转换为 Document
    pub fn to_document(&self) -> Result<Document, mongodb::bson::ser::Error> {
        mongodb::bson::to_document(self)
    }
}

/// 项目查询参数
#[derive(Debug, Serialize, Deserialize)]
pub struct ProjectQuery {
    /// 项目名称（模糊查询）
    #[serde(skip_serializing_if = "Option::is_none")]
    pub project_name: Option<String>,

    /// 项目简称（模糊查询）
    #[serde(skip_serializing_if = "Option::is_none")]
    pub project_short_name: Option<String>,

    /// 项目编号（模糊查询）
    #[serde(skip_serializing_if = "Option::is_none")]
    pub project_number: Option<String>,

    /// 申办方
    #[serde(skip_serializing_if = "Option::is_none")]
    pub sponsor: Option<String>,

    /// 适应症
    #[serde(skip_serializing_if = "Option::is_none")]
    pub disease: Option<String>,

    /// 研究分期
    #[serde(skip_serializing_if = "Option::is_none")]
    pub project_stage: Option<String>,

    /// 项目状态
    #[serde(skip_serializing_if = "Option::is_none")]
    pub project_status: Option<String>,

    /// 招募状态
    #[serde(skip_serializing_if = "Option::is_none")]
    pub recruitment_status: Option<String>,

    /// 标签
    #[serde(skip_serializing_if = "Option::is_none")]
    pub tag: Option<String>,
}

/// 项目分页结果
#[derive(Debug, Serialize, Deserialize)]
pub struct ProjectPagination {
    /// 项目列表
    pub items: Vec<Project>,

    /// 总数
    pub total: u64,

    /// 页码
    pub page: u64,

    /// 每页数量
    pub page_size: u64,
}
