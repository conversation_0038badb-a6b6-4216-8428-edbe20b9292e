use serde::{Deserialize, Serialize};

/// 规则定义模型
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serial<PERSON>, Deserialize)]
pub struct RuleDefinition {
    pub rule_definition_id: Option<i64>,
    pub rule_name: String,
    pub rule_description: Option<String>,
    pub category: Option<String>,
    pub parameter_schema: String, // JSON string
    pub created_at: Option<String>,
    pub updated_at: Option<String>,
}

/// 项目标准模型
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ProjectCriterion {
    pub project_criterion_id: Option<i64>,
    pub project_id: String,
    pub rule_definition_id: i64,
    pub criterion_type: String,   // "inclusion" or "exclusion"
    pub parameter_values: String, // JSON string
    pub is_active: Option<bool>,
    pub display_order: Option<i64>,
    pub created_at: Option<String>,
    pub updated_at: Option<String>,
    pub criteria_group_id: Option<String>,
    pub group_operator: Option<String>, // "OR"
}

/// 创建规则定义请求
#[derive(Debug, Serialize, Deserialize)]
pub struct CreateRuleDefinitionRequest {
    pub rule_name: String,
    pub rule_description: Option<String>,
    pub category: Option<String>,
    pub parameter_schema: String, // JSON string
}

/// 更新规则定义请求
#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateRuleDefinitionRequest {
    pub rule_name: Option<String>,
    pub rule_description: Option<String>,
    pub category: Option<String>,
    pub parameter_schema: Option<String>, // JSON string
}

/// 创建项目标准请求
#[derive(Debug, Serialize, Deserialize)]
pub struct CreateProjectCriterionRequest {
    pub project_id: String,
    pub rule_definition_id: i64,
    pub criterion_type: String,   // "inclusion" or "exclusion"
    pub parameter_values: String, // JSON string
    pub is_active: Option<bool>,
    pub display_order: Option<i64>,
    pub criteria_group_id: Option<String>,
    pub group_operator: Option<String>, // "OR"
}

/// 更新项目标准请求
#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateProjectCriterionRequest {
    pub rule_definition_id: Option<i64>,
    pub criterion_type: Option<String>, // "inclusion" or "exclusion"
    pub parameter_values: Option<String>, // JSON string
    pub is_active: Option<bool>,
    pub display_order: Option<i64>,
    pub criteria_group_id: Option<String>,
    pub group_operator: Option<String>, // "OR"
}

/// 规则定义查询参数
#[derive(Debug, Serialize, Deserialize)]
pub struct RuleDefinitionQuery {
    pub category: Option<String>,
}

/// 项目标准查询参数
#[derive(Debug, Serialize, Deserialize)]
pub struct ProjectCriterionQuery {
    pub project_id: String,
    pub criterion_type: Option<String>, // "inclusion" or "exclusion"
}

/// 项目标准详情（包含规则定义信息）
#[derive(Debug, Serialize, Deserialize)]
pub struct ProjectCriterionWithRule {
    pub criterion: ProjectCriterion,
    pub rule_definition: RuleDefinition,
}
