use crate::error::AppError;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

/// Unified Project model that works with both SQLite and MongoDB
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Project {
    /// Unique identifier (UUID for SQLite, ObjectId for MongoDB)
    #[serde(skip_serializing_if = "Option::is_none")]
    pub id: Option<String>,

    /// Project name
    pub name: String,

    /// Project short name
    pub short_name: String,

    /// Project number
    #[serde(skip_serializing_if = "Option::is_none")]
    pub number: Option<String>,

    /// Project stage
    #[serde(skip_serializing_if = "Option::is_none")]
    pub stage: Option<String>,

    /// Project status
    #[serde(skip_serializing_if = "Option::is_none")]
    pub status: Option<String>,

    /// Recruitment status
    #[serde(skip_serializing_if = "Option::is_none")]
    pub recruitment_status: Option<String>,

    /// Remarks
    #[serde(skip_serializing_if = "Option::is_none")]
    pub remarks: Option<String>,

    /// Disease categories
    #[serde(skip_serializing_if = "Option::is_none")]
    pub diseases: Option<Vec<String>>,

    /// Sponsors
    #[serde(skip_serializing_if = "Option::is_none")]
    pub sponsors: Option<Vec<String>>,

    /// Tags
    #[serde(skip_serializing_if = "Option::is_none")]
    pub tags: Option<Vec<String>>,

    /// Created timestamp
    #[serde(skip_serializing_if = "Option::is_none")]
    pub created_at: Option<String>,

    /// Updated timestamp
    #[serde(skip_serializing_if = "Option::is_none")]
    pub updated_at: Option<String>,
}

impl Project {
    /// Create a new project
    pub fn new(
        name: String,
        short_name: String,
        number: Option<String>,
        stage: Option<String>,
        status: Option<String>,
        recruitment_status: Option<String>,
    ) -> Self {
        let now = chrono::Utc::now().to_rfc3339();

        Self {
            id: Some(Uuid::new_v4().to_string()),
            name,
            short_name,
            number,
            stage,
            status,
            recruitment_status,
            remarks: None,
            diseases: None,
            sponsors: None,
            tags: None,
            created_at: Some(now.clone()),
            updated_at: Some(now),
        }
    }
}

/// Project query parameters
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProjectQuery {
    /// Page number (for pagination)
    #[serde(skip_serializing_if = "Option::is_none")]
    pub page: Option<u32>,

    /// Page size (for pagination)
    #[serde(skip_serializing_if = "Option::is_none")]
    pub page_size: Option<u32>,

    /// Search term (for name, short name, number)
    #[serde(skip_serializing_if = "Option::is_none")]
    pub search: Option<String>,

    /// Project name (partial match)
    #[serde(skip_serializing_if = "Option::is_none")]
    pub name: Option<String>,

    /// Project short name (partial match)
    #[serde(skip_serializing_if = "Option::is_none")]
    pub short_name: Option<String>,

    /// Project number (partial match)
    #[serde(skip_serializing_if = "Option::is_none")]
    pub number: Option<String>,

    /// Project stage (exact match)
    #[serde(skip_serializing_if = "Option::is_none")]
    pub stage: Option<String>,

    /// Project status (exact match)
    #[serde(skip_serializing_if = "Option::is_none")]
    pub status: Option<String>,

    /// Recruitment status (exact match)
    #[serde(skip_serializing_if = "Option::is_none")]
    pub recruitment_status: Option<String>,

    /// Disease (contains)
    #[serde(skip_serializing_if = "Option::is_none")]
    pub disease: Option<String>,

    /// Sponsor (contains)
    #[serde(skip_serializing_if = "Option::is_none")]
    pub sponsor: Option<String>,

    /// Tag (contains)
    #[serde(skip_serializing_if = "Option::is_none")]
    pub tag: Option<String>,

    /// Sort field
    #[serde(skip_serializing_if = "Option::is_none")]
    pub sort_by: Option<String>,

    /// Sort order (asc or desc)
    #[serde(skip_serializing_if = "Option::is_none")]
    pub sort_order: Option<String>,
}

/// Project pagination result
#[derive(Debug, Serialize, Deserialize)]
pub struct ProjectPagination {
    /// List of projects
    pub items: Vec<Project>,

    /// Total count
    pub total: u64,

    /// Current page
    pub page: u64,

    /// Page size
    pub page_size: u64,
}

/// Project creation request
#[derive(Debug, Deserialize)]
pub struct CreateProjectRequest {
    /// Project name
    pub name: String,

    /// Project short name
    pub short_name: String,

    /// Project number
    pub number: Option<String>,

    /// Project stage
    pub stage: Option<String>,

    /// Project status
    pub status: Option<String>,

    /// Recruitment status
    pub recruitment_status: Option<String>,

    /// Remarks
    pub remarks: Option<String>,

    /// Disease categories
    pub diseases: Option<Vec<String>>,

    /// Sponsors
    pub sponsors: Option<Vec<String>>,

    /// Tags
    pub tags: Option<Vec<String>>,
}

/// Project update request
#[derive(Debug, Deserialize)]
pub struct UpdateProjectRequest {
    /// Project ID
    pub id: String,

    /// Project name
    pub name: String,

    /// Project short name
    pub short_name: String,

    /// Project number
    pub number: Option<String>,

    /// Project stage
    pub stage: Option<String>,

    /// Project status
    pub status: Option<String>,

    /// Recruitment status
    pub recruitment_status: Option<String>,

    /// Remarks
    pub remarks: Option<String>,

    /// Disease categories
    pub diseases: Option<Vec<String>>,

    /// Sponsors
    pub sponsors: Option<Vec<String>>,

    /// Tags
    pub tags: Option<Vec<String>>,
}
