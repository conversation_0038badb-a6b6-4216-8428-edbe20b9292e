use mongodb::bson::{DateTime, Document};
use serde::{Deserialize, Serialize};

/// 配置项模型
#[derive(Debug, Serialize, Deserialize)]
pub struct Config {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<mongodb::bson::oid::ObjectId>,

    /// 配置名称
    pub config_name: String,

    /// 配置内容
    #[serde(skip_serializing_if = "Option::is_none")]
    pub config: Option<serde_json::Value>,

    /// 创建时间
    #[serde(skip_serializing_if = "Option::is_none")]
    pub created_at: Option<DateTime>,

    /// 更新时间
    #[serde(skip_serializing_if = "Option::is_none")]
    pub updated_at: Option<DateTime>,
}

impl Config {
    /// 创建新配置
    pub fn new(config_name: String, config: Option<serde_json::Value>) -> Self {
        let now = DateTime::now();

        Self {
            id: None,
            config_name,
            config,
            created_at: Some(now),
            updated_at: Some(now),
        }
    }

    /// 从 Document 转换为 Config
    pub fn from_document(doc: Document) -> Result<Self, mongodb::bson::de::Error> {
        mongodb::bson::from_document(doc)
    }

    /// 转换为 Document
    pub fn to_document(&self) -> Result<Document, mongodb::bson::ser::Error> {
        mongodb::bson::to_document(self)
    }
}

/// 系统配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemConfig {
    /// 临床研究文件夹路径
    #[serde(skip_serializing_if = "Option::is_none")]
    pub clinical_research_folder_path: Option<String>,

    /// Notion API 密钥
    #[serde(skip_serializing_if = "Option::is_none")]
    pub notion_api_key: Option<String>,

    /// Notion 数据库 ID
    #[serde(skip_serializing_if = "Option::is_none")]
    pub notion_database_id: Option<String>,

    /// 其他配置项
    #[serde(flatten, skip_serializing_if = "Option::is_none")]
    pub other: Option<serde_json::Map<String, serde_json::Value>>,
}

/// 配置项列表
#[derive(Debug, Serialize, Deserialize)]
pub struct ConfigList {
    /// 配置名称
    pub config_name: String,

    /// 配置列表
    pub config: Vec<String>,
}

/// 配置查询参数
#[derive(Debug, Serialize, Deserialize)]
pub struct ConfigQuery {
    /// 配置名称
    pub config_name: String,
}

/// 配置保存请求
#[derive(Debug, Serialize, Deserialize)]
pub struct SaveConfigRequest {
    /// 配置名称
    pub config_name: String,

    /// 配置内容
    pub config: serde_json::Value,
}

/// 配置响应
#[derive(Debug, Serialize, Deserialize)]
pub struct ConfigResponse {
    /// 是否成功
    pub success: bool,

    /// 错误信息
    #[serde(skip_serializing_if = "Option::is_none")]
    pub error: Option<String>,

    /// 数据
    #[serde(skip_serializing_if = "Option::is_none")]
    pub data: Option<serde_json::Value>,
}
