use crate::db::get_connection;
use crate::error::AppError;
use crate::response::AppResponse;
use chrono::Utc;
use rusqlite::{params, Connection, Result as SqliteResult};
use serde::{Deserialize, Serialize};
use tauri::command;

#[derive(Debug, Serialize, Deserialize)]
pub struct Staff {
    pub id: Option<i64>,
    pub name: String,
    pub gender: String,
    pub birthday: String,
    pub phone: String,
    pub email: String,
    pub position_item_id: i64,
    pub isPI: bool,
    pub organization: String,
    pub created_at: Option<String>,
    pub updated_at: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct StaffQuery {
    pub name: Option<String>,
    pub gender: Option<String>,
    pub position_item_id: Option<i64>,
    pub organization: Option<String>,
    pub isPI: Option<bool>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct DictionaryItem {
    pub id: i64,
    pub key: String,
    pub value: String,
}

// 获取所有人员
#[command]
pub fn get_all_staff() -> AppResponse<Vec<Staff>> {
    match get_connection() {
        Ok(conn) => match get_all_staff_internal(&conn) {
            Ok(staff) => AppResponse::success(staff),
            Err(e) => AppResponse::error(format!("获取人员失败: {}", e)),
        },
        Err(e) => AppResponse::error(format!("数据库连接失败: {}", e)),
    }
}

// 查询人员
#[command]
pub fn query_staff(query: StaffQuery) -> AppResponse<Vec<Staff>> {
    println!("开始查询人员: {:?}", query);
    match get_connection() {
        Ok(conn) => {
            println!("数据库连接成功");
            match query_staff_internal(&conn, query) {
                Ok(staff) => {
                    println!("查询成功，找到 {} 条记录", staff.len());
                    AppResponse::success(staff)
                }
                Err(e) => {
                    println!("查询失败: {}", e);
                    AppResponse::error(format!("查询人员失败: {}", e))
                }
            }
        }
        Err(e) => {
            println!("数据库连接失败: {}", e);
            AppResponse::error(format!("数据库连接失败: {}", e))
        }
    }
}

// 根据ID获取人员
#[command]
pub fn get_staff_by_id(id: i64) -> AppResponse<Staff> {
    match get_connection() {
        Ok(conn) => match get_staff_by_id_internal(&conn, id) {
            Ok(Some(staff)) => AppResponse::success(staff),
            Ok(None) => AppResponse::error(format!("人员不存在: ID = {}", id)),
            Err(e) => AppResponse::error(format!("获取人员失败: {}", e)),
        },
        Err(e) => AppResponse::error(format!("数据库连接失败: {}", e)),
    }
}

// 创建人员
#[command]
pub fn create_staff(staff: Staff) -> AppResponse<i64> {
    match get_connection() {
        Ok(conn) => match create_staff_internal(&conn, staff) {
            Ok(id) => AppResponse::success(id),
            Err(e) => AppResponse::error(format!("创建人员失败: {}", e)),
        },
        Err(e) => AppResponse::error(format!("数据库连接失败: {}", e)),
    }
}

// 更新人员
#[command]
pub fn update_staff(id: i64, staff: Staff) -> AppResponse<bool> {
    match get_connection() {
        Ok(conn) => match update_staff_internal(&conn, id, staff) {
            Ok(_) => AppResponse::success(true),
            Err(e) => AppResponse::error(format!("更新人员失败: {}", e)),
        },
        Err(e) => AppResponse::error(format!("数据库连接失败: {}", e)),
    }
}

// 删除人员
#[command]
pub fn delete_staff(id: i64) -> AppResponse<bool> {
    match get_connection() {
        Ok(conn) => match delete_staff_internal(&conn, id) {
            Ok(_) => AppResponse::success(true),
            Err(e) => AppResponse::error(format!("删除人员失败: {}", e)),
        },
        Err(e) => AppResponse::error(format!("数据库连接失败: {}", e)),
    }
}

// 获取所有用户角色
#[command]
pub fn get_positions() -> AppResponse<Vec<DictionaryItem>> {
    match get_connection() {
        Ok(conn) => match get_positions_internal(&conn) {
            Ok(positions) => AppResponse::success(positions),
            Err(e) => AppResponse::error(format!("获取用户角色失败: {}", e)),
        },
        Err(e) => AppResponse::error(format!("数据库连接失败: {}", e)),
    }
}

// 内部函数实现

fn get_all_staff_internal(conn: &Connection) -> Result<Vec<Staff>, AppError> {
    let mut stmt = conn.prepare(
        "SELECT id, name, gender, birthday, phone, email, position_item_id, isPI, organization, created_at, updated_at
         FROM staff
         ORDER BY name"
    )?;

    let staff_iter = stmt.query_map([], |row| {
        Ok(Staff {
            id: Some(row.get(0)?),
            name: row.get(1)?,
            gender: row.get(2)?,
            birthday: row.get(3)?,
            phone: row.get(4)?,
            email: row.get(5)?,
            position_item_id: row.get(6)?,
            isPI: row.get(7)?,
            organization: row.get(8)?,
            created_at: row.get(9)?,
            updated_at: row.get(10)?,
        })
    })?;

    let mut staff = Vec::new();
    for s in staff_iter {
        staff.push(s?);
    }

    Ok(staff)
}

fn query_staff_internal(conn: &Connection, query: StaffQuery) -> Result<Vec<Staff>, AppError> {
    let mut conditions = Vec::new();
    let mut params: Vec<Box<dyn rusqlite::ToSql>> = Vec::new();

    if let Some(name) = query.name {
        if !name.is_empty() {
            conditions.push("name LIKE ?");
            params.push(Box::new(format!("%{}%", name)));
        }
    }

    if let Some(gender) = query.gender {
        if !gender.is_empty() {
            conditions.push("gender = ?");
            params.push(Box::new(gender));
        }
    }

    if let Some(position_item_id) = query.position_item_id {
        if position_item_id > 0 {
            conditions.push("position_item_id = ?");
            params.push(Box::new(position_item_id));
        }
    }

    if let Some(organization) = query.organization {
        if !organization.is_empty() {
            conditions.push("organization LIKE ?");
            params.push(Box::new(format!("%{}%", organization)));
        }
    }

    if let Some(isPI) = query.isPI {
        conditions.push("isPI = ?");
        // 将布尔值转换为整数，因为数据库中isPI是INTEGER类型
        params.push(Box::new(if isPI { 1 } else { 0 }));
    }

    let mut sql = String::from(
        "SELECT id, name, gender, birthday, phone, email, position_item_id, isPI, organization, created_at, updated_at
         FROM staff"
    );

    if !conditions.is_empty() {
        sql.push_str(" WHERE ");
        sql.push_str(&conditions.join(" AND "));
    }

    sql.push_str(" ORDER BY name");

    // 打印SQL和参数，用于调试
    println!("SQL: {}", sql);
    println!("条件数量: {}", conditions.len());

    let mut stmt = conn.prepare(&sql)?;

    let param_refs: Vec<&dyn rusqlite::ToSql> = params.iter().map(|p| p.as_ref()).collect();

    let staff_iter = stmt.query_map(rusqlite::params_from_iter(param_refs), |row| {
        Ok(Staff {
            id: Some(row.get(0)?),
            name: row.get(1)?,
            gender: row.get(2)?,
            birthday: row.get(3)?,
            phone: row.get(4)?,
            email: row.get(5)?,
            position_item_id: row.get(6)?,
            isPI: row.get(7)?,
            organization: row.get(8)?,
            created_at: row.get(9)?,
            updated_at: row.get(10)?,
        })
    })?;

    let mut staff = Vec::new();
    for s in staff_iter {
        staff.push(s?);
    }

    Ok(staff)
}

fn get_staff_by_id_internal(conn: &Connection, id: i64) -> Result<Option<Staff>, AppError> {
    let mut stmt = conn.prepare(
        "SELECT id, name, gender, birthday, phone, email, position_item_id, isPI, organization, created_at, updated_at
         FROM staff
         WHERE id = ?"
    )?;

    let staff = stmt.query_row(params![id], |row| {
        Ok(Staff {
            id: Some(row.get(0)?),
            name: row.get(1)?,
            gender: row.get(2)?,
            birthday: row.get(3)?,
            phone: row.get(4)?,
            email: row.get(5)?,
            position_item_id: row.get(6)?,
            isPI: row.get(7)?,
            organization: row.get(8)?,
            created_at: row.get(9)?,
            updated_at: row.get(10)?,
        })
    });

    match staff {
        Ok(s) => Ok(Some(s)),
        Err(rusqlite::Error::QueryReturnedNoRows) => Ok(None),
        Err(e) => Err(AppError::from(e)),
    }
}

fn create_staff_internal(conn: &Connection, staff: Staff) -> Result<i64, AppError> {
    let now = Utc::now().to_rfc3339();

    conn.execute(
        "INSERT INTO staff (name, gender, birthday, phone, email, position_item_id, isPI, organization, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
        params![
            staff.name,
            staff.gender,
            staff.birthday,
            staff.phone,
            staff.email,
            staff.position_item_id,
            staff.isPI,
            staff.organization,
            now,
            now
        ],
    )?;

    Ok(conn.last_insert_rowid())
}

fn update_staff_internal(conn: &Connection, id: i64, staff: Staff) -> Result<(), AppError> {
    let now = Utc::now().to_rfc3339();

    let rows_affected = conn.execute(
        "UPDATE staff
         SET name = ?, gender = ?, birthday = ?, phone = ?, email = ?, position_item_id = ?, isPI = ?, organization = ?, updated_at = ?
         WHERE id = ?",
        params![
            staff.name,
            staff.gender,
            staff.birthday,
            staff.phone,
            staff.email,
            staff.position_item_id,
            staff.isPI,
            staff.organization,
            now,
            id
        ],
    )?;

    if rows_affected == 0 {
        return Err(AppError::NotFound(format!("人员不存在: ID = {}", id)));
    }

    Ok(())
}

fn delete_staff_internal(conn: &Connection, id: i64) -> Result<(), AppError> {
    let rows_affected = conn.execute("DELETE FROM staff WHERE id = ?", params![id])?;

    if rows_affected == 0 {
        return Err(AppError::NotFound(format!("人员不存在: ID = {}", id)));
    }

    Ok(())
}

fn get_positions_internal(conn: &Connection) -> Result<Vec<DictionaryItem>, AppError> {
    // 用户角色信息存储在 dictionary_items 表中
    let mut stmt = conn.prepare(
        "SELECT item_id, item_key, item_value
         FROM dictionary_items
         WHERE dictionary_id = (SELECT id FROM dictionaries WHERE name = '用户角色' LIMIT 1)
         AND status = 'active'
         ORDER BY item_value",
    )?;

    let positions_iter = stmt.query_map([], |row| {
        Ok(DictionaryItem {
            id: row.get(0)?,
            key: row.get(1)?,
            value: row.get(2)?,
        })
    })?;

    let mut positions = Vec::new();
    for p in positions_iter {
        positions.push(p?);
    }

    Ok(positions)
}

// 初始化数据库表
pub fn init_staff_table(conn: &Connection) -> SqliteResult<()> {
    conn.execute(
        "CREATE TABLE IF NOT EXISTS staff (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            gender TEXT NOT NULL,
            birthday TEXT NOT NULL,
            phone TEXT NOT NULL,
            email TEXT NOT NULL,
            position_item_id INTEGER NOT NULL,
            isPI BOOLEAN NOT NULL DEFAULT 0,
            organization TEXT NOT NULL,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            FOREIGN KEY (position_item_id) REFERENCES dictionary_items (id)
        )",
        [],
    )?;

    Ok(())
}
