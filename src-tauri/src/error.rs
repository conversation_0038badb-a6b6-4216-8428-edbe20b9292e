use serde::{Deserialize, Serialize};
use std::error::Error;
use std::fmt;

/// Application error types
#[derive(Debug)]
pub enum AppError {
    /// Database errors (SQLite)
    DatabaseError(String),

    /// MongoDB errors
    MongoError(String),

    /// Entity not found
    NotFound(String),

    /// Invalid input data
    InvalidInput(String),

    /// API errors (e.g., Notion API)
    ApiError(String),

    /// Configuration errors
    ConfigError(String),

    /// File system errors
    FileSystemError(String),

    /// Other errors
    Other(String),
}

impl fmt::Display for AppError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            AppError::DatabaseError(e) => write!(f, "Database error: {}", e),
            AppError::MongoError(e) => write!(f, "MongoDB error: {}", e),
            AppError::NotFound(msg) => write!(f, "Not found: {}", msg),
            AppError::InvalidInput(msg) => write!(f, "Invalid input: {}", msg),
            AppError::ApiError(msg) => write!(f, "API error: {}", msg),
            AppError::ConfigError(msg) => write!(f, "Configuration error: {}", msg),
            AppError::FileSystemError(msg) => write!(f, "File system error: {}", msg),
            AppError::Other(msg) => write!(f, "Error: {}", msg),
        }
    }
}

impl Error for AppError {}

// Implement conversions from common error types
impl From<rusqlite::Error> for AppError {
    fn from(error: rusqlite::Error) -> Self {
        AppError::DatabaseError(error.to_string())
    }
}

impl From<mongodb::error::Error> for AppError {
    fn from(error: mongodb::error::Error) -> Self {
        AppError::MongoError(error.to_string())
    }
}

impl From<mongodb::bson::oid::Error> for AppError {
    fn from(error: mongodb::bson::oid::Error) -> Self {
        AppError::MongoError(error.to_string())
    }
}

impl From<mongodb::bson::ser::Error> for AppError {
    fn from(error: mongodb::bson::ser::Error) -> Self {
        AppError::MongoError(format!("BSON serialization error: {}", error))
    }
}

impl From<mongodb::bson::de::Error> for AppError {
    fn from(error: mongodb::bson::de::Error) -> Self {
        AppError::MongoError(format!("BSON deserialization error: {}", error))
    }
}

impl From<mongodb::bson::document::ValueAccessError> for AppError {
    fn from(error: mongodb::bson::document::ValueAccessError) -> Self {
        AppError::MongoError(format!("BSON value access error: {}", error))
    }
}

impl From<std::io::Error> for AppError {
    fn from(error: std::io::Error) -> Self {
        AppError::FileSystemError(error.to_string())
    }
}

impl From<reqwest::Error> for AppError {
    fn from(error: reqwest::Error) -> Self {
        AppError::ApiError(error.to_string())
    }
}

impl From<String> for AppError {
    fn from(error: String) -> Self {
        AppError::Other(error)
    }
}

impl From<&str> for AppError {
    fn from(error: &str) -> Self {
        AppError::Other(error.to_string())
    }
}

/// API response wrapper for consistent error handling
#[derive(Debug, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    /// Whether the operation was successful
    pub success: bool,

    /// The data returned (if successful)
    #[serde(skip_serializing_if = "Option::is_none")]
    pub data: Option<T>,

    /// Error message (if unsuccessful)
    #[serde(skip_serializing_if = "Option::is_none")]
    pub error: Option<String>,
}

impl<T> ApiResponse<T> {
    /// Create a successful response with data
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            data: Some(data),
            error: None,
        }
    }

    /// Create a successful response without data
    pub fn ok() -> ApiResponse<()> {
        ApiResponse {
            success: true,
            data: None,
            error: None,
        }
    }

    /// Create an error response
    pub fn error<E: ToString>(error: E) -> Self {
        Self {
            success: false,
            data: None,
            error: Some(error.to_string()),
        }
    }
}

/// Convert AppError to ApiResponse
impl<T> From<AppError> for ApiResponse<T> {
    fn from(error: AppError) -> Self {
        Self {
            success: false,
            data: None,
            error: Some(error.to_string()),
        }
    }
}
