use serde::Serialize;

// 通用响应结构
#[derive(Debug, Serialize)]
pub struct AppResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub error: Option<String>,
}

impl<T: Serialize> AppResponse<T> {
    // 创建成功响应
    pub fn success(data: T) -> Self {
        AppResponse {
            success: true,
            data: Some(data),
            error: None,
        }
    }

    // 创建错误响应
    pub fn error(message: String) -> Self {
        AppResponse {
            success: false,
            data: None,
            error: Some(message),
        }
    }
}
