use crate::config::database::SqliteConnection;
use crate::error::AppError;
use crate::models::unified::project::{Project, ProjectPagination, ProjectQuery};
use crate::repositories::project_repository::ProjectRepository;
use async_trait::async_trait;
use chrono::Utc;
use rusqlite::{params, Connection, OptionalExtension};
use std::sync::Arc;
use uuid::Uuid;

/// SQLite implementation of the project repository
pub struct SqliteProjectRepository {
    /// SQLite connection
    db: Arc<SqliteConnection>,
}

impl SqliteProjectRepository {
    /// Create a new SQLite project repository
    pub fn new(db: Arc<SqliteConnection>) -> Self {
        Self { db }
    }

    /// Initialize the projects table if it doesn't exist
    pub fn init_table(&self) -> Result<(), AppError> {
        let conn = self.db.get()?;

        conn.execute(
            "CREATE TABLE IF NOT EXISTS projects (
                project_id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                short_name TEXT NOT NULL,
                number TEXT,
                stage TEXT,
                status TEXT,
                recruitment_status TEXT,
                remarks TEXT,
                diseases TEXT,
                sponsors TEXT,
                tags TEXT,
                created_at TEXT,
                updated_at TEXT
            )",
            [],
        )?;

        Ok(())
    }

    /// Convert a row to a Project
    fn row_to_project(&self, row: &rusqlite::Row) -> Result<Project, rusqlite::Error> {
        Ok(Project {
            id: Some(row.get(0)?),
            name: row.get(1)?,
            short_name: row.get(2)?,
            number: row.get(3)?,
            stage: row.get(4)?,
            status: row.get(5)?,
            recruitment_status: row.get(6)?,
            remarks: row.get(7)?,
            diseases: row
                .get::<_, Option<String>>(8)?
                .map(|s| s.split(',').map(|s| s.to_string()).collect()),
            sponsors: row
                .get::<_, Option<String>>(9)?
                .map(|s| s.split(',').map(|s| s.to_string()).collect()),
            tags: row
                .get::<_, Option<String>>(10)?
                .map(|s| s.split(',').map(|s| s.to_string()).collect()),
            created_at: row.get(11)?,
            updated_at: row.get(12)?,
        })
    }
}

#[async_trait]
impl ProjectRepository for SqliteProjectRepository {
    async fn get_all(&self) -> Result<Vec<Project>, AppError> {
        let conn = self.db.get()?;

        let mut stmt = conn.prepare("SELECT * FROM projects")?;
        let rows = stmt.query_map([], |row| self.row_to_project(row))?;

        let mut projects = Vec::new();
        for project_result in rows {
            projects.push(project_result?);
        }

        Ok(projects)
    }

    async fn get_by_id(&self, id: &str) -> Result<Option<Project>, AppError> {
        let conn = self.db.get()?;

        let mut stmt = conn.prepare("SELECT * FROM projects WHERE project_id = ?")?;

        let result = stmt
            .query_row(params![id], |row| self.row_to_project(row))
            .optional()?;

        Ok(result)
    }

    async fn query(&self, query: &ProjectQuery) -> Result<ProjectPagination, AppError> {
        let conn = self.db.get()?;

        // Build query
        let mut sql = String::from("SELECT * FROM projects");
        let mut conditions = Vec::new();
        let mut params = Vec::new();

        // Add search conditions
        if let Some(search) = &query.search {
            if !search.is_empty() {
                conditions.push("(name LIKE ? OR short_name LIKE ? OR number LIKE ?)");
                let search_pattern = format!("%{}%", search);
                params.push(search_pattern.clone());
                params.push(search_pattern.clone());
                params.push(search_pattern);
            }
        }

        // Add specific field conditions
        if let Some(name) = &query.name {
            conditions.push("name LIKE ?");
            params.push(format!("%{}%", name));
        }

        if let Some(short_name) = &query.short_name {
            conditions.push("short_name LIKE ?");
            params.push(format!("%{}%", short_name));
        }

        if let Some(number) = &query.number {
            conditions.push("number LIKE ?");
            params.push(format!("%{}%", number));
        }

        if let Some(stage) = &query.stage {
            conditions.push("stage = ?");
            params.push(stage.clone());
        }

        if let Some(status) = &query.status {
            conditions.push("status = ?");
            params.push(status.clone());
        }

        if let Some(recruitment_status) = &query.recruitment_status {
            conditions.push("recruitment_status = ?");
            params.push(recruitment_status.clone());
        }

        if let Some(disease) = &query.disease {
            conditions.push("diseases LIKE ?");
            params.push(format!("%{}%", disease));
        }

        if let Some(sponsor) = &query.sponsor {
            conditions.push("sponsors LIKE ?");
            params.push(format!("%{}%", sponsor));
        }

        if let Some(tag) = &query.tag {
            conditions.push("tags LIKE ?");
            params.push(format!("%{}%", tag));
        }

        // Add WHERE clause
        if !conditions.is_empty() {
            sql.push_str(" WHERE ");
            sql.push_str(&conditions.join(" AND "));
        }

        // Count total
        let where_clause = if conditions.is_empty() {
            String::new()
        } else {
            format!(" WHERE {}", conditions.join(" AND "))
        };
        let count_sql = format!("SELECT COUNT(*) FROM projects{}", where_clause);

        let mut count_stmt = conn.prepare(&count_sql)?;
        let param_refs: Vec<&dyn rusqlite::ToSql> =
            params.iter().map(|p| p as &dyn rusqlite::ToSql).collect();
        let total: u64 = count_stmt.query_row(param_refs.as_slice(), |row| row.get(0))?;

        // Add sorting
        if let Some(sort_by) = &query.sort_by {
            sql.push_str(" ORDER BY ");
            sql.push_str(sort_by);

            if let Some(sort_order) = &query.sort_order {
                if sort_order.to_uppercase() == "DESC" {
                    sql.push_str(" DESC");
                } else {
                    sql.push_str(" ASC");
                }
            }
        } else {
            sql.push_str(" ORDER BY created_at DESC");
        }

        // Add pagination
        let page = query.page.unwrap_or(1);
        let page_size = query.page_size.unwrap_or(10);
        let offset = (page - 1) * page_size;
        sql.push_str(&format!(" LIMIT {} OFFSET {}", page_size, offset));

        // Execute query
        let mut stmt = conn.prepare(&sql)?;
        let rows = stmt.query_map(param_refs.as_slice(), |row| self.row_to_project(row))?;

        let mut items = Vec::new();
        for project_result in rows {
            items.push(project_result?);
        }

        Ok(ProjectPagination {
            items,
            total,
            page: page as u64,
            page_size: page_size as u64,
        })
    }

    async fn create(&self, project: &Project) -> Result<String, AppError> {
        let conn = self.db.get()?;

        // Generate ID if not provided
        let project_id = match &project.id {
            Some(id) => id.clone(),
            None => Uuid::new_v4().to_string(),
        };

        // Process array fields
        let diseases_str = project.diseases.as_ref().map(|d| d.join(","));
        let sponsors_str = project.sponsors.as_ref().map(|s| s.join(","));
        let tags_str = project.tags.as_ref().map(|t| t.join(","));

        // Get current time
        let now = Utc::now().to_rfc3339();

        conn.execute(
            "INSERT INTO projects (
                project_id, name, short_name, number,
                stage, status, recruitment_status, remarks,
                diseases, sponsors, tags, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
            params![
                project_id,
                project.name,
                project.short_name,
                project.number,
                project.stage,
                project.status,
                project.recruitment_status,
                project.remarks,
                diseases_str,
                sponsors_str,
                tags_str,
                project.created_at.as_ref().unwrap_or(&now),
                project.updated_at.as_ref().unwrap_or(&now)
            ],
        )?;

        Ok(project_id)
    }

    async fn update(&self, id: &str, project: &Project) -> Result<bool, AppError> {
        let conn = self.db.get()?;

        // Process array fields
        let diseases_str = project.diseases.as_ref().map(|d| d.join(","));
        let sponsors_str = project.sponsors.as_ref().map(|s| s.join(","));
        let tags_str = project.tags.as_ref().map(|t| t.join(","));

        // Get current time
        let now = Utc::now().to_rfc3339();

        let result = conn.execute(
            "UPDATE projects SET
                name = ?,
                short_name = ?,
                number = ?,
                stage = ?,
                status = ?,
                recruitment_status = ?,
                remarks = ?,
                diseases = ?,
                sponsors = ?,
                tags = ?,
                updated_at = ?
            WHERE project_id = ?",
            params![
                project.name,
                project.short_name,
                project.number,
                project.stage,
                project.status,
                project.recruitment_status,
                project.remarks,
                diseases_str,
                sponsors_str,
                tags_str,
                now,
                id
            ],
        )?;

        Ok(result > 0)
    }

    async fn delete(&self, id: &str) -> Result<bool, AppError> {
        let conn = self.db.get()?;

        let result = conn.execute("DELETE FROM projects WHERE project_id = ?", params![id])?;

        Ok(result > 0)
    }
}
