use crate::error::AppError;
use crate::models::unified::project::{Project, ProjectPagination, ProjectQuery};
use crate::repositories::project_repository::ProjectRepository;
use async_trait::async_trait;
use chrono::Utc;
use futures::stream::TryStreamExt;
use mongodb::{
    bson::{doc, oid::ObjectId, DateTime, Document},
    options::FindOptions,
    Client, Collection,
};
use std::sync::Arc;

/// MongoDB implementation of the project repository
pub struct MongoProjectRepository {
    /// MongoDB collection
    collection: Collection<Document>,
}

impl MongoProjectRepository {
    /// Create a new MongoDB project repository
    pub fn new(client: &Client, db_name: &str, collection_name: &str) -> Self {
        let db = client.database(db_name);
        let collection = db.collection::<Document>(collection_name);

        Self { collection }
    }

    /// Convert a MongoDB document to a Project
    fn document_to_project(&self, doc: Document) -> Result<Project, AppError> {
        let id = doc.get_object_id("_id")?.to_hex();

        let created_at = doc.get_datetime("created_at").map(|dt| dt.to_string()).ok();

        let updated_at = doc.get_datetime("updated_at").map(|dt| dt.to_string()).ok();

        Ok(Project {
            id: Some(id),
            name: doc.get_str("project_name")?.to_string(),
            short_name: doc.get_str("project_short_name")?.to_string(),
            number: doc.get_str("project_number").ok().map(|s| s.to_string()),
            stage: doc.get_str("project_stage").ok().map(|s| s.to_string()),
            status: doc.get_str("project_status").ok().map(|s| s.to_string()),
            recruitment_status: doc
                .get_str("recruitment_status")
                .ok()
                .map(|s| s.to_string()),
            remarks: doc.get_str("remarks").ok().map(|s| s.to_string()),
            diseases: self.get_string_array(&doc, "disease"),
            sponsors: self.get_string_array(&doc, "sponsor"),
            tags: self.get_string_array(&doc, "tags"),
            created_at,
            updated_at,
        })
    }

    /// Convert a Project to a MongoDB document
    fn project_to_document(&self, project: &Project) -> Document {
        let mut doc = Document::new();

        // Add fields
        doc.insert("project_name", &project.name);
        doc.insert("project_short_name", &project.short_name);

        if let Some(number) = &project.number {
            doc.insert("project_number", number);
        }

        if let Some(stage) = &project.stage {
            doc.insert("project_stage", stage);
        }

        if let Some(status) = &project.status {
            doc.insert("project_status", status);
        }

        if let Some(recruitment_status) = &project.recruitment_status {
            doc.insert("recruitment_status", recruitment_status);
        }

        if let Some(remarks) = &project.remarks {
            doc.insert("remarks", remarks);
        }

        if let Some(diseases) = &project.diseases {
            doc.insert("disease", diseases);
        }

        if let Some(sponsors) = &project.sponsors {
            doc.insert("sponsor", sponsors);
        }

        if let Some(tags) = &project.tags {
            doc.insert("tags", tags);
        }

        // Add timestamps
        let now = DateTime::now();

        if let Some(created_at) = &project.created_at {
            // Try to parse the RFC3339 string
            if let Ok(dt) = chrono::DateTime::parse_from_rfc3339(created_at) {
                let millis = dt.with_timezone(&chrono::Utc).timestamp_millis();
                doc.insert("created_at", DateTime::from_millis(millis));
            } else {
                doc.insert("created_at", now);
            }
        } else {
            doc.insert("created_at", now);
        }

        if let Some(updated_at) = &project.updated_at {
            // Try to parse the RFC3339 string
            if let Ok(dt) = chrono::DateTime::parse_from_rfc3339(updated_at) {
                let millis = dt.with_timezone(&chrono::Utc).timestamp_millis();
                doc.insert("updated_at", DateTime::from_millis(millis));
            } else {
                doc.insert("updated_at", now);
            }
        } else {
            doc.insert("updated_at", now);
        }

        doc
    }

    /// Get a string array from a document
    fn get_string_array(&self, doc: &Document, field: &str) -> Option<Vec<String>> {
        doc.get_array(field).ok().map(|arr| {
            arr.iter()
                .filter_map(|v| v.as_str().map(|s| s.to_string()))
                .collect()
        })
    }
}

#[async_trait]
impl ProjectRepository for MongoProjectRepository {
    async fn get_all(&self) -> Result<Vec<Project>, AppError> {
        let cursor = self.collection.find(None, None).await?;
        let documents: Vec<Document> = cursor.try_collect().await?;

        let mut projects = Vec::new();
        for doc in documents {
            match self.document_to_project(doc) {
                Ok(project) => projects.push(project),
                Err(e) => log::error!("Failed to parse project document: {}", e),
            }
        }

        Ok(projects)
    }

    async fn get_by_id(&self, id: &str) -> Result<Option<Project>, AppError> {
        let object_id = ObjectId::parse_str(id)?;
        let filter = doc! { "_id": object_id };

        let result = self.collection.find_one(filter, None).await?;

        match result {
            Some(doc) => {
                let project = self.document_to_project(doc)?;
                Ok(Some(project))
            }
            None => Ok(None),
        }
    }

    async fn query(&self, query: &ProjectQuery) -> Result<ProjectPagination, AppError> {
        // Build filter
        let mut filter = Document::new();

        if let Some(name) = &query.name {
            filter.insert("project_name", doc! { "$regex": name, "$options": "i" });
        }

        if let Some(short_name) = &query.short_name {
            filter.insert(
                "project_short_name",
                doc! { "$regex": short_name, "$options": "i" },
            );
        }

        if let Some(number) = &query.number {
            filter.insert("project_number", doc! { "$regex": number, "$options": "i" });
        }

        if let Some(stage) = &query.stage {
            filter.insert("project_stage", stage);
        }

        if let Some(status) = &query.status {
            filter.insert("project_status", status);
        }

        if let Some(recruitment_status) = &query.recruitment_status {
            filter.insert("recruitment_status", recruitment_status);
        }

        if let Some(disease) = &query.disease {
            filter.insert("disease", doc! { "$in": [disease] });
        }

        if let Some(sponsor) = &query.sponsor {
            filter.insert("sponsor", doc! { "$in": [sponsor] });
        }

        if let Some(tag) = &query.tag {
            filter.insert("tags", doc! { "$in": [tag] });
        }

        if let Some(search) = &query.search {
            if !search.is_empty() {
                filter = doc! {
                    "$or": [
                        { "project_name": { "$regex": search, "$options": "i" } },
                        { "project_short_name": { "$regex": search, "$options": "i" } },
                        { "project_number": { "$regex": search, "$options": "i" } }
                    ]
                };
            }
        }

        // Count total
        let total = self
            .collection
            .count_documents(filter.clone(), None)
            .await?;

        // Set pagination options
        let page = query.page.unwrap_or(1);
        let page_size = query.page_size.unwrap_or(10);
        let skip = ((page - 1) * page_size) as u64;

        // Set sort options
        let sort_doc = match (&query.sort_by, &query.sort_order) {
            (Some(sort_by), Some(sort_order)) => {
                let sort_field = match sort_by.as_str() {
                    "name" => "project_name",
                    "short_name" => "project_short_name",
                    "number" => "project_number",
                    "stage" => "project_stage",
                    "status" => "project_status",
                    "recruitment_status" => "recruitment_status",
                    "created_at" => "created_at",
                    "updated_at" => "updated_at",
                    _ => "updated_at",
                };

                let sort_value = if sort_order.to_uppercase() == "DESC" {
                    -1
                } else {
                    1
                };
                doc! { sort_field: sort_value }
            }
            _ => doc! { "updated_at": -1 },
        };

        let options = FindOptions::builder()
            .skip(skip)
            .limit(page_size as i64)
            .sort(sort_doc)
            .build();

        // Execute query
        let cursor = self.collection.find(filter, options).await?;
        let documents: Vec<Document> = cursor.try_collect().await?;

        // Convert to projects
        let mut items = Vec::new();
        for doc in documents {
            match self.document_to_project(doc) {
                Ok(project) => items.push(project),
                Err(e) => log::error!("Failed to parse project document: {}", e),
            }
        }

        Ok(ProjectPagination {
            items,
            total,
            page: page as u64,
            page_size: page_size as u64,
        })
    }

    async fn create(&self, project: &Project) -> Result<String, AppError> {
        let mut doc = self.project_to_document(project);

        // Remove ID field if present
        doc.remove("_id");

        // Insert document
        let result = self.collection.insert_one(doc, None).await?;
        let id = result
            .inserted_id
            .as_object_id()
            .ok_or_else(|| AppError::MongoError("Failed to get inserted ID".to_string()))?;

        Ok(id.to_hex())
    }

    async fn update(&self, id: &str, project: &Project) -> Result<bool, AppError> {
        let object_id = ObjectId::parse_str(id)?;
        let filter = doc! { "_id": object_id };

        let mut doc = self.project_to_document(project);

        // Remove ID field
        doc.remove("_id");

        // Update timestamp
        doc.insert("updated_at", DateTime::now());

        // Execute update
        let update = doc! { "$set": doc };
        let result = self.collection.update_one(filter, update, None).await?;

        Ok(result.modified_count > 0)
    }

    async fn delete(&self, id: &str) -> Result<bool, AppError> {
        let object_id = ObjectId::parse_str(id)?;
        let filter = doc! { "_id": object_id };

        let result = self.collection.delete_one(filter, None).await?;

        Ok(result.deleted_count > 0)
    }
}
