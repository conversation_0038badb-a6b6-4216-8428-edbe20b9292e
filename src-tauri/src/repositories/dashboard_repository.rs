use crate::models::dashboard::*;
use rusqlite::{Connection, Result as SqliteResult};
use std::error::Error;

pub struct DashboardRepository {
    db_path: String,
}

impl DashboardRepository {
    /// 创建新的仪表盘仓储实例
    pub fn new(db_path: String) -> Self {
        Self { db_path }
    }

    /// 获取数据库连接
    fn get_connection(&self) -> Result<Connection, Box<dyn Error>> {
        let conn = Connection::open(&self.db_path)?;
        Ok(conn)
    }

    /// 获取仪表盘概览数据
    pub fn get_dashboard_overview(
        &self,
        filter_params: Option<&DashboardFilterParams>,
    ) -> Result<DashboardOverview, Box<dyn Error>> {
        let conn = self.get_connection()?;

        // 添加调试日志
        if let Some(filter) = filter_params {
            println!("后端收到筛选参数: {:?}", filter);
        } else {
            println!("后端没有收到筛选参数");
        }

        // 基础条件和参数
        let mut conditions = Vec::<String>::new();
        let mut params_values: Vec<Box<dyn rusqlite::ToSql>> = Vec::new();

        if let Some(filter) = filter_params {
            // 日期筛选
            if let Some(start_date) = &filter.start_date {
                conditions.push("p.project_start_date >= ?".to_string());
                params_values.push(Box::new(start_date.clone()));
            }
            if let Some(end_date) = &filter.end_date {
                conditions.push("p.project_start_date <= ?".to_string());
                params_values.push(Box::new(end_date.clone()));
            }
            
            // 项目状态筛选
            if let Some(status_ids) = &filter.status_ids {
                if !status_ids.is_empty() {
                    let placeholders = vec!["?"; status_ids.len()].join(",");
                    conditions.push(format!("p.project_status_item_id IN ({})", placeholders));
                    for id in status_ids {
                        params_values.push(Box::new(*id));
                    }
                }
            }
            
            // 项目阶段筛选
            if let Some(stage_ids) = &filter.stage_ids {
                if !stage_ids.is_empty() {
                    let placeholders = vec!["?"; stage_ids.len()].join(",");
                    conditions.push(format!("p.project_stage_item_id IN ({})", placeholders));
                    for id in stage_ids {
                        params_values.push(Box::new(*id));
                    }
                }
            }
            
            // 招募状态筛选
            if let Some(recruitment_ids) = &filter.recruitment_ids {
                if !recruitment_ids.is_empty() {
                    let placeholders = vec!["?"; recruitment_ids.len()].join(",");
                    conditions.push(format!("p.recruitment_status_item_id IN ({})", placeholders));
                    for id in recruitment_ids {
                        params_values.push(Box::new(*id));
                    }
                }
            }
            
            // 申办方筛选 - 需要JOIN project_sponsors表
            if let Some(sponsor_ids) = &filter.sponsor_ids {
                if !sponsor_ids.is_empty() {
                    let placeholders = vec!["?"; sponsor_ids.len()].join(",");
                    conditions.push(format!("p.project_id IN (SELECT DISTINCT ps.project_id FROM project_sponsors ps WHERE ps.sponsor_item_id IN ({}))", placeholders));
                    for id in sponsor_ids {
                        params_values.push(Box::new(*id));
                    }
                }
            }
            
            // 疾病筛选
            if let Some(disease_ids) = &filter.disease_ids {
                if !disease_ids.is_empty() {
                    let placeholders = vec!["?"; disease_ids.len()].join(",");
                    conditions.push(format!("p.disease_item_id IN ({})", placeholders));
                    for id in disease_ids {
                        params_values.push(Box::new(*id));
                    }
                }
            }
        }

        let where_clause = if !conditions.is_empty() {
            format!("WHERE {}", conditions.join(" AND "))
        } else {
            String::new()
        };

        // 获取活动项目数量
        let active_sql = if where_clause.is_empty() {
            "SELECT COUNT(*) FROM projects p WHERE p.project_status_item_id IN (SELECT item_id FROM dictionary_items WHERE item_value NOT IN ('已完成', '已关闭', '已取消'))".to_string()
        } else {
            format!("SELECT COUNT(*) FROM projects p {} AND p.project_status_item_id IN (SELECT item_id FROM dictionary_items WHERE item_value NOT IN ('已完成', '已关闭', '已取消'))", where_clause)
        };

        let active_project_count = self.execute_count_query(&conn, &active_sql, &params_values)?;

        // 获取总项目数量
        let total_sql = if where_clause.is_empty() {
            "SELECT COUNT(*) FROM projects p".to_string()
        } else {
            format!("SELECT COUNT(*) FROM projects p {}", where_clause)
        };

        let total_project_count = self.execute_count_query(&conn, &total_sql, &params_values)?;

        // 获取招募中项目数量
        let recruiting_sql = if where_clause.is_empty() {
            "SELECT COUNT(*) FROM projects p WHERE p.recruitment_status_item_id IN (SELECT item_id FROM dictionary_items WHERE item_value = '招募中')".to_string()
        } else {
            format!("SELECT COUNT(*) FROM projects p {} AND p.recruitment_status_item_id IN (SELECT item_id FROM dictionary_items WHERE item_value = '招募中')", where_clause)
        };

        let recruiting_project_count = self.execute_count_query(&conn, &recruiting_sql, &params_values)?;

        Ok(DashboardOverview {
            active_project_count,
            total_project_count,
            recruiting_project_count,
        })
    }

    /// 获取项目状态分布
    pub fn get_project_status_distribution(
        &self,
        filter_params: Option<&DashboardFilterParams>,
    ) -> Result<Vec<ProjectStatusDistribution>, Box<dyn Error>> {
        let conn = self.get_connection()?;

        // 构建筛选条件（排除项目状态筛选，因为这就是我们要统计的维度）
        let mut conditions = Vec::<String>::new();
        let mut params_values: Vec<Box<dyn rusqlite::ToSql>> = Vec::new();

        if let Some(filter) = filter_params {
            if let Some(start_date) = &filter.start_date {
                conditions.push("p.project_start_date >= ?".to_string());
                params_values.push(Box::new(start_date.clone()));
            }
            if let Some(end_date) = &filter.end_date {
                conditions.push("p.project_start_date <= ?".to_string());
                params_values.push(Box::new(end_date.clone()));
            }
            if let Some(stage_ids) = &filter.stage_ids {
                if !stage_ids.is_empty() {
                    let placeholders = vec!["?"; stage_ids.len()].join(",");
                    conditions.push(format!("p.project_stage_item_id IN ({})", placeholders));
                    for id in stage_ids {
                        params_values.push(Box::new(*id));
                    }
                }
            }
            if let Some(recruitment_ids) = &filter.recruitment_ids {
                if !recruitment_ids.is_empty() {
                    let placeholders = vec!["?"; recruitment_ids.len()].join(",");
                    conditions.push(format!("p.recruitment_status_item_id IN ({})", placeholders));
                    for id in recruitment_ids {
                        params_values.push(Box::new(*id));
                    }
                }
            }
            if let Some(sponsor_ids) = &filter.sponsor_ids {
                if !sponsor_ids.is_empty() {
                    let placeholders = vec!["?"; sponsor_ids.len()].join(",");
                    conditions.push(format!("p.project_id IN (SELECT DISTINCT ps.project_id FROM project_sponsors ps WHERE ps.sponsor_item_id IN ({}))", placeholders));
                    for id in sponsor_ids {
                        params_values.push(Box::new(*id));
                    }
                }
            }
            if let Some(disease_ids) = &filter.disease_ids {
                if !disease_ids.is_empty() {
                    let placeholders = vec!["?"; disease_ids.len()].join(",");
                    conditions.push(format!("p.disease_item_id IN ({})", placeholders));
                    for id in disease_ids {
                        params_values.push(Box::new(*id));
                    }
                }
            }
        }

        let where_clause = if !conditions.is_empty() {
            format!("WHERE {}", conditions.join(" AND "))
        } else {
            String::new()
        };

        let sql = format!(
            "SELECT di.item_value as status_name, COUNT(p.project_id) as project_count
             FROM projects p
             JOIN dictionary_items di ON p.project_status_item_id = di.item_id
             {}
             GROUP BY di.item_value
             ORDER BY project_count DESC",
            where_clause
        );

        let mut stmt = conn.prepare(&sql)?;
        
        let mut result = Vec::new();
        if params_values.is_empty() {
            let rows = stmt.query_map([], |row| {
                Ok(ProjectStatusDistribution {
                    status_name: row.get(0)?,
                    project_count: row.get(1)?,
                })
            })?;
            for row in rows {
                result.push(row?);
            }
        } else {
            let params_refs: Vec<&dyn rusqlite::ToSql> = params_values
                .iter()
                .map(|p| p.as_ref() as &dyn rusqlite::ToSql)
                .collect();
            let rows = stmt.query_map(params_refs.as_slice(), |row| {
                Ok(ProjectStatusDistribution {
                    status_name: row.get(0)?,
                    project_count: row.get(1)?,
                })
            })?;
            for row in rows {
                result.push(row?);
            }
        }

        Ok(result)
    }

    /// 获取项目阶段分布
    pub fn get_project_stage_distribution(
        &self,
        filter_params: Option<&DashboardFilterParams>,
    ) -> Result<Vec<ProjectStageDistribution>, Box<dyn Error>> {
        let conn = self.get_connection()?;

        // 构建筛选条件（排除项目阶段筛选，因为这就是我们要统计的维度）
        let mut conditions = Vec::<String>::new();
        let mut params_values: Vec<Box<dyn rusqlite::ToSql>> = Vec::new();

        if let Some(filter) = filter_params {
            // 日期筛选
            if let Some(start_date) = &filter.start_date {
                conditions.push("p.project_start_date >= ?".to_string());
                params_values.push(Box::new(start_date.clone()));
            }
            if let Some(end_date) = &filter.end_date {
                conditions.push("p.project_start_date <= ?".to_string());
                params_values.push(Box::new(end_date.clone()));
            }
            
            // 项目状态筛选
            if let Some(status_ids) = &filter.status_ids {
                if !status_ids.is_empty() {
                    let placeholders = vec!["?"; status_ids.len()].join(",");
                    conditions.push(format!("p.project_status_item_id IN ({})", placeholders));
                    for id in status_ids {
                        params_values.push(Box::new(*id));
                    }
                }
            }
            
            // 招募状态筛选
            if let Some(recruitment_ids) = &filter.recruitment_ids {
                if !recruitment_ids.is_empty() {
                    let placeholders = vec!["?"; recruitment_ids.len()].join(",");
                    conditions.push(format!("p.recruitment_status_item_id IN ({})", placeholders));
                    for id in recruitment_ids {
                        params_values.push(Box::new(*id));
                    }
                }
            }
            
            // 申办方筛选
            if let Some(sponsor_ids) = &filter.sponsor_ids {
                if !sponsor_ids.is_empty() {
                    let placeholders = vec!["?"; sponsor_ids.len()].join(",");
                    conditions.push(format!("p.project_id IN (SELECT DISTINCT ps.project_id FROM project_sponsors ps WHERE ps.sponsor_item_id IN ({}))", placeholders));
                    for id in sponsor_ids {
                        params_values.push(Box::new(*id));
                    }
                }
            }
            
            // 疾病筛选
            if let Some(disease_ids) = &filter.disease_ids {
                if !disease_ids.is_empty() {
                    let placeholders = vec!["?"; disease_ids.len()].join(",");
                    conditions.push(format!("p.disease_item_id IN ({})", placeholders));
                    for id in disease_ids {
                        params_values.push(Box::new(*id));
                    }
                }
            }
        }

        let where_clause = if !conditions.is_empty() {
            format!("WHERE {}", conditions.join(" AND "))
        } else {
            String::new()
        };

        let sql = format!(
            "SELECT di.item_value as stage_name, COUNT(p.project_id) as project_count
             FROM projects p
             JOIN dictionary_items di ON p.project_stage_item_id = di.item_id
             {}
             GROUP BY di.item_value
             ORDER BY project_count DESC",
            where_clause
        );

        let mut stmt = conn.prepare(&sql)?;
        
        let mut result = Vec::new();
        if params_values.is_empty() {
            let rows = stmt.query_map([], |row| {
                Ok(ProjectStageDistribution {
                    stage_name: row.get(0)?,
                    project_count: row.get(1)?,
                })
            })?;
            for row in rows {
                result.push(row?);
            }
        } else {
            let params_refs: Vec<&dyn rusqlite::ToSql> = params_values
                .iter()
                .map(|p| p.as_ref() as &dyn rusqlite::ToSql)
                .collect();
            let rows = stmt.query_map(params_refs.as_slice(), |row| {
                Ok(ProjectStageDistribution {
                    stage_name: row.get(0)?,
                    project_count: row.get(1)?,
                })
            })?;
            for row in rows {
                result.push(row?);
            }
        }

        Ok(result)
    }

    /// 获取招募状态分布
    pub fn get_recruitment_status_distribution(
        &self,
        filter_params: Option<&DashboardFilterParams>,
    ) -> Result<Vec<RecruitmentStatusDistribution>, Box<dyn Error>> {
        let conn = self.get_connection()?;

        // 构建筛选条件（排除招募状态筛选，因为这就是我们要统计的维度）
        let mut conditions = Vec::<String>::new();
        let mut params_values: Vec<Box<dyn rusqlite::ToSql>> = Vec::new();

        if let Some(filter) = filter_params {
            // 日期筛选
            if let Some(start_date) = &filter.start_date {
                conditions.push("p.project_start_date >= ?".to_string());
                params_values.push(Box::new(start_date.clone()));
            }
            if let Some(end_date) = &filter.end_date {
                conditions.push("p.project_start_date <= ?".to_string());
                params_values.push(Box::new(end_date.clone()));
            }
            
            // 项目状态筛选
            if let Some(status_ids) = &filter.status_ids {
                if !status_ids.is_empty() {
                    let placeholders = vec!["?"; status_ids.len()].join(",");
                    conditions.push(format!("p.project_status_item_id IN ({})", placeholders));
                    for id in status_ids {
                        params_values.push(Box::new(*id));
                    }
                }
            }
            
            // 项目阶段筛选
            if let Some(stage_ids) = &filter.stage_ids {
                if !stage_ids.is_empty() {
                    let placeholders = vec!["?"; stage_ids.len()].join(",");
                    conditions.push(format!("p.project_stage_item_id IN ({})", placeholders));
                    for id in stage_ids {
                        params_values.push(Box::new(*id));
                    }
                }
            }
            
            // 申办方筛选
            if let Some(sponsor_ids) = &filter.sponsor_ids {
                if !sponsor_ids.is_empty() {
                    let placeholders = vec!["?"; sponsor_ids.len()].join(",");
                    conditions.push(format!("p.project_id IN (SELECT DISTINCT ps.project_id FROM project_sponsors ps WHERE ps.sponsor_item_id IN ({}))", placeholders));
                    for id in sponsor_ids {
                        params_values.push(Box::new(*id));
                    }
                }
            }
            
            // 疾病筛选
            if let Some(disease_ids) = &filter.disease_ids {
                if !disease_ids.is_empty() {
                    let placeholders = vec!["?"; disease_ids.len()].join(",");
                    conditions.push(format!("p.disease_item_id IN ({})", placeholders));
                    for id in disease_ids {
                        params_values.push(Box::new(*id));
                    }
                }
            }
        }

        let where_clause = if !conditions.is_empty() {
            format!("WHERE {}", conditions.join(" AND "))
        } else {
            String::new()
        };

        let sql = format!(
            "SELECT di.item_value as status_name, COUNT(p.project_id) as project_count
             FROM projects p
             JOIN dictionary_items di ON p.recruitment_status_item_id = di.item_id
             {}
             GROUP BY di.item_value
             ORDER BY project_count DESC",
            where_clause
        );

        let mut stmt = conn.prepare(&sql)?;
        
        let mut result = Vec::new();
        if params_values.is_empty() {
            let rows = stmt.query_map([], |row| {
                Ok(RecruitmentStatusDistribution {
                    status_name: row.get(0)?,
                    project_count: row.get(1)?,
                })
            })?;
            for row in rows {
                result.push(row?);
            }
        } else {
            let params_refs: Vec<&dyn rusqlite::ToSql> = params_values
                .iter()
                .map(|p| p.as_ref() as &dyn rusqlite::ToSql)
                .collect();
            let rows = stmt.query_map(params_refs.as_slice(), |row| {
                Ok(RecruitmentStatusDistribution {
                    status_name: row.get(0)?,
                    project_count: row.get(1)?,
                })
            })?;
            for row in rows {
                result.push(row?);
            }
        }

        Ok(result)
    }

    /// 获取疾病领域分布
    pub fn get_disease_distribution(
        &self,
        filter_params: Option<&DashboardFilterParams>,
    ) -> Result<Vec<DiseaseDistribution>, Box<dyn Error>> {
        let conn = self.get_connection()?;

        // 构建筛选条件（排除疾病筛选，因为这就是我们要统计的维度）
        let mut conditions = Vec::<String>::new();
        let mut params_values: Vec<Box<dyn rusqlite::ToSql>> = Vec::new();

        if let Some(filter) = filter_params {
            // 日期筛选
            if let Some(start_date) = &filter.start_date {
                conditions.push("p.project_start_date >= ?".to_string());
                params_values.push(Box::new(start_date.clone()));
            }
            if let Some(end_date) = &filter.end_date {
                conditions.push("p.project_start_date <= ?".to_string());
                params_values.push(Box::new(end_date.clone()));
            }
            
            // 项目状态筛选
            if let Some(status_ids) = &filter.status_ids {
                if !status_ids.is_empty() {
                    let placeholders = vec!["?"; status_ids.len()].join(",");
                    conditions.push(format!("p.project_status_item_id IN ({})", placeholders));
                    for id in status_ids {
                        params_values.push(Box::new(*id));
                    }
                }
            }
            
            // 项目阶段筛选
            if let Some(stage_ids) = &filter.stage_ids {
                if !stage_ids.is_empty() {
                    let placeholders = vec!["?"; stage_ids.len()].join(",");
                    conditions.push(format!("p.project_stage_item_id IN ({})", placeholders));
                    for id in stage_ids {
                        params_values.push(Box::new(*id));
                    }
                }
            }
            
            // 招募状态筛选
            if let Some(recruitment_ids) = &filter.recruitment_ids {
                if !recruitment_ids.is_empty() {
                    let placeholders = vec!["?"; recruitment_ids.len()].join(",");
                    conditions.push(format!("p.recruitment_status_item_id IN ({})", placeholders));
                    for id in recruitment_ids {
                        params_values.push(Box::new(*id));
                    }
                }
            }
            
            // 申办方筛选
            if let Some(sponsor_ids) = &filter.sponsor_ids {
                if !sponsor_ids.is_empty() {
                    let placeholders = vec!["?"; sponsor_ids.len()].join(",");
                    conditions.push(format!("p.project_id IN (SELECT DISTINCT ps.project_id FROM project_sponsors ps WHERE ps.sponsor_item_id IN ({}))", placeholders));
                    for id in sponsor_ids {
                        params_values.push(Box::new(*id));
                    }
                }
            }
        }

        let where_clause = if !conditions.is_empty() {
            format!("WHERE {}", conditions.join(" AND "))
        } else {
            String::new()
        };

        let sql = format!(
            "SELECT di.item_value as disease_name, COUNT(p.project_id) as project_count
             FROM projects p
             JOIN dictionary_items di ON p.disease_item_id = di.item_id
             {}
             GROUP BY di.item_value
             ORDER BY project_count DESC",
            where_clause
        );

        let mut stmt = conn.prepare(&sql)?;
        
        let mut result = Vec::new();
        if params_values.is_empty() {
            let rows = stmt.query_map([], |row| {
                Ok(DiseaseDistribution {
                    disease_name: row.get(0)?,
                    project_count: row.get(1)?,
                })
            })?;
            for row in rows {
                result.push(row?);
            }
        } else {
            let params_refs: Vec<&dyn rusqlite::ToSql> = params_values
                .iter()
                .map(|p| p.as_ref() as &dyn rusqlite::ToSql)
                .collect();
            let rows = stmt.query_map(params_refs.as_slice(), |row| {
                Ok(DiseaseDistribution {
                    disease_name: row.get(0)?,
                    project_count: row.get(1)?,
                })
            })?;
            for row in rows {
                result.push(row?);
            }
        }

        Ok(result)
    }

    /// 获取申办方项目分布
    pub fn get_sponsor_distribution(
        &self,
        filter_params: Option<&DashboardFilterParams>,
    ) -> Result<Vec<SponsorDistribution>, Box<dyn Error>> {
        let conn = self.get_connection()?;

        // 构建状态筛选条件
        let mut where_conditions = Vec::new();
        let mut params = Vec::new();

        if let Some(filter) = filter_params {
            if let Some(status_ids) = &filter.status_ids {
                if !status_ids.is_empty() {
                    let placeholders = vec!["?"; status_ids.len()].join(",");
                    where_conditions.push(format!("p.project_status_item_id IN ({})", placeholders));
                    for id in status_ids {
                        params.push(*id as i64);
                    }
                }
            }
        }

        let where_clause = if !where_conditions.is_empty() {
            format!("WHERE {}", where_conditions.join(" AND "))
        } else {
            String::new()
        };

        let sql = format!(
            "SELECT di.item_value as sponsor_name, COUNT(DISTINCT p.project_id) as project_count
             FROM projects p
             JOIN project_sponsors ps ON p.project_id = ps.project_id
             JOIN dictionary_items di ON ps.sponsor_item_id = di.item_id
             {}
             GROUP BY di.item_value, di.item_id
             ORDER BY project_count DESC",
            where_clause
        );

        let mut stmt = conn.prepare(&sql)?;
        
        let mut result = Vec::new();
        if params.is_empty() {
            let rows = stmt.query_map([], |row| {
                Ok(SponsorDistribution {
                    sponsor_name: row.get(0)?,
                    project_count: row.get(1)?,
                })
            })?;
            for row in rows {
                result.push(row?);
            }
        } else {
            let params_refs: Vec<&dyn rusqlite::ToSql> = params.iter()
                .map(|p| p as &dyn rusqlite::ToSql)
                .collect();
            let rows = stmt.query_map(params_refs.as_slice(), |row| {
                Ok(SponsorDistribution {
                    sponsor_name: row.get(0)?,
                    project_count: row.get(1)?,
                })
            })?;
            for row in rows {
                result.push(row?);
            }
        }

        Ok(result)
    }

    /// 获取每月新启动项目数据
    pub fn get_monthly_new_projects(
        &self,
        filter_params: Option<&DashboardFilterParams>,
    ) -> Result<Vec<MonthlyNewProjects>, Box<dyn Error>> {
        let conn = self.get_connection()?;

        // 构建筛选条件（包含所有维度的筛选）
        let mut conditions = vec!["p.project_start_date IS NOT NULL AND p.project_start_date != ''".to_string()];
        let mut params_values: Vec<Box<dyn rusqlite::ToSql>> = Vec::new();

        if let Some(filter) = filter_params {
            // 日期筛选
            if let Some(start_date) = &filter.start_date {
                conditions.push("p.project_start_date >= ?".to_string());
                params_values.push(Box::new(start_date.clone()));
            }
            if let Some(end_date) = &filter.end_date {
                conditions.push("p.project_start_date <= ?".to_string());
                params_values.push(Box::new(end_date.clone()));
            }
            
            // 项目状态筛选
            if let Some(status_ids) = &filter.status_ids {
                if !status_ids.is_empty() {
                    let placeholders = vec!["?"; status_ids.len()].join(",");
                    conditions.push(format!("p.project_status_item_id IN ({})", placeholders));
                    for id in status_ids {
                        params_values.push(Box::new(*id));
                    }
                }
            }
            
            // 项目阶段筛选
            if let Some(stage_ids) = &filter.stage_ids {
                if !stage_ids.is_empty() {
                    let placeholders = vec!["?"; stage_ids.len()].join(",");
                    conditions.push(format!("p.project_stage_item_id IN ({})", placeholders));
                    for id in stage_ids {
                        params_values.push(Box::new(*id));
                    }
                }
            }
            
            // 招募状态筛选
            if let Some(recruitment_ids) = &filter.recruitment_ids {
                if !recruitment_ids.is_empty() {
                    let placeholders = vec!["?"; recruitment_ids.len()].join(",");
                    conditions.push(format!("p.recruitment_status_item_id IN ({})", placeholders));
                    for id in recruitment_ids {
                        params_values.push(Box::new(*id));
                    }
                }
            }
            
            // 申办方筛选
            if let Some(sponsor_ids) = &filter.sponsor_ids {
                if !sponsor_ids.is_empty() {
                    let placeholders = vec!["?"; sponsor_ids.len()].join(",");
                    conditions.push(format!("p.project_id IN (SELECT DISTINCT ps.project_id FROM project_sponsors ps WHERE ps.sponsor_item_id IN ({}))", placeholders));
                    for id in sponsor_ids {
                        params_values.push(Box::new(*id));
                    }
                }
            }
            
            // 疾病筛选
            if let Some(disease_ids) = &filter.disease_ids {
                if !disease_ids.is_empty() {
                    let placeholders = vec!["?"; disease_ids.len()].join(",");
                    conditions.push(format!("p.disease_item_id IN ({})", placeholders));
                    for id in disease_ids {
                        params_values.push(Box::new(*id));
                    }
                }
            }
        }

        let where_clause = format!("WHERE {}", conditions.join(" AND "));

        let sql = format!(
            "SELECT strftime('%Y-%m', p.project_start_date) as month, COUNT(p.project_id) as project_count
             FROM projects p
             {}
             GROUP BY month
             ORDER BY month",
            where_clause
        );

        let mut stmt = conn.prepare(&sql)?;
        
        let mut result = Vec::new();
        if params_values.is_empty() {
            let rows = stmt.query_map([], |row| {
                let month: Option<String> = row.get(0)?;
                if let Some(month_str) = month {
                    Ok(MonthlyNewProjects {
                        month: month_str,
                        project_count: row.get(1)?,
                    })
                } else {
                    Err(rusqlite::Error::InvalidColumnType(0, "month".to_string(), rusqlite::types::Type::Null))
                }
            })?;
            for row in rows {
                if let Ok(monthly_project) = row {
                    result.push(monthly_project);
                }
            }
        } else {
            let params_refs: Vec<&dyn rusqlite::ToSql> = params_values
                .iter()
                .map(|p| p.as_ref() as &dyn rusqlite::ToSql)
                .collect();
            let rows = stmt.query_map(params_refs.as_slice(), |row| {
                let month: Option<String> = row.get(0)?;
                if let Some(month_str) = month {
                    Ok(MonthlyNewProjects {
                        month: month_str,
                        project_count: row.get(1)?,
                    })
                } else {
                    Err(rusqlite::Error::InvalidColumnType(0, "month".to_string(), rusqlite::types::Type::Null))
                }
            })?;
            for row in rows {
                if let Ok(monthly_project) = row {
                    result.push(monthly_project);
                }
            }
        }

        Ok(result)
    }

    /// 辅助方法：执行计数查询
    fn execute_count_query(
        &self,
        conn: &Connection,
        sql: &str,
        params: &[Box<dyn rusqlite::ToSql>],
    ) -> Result<i64, Box<dyn Error>> {
        if params.is_empty() {
            let count: i64 = conn.query_row(sql, [], |row| row.get(0))?;
            Ok(count)
        } else {
            let params_slice: Vec<&dyn rusqlite::ToSql> = params
                .iter()
                .map(|p| p.as_ref() as &dyn rusqlite::ToSql)
                .collect();
            let count: i64 = conn.query_row(sql, params_slice.as_slice(), |row| row.get(0))?;
            Ok(count)
        }
    }

    /// 调试项目日期统计
    pub fn get_project_date_statistics(&self) -> Result<(), Box<dyn Error>> {
        let conn = self.get_connection()?;

        let sql = "SELECT project_id, project_name, project_start_date FROM projects LIMIT 10";
        let mut stmt = conn.prepare(sql)?;
        let rows = stmt.query_map([], |row| {
            Ok((
                row.get::<_, String>(0)?,
                row.get::<_, String>(1)?,
                row.get::<_, Option<String>>(2)?,
            ))
        })?;

        for row in rows {
            let (id, name, date) = row?;
            println!("项目: {} | 名称: {} | 开始日期: {:?}", id, name, date);
        }

        Ok(())
    }

    /// 获取补贴类型分布（占位符实现）
    pub fn get_subsidy_type_distribution(
        &self,
        _filter_params: Option<&DashboardFilterParams>,
    ) -> Result<Vec<SubsidyTypeDistribution>, Box<dyn Error>> {
        Ok(vec![])
    }

    /// 获取项目补贴概览（占位符实现）
    pub fn get_project_subsidy_overview(
        &self,
        _filter_params: Option<&DashboardFilterParams>,
    ) -> Result<Vec<ProjectSubsidyOverview>, Box<dyn Error>> {
        Ok(vec![])
    }

    /// 获取角色分布（占位符实现）
    pub fn get_role_distribution(
        &self,
        _filter_params: Option<&DashboardFilterParams>,
    ) -> Result<Vec<RoleDistribution>, Box<dyn Error>> {
        Ok(vec![])
    }

    /// 获取人员工作负荷（占位符实现）
    pub fn get_personnel_workload(
        &self,
        _filter_params: Option<&DashboardFilterParams>,
    ) -> Result<Vec<PersonnelWorkload>, Box<dyn Error>> {
        Ok(vec![])
    }
} 