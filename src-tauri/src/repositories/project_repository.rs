use crate::error::AppError;
use crate::models::unified::project::{Project, ProjectPagination, ProjectQuery};
use async_trait::async_trait;

/// Project repository interface
#[async_trait]
pub trait ProjectRepository: Send + Sync {
    /// Get all projects
    async fn get_all(&self) -> Result<Vec<Project>, AppError>;

    /// Get project by ID
    async fn get_by_id(&self, id: &str) -> Result<Option<Project>, AppError>;

    /// Query projects with pagination
    async fn query(&self, query: &ProjectQuery) -> Result<ProjectPagination, AppError>;

    /// Create a new project
    async fn create(&self, project: &Project) -> Result<String, AppError>;

    /// Update an existing project
    async fn update(&self, id: &str, project: &Project) -> Result<bool, AppError>;

    /// Delete a project
    async fn delete(&self, id: &str) -> Result<bool, AppError>;
}
