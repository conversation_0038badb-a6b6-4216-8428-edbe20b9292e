use rusqlite::{Connection, Result};
use std::path::PathBuf;
use std::sync::{Arc, Mutex, Once};

// 全局数据库路径
static mut DB_PATH: Option<PathBuf> = None;
static INIT: Once = Once::new();

// 初始化数据库路径
pub fn init_db_path(path: PathBuf) {
    INIT.call_once(|| unsafe {
        DB_PATH = Some(path);
    });
}

// 获取数据库连接
pub fn get_connection() -> Result<Connection> {
    let path = unsafe {
        match &DB_PATH {
            Some(path) => path.to_string_lossy().to_string(),
            None => {
                return Err(rusqlite::Error::InvalidParameterName(
                    "Database path not initialized".to_string(),
                ))
            }
        }
    };

    Connection::open(path)
}

// 线程安全的数据库连接包装器
pub struct DbConnection {
    conn: Arc<Mutex<Connection>>,
}

impl DbConnection {
    pub fn new(path: &str) -> Result<Self> {
        let conn = Connection::open(path)?;
        Ok(Self {
            conn: Arc::new(Mutex::new(conn)),
        })
    }

    // 获取连接的锁
    pub fn get(&self) -> Result<std::sync::MutexGuard<'_, Connection>> {
        self.conn.lock().map_err(|_| {
            rusqlite::Error::InvalidParameterName(
                "Failed to acquire database connection lock".to_string(),
            )
        })
    }
}

// 实现Send和Sync特性，使其可以在线程间安全共享
unsafe impl Send for DbConnection {}
unsafe impl Sync for DbConnection {}

// 导入所需的模块
use rusqlite::params;
use rusqlite::OptionalExtension; // 添加这个导入
use serde::{Deserialize, Serialize};
use tauri::State;
use uuid::Uuid;

// 项目结构体定义
#[derive(Debug, Serialize, Deserialize)]
pub struct Project {
    pub project_id: Option<String>,
    pub project_name: String,
    pub project_short_name: String,
    pub project_number: Option<String>,
    pub project_stage: Option<String>,
    pub project_status: Option<String>,
    pub recruitment_status: Option<String>,
    pub remarks: Option<String>,
    pub disease: Option<Vec<String>>,
    pub sponsor: Option<Vec<String>>,
    pub tags: Option<Vec<String>>,
    pub created_at: Option<String>,
    pub updated_at: Option<String>,
}

// 查询参数结构体
#[derive(Debug, Deserialize)]
pub struct ProjectQuery {
    pub page: Option<u32>,
    pub page_size: Option<u32>,
    pub search: Option<String>,
    pub sort_by: Option<String>,
    pub sort_order: Option<String>,
}

// 获取所有项目
pub async fn get_all_projects_db(db: State<'_, DbConnection>) -> Result<Vec<Project>, String> {
    let conn = db.get().map_err(|e| e.to_string())?;

    let mut stmt = conn
        .prepare("SELECT * FROM projects")
        .map_err(|e| e.to_string())?;
    let project_iter = stmt
        .query_map([], |row| {
            Ok(Project {
                project_id: Some(row.get(0)?),
                project_name: row.get(1)?,
                project_short_name: row.get(2)?,
                project_number: row.get(3)?,
                project_stage: row.get(4)?,
                project_status: row.get(5)?,
                recruitment_status: row.get(6)?,
                remarks: row.get(7)?,
                disease: row
                    .get::<_, Option<String>>(8)?
                    .map(|s| s.split(',').map(|s| s.to_string()).collect()),
                sponsor: row
                    .get::<_, Option<String>>(9)?
                    .map(|s| s.split(',').map(|s| s.to_string()).collect()),
                tags: row
                    .get::<_, Option<String>>(10)?
                    .map(|s| s.split(',').map(|s| s.to_string()).collect()),
                created_at: row.get(11)?,
                updated_at: row.get(12)?,
            })
        })
        .map_err(|e| e.to_string())?;

    let mut projects = Vec::new();
    for project in project_iter {
        projects.push(project.map_err(|e| e.to_string())?);
    }

    Ok(projects)
}

// 根据ID获取项目
pub async fn get_project_by_id_db(
    db: State<'_, DbConnection>,
    project_id: String,
) -> Result<Option<Project>, String> {
    let conn = db.get().map_err(|e| e.to_string())?;

    let mut stmt = conn
        .prepare("SELECT * FROM projects WHERE project_id = ?")
        .map_err(|e| e.to_string())?;

    let project = stmt
        .query_row(params![project_id], |row| {
            Ok(Project {
                project_id: Some(row.get(0)?),
                project_name: row.get(1)?,
                project_short_name: row.get(2)?,
                project_number: row.get(3)?,
                project_stage: row.get(4)?,
                project_status: row.get(5)?,
                recruitment_status: row.get(6)?,
                remarks: row.get(7)?,
                disease: row
                    .get::<_, Option<String>>(8)?
                    .map(|s| s.split(',').map(|s| s.to_string()).collect()),
                sponsor: row
                    .get::<_, Option<String>>(9)?
                    .map(|s| s.split(',').map(|s| s.to_string()).collect()),
                tags: row
                    .get::<_, Option<String>>(10)?
                    .map(|s| s.split(',').map(|s| s.to_string()).collect()),
                created_at: row.get(11)?,
                updated_at: row.get(12)?,
            })
        })
        .optional()
        .map_err(|e| e.to_string())?;

    Ok(project)
}

// 创建项目
pub async fn create_project_db(
    db: State<'_, DbConnection>,
    project: Project,
) -> Result<String, String> {
    let conn = db.get().map_err(|e| e.to_string())?;

    let mut project = project;
    let project_id = Uuid::new_v4().to_string();
    project.project_id = Some(project_id.clone());

    // 处理数组字段
    let disease_str = project.disease.as_ref().map(|d| d.join(","));
    let sponsor_str = project.sponsor.as_ref().map(|s| s.join(","));
    let tags_str = project.tags.as_ref().map(|t| t.join(","));

    // 获取当前时间
    let now = chrono::Local::now().format("%Y-%m-%d %H:%M:%S").to_string();

    conn.execute(
        "INSERT INTO projects (
            project_id, project_name, project_short_name, project_number,
            project_stage, project_status, recruitment_status, remarks,
            disease, sponsor, tags, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
        params![
            project_id,
            project.project_name,
            project.project_short_name,
            project.project_number,
            project.project_stage,
            project.project_status,
            project.recruitment_status,
            project.remarks,
            disease_str,
            sponsor_str,
            tags_str,
            now,
            now
        ],
    )
    .map_err(|e| e.to_string())?;

    Ok(project_id)
}

// 更新项目
pub async fn update_project_db(
    db: State<'_, DbConnection>,
    project_id: String,
    project: Project,
) -> Result<(), String> {
    let conn = db.get().map_err(|e| e.to_string())?;

    // 处理数组字段
    let disease_str = project.disease.as_ref().map(|d| d.join(","));
    let sponsor_str = project.sponsor.as_ref().map(|s| s.join(","));
    let tags_str = project.tags.as_ref().map(|t| t.join(","));

    // 获取当前时间
    let now = chrono::Local::now().format("%Y-%m-%d %H:%M:%S").to_string();

    conn.execute(
        "UPDATE projects SET
            project_name = ?,
            project_short_name = ?,
            project_number = ?,
            project_stage = ?,
            project_status = ?,
            recruitment_status = ?,
            remarks = ?,
            disease = ?,
            sponsor = ?,
            tags = ?,
            updated_at = ?
        WHERE project_id = ?",
        params![
            project.project_name,
            project.project_short_name,
            project.project_number,
            project.project_stage,
            project.project_status,
            project.recruitment_status,
            project.remarks,
            disease_str,
            sponsor_str,
            tags_str,
            now,
            project_id
        ],
    )
    .map_err(|e| e.to_string())?;

    Ok(())
}

// 删除项目
pub async fn delete_project_db(
    db: State<'_, DbConnection>,
    project_id: String,
) -> Result<(), String> {
    let mut conn = db.get().map_err(|e| e.to_string())?;

    let tx = conn.transaction().map_err(|e| e.to_string())?;

    // 删除项目相关的所有数据
    tx.execute(
        "DELETE FROM project_sponsors WHERE project_id = ?",
        params![project_id],
    )
    .map_err(|e| e.to_string())?;

    // 删除项目本身
    tx.execute(
        "DELETE FROM projects WHERE project_id = ?",
        params![project_id],
    )
    .map_err(|e| e.to_string())?;

    tx.commit().map_err(|e| e.to_string())?;

    Ok(())
}

// 查询项目
#[tauri::command]
pub async fn query_projects(
    db: State<'_, DbConnection>,
    query: ProjectQuery,
) -> Result<Vec<Project>, String> {
    let conn = db.get().map_err(|e| e.to_string())?;

    // 构建查询
    let mut sql = String::from("SELECT * FROM projects");
    let mut conditions = Vec::new();
    let mut params = Vec::new();

    // 添加搜索条件
    if let Some(search) = &query.search {
        if !search.is_empty() {
            conditions.push(
                "(project_name LIKE ? OR project_short_name LIKE ? OR project_number LIKE ?)",
            );
            let search_pattern = format!("%{}%", search);
            params.push(search_pattern.clone());
            params.push(search_pattern.clone());
            params.push(search_pattern);
        }
    }

    // 添加WHERE子句
    if !conditions.is_empty() {
        sql.push_str(" WHERE ");
        sql.push_str(&conditions.join(" AND "));
    }

    // 添加排序
    if let Some(sort_by) = &query.sort_by {
        sql.push_str(" ORDER BY ");
        sql.push_str(sort_by);

        if let Some(sort_order) = &query.sort_order {
            if sort_order.to_uppercase() == "DESC" {
                sql.push_str(" DESC");
            } else {
                sql.push_str(" ASC");
            }
        }
    } else {
        sql.push_str(" ORDER BY created_at DESC");
    }

    // 添加分页
    if let (Some(page), Some(page_size)) = (query.page, query.page_size) {
        let offset = (page - 1) * page_size;
        sql.push_str(&format!(" LIMIT {} OFFSET {}", page_size, offset));
    }

    // 执行查询
    let mut stmt = conn.prepare(&sql).map_err(|e| e.to_string())?;

    let param_refs: Vec<&dyn rusqlite::ToSql> =
        params.iter().map(|p| p as &dyn rusqlite::ToSql).collect();

    let project_iter = stmt
        .query_map(param_refs.as_slice(), |row| {
            Ok(Project {
                project_id: Some(row.get(0)?),
                project_name: row.get(1)?,
                project_short_name: row.get(2)?,
                project_number: row.get(3)?,
                project_stage: row.get(4)?,
                project_status: row.get(5)?,
                recruitment_status: row.get(6)?,
                remarks: row.get(7)?,
                disease: row
                    .get::<_, Option<String>>(8)?
                    .map(|s| s.split(',').map(|s| s.to_string()).collect()),
                sponsor: row
                    .get::<_, Option<String>>(9)?
                    .map(|s| s.split(',').map(|s| s.to_string()).collect()),
                tags: row
                    .get::<_, Option<String>>(10)?
                    .map(|s| s.split(',').map(|s| s.to_string()).collect()),
                created_at: row.get(11)?,
                updated_at: row.get(12)?,
            })
        })
        .map_err(|e| e.to_string())?;

    let mut projects = Vec::new();
    for project in project_iter {
        projects.push(project.map_err(|e| e.to_string())?);
    }

    Ok(projects)
}

// 获取项目申办方
#[tauri::command]
pub async fn get_project_sponsors(
    db: State<'_, DbConnection>,
    project_id: String,
) -> Result<Vec<i64>, String> {
    let conn = db.get().map_err(|e| e.to_string())?;

    let mut stmt = conn
        .prepare("SELECT sponsor_item_id FROM project_sponsors WHERE project_id = ?")
        .map_err(|e| e.to_string())?;

    let sponsor_iter = stmt
        .query_map(params![project_id], |row| row.get(0))
        .map_err(|e| e.to_string())?;

    let mut sponsors = Vec::new();
    for sponsor in sponsor_iter {
        sponsors.push(sponsor.map_err(|e| e.to_string())?);
    }

    Ok(sponsors)
}

// 添加项目申办方
#[tauri::command]
pub async fn add_project_sponsor(
    db: State<'_, DbConnection>,
    project_id: String,
    sponsor_item_id: i64,
) -> Result<(), String> {
    let conn = db.get().map_err(|e| e.to_string())?;

    conn.execute(
        "INSERT INTO project_sponsors (project_id, sponsor_item_id) VALUES (?, ?)",
        params![project_id, sponsor_item_id],
    )
    .map_err(|e| e.to_string())?;

    Ok(())
}

// 移除项目申办方
#[tauri::command]
pub async fn remove_project_sponsor(
    db: State<'_, DbConnection>,
    project_id: String,
    sponsor_item_id: i64,
) -> Result<(), String> {
    let conn = db.get().map_err(|e| e.to_string())?;

    conn.execute(
        "DELETE FROM project_sponsors WHERE project_id = ? AND sponsor_item_id = ?",
        params![project_id, sponsor_item_id],
    )
    .map_err(|e| e.to_string())?;

    Ok(())
}
