use std::sync::Arc;
use tauri::State;
use crate::error::ApiResponse;
use crate::models::unified::project::{
    Project, ProjectQuery, ProjectPagination, 
    CreateProjectRequest, UpdateProjectRequest
};
use crate::services::unified::ProjectService;

/// Project service state
pub struct ProjectServiceState {
    service: Arc<ProjectService>,
}

impl ProjectServiceState {
    /// Create a new project service state
    pub fn new(service: Arc<ProjectService>) -> Self {
        Self { service }
    }
}

/// Get all projects
#[tauri::command]
pub async fn get_all_projects(
    state: State<'_, ProjectServiceState>,
) -> Result<ApiResponse<Vec<Project>>, String> {
    match state.service.get_all_projects().await {
        Ok(projects) => Ok(ApiResponse::success(projects)),
        Err(e) => Ok(ApiResponse::error(e)),
    }
}

/// Get project by ID
#[tauri::command]
pub async fn get_project_by_id(
    state: State<'_, ProjectServiceState>,
    id: String,
) -> Result<ApiResponse<Option<Project>>, String> {
    match state.service.get_project_by_id(&id).await {
        Ok(project) => Ok(ApiResponse::success(project)),
        Err(e) => Ok(ApiResponse::error(e)),
    }
}

/// Query projects with pagination
#[tauri::command]
pub async fn query_projects(
    state: State<'_, ProjectServiceState>,
    query: ProjectQuery,
) -> Result<ApiResponse<ProjectPagination>, String> {
    match state.service.query_projects(query).await {
        Ok(pagination) => Ok(ApiResponse::success(pagination)),
        Err(e) => Ok(ApiResponse::error(e)),
    }
}

/// Create a new project
#[tauri::command]
pub async fn create_project(
    state: State<'_, ProjectServiceState>,
    request: CreateProjectRequest,
) -> Result<ApiResponse<String>, String> {
    match state.service.create_project(request).await {
        Ok(response) => Ok(response),
        Err(e) => Ok(ApiResponse::error(e)),
    }
}

/// Update an existing project
#[tauri::command]
pub async fn update_project(
    state: State<'_, ProjectServiceState>,
    request: UpdateProjectRequest,
) -> Result<ApiResponse<bool>, String> {
    match state.service.update_project(request).await {
        Ok(response) => Ok(response),
        Err(e) => Ok(ApiResponse::error(e)),
    }
}

/// Delete a project
#[tauri::command]
pub async fn delete_project(
    state: State<'_, ProjectServiceState>,
    id: String,
) -> Result<ApiResponse<bool>, String> {
    match state.service.delete_project(&id).await {
        Ok(response) => Ok(response),
        Err(e) => Ok(ApiResponse::error(e)),
    }
}
