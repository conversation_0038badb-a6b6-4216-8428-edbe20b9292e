use crate::config::{AppConfig, DatabaseType};
use crate::error::ApiResponse;

/// Get the current application configuration
#[tauri::command]
pub fn get_app_config() -> Result<ApiResponse<AppConfig>, String> {
    let config_path = AppConfig::default_path();
    
    match AppConfig::load(&config_path) {
        Ok(config) => Ok(ApiResponse::success(config)),
        Err(e) => Ok(ApiResponse::error(e)),
    }
}

/// Save the application configuration
#[tauri::command]
pub fn save_app_config(config: AppConfig) -> Result<ApiResponse<bool>, String> {
    let config_path = AppConfig::default_path();
    
    match config.save(&config_path) {
        Ok(_) => Ok(ApiResponse::success(true)),
        Err(e) => Ok(ApiResponse::error(e)),
    }
}

/// Set the database type
#[tauri::command]
pub fn set_database_type(db_type: String) -> Result<ApiResponse<bool>, String> {
    let config_path = AppConfig::default_path();
    
    // Load current config
    let mut config = match AppConfig::load(&config_path) {
        Ok(config) => config,
        Err(e) => return Ok(ApiResponse::error(e)),
    };
    
    // Update database type
    config.database_type = match db_type.to_lowercase().as_str() {
        "sqlite" => DatabaseType::Sqlite,
        "mongodb" => DatabaseType::MongoDB,
        _ => return Ok(ApiResponse::error(format!("Invalid database type: {}", db_type))),
    };
    
    // Save config
    match config.save(&config_path) {
        Ok(_) => Ok(ApiResponse::success(true)),
        Err(e) => Ok(ApiResponse::error(e)),
    }
}
