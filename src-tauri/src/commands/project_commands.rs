use crate::models::project::{Project, ProjectPagination, ProjectQuery};
use crate::services::project_service::{ProjectService, PROJECT_SERVICE};
use log::{error, info};
use serde::{Deserialize, Serialize};
use std::error::Error;

/// 初始化项目服务
async fn init_project_service() -> Result<&'static ProjectService, Box<dyn Error>> {
    if PROJECT_SERVICE.get().is_none() {
        let service = ProjectService::new().await?;
        PROJECT_SERVICE
            .set(service)
            .map_err(|_| "无法设置项目服务实例".to_string())?;
    }

    Ok(PROJECT_SERVICE.get().unwrap())
}

/// 获取所有项目
#[tauri::command]
pub async fn get_all_projects() -> Result<Vec<Project>, String> {
    info!("获取所有项目");

    let service = init_project_service()
        .await
        .map_err(|e| format!("初始化项目服务失败: {}", e))?;

    service
        .get_all_projects()
        .await
        .map_err(|e| format!("获取项目失败: {}", e))
}

/// 分页查询项目
#[tauri::command]
pub async fn get_projects_paginated(
    query: ProjectQuery,
    page: u64,
    page_size: u64,
) -> Result<ProjectPagination, String> {
    info!("分页查询项目: 页码={}, 每页数量={}", page, page_size);

    let service = init_project_service()
        .await
        .map_err(|e| format!("初始化项目服务失败: {}", e))?;

    service
        .get_projects_paginated(query, page, page_size)
        .await
        .map_err(|e| format!("查询项目失败: {}", e))
}

/// 根据 ID 获取项目
#[tauri::command]
pub async fn get_project_by_id(id: String) -> Result<Option<Project>, String> {
    info!("获取项目: ID={}", id);

    let service = init_project_service()
        .await
        .map_err(|e| format!("初始化项目服务失败: {}", e))?;

    service
        .get_project_by_id(&id)
        .await
        .map_err(|e| format!("获取项目失败: {}", e))
}

/// 创建项目请求
#[derive(Debug, Deserialize)]
pub struct CreateProjectRequest {
    pub project_name: String,
    pub project_short_name: String,
    pub project_number: Option<String>,
    pub sponsor: Option<Vec<String>>,
    pub disease: Option<Vec<String>>,
    pub project_stage: Option<String>,
    pub project_status: Option<String>,
    pub recruitment_status: Option<String>,
    pub remarks: Option<String>,
}

/// 创建项目响应
#[derive(Debug, Serialize)]
pub struct CreateProjectResponse {
    pub success: bool,
    pub id: Option<String>,
    pub error: Option<String>,
}

/// 创建项目
#[tauri::command]
pub async fn create_project(
    request: CreateProjectRequest,
) -> Result<CreateProjectResponse, String> {
    info!("创建项目: {}", request.project_name);

    let service = init_project_service()
        .await
        .map_err(|e| format!("初始化项目服务失败: {}", e))?;

    let mut project = Project::new(
        request.project_name,
        request.project_short_name,
        request.project_number,
        request.sponsor,
        request.disease,
        request.project_stage,
        request.project_status,
        request.recruitment_status,
    );

    project.remarks = request.remarks;

    match service.create_project(project).await {
        Ok(id) => Ok(CreateProjectResponse {
            success: true,
            id: Some(id),
            error: None,
        }),
        Err(e) => {
            error!("创建项目失败: {}", e);
            Ok(CreateProjectResponse {
                success: false,
                id: None,
                error: Some(format!("创建项目失败: {}", e)),
            })
        }
    }
}

/// 更新项目请求
#[derive(Debug, Deserialize)]
pub struct UpdateProjectRequest {
    pub id: String,
    pub project_name: String,
    pub project_short_name: String,
    pub project_number: Option<String>,
    pub sponsor: Option<Vec<String>>,
    pub disease: Option<Vec<String>>,
    pub project_stage: Option<String>,
    pub project_status: Option<String>,
    pub recruitment_status: Option<String>,
    pub remarks: Option<String>,
}

/// 更新项目响应
#[derive(Debug, Serialize)]
pub struct UpdateProjectResponse {
    pub success: bool,
    pub error: Option<String>,
}

/// 更新项目
#[tauri::command]
pub async fn update_project(
    request: UpdateProjectRequest,
) -> Result<UpdateProjectResponse, String> {
    info!("更新项目: ID={}", request.id);

    let service = init_project_service()
        .await
        .map_err(|e| format!("初始化项目服务失败: {}", e))?;

    let mut project = Project::new(
        request.project_name,
        request.project_short_name,
        request.project_number,
        request.sponsor,
        request.disease,
        request.project_stage,
        request.project_status,
        request.recruitment_status,
    );

    project.remarks = request.remarks;

    match service.update_project(&request.id, project).await {
        Ok(updated) => {
            if updated {
                Ok(UpdateProjectResponse {
                    success: true,
                    error: None,
                })
            } else {
                Ok(UpdateProjectResponse {
                    success: false,
                    error: Some("未找到要更新的项目".to_string()),
                })
            }
        }
        Err(e) => {
            error!("更新项目失败: {}", e);
            Ok(UpdateProjectResponse {
                success: false,
                error: Some(format!("更新项目失败: {}", e)),
            })
        }
    }
}

/// 删除项目响应
#[derive(Debug, Serialize)]
pub struct DeleteProjectResponse {
    pub success: bool,
    pub error: Option<String>,
}

/// 删除项目
#[tauri::command]
pub async fn delete_project(id: String) -> Result<DeleteProjectResponse, String> {
    info!("删除项目: ID={}", id);

    let service = init_project_service()
        .await
        .map_err(|e| format!("初始化项目服务失败: {}", e))?;

    match service.delete_project(&id).await {
        Ok(deleted) => {
            if deleted {
                Ok(DeleteProjectResponse {
                    success: true,
                    error: None,
                })
            } else {
                Ok(DeleteProjectResponse {
                    success: false,
                    error: Some("未找到要删除的项目".to_string()),
                })
            }
        }
        Err(e) => {
            error!("删除项目失败: {}", e);
            Ok(DeleteProjectResponse {
                success: false,
                error: Some(format!("删除项目失败: {}", e)),
            })
        }
    }
}
