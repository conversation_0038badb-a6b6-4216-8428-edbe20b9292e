use serde::{Deserialize, Serialize};
use tauri::AppHandle;
use reqwest::{Client, header};
use std::time::Duration;

// 定义用户类型
#[derive(Debug, Serialize, Deserialize)]
pub struct LighthouseUser {
    pub id: i32,
    pub username: String,
    pub email: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub full_name: Option<String>,
    pub role: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub phone: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub address: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub city: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub country: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub postal_code: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub birth_date: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub avatar: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub bio: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub created_at: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub updated_at: Option<String>,
}

// 创建用户请求类型
#[derive(Debug, Serialize, Deserialize)]
pub struct CreateUserRequest {
    pub username: String,
    pub email: String,
    pub password: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub full_name: Option<String>,
    pub role: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub phone: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub address: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub city: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub country: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub postal_code: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub birth_date: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub avatar: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub bio: Option<String>,
}

// 更新用户请求类型
#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateUserRequest {
    #[serde(skip_serializing_if = "Option::is_none")]
    pub username: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub email: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub password: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub full_name: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub role: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub phone: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub address: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub city: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub country: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub postal_code: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub birth_date: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub avatar: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub bio: Option<String>,
}

// 登录请求类型
#[derive(Debug, Serialize, Deserialize)]
pub struct LoginRequest {
    pub username: String,
    pub password: String,
}

// 登录响应类型
#[derive(Debug, Serialize, Deserialize)]
pub struct LoginResponse {
    pub access_token: String,
    pub user: UserInfo,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UserInfo {
    pub id: i32,
    pub username: String,
    pub email: String,
    pub role: String,
}

// 分页响应类型
#[derive(Debug, Serialize, Deserialize)]
pub struct PaginatedResponse<T> {
    pub data: Vec<T>,
    pub total: i32,
}

// 测试连接
#[tauri::command]
pub async fn test_lighthouse_connection(
    _app_handle: AppHandle,
    server_url: String,
    port: u16,
) -> Result<bool, String> {
    let client = Client::builder()
        .timeout(Duration::from_secs(10))
        .build()
        .map_err(|e| e.to_string())?;

    let url = format!("{}:{}/api/health", server_url, port);

    let response = client
        .get(&url)
        .send()
        .await
        .map_err(|e| e.to_string())?;

    Ok(response.status().is_success())
}

// 登录并获取 Token
#[tauri::command]
pub async fn lighthouse_login(
    _app_handle: AppHandle,
    server_url: String,
    port: u16,
    username: String,
    password: String,
) -> Result<LoginResponse, String> {
    let client = Client::builder()
        .timeout(Duration::from_secs(10))
        .build()
        .map_err(|e| e.to_string())?;

    let url = format!("{}:{}/api/auth/login", server_url, port);

    let login_request = LoginRequest { username, password };

    let response = client
        .post(&url)
        .header(header::CONTENT_TYPE, "application/json")
        .json(&login_request)
        .send()
        .await
        .map_err(|e| e.to_string())?;

    if !response.status().is_success() {
        let error_text = response.text().await.map_err(|e| e.to_string())?;
        return Err(format!("登录失败: {}", error_text));
    }

    let login_response: LoginResponse = response.json().await.map_err(|e| e.to_string())?;

    Ok(login_response)
}

// 获取用户列表
#[tauri::command]
pub async fn get_lighthouse_users(
    _app_handle: AppHandle,
    server_url: String,
    port: u16,
    token: String,
    page: u32,
    limit: u32,
    search: Option<String>,
) -> Result<PaginatedResponse<LighthouseUser>, String> {
    let client = Client::builder()
        .timeout(Duration::from_secs(10))
        .build()
        .map_err(|e| e.to_string())?;

    let skip = (page - 1) * limit;

    let mut url = format!("{}:{}/api/users?skip={}&take={}", server_url, port, skip, limit);

    if let Some(search_term) = search {
        if !search_term.is_empty() {
            url = format!("{}&username={}", url, urlencoding::encode(&search_term));
        }
    }

    let response = client
        .get(&url)
        .header(header::CONTENT_TYPE, "application/json")
        .header(header::AUTHORIZATION, format!("Bearer {}", token))
        .send()
        .await
        .map_err(|e| e.to_string())?;

    if !response.status().is_success() {
        let error_text = response.text().await.map_err(|e| e.to_string())?;
        return Err(format!("获取用户列表失败: {}", error_text));
    }

    let users_response: PaginatedResponse<LighthouseUser> = response.json().await.map_err(|e| e.to_string())?;

    Ok(users_response)
}

// 获取单个用户详情
#[tauri::command]
pub async fn get_lighthouse_user_by_id(
    _app_handle: AppHandle,
    server_url: String,
    port: u16,
    token: String,
    user_id: i32,
) -> Result<LighthouseUser, String> {
    let client = Client::builder()
        .timeout(Duration::from_secs(10))
        .build()
        .map_err(|e| e.to_string())?;

    let url = format!("{}:{}/api/users/{}", server_url, port, user_id);

    let response = client
        .get(&url)
        .header(header::CONTENT_TYPE, "application/json")
        .header(header::AUTHORIZATION, format!("Bearer {}", token))
        .send()
        .await
        .map_err(|e| e.to_string())?;

    if !response.status().is_success() {
        let error_text = response.text().await.map_err(|e| e.to_string())?;
        return Err(format!("获取用户详情失败: {}", error_text));
    }

    let user: LighthouseUser = response.json().await.map_err(|e| e.to_string())?;

    Ok(user)
}

// 创建用户
#[tauri::command]
pub async fn create_lighthouse_user(
    _app_handle: AppHandle,
    server_url: String,
    port: u16,
    token: String,
    user: CreateUserRequest,
) -> Result<LighthouseUser, String> {
    let client = Client::builder()
        .timeout(Duration::from_secs(10))
        .build()
        .map_err(|e| e.to_string())?;

    let url = format!("{}:{}/api/users", server_url, port);

    let response = client
        .post(&url)
        .header(header::CONTENT_TYPE, "application/json")
        .header(header::AUTHORIZATION, format!("Bearer {}", token))
        .json(&user)
        .send()
        .await
        .map_err(|e| e.to_string())?;

    if !response.status().is_success() {
        let error_text = response.text().await.map_err(|e| e.to_string())?;
        return Err(format!("创建用户失败: {}", error_text));
    }

    let created_user: LighthouseUser = response.json().await.map_err(|e| e.to_string())?;

    Ok(created_user)
}

// 更新用户
#[tauri::command]
pub async fn update_lighthouse_user(
    _app_handle: AppHandle,
    server_url: String,
    port: u16,
    token: String,
    user_id: i32,
    user: UpdateUserRequest,
) -> Result<LighthouseUser, String> {
    let client = Client::builder()
        .timeout(Duration::from_secs(10))
        .build()
        .map_err(|e| e.to_string())?;

    let url = format!("{}:{}/api/users/{}", server_url, port, user_id);

    let response = client
        .patch(&url)
        .header(header::CONTENT_TYPE, "application/json")
        .header(header::AUTHORIZATION, format!("Bearer {}", token))
        .json(&user)
        .send()
        .await
        .map_err(|e| e.to_string())?;

    if !response.status().is_success() {
        let error_text = response.text().await.map_err(|e| e.to_string())?;
        return Err(format!("更新用户失败: {}", error_text));
    }

    let updated_user: LighthouseUser = response.json().await.map_err(|e| e.to_string())?;

    Ok(updated_user)
}

// 删除用户
#[tauri::command]
pub async fn delete_lighthouse_user(
    _app_handle: AppHandle,
    server_url: String,
    port: u16,
    token: String,
    user_id: i32,
) -> Result<bool, String> {
    let client = Client::builder()
        .timeout(Duration::from_secs(10))
        .build()
        .map_err(|e| e.to_string())?;

    let url = format!("{}:{}/api/users/{}", server_url, port, user_id);

    let response = client
        .delete(&url)
        .header(header::CONTENT_TYPE, "application/json")
        .header(header::AUTHORIZATION, format!("Bearer {}", token))
        .send()
        .await
        .map_err(|e| e.to_string())?;

    if !response.status().is_success() {
        let error_text = response.text().await.map_err(|e| e.to_string())?;
        return Err(format!("删除用户失败: {}", error_text));
    }

    Ok(true)
}
