use crate::models::config::{<PERSON>fi<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ConfigResponse, SaveConfigRequest, SystemConfig};
use crate::services::config_service::{ConfigService, CONFIG_SERVICE};
use log::{error, info};
use serde::Deserialize;
use std::error::Error;

/// 初始化配置服务
async fn init_config_service() -> Result<&'static ConfigService, Box<dyn Error>> {
    if CONFIG_SERVICE.get().is_none() {
        let service = ConfigService::new().await?;
        CONFIG_SERVICE
            .set(service)
            .map_err(|_| "无法设置配置服务实例".to_string())?;
    }

    Ok(CONFIG_SERVICE.get().unwrap())
}

/// 获取系统配置
#[tauri::command]
pub async fn get_system_config() -> Result<ConfigResponse, String> {
    info!("获取系统配置");

    let service = init_config_service()
        .await
        .map_err(|e| format!("初始化配置服务失败: {}", e))?;

    match service.get_system_config().await {
        Ok(config) => {
            let data =
                serde_json::to_value(config).map_err(|e| format!("序列化配置失败: {}", e))?;

            Ok(ConfigResponse {
                success: true,
                error: None,
                data: Some(data),
            })
        }
        Err(e) => {
            error!("获取系统配置失败: {}", e);
            Ok(ConfigResponse {
                success: false,
                error: Some(format!("获取系统配置失败: {}", e)),
                data: None,
            })
        }
    }
}

/// 保存系统配置请求
#[derive(Debug, Deserialize)]
pub struct SaveSystemConfigRequest {
    pub clinical_research_folder_path: Option<String>,
    pub notion_api_key: Option<String>,
    pub notion_database_id: Option<String>,
    #[serde(flatten)]
    pub other: Option<serde_json::Map<String, serde_json::Value>>,
}

/// 保存系统配置
#[tauri::command]
pub async fn save_system_config(config: SaveSystemConfigRequest) -> Result<ConfigResponse, String> {
    info!("保存系统配置");

    let service = init_config_service()
        .await
        .map_err(|e| format!("初始化配置服务失败: {}", e))?;

    let system_config = SystemConfig {
        clinical_research_folder_path: config.clinical_research_folder_path,
        notion_api_key: config.notion_api_key,
        notion_database_id: config.notion_database_id,
        other: config.other,
    };

    match service.save_system_config(system_config.clone()).await {
        Ok(success) => {
            if success {
                let data = serde_json::to_value(system_config)
                    .map_err(|e| format!("序列化配置失败: {}", e))?;

                Ok(ConfigResponse {
                    success: true,
                    error: None,
                    data: Some(data),
                })
            } else {
                Ok(ConfigResponse {
                    success: false,
                    error: Some("保存系统配置失败".to_string()),
                    data: None,
                })
            }
        }
        Err(e) => {
            error!("保存系统配置失败: {}", e);
            Ok(ConfigResponse {
                success: false,
                error: Some(format!("保存系统配置失败: {}", e)),
                data: None,
            })
        }
    }
}

/// 获取配置列表
#[tauri::command]
pub async fn get_config_list(config_name: String) -> Result<ConfigResponse, String> {
    info!("获取配置列表: {}", config_name);

    let service = init_config_service()
        .await
        .map_err(|e| format!("初始化配置服务失败: {}", e))?;

    match service.get_config_list(&config_name).await {
        Ok(config_list) => {
            let data = serde_json::to_value(config_list)
                .map_err(|e| format!("序列化配置列表失败: {}", e))?;

            Ok(ConfigResponse {
                success: true,
                error: None,
                data: Some(data),
            })
        }
        Err(e) => {
            error!("获取配置列表失败: {}", e);
            Ok(ConfigResponse {
                success: false,
                error: Some(format!("获取配置列表失败: {}", e)),
                data: None,
            })
        }
    }
}

/// 保存配置列表
#[tauri::command]
pub async fn save_config_list(
    config_name: String,
    items: Vec<String>,
) -> Result<ConfigResponse, String> {
    info!("保存配置列表: {}", config_name);

    let service = init_config_service()
        .await
        .map_err(|e| format!("初始化配置服务失败: {}", e))?;

    match service.save_config_list(&config_name, items.clone()).await {
        Ok(success) => {
            if success {
                let config_list = ConfigList {
                    config_name: config_name.clone(),
                    config: items,
                };

                let data = serde_json::to_value(config_list)
                    .map_err(|e| format!("序列化配置列表失败: {}", e))?;

                Ok(ConfigResponse {
                    success: true,
                    error: None,
                    data: Some(data),
                })
            } else {
                Ok(ConfigResponse {
                    success: false,
                    error: Some("保存配置列表失败".to_string()),
                    data: None,
                })
            }
        }
        Err(e) => {
            error!("保存配置列表失败: {}", e);
            Ok(ConfigResponse {
                success: false,
                error: Some(format!("保存配置列表失败: {}", e)),
                data: None,
            })
        }
    }
}

/// 获取所有配置
#[tauri::command]
pub async fn get_all_configs() -> Result<Vec<Config>, String> {
    info!("获取所有配置");

    let service = init_config_service()
        .await
        .map_err(|e| format!("初始化配置服务失败: {}", e))?;

    service
        .get_all_configs()
        .await
        .map_err(|e| format!("获取所有配置失败: {}", e))
}

/// 获取配置
#[tauri::command]
pub async fn get_config(config_name: String) -> Result<ConfigResponse, String> {
    info!("获取配置: {}", config_name);

    let service = init_config_service()
        .await
        .map_err(|e| format!("初始化配置服务失败: {}", e))?;

    match service.get_config(&config_name).await {
        Ok(Some(config)) => {
            let data =
                serde_json::to_value(config).map_err(|e| format!("序列化配置失败: {}", e))?;

            Ok(ConfigResponse {
                success: true,
                error: None,
                data: Some(data),
            })
        }
        Ok(None) => Ok(ConfigResponse {
            success: true,
            error: None,
            data: None,
        }),
        Err(e) => {
            error!("获取配置失败: {}", e);
            Ok(ConfigResponse {
                success: false,
                error: Some(format!("获取配置失败: {}", e)),
                data: None,
            })
        }
    }
}

/// 保存配置
#[tauri::command]
pub async fn save_config(request: SaveConfigRequest) -> Result<ConfigResponse, String> {
    info!("保存配置: {}", request.config_name);

    let service = init_config_service()
        .await
        .map_err(|e| format!("初始化配置服务失败: {}", e))?;

    let config = Config::new(request.config_name.clone(), Some(request.config.clone()));

    match service.save_config(config).await {
        Ok(success) => {
            if success {
                Ok(ConfigResponse {
                    success: true,
                    error: None,
                    data: Some(request.config),
                })
            } else {
                Ok(ConfigResponse {
                    success: false,
                    error: Some("保存配置失败".to_string()),
                    data: None,
                })
            }
        }
        Err(e) => {
            error!("保存配置失败: {}", e);
            Ok(ConfigResponse {
                success: false,
                error: Some(format!("保存配置失败: {}", e)),
                data: None,
            })
        }
    }
}

/// 删除配置
#[tauri::command]
pub async fn delete_config(config_name: String) -> Result<ConfigResponse, String> {
    info!("删除配置: {}", config_name);

    let service = init_config_service()
        .await
        .map_err(|e| format!("初始化配置服务失败: {}", e))?;

    match service.delete_config(&config_name).await {
        Ok(success) => {
            if success {
                Ok(ConfigResponse {
                    success: true,
                    error: None,
                    data: None,
                })
            } else {
                Ok(ConfigResponse {
                    success: false,
                    error: Some("删除配置失败，配置可能不存在".to_string()),
                    data: None,
                })
            }
        }
        Err(e) => {
            error!("删除配置失败: {}", e);
            Ok(ConfigResponse {
                success: false,
                error: Some(format!("删除配置失败: {}", e)),
                data: None,
            })
        }
    }
}
