use log::{error, info};
use tauri::command;

use crate::models::rule_designer::{
    CreateProjectCriterionRequest, CreateRuleDefinitionRequest, ProjectCriterionQuery,
    ProjectCriterionWithRule, RuleDefinition, RuleDefinitionQuery, UpdateProjectCriterionRequest,
    UpdateRuleDefinitionRequest,
};
use crate::services::rule_designer_service::RuleDesignerService;

/// 初始化规则设计器表
#[command]
pub fn init_rule_designer_tables(db_path: String) -> Result<bool, String> {
    info!("初始化规则设计器表");

    let service = RuleDesignerService::new(db_path);

    match service.init_tables() {
        Ok(_) => Ok(true),
        Err(e) => {
            error!("初始化规则设计器表失败: {}", e);
            Err(format!("初始化规则设计器表失败: {}", e))
        }
    }
}

/// 获取所有规则定义
#[command]
pub fn get_rule_definitions(
    query: RuleDefinitionQuery,
    db_path: String,
) -> Result<Vec<RuleDefinition>, String> {
    info!("获取规则定义列表，查询参数: {:?}", query);

    let service = RuleDesignerService::new(db_path);

    match service.get_rule_definitions(&query) {
        Ok(rules) => Ok(rules),
        Err(e) => {
            error!("获取规则定义列表失败: {}", e);
            Err(format!("获取规则定义列表失败: {}", e))
        }
    }
}

/// 根据ID获取规则定义
#[command]
pub fn get_rule_definition_by_id(
    rule_definition_id: i64,
    db_path: String,
) -> Result<Option<RuleDefinition>, String> {
    info!("获取规则定义，ID: {}", rule_definition_id);

    let service = RuleDesignerService::new(db_path);

    match service.get_rule_definition_by_id(rule_definition_id) {
        Ok(rule) => Ok(rule),
        Err(e) => {
            error!("获取规则定义失败: {}", e);
            Err(format!("获取规则定义失败: {}", e))
        }
    }
}

/// 创建规则定义
#[command]
pub fn create_rule_definition(
    request: CreateRuleDefinitionRequest,
    db_path: String,
) -> Result<RuleDefinition, String> {
    info!("创建规则定义: {}", request.rule_name);

    let service = RuleDesignerService::new(db_path);

    match service.create_rule_definition(&request) {
        Ok(rule) => Ok(rule),
        Err(e) => {
            error!("创建规则定义失败: {}", e);
            Err(format!("创建规则定义失败: {}", e))
        }
    }
}

/// 更新规则定义
#[command]
pub fn update_rule_definition(
    rule_definition_id: i64,
    request: UpdateRuleDefinitionRequest,
    db_path: String,
) -> Result<RuleDefinition, String> {
    info!("更新规则定义，ID: {}", rule_definition_id);

    let service = RuleDesignerService::new(db_path);

    match service.update_rule_definition(rule_definition_id, &request) {
        Ok(rule) => Ok(rule),
        Err(e) => {
            error!("更新规则定义失败: {}", e);
            Err(format!("更新规则定义失败: {}", e))
        }
    }
}

/// 查找使用特定规则定义的项目
#[command]
pub fn find_projects_using_rule(
    rule_definition_id: i64,
    db_path: String,
) -> Result<Vec<(String, String, String)>, String> {
    info!("查找使用规则定义的项目，规则ID: {}", rule_definition_id);

    let service = RuleDesignerService::new(db_path);

    match service.find_projects_using_rule(rule_definition_id) {
        Ok(projects) => Ok(projects),
        Err(e) => {
            error!("查找使用规则定义的项目失败: {}", e);
            Err(format!("查找使用规则定义的项目失败: {}", e))
        }
    }
}

/// 删除规则定义
#[command]
pub fn delete_rule_definition(rule_definition_id: i64, db_path: String) -> Result<bool, String> {
    info!("删除规则定义，ID: {}", rule_definition_id);

    let service = RuleDesignerService::new(db_path);

    match service.delete_rule_definition(rule_definition_id) {
        Ok(_) => Ok(true),
        Err(e) => {
            error!("删除规则定义失败: {}", e);
            Err(format!("删除规则定义失败: {}", e))
        }
    }
}

/// 获取项目标准
#[command]
pub fn get_project_criteria(
    query: ProjectCriterionQuery,
    db_path: String,
) -> Result<Vec<ProjectCriterionWithRule>, String> {
    info!("获取项目标准列表，查询参数: {:?}", query);

    let service = RuleDesignerService::new(db_path);

    match service.get_project_criteria(&query) {
        Ok(criteria) => Ok(criteria),
        Err(e) => {
            error!("获取项目标准列表失败: {}", e);
            Err(format!("获取项目标准列表失败: {}", e))
        }
    }
}

/// 根据ID获取项目标准
#[command]
pub fn get_project_criterion_by_id(
    project_criterion_id: i64,
    db_path: String,
) -> Result<Option<ProjectCriterionWithRule>, String> {
    info!("获取项目标准，ID: {}", project_criterion_id);

    let service = RuleDesignerService::new(db_path);

    match service.get_project_criterion_by_id(project_criterion_id) {
        Ok(criterion) => Ok(criterion),
        Err(e) => {
            error!("获取项目标准失败: {}", e);
            Err(format!("获取项目标准失败: {}", e))
        }
    }
}

/// 创建项目标准
#[command]
pub fn create_project_criterion(
    request: CreateProjectCriterionRequest,
    db_path: String,
) -> Result<ProjectCriterionWithRule, String> {
    info!(
        "创建项目标准，项目ID: {}, 规则定义ID: {}",
        request.project_id, request.rule_definition_id
    );

    let service = RuleDesignerService::new(db_path);

    match service.create_project_criterion(&request) {
        Ok(criterion) => Ok(criterion),
        Err(e) => {
            error!("创建项目标准失败: {}", e);
            Err(format!("创建项目标准失败: {}", e))
        }
    }
}

/// 更新项目标准
#[command]
pub fn update_project_criterion(
    project_criterion_id: i64,
    request: UpdateProjectCriterionRequest,
    db_path: String,
) -> Result<ProjectCriterionWithRule, String> {
    info!("更新项目标准，ID: {}", project_criterion_id);

    let service = RuleDesignerService::new(db_path);

    match service.update_project_criterion(project_criterion_id, &request) {
        Ok(criterion) => Ok(criterion),
        Err(e) => {
            error!("更新项目标准失败: {}", e);
            Err(format!("更新项目标准失败: {}", e))
        }
    }
}

/// 删除项目标准
#[command]
pub fn delete_project_criterion(
    project_criterion_id: i64,
    db_path: String,
) -> Result<bool, String> {
    info!("删除项目标准，ID: {}", project_criterion_id);

    let service = RuleDesignerService::new(db_path);

    match service.delete_project_criterion(project_criterion_id) {
        Ok(_) => Ok(true),
        Err(e) => {
            error!("删除项目标准失败: {}", e);
            Err(format!("删除项目标准失败: {}", e))
        }
    }
}
