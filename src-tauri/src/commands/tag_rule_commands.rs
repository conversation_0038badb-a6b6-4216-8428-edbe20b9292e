use crate::models::file_system::{FileResponse, TagColor};
use crate::services::tag_rule_service::TAG_RULE_SERVICE;
use log::{error, info};
use serde::Deserialize;

/// 获取所有标签规则
#[tauri::command]
pub async fn get_tag_rules() -> Result<FileResponse, String> {
    info!("获取标签规则");

    match TAG_RULE_SERVICE.get_tag_rules().await {
        Ok(rules) => {
            let data =
                serde_json::to_value(rules).map_err(|e| format!("序列化标签规则失败: {}", e))?;

            Ok(FileResponse {
                success: true,
                error: None,
                data: Some(data),
            })
        }
        Err(e) => {
            error!("获取标签规则失败: {}", e);
            Ok(FileResponse {
                success: false,
                error: Some(format!("获取标签规则失败: {}", e)),
                data: None,
            })
        }
    }
}

/// 添加标签规则请求
#[derive(Debug, Deserialize)]
pub struct AddTagRuleRequest {
    pub label: String,
    pub keywords: Vec<String>,
    pub tag_color: TagColor,
}

/// 添加标签规则
#[tauri::command]
pub async fn add_tag_rule(request: AddTagRuleRequest) -> Result<FileResponse, String> {
    info!("添加标签规则: {}", request.label);

    match TAG_RULE_SERVICE
        .add_tag_rule(&request.label, request.keywords, request.tag_color)
        .await
    {
        Ok(_) => Ok(FileResponse {
            success: true,
            error: None,
            data: None,
        }),
        Err(e) => {
            error!("添加标签规则失败: {}", e);
            Ok(FileResponse {
                success: false,
                error: Some(format!("添加标签规则失败: {}", e)),
                data: None,
            })
        }
    }
}

/// 删除标签规则请求
#[derive(Debug, Deserialize)]
pub struct DeleteTagRuleRequest {
    pub label: String,
}

/// 删除标签规则
#[tauri::command]
pub async fn delete_tag_rule(request: DeleteTagRuleRequest) -> Result<FileResponse, String> {
    info!("删除标签规则: {}", request.label);

    match TAG_RULE_SERVICE.delete_tag_rule(&request.label).await {
        Ok(_) => Ok(FileResponse {
            success: true,
            error: None,
            data: None,
        }),
        Err(e) => {
            error!("删除标签规则失败: {}", e);
            Ok(FileResponse {
                success: false,
                error: Some(format!("删除标签规则失败: {}", e)),
                data: None,
            })
        }
    }
}

/// 更新标签规则请求
#[derive(Debug, Deserialize)]
pub struct UpdateTagRuleRequest {
    pub old_label: String,
    pub new_label: String,
    pub keywords: Vec<String>,
    pub tag_color: TagColor,
}

/// 更新标签规则
#[tauri::command]
pub async fn update_tag_rule(request: UpdateTagRuleRequest) -> Result<FileResponse, String> {
    info!(
        "更新标签规则: {} -> {}",
        request.old_label, request.new_label
    );

    match TAG_RULE_SERVICE
        .update_tag_rule(
            &request.old_label,
            &request.new_label,
            request.keywords,
            request.tag_color,
        )
        .await
    {
        Ok(_) => Ok(FileResponse {
            success: true,
            error: None,
            data: None,
        }),
        Err(e) => {
            error!("更新标签规则失败: {}", e);
            Ok(FileResponse {
                success: false,
                error: Some(format!("更新标签规则失败: {}", e)),
                data: None,
            })
        }
    }
}
