use crate::services::notion_service::NOTION_SERVICE;
use log::{error, info};
use serde::Serialize;

#[derive(Debug, Serialize)]
pub struct SyncResponse {
    pub success: bool,
    pub data: Option<SyncStats>,
    pub error: Option<String>,
}

#[derive(Debug, Serialize)]
pub struct SyncStats {
    pub total: usize,
    pub created: usize,
    pub updated: usize,
    pub failed: usize,
}

/// 将项目数据同步到 Notion
#[tauri::command]
pub async fn sync_projects_to_notion() -> Result<SyncResponse, String> {
    info!("开始同步项目到 Notion...");

    let result = {
        let mut service = NOTION_SERVICE.lock().await;
        service.sync_projects_to_notion().await
    };

    if result.success {
        info!("同步项目到 Notion 成功");
        Ok(SyncResponse {
            success: true,
            data: result.data.map(|stats| SyncStats {
                total: stats.total,
                created: stats.created,
                updated: stats.updated,
                failed: stats.failed,
            }),
            error: None,
        })
    } else {
        let error_msg = result.error.unwrap_or_else(|| "未知错误".to_string());
        error!("同步项目到 Notion 失败: {}", error_msg);
        Ok(SyncResponse {
            success: false,
            data: None,
            error: Some(error_msg),
        })
    }
}
