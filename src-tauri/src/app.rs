use std::sync::Arc;
use log::{info, error};
use tauri::Manager;
use crate::config::{AppConfig, DatabaseConfig, DatabaseType, SqliteConnection};
use crate::repositories::sqlite::SqliteProjectRepository;
use crate::repositories::mongodb::MongoProjectRepository;
use crate::services::unified::ProjectService;
use crate::commands::unified::ProjectServiceState;
use crate::error::AppError;

/// Initialize the application
pub async fn init_app(app: &mut tauri::App) -> Result<(), AppError> {
    // Load application configuration
    let config_path = AppConfig::default_path();
    let app_config = AppConfig::load(&config_path)?;

    info!("Using database type: {:?}", app_config.database_type);

    // Initialize database configuration
    let db_config = DatabaseConfig::new();

    // Choose which repository to use based on configuration
    let project_repo: Arc<dyn crate::repositories::ProjectRepository> = match app_config.database_type {
        DatabaseType::Sqlite => {
            // Initialize SQLite connection
            let sqlite_conn = db_config.init_sqlite_connection()?;
            let sqlite_conn = Arc::new(SqliteConnection::new(sqlite_conn));

            // Initialize SQLite repositories
            let sqlite_project_repo = Arc::new(SqliteProjectRepository::new(sqlite_conn.clone()));

            // Initialize tables
            sqlite_project_repo.init_table()?;

            info!("Using SQLite database at: {}", db_config.sqlite_path.display());
            sqlite_project_repo
        },
        DatabaseType::MongoDB => {
            // Initialize MongoDB client
            let mongo_client = db_config.init_mongo_client().await?;

            // Initialize MongoDB repositories
            let mongo_project_repo = Arc::new(MongoProjectRepository::new(
                &mongo_client,
                &db_config.mongo_db_name,
                "project_info",
            ));

            info!("Using MongoDB database at: {}", db_config.mongo_uri);
            mongo_project_repo
        }
    };

    // Initialize services
    let project_service = Arc::new(ProjectService::new(project_repo));

    // Initialize service states
    let project_service_state = ProjectServiceState::new(project_service);

    // Manage states
    app.manage(project_service_state);

    info!("Application initialized successfully");
    Ok(())
}
