use std::sync::Arc;
use crate::error::{AppError, ApiResponse};
use crate::models::unified::project::{
    Project, ProjectQuery, ProjectPagination, 
    CreateProjectRequest, UpdateProjectRequest
};
use crate::repositories::project_repository::ProjectRepository;

/// Unified project service that works with any repository implementation
pub struct ProjectService {
    /// The repository implementation
    repository: Arc<dyn ProjectRepository>,
}

impl ProjectService {
    /// Create a new project service with the given repository
    pub fn new(repository: Arc<dyn ProjectRepository>) -> Self {
        Self { repository }
    }
    
    /// Get all projects
    pub async fn get_all_projects(&self) -> Result<Vec<Project>, AppError> {
        self.repository.get_all().await
    }
    
    /// Get project by ID
    pub async fn get_project_by_id(&self, id: &str) -> Result<Option<Project>, AppError> {
        self.repository.get_by_id(id).await
    }
    
    /// Query projects with pagination
    pub async fn query_projects(&self, query: ProjectQuery) -> Result<ProjectPagination, AppError> {
        self.repository.query(&query).await
    }
    
    /// Create a new project
    pub async fn create_project(&self, request: CreateProjectRequest) -> Result<ApiResponse<String>, AppError> {
        // Create project from request
        let project = Project {
            id: None,
            name: request.name,
            short_name: request.short_name,
            number: request.number,
            stage: request.stage,
            status: request.status,
            recruitment_status: request.recruitment_status,
            remarks: request.remarks,
            diseases: request.diseases,
            sponsors: request.sponsors,
            tags: request.tags,
            created_at: None,
            updated_at: None,
        };
        
        // Create project in repository
        let id = self.repository.create(&project).await?;
        
        Ok(ApiResponse::success(id))
    }
    
    /// Update an existing project
    pub async fn update_project(&self, request: UpdateProjectRequest) -> Result<ApiResponse<bool>, AppError> {
        // Check if project exists
        let existing = self.repository.get_by_id(&request.id).await?;
        if existing.is_none() {
            return Ok(ApiResponse::error("Project not found"));
        }
        
        // Create project from request
        let project = Project {
            id: Some(request.id.clone()),
            name: request.name,
            short_name: request.short_name,
            number: request.number,
            stage: request.stage,
            status: request.status,
            recruitment_status: request.recruitment_status,
            remarks: request.remarks,
            diseases: request.diseases,
            sponsors: request.sponsors,
            tags: request.tags,
            created_at: existing.unwrap().created_at,
            updated_at: None,
        };
        
        // Update project in repository
        let updated = self.repository.update(&request.id, &project).await?;
        
        Ok(ApiResponse::success(updated))
    }
    
    /// Delete a project
    pub async fn delete_project(&self, id: &str) -> Result<ApiResponse<bool>, AppError> {
        // Check if project exists
        let existing = self.repository.get_by_id(id).await?;
        if existing.is_none() {
            return Ok(ApiResponse::error("Project not found"));
        }
        
        // Delete project from repository
        let deleted = self.repository.delete(id).await?;
        
        Ok(ApiResponse::success(deleted))
    }
}
