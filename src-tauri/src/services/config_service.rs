use crate::models::config::{Config, Confi<PERSON><PERSON><PERSON>, SystemConfig};
use dotenv::dotenv;
use futures::stream::TryStreamExt;
use log::{error, info};
use mongodb::{
    bson::{doc, DateTime, Document},
    options::ClientOptions,
    Client, Collection,
};
use serde_json::Value;
use std::env;
use std::error::Error;

/// 配置服务
pub struct ConfigService {
    collection: Collection<Document>,
}

impl ConfigService {
    /// 创建新的配置服务实例
    pub async fn new() -> Result<Self, Box<dyn Error>> {
        dotenv().ok();

        let mongo_uri =
            env::var("MONGO_URI").unwrap_or_else(|_| "mongodb://127.0.0.1:27017/".to_string());

        let db_name = env::var("DB_NAME").unwrap_or_else(|_| "project_manage".to_string());

        let collection_name =
            env::var("CONFIG_COLLECTION").unwrap_or_else(|_| "config".to_string());

        let client_options = ClientOptions::parse(&mongo_uri).await?;
        let client = Client::with_options(client_options)?;
        let db = client.database(&db_name);
        let collection = db.collection::<Document>(&collection_name);

        Ok(Self { collection })
    }

    /// 获取系统配置
    pub async fn get_system_config(&self) -> Result<SystemConfig, Box<dyn Error>> {
        let filter = doc! { "config_name": "system_config" };
        let result = self.collection.find_one(filter, None).await?;

        match result {
            Some(doc) => {
                let config = Config::from_document(doc)?;

                if let Some(config_value) = config.config {
                    let system_config: SystemConfig = serde_json::from_value(config_value)?;
                    Ok(system_config)
                } else {
                    Ok(SystemConfig {
                        clinical_research_folder_path: None,
                        notion_api_key: None,
                        notion_database_id: None,
                        other: None,
                    })
                }
            }
            None => {
                // 如果没有找到配置，返回默认配置
                Ok(SystemConfig {
                    clinical_research_folder_path: None,
                    notion_api_key: None,
                    notion_database_id: None,
                    other: None,
                })
            }
        }
    }

    /// 保存系统配置
    pub async fn save_system_config(&self, config: SystemConfig) -> Result<bool, Box<dyn Error>> {
        let config_value = mongodb::bson::to_bson(&config)?
            .as_document()
            .ok_or_else(|| "无法将配置转换为文档".to_string())?
            .clone();

        let now = DateTime::now();
        let update = doc! {
            "$set": {
                "config_name": "system_config",
                "config": config_value,
                "updated_at": now
            },
            "$setOnInsert": {
                "created_at": now
            }
        };

        let filter = doc! { "config_name": "system_config" };
        let options = mongodb::options::UpdateOptions::builder()
            .upsert(true)
            .build();

        let result = self.collection.update_one(filter, update, options).await?;

        Ok(result.modified_count > 0 || result.upserted_id.is_some())
    }

    /// 获取配置列表
    pub async fn get_config_list(&self, config_name: &str) -> Result<ConfigList, Box<dyn Error>> {
        let filter = doc! { "config_name": config_name };
        let result = self.collection.find_one(filter, None).await?;

        match result {
            Some(doc) => {
                let config = Config::from_document(doc)?;

                if let Some(config_value) = config.config {
                    if let Value::Array(items) = config_value {
                        let config_list: Vec<String> = items
                            .into_iter()
                            .filter_map(|v| v.as_str().map(|s| s.to_string()))
                            .collect();

                        Ok(ConfigList {
                            config_name: config_name.to_string(),
                            config: config_list,
                        })
                    } else {
                        Ok(ConfigList {
                            config_name: config_name.to_string(),
                            config: vec![],
                        })
                    }
                } else {
                    Ok(ConfigList {
                        config_name: config_name.to_string(),
                        config: vec![],
                    })
                }
            }
            None => {
                // 如果没有找到配置，返回空列表
                Ok(ConfigList {
                    config_name: config_name.to_string(),
                    config: vec![],
                })
            }
        }
    }

    /// 保存配置列表
    pub async fn save_config_list(
        &self,
        config_name: &str,
        items: Vec<String>,
    ) -> Result<bool, Box<dyn Error>> {
        let config_bson = mongodb::bson::to_bson(&items)?
            .as_document()
            .ok_or_else(|| "无法将配置转换为文档".to_string())?
            .clone();

        let now = DateTime::now();
        let update = doc! {
            "$set": {
                "config_name": config_name,
                "config": config_bson,
                "updated_at": now
            },
            "$setOnInsert": {
                "created_at": now
            }
        };

        let filter = doc! { "config_name": config_name };
        let options = mongodb::options::UpdateOptions::builder()
            .upsert(true)
            .build();

        let result = self.collection.update_one(filter, update, options).await?;

        Ok(result.modified_count > 0 || result.upserted_id.is_some())
    }

    /// 获取所有配置
    pub async fn get_all_configs(&self) -> Result<Vec<Config>, Box<dyn Error>> {
        let cursor = self.collection.find(None, None).await?;
        let documents: Vec<Document> = cursor.try_collect().await?;

        let mut configs = Vec::new();
        for doc in documents {
            match Config::from_document(doc) {
                Ok(config) => configs.push(config),
                Err(e) => error!("解析配置文档失败: {}", e),
            }
        }

        info!("获取了 {} 个配置", configs.len());
        Ok(configs)
    }

    /// 获取配置
    pub async fn get_config(&self, config_name: &str) -> Result<Option<Config>, Box<dyn Error>> {
        let filter = doc! { "config_name": config_name };
        let result = self.collection.find_one(filter, None).await?;

        match result {
            Some(doc) => {
                let config = Config::from_document(doc)?;
                Ok(Some(config))
            }
            None => Ok(None),
        }
    }

    /// 保存配置
    pub async fn save_config(&self, config: Config) -> Result<bool, Box<dyn Error>> {
        let mut doc = config.to_document()?;

        // 确保创建和更新时间存在
        let now = DateTime::now();
        if !doc.contains_key("updated_at") {
            doc.insert("updated_at", now);
        }

        let config_name = doc.get_str("config_name")?.to_string();

        // 移除 _id 字段，避免更新时冲突
        doc.remove("_id");

        let update = doc! {
            "$set": doc.clone(),
            "$setOnInsert": {
                "created_at": now
            }
        };

        let filter = doc! { "config_name": config_name };
        let options = mongodb::options::UpdateOptions::builder()
            .upsert(true)
            .build();

        let result = self.collection.update_one(filter, update, options).await?;

        Ok(result.modified_count > 0 || result.upserted_id.is_some())
    }

    /// 删除配置
    pub async fn delete_config(&self, config_name: &str) -> Result<bool, Box<dyn Error>> {
        let filter = doc! { "config_name": config_name };
        let result = self.collection.delete_one(filter, None).await?;

        Ok(result.deleted_count > 0)
    }
}

// 创建单例实例
lazy_static::lazy_static! {
    pub static ref CONFIG_SERVICE: tokio::sync::OnceCell<ConfigService> = tokio::sync::OnceCell::new();
}
