use log::{error, info};
use regex::Regex;
use rusqlite::{params, Connection};
use std::collections::HashSet;

use crate::models::project_management::{
    CsvImportError, CsvImportErrorType, CsvImportRecord, CsvImportResult, CsvImportStatistics,
    CsvImportValidation, CsvPersonnelRow,
    ParsedPersonnelRole, PersonnelMatch, PersonnelRoleAssignment, ProjectMatch, QualityControlResult,
    RoleMatch,
};

/// CSV导入服务
pub struct CsvImportService {
    db_path: String,
}

impl CsvImportService {
    /// 创建新的CSV导入服务实例
    pub fn new(db_path: String) -> Self {
        Self { db_path }
    }

    /// 解析CSV内容并验证数据
    pub fn parse_and_validate_csv(&self, csv_content: &str) -> Result<CsvImportValidation, String> {
        info!("开始解析CSV内容");

        // 解析CSV行
        let csv_rows = self.parse_csv_content(csv_content)?;
        info!("解析到 {} 行CSV数据", csv_rows.len());

        // 验证数据
        self.validate_csv_data(csv_rows)
    }

    /// 解析CSV内容为结构化数据
    fn parse_csv_content(&self, csv_content: &str) -> Result<Vec<CsvPersonnelRow>, String> {
        let mut rows = Vec::new();
        let lines: Vec<&str> = csv_content.lines().collect();

        if lines.is_empty() {
            return Err("CSV文件为空".to_string());
        }

        // 检查标题行
        let header = lines[0];
        if !header.contains("项目") || !header.contains("授权人员") {
            return Err("CSV文件缺少必要的列标题: 项目、授权人员".to_string());
        }

        // 跳过标题行
        for (index, line) in lines.iter().enumerate().skip(1) {
            if line.trim().is_empty() {
                continue;
            }

            // 解析CSV行，处理可能包含逗号的字段
            let parts = self.parse_csv_line(line)?;

            if parts.len() >= 5 {
                // 根据实际CSV格式：序号,项目,项目全称,启动日期,授权人员,...
                let project_short_name = parts[1].trim().to_string();
                let project_full_name = parts[2].trim().to_string();
                let authorized_personnel = parts[4].trim().to_string();

                // 优先使用项目简称，如果为空则使用全称
                let project_name = if !project_short_name.is_empty() {
                    project_short_name
                } else {
                    project_full_name
                };

                if !project_name.is_empty() && !authorized_personnel.is_empty() {
                    rows.push(CsvPersonnelRow {
                        project_name,
                        authorized_personnel,
                        row_number: index + 1,
                    });
                }
            }
        }

        Ok(rows)
    }

    /// 解析CSV行，处理引号包围的字段
    fn parse_csv_line(&self, line: &str) -> Result<Vec<String>, String> {
        let mut fields = Vec::new();
        let mut current_field = String::new();
        let mut in_quotes = false;
        let mut chars = line.chars().peekable();

        while let Some(ch) = chars.next() {
            match ch {
                '"' => {
                    in_quotes = !in_quotes;
                }
                ',' if !in_quotes => {
                    fields.push(current_field.trim().trim_matches('"').to_string());
                    current_field.clear();
                }
                _ => {
                    current_field.push(ch);
                }
            }
        }

        // 添加最后一个字段
        fields.push(current_field.trim().trim_matches('"').to_string());

        Ok(fields)
    }

    /// 验证CSV数据
    fn validate_csv_data(&self, csv_rows: Vec<CsvPersonnelRow>) -> Result<CsvImportValidation, String> {
        let conn = self.get_connection()?;
        let mut valid_records = Vec::new();
        let mut errors = Vec::new();
        let warnings = Vec::new();

        let mut total_assignments = 0;
        let mut new_assignments = 0;
        let mut duplicate_assignments = 0;
        let mut affected_projects = HashSet::new();

        let total_rows = csv_rows.len(); // 保存长度

        for row in csv_rows {
            match self.validate_single_row(&conn, &row) {
                Ok(record) => {
                    affected_projects.insert(record.project_match.project_id.clone());

                    for assignment in &record.personnel_assignments {
                        total_assignments += 1;
                        if assignment.already_exists {
                            duplicate_assignments += 1;
                        } else {
                            new_assignments += 1;
                        }
                    }

                    valid_records.push(record);
                }
                Err(error) => {
                    errors.push(error);
                }
            }
        }

        let statistics = CsvImportStatistics {
            total_rows,
            successful_rows: valid_records.len(),
            error_rows: errors.len(),
            warning_rows: warnings.len(),
            total_assignments,
            new_assignments,
            duplicate_assignments,
            affected_projects: affected_projects.len(),
        };

        Ok(CsvImportValidation {
            valid_records,
            errors,
            warnings,
            statistics,
        })
    }

    /// 验证单行数据
    fn validate_single_row(&self, conn: &Connection, row: &CsvPersonnelRow) -> Result<CsvImportRecord, CsvImportError> {
        // 1. 匹配项目
        let project_match = self.match_project(conn, &row.project_name)
            .map_err(|_| CsvImportError {
                error_type: CsvImportErrorType::ProjectNotFound,
                row_number: row.row_number,
                message: format!("未找到项目: {}", row.project_name),
                context: Some(row.project_name.clone()),
            })?;

        // 2. 解析授权人员字符串
        let parsed_roles = self.parse_personnel_string(&row.authorized_personnel)
            .map_err(|err| CsvImportError {
                error_type: CsvImportErrorType::PersonnelParsingError,
                row_number: row.row_number,
                message: format!("解析授权人员失败: {}", err),
                context: Some(row.authorized_personnel.clone()),
            })?;

        // 3. 匹配人员和角色
        let mut personnel_assignments = Vec::new();
        for parsed_role in parsed_roles {
            let personnel_match = self.match_personnel(conn, &parsed_role.name)
                .map_err(|_| CsvImportError {
                    error_type: CsvImportErrorType::PersonnelNotFound,
                    row_number: row.row_number,
                    message: format!("未找到人员: {}", parsed_role.name),
                    context: Some(parsed_role.name.clone()),
                })?;

            let role_match = self.match_role(conn, &parsed_role.role_name)
                .map_err(|_| CsvImportError {
                    error_type: CsvImportErrorType::RoleNotFound,
                    row_number: row.row_number,
                    message: format!("未找到角色: {}", parsed_role.role_name),
                    context: Some(parsed_role.role_name.clone()),
                })?;

            // 检查是否已存在相同分配
            let already_exists = self.check_assignment_exists(
                conn,
                &project_match.project_id,
                personnel_match.personnel_id,
                role_match.role_item_id,
            ).unwrap_or(false);

            personnel_assignments.push(PersonnelRoleAssignment {
                personnel_match,
                role_match,
                parsed_role,
                already_exists,
            });
        }

        Ok(CsvImportRecord {
            row_number: row.row_number,
            project_match,
            personnel_assignments,
            original_row: row.clone(),
        })
    }

    /// 解析授权人员字符串
    /// 支持格式: "1：（人员名称：张三，角色：PI），2：（人员名称：李四，角色：CRC），..."
    fn parse_personnel_string(&self, personnel_str: &str) -> Result<Vec<ParsedPersonnelRole>, String> {
        let mut roles = Vec::new();

        // 使用正则表达式匹配复杂格式：序号：（人员名称：姓名，角色：角色名）
        let re = Regex::new(r"\d+：（人员名称：([^，]+)，角色：([^）]+)）")
            .map_err(|e| format!("正则表达式错误: {}", e))?;

        for cap in re.captures_iter(personnel_str) {
            let name = cap[1].trim().to_string();
            let role_str = cap[2].trim().to_string();

            if !name.is_empty() && !role_str.is_empty() {
                // 处理多个角色的情况，用逗号分隔
                let role_parts: Vec<&str> = role_str.split('，').collect();

                for role_part in role_parts {
                    let role_name = role_part.trim().to_string();
                    if !role_name.is_empty() {
                        // 角色名称映射
                        let mapped_role = self.map_role_name(&role_name);

                        roles.push(ParsedPersonnelRole {
                            name: name.clone(),
                            role_name: mapped_role,
                            original_string: format!("{}({})", name, role_name),
                        });
                    }
                }
            }
        }

        if roles.is_empty() {
            return Err(format!("无法解析授权人员字符串: {}", personnel_str));
        }

        Ok(roles)
    }

    /// 角色名称映射，将CSV中的角色名称映射到数据库中的标准角色名称
    fn map_role_name(&self, role_name: &str) -> String {
        match role_name {
            // 将常见的简写映射到数据库中的完整名称
            "PI" => "主要研究者".to_string(),
            "CRC" => "临床协调员(CRC)".to_string(),
            "SUBI" => "协调研究医生".to_string(),
            // 保持原有的完整名称不变
            "主要研究者" => "主要研究者".to_string(),
            "临床协调员(CRC)" => "临床协调员(CRC)".to_string(),
            "CRA" => "CRA".to_string(),
            "研究护士" => "研究护士".to_string(),
            "协调研究医生" => "协调研究医生".to_string(),
            "研究医生" => "研究医生".to_string(),
            "药品管理员" => "药品管理员".to_string(),
            "样本管理员" => "样本管理员".to_string(),
            "物资管理员" => "物资管理员".to_string(),
            "肺功能师" => "肺功能师".to_string(),
            "质控员" => "质控员".to_string(),
            "非盲护士" => "非盲护士".to_string(),
            "非盲药品管理员" => "非盲药品管理员".to_string(),
            "盲态护士" => "盲态护士".to_string(),
            "盲态药品管理员" => "盲态药品管理员".to_string(),
            _ if role_name.starts_with("其他:") => role_name.to_string(),
            _ => role_name.to_string(),
        }
    }

    /// 匹配项目
    fn match_project(&self, conn: &Connection, project_name: &str) -> Result<ProjectMatch, String> {
        // 首先尝试精确匹配项目简称
        let mut stmt = conn.prepare(
            "SELECT project_id, project_short_name, project_name FROM projects 
             WHERE project_short_name = ? OR project_name = ?"
        ).map_err(|e| format!("准备SQL语句失败: {}", e))?;

        let mut rows = stmt.query_map(params![project_name, project_name], |row| {
            Ok((
                row.get::<_, String>(0)?,
                row.get::<_, String>(1)?,
                row.get::<_, String>(2)?,
            ))
        }).map_err(|e| format!("查询项目失败: {}", e))?;

        if let Some(row) = rows.next() {
            let (project_id, short_name, full_name) = row.map_err(|e| format!("读取项目数据失败: {}", e))?;
            
            let match_type = if short_name == project_name {
                "short_name".to_string()
            } else {
                "full_name".to_string()
            };

            return Ok(ProjectMatch {
                project_id,
                project_short_name: short_name,
                project_name: full_name,
                match_type,
            });
        }

        Err(format!("未找到项目: {}", project_name))
    }

    /// 匹配人员
    fn match_personnel(&self, conn: &Connection, name: &str) -> Result<PersonnelMatch, String> {
        let mut stmt = conn.prepare("SELECT id, name FROM staff WHERE name = ?")
            .map_err(|e| format!("准备SQL语句失败: {}", e))?;

        let mut rows = stmt.query_map(params![name], |row| {
            Ok((row.get::<_, i64>(0)?, row.get::<_, String>(1)?))
        }).map_err(|e| format!("查询人员失败: {}", e))?;

        if let Some(row) = rows.next() {
            let (personnel_id, personnel_name) = row.map_err(|e| format!("读取人员数据失败: {}", e))?;
            return Ok(PersonnelMatch {
                personnel_id,
                name: personnel_name,
                exact_match: true,
            });
        }

        Err(format!("未找到人员: {}", name))
    }

    /// 匹配角色
    fn match_role(&self, conn: &Connection, role_name: &str) -> Result<RoleMatch, String> {
        let mut stmt = conn.prepare(
            "SELECT di.item_id, di.item_value FROM dictionary_items di 
             JOIN dictionaries d ON di.dictionary_id = d.id 
             WHERE d.name = '研究角色' AND di.item_value = ?"
        ).map_err(|e| format!("准备SQL语句失败: {}", e))?;

        let mut rows = stmt.query_map(params![role_name], |row| {
            Ok((row.get::<_, i64>(0)?, row.get::<_, String>(1)?))
        }).map_err(|e| format!("查询角色失败: {}", e))?;

        if let Some(row) = rows.next() {
            let (role_item_id, role_value) = row.map_err(|e| format!("读取角色数据失败: {}", e))?;
            return Ok(RoleMatch {
                role_item_id,
                role_name: role_value,
                exact_match: true,
            });
        }

        Err(format!("未找到角色: {}", role_name))
    }

    /// 检查分配是否已存在
    fn check_assignment_exists(&self, conn: &Connection, project_id: &str, personnel_id: i64, role_item_id: i64) -> Result<bool, String> {
        let mut stmt = conn.prepare(
            "SELECT COUNT(*) FROM project_personnel_roles 
             WHERE project_id = ? AND personnel_id = ? AND role_item_id = ?"
        ).map_err(|e| format!("准备SQL语句失败: {}", e))?;

        let count: i64 = stmt.query_row(params![project_id, personnel_id, role_item_id], |row| {
            row.get(0)
        }).map_err(|e| format!("查询分配记录失败: {}", e))?;

        Ok(count > 0)
    }

    /// 执行CSV导入
    pub fn execute_import(&self, validation: CsvImportValidation) -> Result<CsvImportResult, String> {
        info!("开始执行CSV导入，共 {} 条有效记录", validation.valid_records.len());

        let conn = self.get_connection()?;
        let mut imported_records = 0;
        let mut skipped_records = 0;
        let mut errors = Vec::new();
        let mut warnings = Vec::new();

        // 开始事务
        conn.execute("BEGIN TRANSACTION", []).map_err(|e| format!("开始事务失败: {}", e))?;

        for record in validation.valid_records {
            match self.import_single_record(&conn, &record) {
                Ok(imported_count) => {
                    imported_records += imported_count;
                    if imported_count == 0 {
                        skipped_records += 1;
                    }
                }
                Err(err) => {
                    errors.push(format!("行 {}: {}", record.row_number, err));
                }
            }
        }

        // 提交事务
        if errors.is_empty() {
            conn.execute("COMMIT", []).map_err(|e| format!("提交事务失败: {}", e))?;
            info!("CSV导入成功完成，导入 {} 条记录", imported_records);
        } else {
            conn.execute("ROLLBACK", []).map_err(|e| format!("回滚事务失败: {}", e))?;
            error!("CSV导入失败，已回滚所有更改");
        }

        Ok(CsvImportResult {
            success: errors.is_empty(),
            imported_records,
            skipped_records,
            errors,
            warnings,
            statistics: validation.statistics,
        })
    }

    /// 导入单条记录
    fn import_single_record(&self, conn: &Connection, record: &CsvImportRecord) -> Result<usize, String> {
        let mut imported_count = 0;

        for assignment in &record.personnel_assignments {
            if !assignment.already_exists {
                // 插入新的人员角色分配
                conn.execute(
                    "INSERT INTO project_personnel_roles (project_id, personnel_id, role_item_id) VALUES (?, ?, ?)",
                    params![
                        record.project_match.project_id,
                        assignment.personnel_match.personnel_id,
                        assignment.role_match.role_item_id
                    ],
                ).map_err(|e| format!("插入人员角色分配失败: {}", e))?;

                imported_count += 1;
                info!(
                    "成功分配人员 {} 到项目 {} 担任角色 {}",
                    assignment.personnel_match.name,
                    record.project_match.project_short_name,
                    assignment.role_match.role_name
                );
            }
        }

        Ok(imported_count)
    }

    /// 执行质量控制检查
    pub fn perform_quality_control(&self, project_ids: Option<Vec<String>>) -> Result<Vec<QualityControlResult>, String> {
        info!("开始执行质量控制检查");

        let conn = self.get_connection()?;
        let mut results = Vec::new();

        // 关键角色列表（使用数据库中的实际角色名称）
        let critical_roles = vec!["主要研究者", "临床协调员(CRC)", "CRA"];

        // 获取要检查的项目列表
        let projects = if let Some(ids) = project_ids {
            self.get_projects_by_ids(&conn, &ids)?
        } else {
            self.get_all_projects(&conn)?
        };

        for (project_id, project_name) in projects {
            let missing_roles = self.check_missing_critical_roles(&conn, &project_id, &critical_roles)?;
            let quality_passed = missing_roles.is_empty();

            let recommendations = if !quality_passed {
                vec![format!("建议为项目 {} 分配以下关键角色: {}", project_name, missing_roles.join(", "))]
            } else {
                vec!["项目人员配置完整".to_string()]
            };

            results.push(QualityControlResult {
                project_id,
                project_name,
                missing_critical_roles: missing_roles,
                quality_passed,
                recommendations,
            });
        }

        info!("质量控制检查完成，检查了 {} 个项目", results.len());
        Ok(results)
    }

    /// 检查项目缺失的关键角色
    fn check_missing_critical_roles(&self, conn: &Connection, project_id: &str, critical_roles: &[&str]) -> Result<Vec<String>, String> {
        let mut missing_roles = Vec::new();

        for role_name in critical_roles {
            let mut stmt = conn.prepare(
                "SELECT COUNT(*) FROM project_personnel_roles ppr
                 JOIN dictionary_items di ON ppr.role_item_id = di.item_id
                 JOIN dictionaries d ON di.dictionary_id = d.id
                 WHERE ppr.project_id = ? AND d.name = '研究角色' AND di.item_value = ?"
            ).map_err(|e| format!("准备SQL语句失败: {}", e))?;

            let count: i64 = stmt.query_row(params![project_id, role_name], |row| {
                row.get(0)
            }).map_err(|e| format!("查询角色分配失败: {}", e))?;

            if count == 0 {
                missing_roles.push(role_name.to_string());
            }
        }

        Ok(missing_roles)
    }

    /// 获取指定ID的项目列表
    fn get_projects_by_ids(&self, conn: &Connection, project_ids: &[String]) -> Result<Vec<(String, String)>, String> {
        let mut projects = Vec::new();

        for project_id in project_ids {
            let mut stmt = conn.prepare("SELECT project_id, project_name FROM projects WHERE project_id = ?")
                .map_err(|e| format!("准备SQL语句失败: {}", e))?;

            let mut rows = stmt.query_map(params![project_id], |row| {
                Ok((row.get::<_, String>(0)?, row.get::<_, String>(1)?))
            }).map_err(|e| format!("查询项目失败: {}", e))?;

            if let Some(row) = rows.next() {
                let (id, name) = row.map_err(|e| format!("读取项目数据失败: {}", e))?;
                projects.push((id, name));
            }
        }

        Ok(projects)
    }

    /// 获取所有项目列表
    fn get_all_projects(&self, conn: &Connection) -> Result<Vec<(String, String)>, String> {
        let mut stmt = conn.prepare("SELECT project_id, project_name FROM projects")
            .map_err(|e| format!("准备SQL语句失败: {}", e))?;

        let rows = stmt.query_map([], |row| {
            Ok((row.get::<_, String>(0)?, row.get::<_, String>(1)?))
        }).map_err(|e| format!("查询项目失败: {}", e))?;

        let mut projects = Vec::new();
        for row in rows {
            let (id, name) = row.map_err(|e| format!("读取项目数据失败: {}", e))?;
            projects.push((id, name));
        }

        Ok(projects)
    }

    /// 获取数据库连接
    fn get_connection(&self) -> Result<Connection, String> {
        Connection::open(&self.db_path).map_err(|e| format!("连接数据库失败: {}", e))
    }
}
