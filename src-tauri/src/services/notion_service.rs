use dotenv::dotenv;
use futures::stream::TryStreamExt;
use log::{error, info, warn};
use mongodb::{
    bson::{doc, Document},
    options::ClientOptions,
    Client,
};
use reqwest::header::{HeaderMap, AUTHORIZATION, CONTENT_TYPE};
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};
use std::collections::HashMap;
use std::env;
use std::error::Error;

// Notion API 响应结构
#[derive(Debug, Deserialize)]
struct NotionResponse {
    object: String,
    results: Option<Vec<Value>>,
    #[serde(flatten)]
    other: HashMap<String, Value>,
}

// 同步结果结构
#[derive(Debug, Serialize)]
pub struct SyncResult {
    pub success: bool,
    pub data: Option<SyncStats>,
    pub error: Option<String>,
}

// 同步统计结构
#[derive(Debug, Serialize)]
pub struct SyncStats {
    pub total: usize,
    pub created: usize,
    pub updated: usize,
    pub failed: usize,
}

// Notion 服务结构
pub struct NotionService {
    api_key: String,
    database_id: String,
    mongo_uri: String,
    db_name: String,
    collection_name: String,
    property_types: HashMap<String, String>,
    status_options: HashMap<String, Vec<String>>,
}

impl NotionService {
    // 创建新的 Notion 服务实例
    pub fn new() -> Self {
        dotenv().ok();

        let api_key = env::var("NOTION_API_KEY")
            .unwrap_or_else(|_| "ntn_V40341044546PN6Fp3PvPWFVHa9qOvfuv2BAEQiuPddbhP".to_string());

        let database_id = env::var("NOTION_DATABASE_ID")
            .unwrap_or_else(|_| "1b00f0738c86806ba2d9df765ad1f83d".to_string());

        let mongo_uri =
            env::var("MONGO_URI").unwrap_or_else(|_| "mongodb://127.0.0.1:27017/".to_string());

        NotionService {
            api_key,
            database_id,
            mongo_uri,
            db_name: "project_manage".to_string(),
            collection_name: "project_info".to_string(),
            property_types: HashMap::new(),
            status_options: HashMap::new(),
        }
    }

    // 从 MongoDB 获取所有项目
    async fn get_all_projects(&self) -> Result<Vec<Document>, Box<dyn Error>> {
        let client_options = ClientOptions::parse(&self.mongo_uri).await?;
        let client = Client::with_options(client_options)?;
        let db = client.database(&self.db_name);
        let collection = db.collection::<Document>(&self.collection_name);

        let cursor = collection.find(None, None).await?;
        let projects: Vec<Document> = cursor.try_collect().await?;

        info!("从 MongoDB 获取了 {} 个项目", projects.len());
        Ok(projects)
    }

    // 获取 Notion 数据库中现有的页面
    async fn get_existing_notion_pages(&mut self) -> Result<Vec<Value>, Box<dyn Error>> {
        let client = reqwest::Client::new();

        // 设置请求头
        let mut headers = HeaderMap::new();
        headers.insert(AUTHORIZATION, format!("Bearer {}", self.api_key).parse()?);
        headers.insert(CONTENT_TYPE, "application/json".parse()?);
        headers.insert("Notion-Version", "2022-06-28".parse()?);

        // 首先获取数据库信息以了解属性类型
        let database_url = format!("https://api.notion.com/v1/databases/{}", self.database_id);
        let database_response = client
            .get(&database_url)
            .headers(headers.clone())
            .send()
            .await?;

        if !database_response.status().is_success() {
            let error_text = database_response.text().await?;
            error!("无法连接到 Notion 数据库: {}", error_text);
            return Err(format!("无法连接到 Notion 数据库: {}", error_text).into());
        }

        let database_json: Value = database_response.json().await?;

        // 获取并存储数据库属性类型
        if let Some(properties) = database_json.get("properties").and_then(|p| p.as_object()) {
            info!(
                "Notion 数据库属性: {:?}",
                properties.keys().collect::<Vec<_>>()
            );

            for (prop_name, prop_value) in properties {
                if let Some(prop_type) = prop_value.get("type").and_then(|t| t.as_str()) {
                    self.property_types
                        .insert(prop_name.clone(), prop_type.to_string());
                    info!("属性 '{}' 的类型是: {}", prop_name, prop_type);

                    // 如果是 status 类型，记录可用的选项
                    if prop_type == "status" {
                        if let Some(options) = prop_value
                            .get("status")
                            .and_then(|s| s.get("options"))
                            .and_then(|o| o.as_array())
                        {
                            let option_names: Vec<String> = options
                                .iter()
                                .filter_map(|opt| {
                                    opt.get("name")
                                        .and_then(|n| n.as_str())
                                        .map(|s| s.to_string())
                                })
                                .collect();

                            self.status_options
                                .insert(prop_name.clone(), option_names.clone());
                            info!("属性 '{}' 的可用选项: {:?}", prop_name, option_names);
                        }
                    }
                }
            }
        }

        // 查询数据库中的页面
        let query_url = format!(
            "https://api.notion.com/v1/databases/{}/query",
            self.database_id
        );
        let query_response = client
            .post(&query_url)
            .headers(headers)
            .json(&json!({}))
            .send()
            .await?;

        if !query_response.status().is_success() {
            let error_text = query_response.text().await?;
            error!("查询 Notion 数据库失败: {}", error_text);
            return Err(format!("查询 Notion 数据库失败: {}", error_text).into());
        }

        let response: NotionResponse = query_response.json().await?;
        let pages = response.results.unwrap_or_default();

        info!("从 Notion 获取了 {} 个页面", pages.len());

        // 打印第一个页面的结构以便调试
        if let Some(first_page) = pages.first() {
            if let Some(properties) = first_page.get("properties").and_then(|p| p.as_object()) {
                info!(
                    "Notion 页面结构示例: {:?}",
                    properties.keys().collect::<Vec<_>>()
                );
            }
        }

        Ok(pages)
    }

    // 准备 Notion 页面属性
    fn prepare_page_properties(&self, project: &Document) -> Value {
        // 将项目简称设为 title 属性
        let project_short_name = project.get_str("project_short_name").unwrap_or("");

        let mut properties = json!({
            "项目简称": {
                "title": [
                    {
                        "text": {
                            "content": project_short_name
                        }
                    }
                ]
            }
        });

        // 处理富文本属性
        let project_name = project.get_str("project_name").unwrap_or("");
        let sponsor = self.format_field_value(project, "sponsor");
        let disease = self.format_field_value(project, "disease");

        let rich_text_fields = [
            ("项目名称", project_name),
            ("申办方", sponsor.as_str()),
            ("适应症", disease.as_str()),
        ];

        for (field_name, content) in rich_text_fields.iter() {
            if !content.is_empty() {
                // 检查字段类型
                if let Some(field_type) = self.property_types.get(*field_name) {
                    if field_type == "rich_text" || *field_name == "适应症" {
                        let content_limited = if content.len() > 2000 {
                            &content[0..2000]
                        } else {
                            content
                        };

                        if let Some(props) = properties.as_object_mut() {
                            props.insert(
                                field_name.to_string(),
                                json!({
                                    "rich_text": [
                                        {
                                            "text": {
                                                "content": content_limited
                                            }
                                        }
                                    ]
                                }),
                            );
                        }
                    }
                }
            }
        }

        // 处理选择类型属性
        let select_fields = [
            ("研究分期", project.get_str("project_stage").unwrap_or("")),
            ("项目状态", project.get_str("project_status").unwrap_or("")),
            (
                "招募状态",
                project.get_str("recruitment_status").unwrap_or(""),
            ),
        ];

        for (field_name, value) in select_fields.iter() {
            if value.is_empty() || !self.property_types.contains_key(*field_name) {
                continue;
            }

            if let Some(field_type) = self.property_types.get(*field_name) {
                let value_limited = if value.len() > 100 {
                    &value[0..100]
                } else {
                    value
                };

                if let Some(props) = properties.as_object_mut() {
                    if field_type == "select" {
                        props.insert(
                            field_name.to_string(),
                            json!({
                                "select": {
                                    "name": value_limited
                                }
                            }),
                        );
                    } else if field_type == "status" {
                        // 检查状态选项是否存在
                        if let Some(options) = self.status_options.get(*field_name) {
                            if options.contains(&value_limited.to_string()) {
                                props.insert(
                                    field_name.to_string(),
                                    json!({
                                        "status": {
                                            "name": value_limited
                                        }
                                    }),
                                );
                            } else {
                                warn!(
                                    "状态选项 '{}' 在字段 '{}' 中不存在，跳过此字段",
                                    value_limited, field_name
                                );
                            }
                        }
                    } else if field_type == "multi_select" {
                        // 处理多选字段
                        let multi_values = if let Ok(array) = project.get_array(*field_name) {
                            array
                                .iter()
                                .filter_map(|v| v.as_str().map(|s| s.to_string()))
                                .collect::<Vec<String>>()
                        } else {
                            vec![value_limited.to_string()]
                        };

                        let multi_select_values = multi_values
                            .iter()
                            .filter(|v| !v.is_empty())
                            .map(|v| {
                                let v_limited = if v.len() > 100 { &v[0..100] } else { v };
                                json!({"name": v_limited})
                            })
                            .collect::<Vec<Value>>();

                        props.insert(
                            field_name.to_string(),
                            json!({
                                "multi_select": multi_select_values
                            }),
                        );
                    }
                }
            }
        }

        properties
    }

    // 格式化字段值
    fn format_field_value(&self, project: &Document, field_name: &str) -> String {
        if let Ok(array) = project.get_array(field_name) {
            array
                .iter()
                .filter_map(|v| v.as_str())
                .collect::<Vec<&str>>()
                .join(", ")
        } else if let Ok(value) = project.get_str(field_name) {
            value.to_string()
        } else {
            String::new()
        }
    }

    // 将项目数据同步到 Notion
    pub async fn sync_projects_to_notion(&mut self) -> SyncResult {
        match self._sync_projects_to_notion().await {
            Ok(stats) => SyncResult {
                success: true,
                data: Some(stats),
                error: None,
            },
            Err(e) => {
                error!("同步项目到 Notion 时出错: {}", e);
                SyncResult {
                    success: false,
                    data: None,
                    error: Some(e.to_string()),
                }
            }
        }
    }

    // 内部同步实现
    async fn _sync_projects_to_notion(&mut self) -> Result<SyncStats, Box<dyn Error>> {
        // 获取所有项目
        let projects = self.get_all_projects().await?;
        if projects.is_empty() {
            return Err("没有找到项目数据".into());
        }

        // 获取 Notion 数据库中现有的项目
        let existing_pages = self.get_existing_notion_pages().await?;

        // 安全地提取现有项目简称
        let mut existing_project_names = HashMap::new();
        for page in &existing_pages {
            if let Some(properties) = page.get("properties").and_then(|p| p.as_object()) {
                if let Some(title_prop) = properties.get("项目简称") {
                    if let Some(title_array) = title_prop.get("title").and_then(|t| t.as_array()) {
                        if !title_array.is_empty() {
                            if let Some(text) = title_array[0].get("text") {
                                if let Some(content) = text.get("content").and_then(|c| c.as_str())
                                {
                                    if !content.is_empty() {
                                        if let Some(id) = page.get("id").and_then(|i| i.as_str()) {
                                            existing_project_names
                                                .insert(content.to_string(), id.to_string());
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        // 同步结果统计
        let mut stats = SyncStats {
            total: projects.len(),
            created: 0,
            updated: 0,
            failed: 0,
        };

        // 设置 HTTP 客户端
        let client = reqwest::Client::new();
        let mut headers = HeaderMap::new();
        headers.insert(AUTHORIZATION, format!("Bearer {}", self.api_key).parse()?);
        headers.insert(CONTENT_TYPE, "application/json".parse()?);
        headers.insert("Notion-Version", "2022-06-28".parse()?);

        // 同步每个项目
        for project in projects {
            let project_short_name = project.get_str("project_short_name").unwrap_or("");
            if project_short_name.is_empty() {
                warn!(
                    "跳过没有项目简称的项目: {}",
                    project.get_str("project_name").unwrap_or("Unknown")
                );
                stats.failed += 1;
                continue;
            }

            // 准备 Notion 页面属性
            let page_properties = self.prepare_page_properties(&project);

            // 如果项目已存在，则更新；否则创建新项目
            if let Some(page_id) = existing_project_names.get(project_short_name) {
                // 更新现有页面
                let update_url = format!("https://api.notion.com/v1/pages/{}", page_id);
                let update_response = client
                    .patch(&update_url)
                    .headers(headers.clone())
                    .json(&json!({
                        "properties": page_properties
                    }))
                    .send()
                    .await;

                match update_response {
                    Ok(response) => {
                        if response.status().is_success() {
                            stats.updated += 1;
                            info!("更新了 Notion 中的项目: {}", project_short_name);
                        } else {
                            let error_text = response
                                .text()
                                .await
                                .unwrap_or_else(|_| "未知错误".to_string());
                            error!("更新项目 {} 时出错: {}", project_short_name, error_text);
                            stats.failed += 1;
                        }
                    }
                    Err(e) => {
                        error!("更新项目 {} 时出错: {}", project_short_name, e);
                        stats.failed += 1;
                    }
                }
            } else {
                // 创建新页面
                let create_url = "https://api.notion.com/v1/pages";
                let create_response = client
                    .post(create_url)
                    .headers(headers.clone())
                    .json(&json!({
                        "parent": { "database_id": self.database_id },
                        "properties": page_properties
                    }))
                    .send()
                    .await;

                match create_response {
                    Ok(response) => {
                        if response.status().is_success() {
                            stats.created += 1;
                            info!("在 Notion 中创建了新项目: {}", project_short_name);
                        } else {
                            let error_text = response
                                .text()
                                .await
                                .unwrap_or_else(|_| "未知错误".to_string());
                            error!("创建项目 {} 时出错: {}", project_short_name, error_text);
                            stats.failed += 1;
                        }
                    }
                    Err(e) => {
                        error!("创建项目 {} 时出错: {}", project_short_name, e);
                        stats.failed += 1;
                    }
                }
            }
        }

        Ok(stats)
    }
}

// 创建单例实例
lazy_static::lazy_static! {
    pub static ref NOTION_SERVICE: tokio::sync::Mutex<NotionService> = tokio::sync::Mutex::new(NotionService::new());
}
