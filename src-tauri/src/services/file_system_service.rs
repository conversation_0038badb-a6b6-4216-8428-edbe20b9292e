use crate::models::file_system::{FileInfo, FileTag, TagRule};
use chrono::{DateTime, Local};
use std::error::Error;
use std::fs;
use std::io;
use std::path::Path;

/// 文件系统服务
pub struct FileSystemService;

impl FileSystemService {
    /// 创建新的文件系统服务实例
    pub fn new() -> Self {
        Self {}
    }

    /// 获取文件列表
    pub fn get_files(&self, dir_path: &str) -> Result<Vec<FileInfo>, Box<dyn Error>> {
        let path = Path::new(dir_path);
        if !path.exists() {
            return Err(format!("路径不存在: {}", dir_path).into());
        }

        if !path.is_dir() {
            return Err(format!("路径不是目录: {}", dir_path).into());
        }

        let mut files = Vec::new();

        for entry in fs::read_dir(path)? {
            let entry = entry?;
            let path = entry.path();
            let metadata = fs::metadata(&path)?;

            let modified = metadata.modified()?;
            let modified_time: DateTime<Local> = DateTime::from(modified);

            files.push(FileInfo {
                name: path.file_name().unwrap().to_string_lossy().to_string(),
                path: path.to_string_lossy().to_string(),
                size: metadata.len(),
                modified_time: modified_time.to_rfc3339(),
                tags: None,
            });
        }

        Ok(files)
    }

    /// 打开文件
    pub fn open_file(&self, file_path: &str) -> Result<(), Box<dyn Error>> {
        let path = Path::new(file_path);
        if !path.exists() {
            return Err(format!("文件不存在: {}", file_path).into());
        }

        if !path.is_file() {
            return Err(format!("路径不是文件: {}", file_path).into());
        }

        #[cfg(target_os = "windows")]
        {
            use std::process::Command;
            Command::new("cmd")
                .args(&["/C", "start", "", file_path])
                .spawn()?;
        }

        #[cfg(target_os = "macos")]
        {
            use std::process::Command;
            Command::new("open").arg(file_path).spawn()?;
        }

        #[cfg(target_os = "linux")]
        {
            use std::process::Command;
            Command::new("xdg-open").arg(file_path).spawn()?;
        }

        Ok(())
    }

    /// 打开文件夹
    pub fn open_folder(&self, folder_path: &str) -> Result<(), Box<dyn Error>> {
        let path = Path::new(folder_path);
        if !path.exists() {
            return Err(format!("文件夹不存在: {}", folder_path).into());
        }

        if !path.is_dir() {
            return Err(format!("路径不是文件夹: {}", folder_path).into());
        }

        #[cfg(target_os = "windows")]
        {
            use std::process::Command;
            Command::new("explorer").arg(folder_path).spawn()?;
        }

        #[cfg(target_os = "macos")]
        {
            use std::process::Command;
            Command::new("open").arg(folder_path).spawn()?;
        }

        #[cfg(target_os = "linux")]
        {
            use std::process::Command;
            Command::new("xdg-open").arg(folder_path).spawn()?;
        }

        Ok(())
    }

    /// 检查文件夹缺失标签
    pub fn check_missing_tags(
        &self,
        folder_path: &str,
        tag_rules: &[TagRule],
    ) -> Result<Vec<FileTag>, Box<dyn Error>> {
        let path = Path::new(folder_path);
        if !path.exists() || !path.is_dir() {
            return Err(format!("无效的文件夹路径: {}", folder_path).into());
        }

        let mut missing_tags = Vec::new();

        let entries = fs::read_dir(path)?;
        let files: Vec<String> = entries
            .filter_map(|entry| {
                entry
                    .ok()
                    .map(|e| e.file_name().to_string_lossy().to_string().to_lowercase())
            })
            .collect();

        for rule in tag_rules {
            // 特殊处理入排小册子标签
            if rule.label == "入排小册子" {
                let has_booklet = files
                    .iter()
                    .any(|file| file.contains("入排小册子") || file.contains("小册子"));

                if !has_booklet {
                    missing_tags.push(FileTag {
                        label: rule.label.clone(),
                        color: rule.tag_color.clone(),
                    });
                }
                continue;
            }

            // 处理其他标签规则
            let has_matching_file = files.iter().any(|file| {
                rule.keywords
                    .iter()
                    .any(|keyword| file.contains(&keyword.to_lowercase()))
            });

            if !has_matching_file {
                missing_tags.push(FileTag {
                    label: rule.label.clone(),
                    color: rule.tag_color.clone(),
                });
            }
        }

        Ok(missing_tags)
    }

    /// 创建文件
    pub fn create_file(&self, file_path: &str, content: &str) -> Result<(), Box<dyn Error>> {
        let path = Path::new(file_path);

        // 确保父目录存在
        if let Some(parent) = path.parent() {
            if !parent.exists() {
                fs::create_dir_all(parent)?;
            }
        }

        fs::write(path, content)?;
        Ok(())
    }

    /// 读取文件
    pub fn read_file(&self, file_path: &str) -> Result<String, Box<dyn Error>> {
        let path = Path::new(file_path);
        if !path.exists() {
            return Err(format!("文件不存在: {}", file_path).into());
        }

        if !path.is_file() {
            return Err(format!("路径不是文件: {}", file_path).into());
        }

        let content = fs::read_to_string(path)?;
        Ok(content)
    }

    /// 删除文件
    pub fn delete_file(&self, file_path: &str) -> Result<(), Box<dyn Error>> {
        let path = Path::new(file_path);
        if !path.exists() {
            return Err(format!("文件不存在: {}", file_path).into());
        }

        if path.is_file() {
            fs::remove_file(path)?;
        } else if path.is_dir() {
            fs::remove_dir_all(path)?;
        } else {
            return Err(format!("无法删除: {}", file_path).into());
        }

        Ok(())
    }

    /// 创建目录
    pub fn create_directory(&self, dir_path: &str) -> Result<(), Box<dyn Error>> {
        let path = Path::new(dir_path);
        if path.exists() {
            return Err(format!("目录已存在: {}", dir_path).into());
        }

        fs::create_dir_all(path)?;
        Ok(())
    }

    /// 复制文件
    pub fn copy_file(&self, src_path: &str, dest_path: &str) -> Result<(), Box<dyn Error>> {
        let src = Path::new(src_path);
        let dest = Path::new(dest_path);

        if !src.exists() {
            return Err(format!("源文件不存在: {}", src_path).into());
        }

        if src.is_file() {
            // 确保目标目录存在
            if let Some(parent) = dest.parent() {
                if !parent.exists() {
                    fs::create_dir_all(parent)?;
                }
            }

            fs::copy(src, dest)?;
        } else if src.is_dir() {
            self.copy_dir_all(src, dest)?;
        } else {
            return Err(format!("无法复制: {}", src_path).into());
        }

        Ok(())
    }

    /// 复制目录
    fn copy_dir_all(&self, src: &Path, dst: &Path) -> io::Result<()> {
        if !dst.exists() {
            fs::create_dir_all(dst)?;
        }

        for entry in fs::read_dir(src)? {
            let entry = entry?;
            let ty = entry.file_type()?;
            let src_path = entry.path();
            let dst_path = dst.join(entry.file_name());

            if ty.is_dir() {
                self.copy_dir_all(&src_path, &dst_path)?;
            } else if ty.is_file() {
                fs::copy(&src_path, &dst_path)?;
            }
        }

        Ok(())
    }

    /// 移动文件
    pub fn move_file(&self, src_path: &str, dest_path: &str) -> Result<(), Box<dyn Error>> {
        let src = Path::new(src_path);
        let dest = Path::new(dest_path);

        if !src.exists() {
            return Err(format!("源文件不存在: {}", src_path).into());
        }

        // 确保目标目录存在
        if let Some(parent) = dest.parent() {
            if !parent.exists() {
                fs::create_dir_all(parent)?;
            }
        }

        fs::rename(src, dest)?;
        Ok(())
    }

    /// 重命名文件
    pub fn rename_file(&self, file_path: &str, new_name: &str) -> Result<String, Box<dyn Error>> {
        let path = Path::new(file_path);
        if !path.exists() {
            return Err(format!("文件不存在: {}", file_path).into());
        }

        let parent = path
            .parent()
            .ok_or_else(|| format!("无法获取父目录: {}", file_path))?;
        let new_path = parent.join(new_name);

        fs::rename(path, &new_path)?;

        Ok(new_path.to_string_lossy().to_string())
    }

    /// 获取文件信息
    pub fn get_file_info(&self, file_path: &str) -> Result<FileInfo, Box<dyn Error>> {
        let path = Path::new(file_path);
        if !path.exists() {
            return Err(format!("文件不存在: {}", file_path).into());
        }

        let metadata = fs::metadata(path)?;
        let modified = metadata.modified()?;
        let modified_time: DateTime<Local> = DateTime::from(modified);

        let file_name = path
            .file_name()
            .ok_or_else(|| format!("无法获取文件名: {}", file_path))?
            .to_string_lossy()
            .to_string();

        Ok(FileInfo {
            name: file_name,
            path: file_path.to_string(),
            size: metadata.len(),
            modified_time: modified_time.to_rfc3339(),
            tags: None,
        })
    }
}

// 创建单例实例
lazy_static::lazy_static! {
    pub static ref FILE_SYSTEM_SERVICE: FileSystemService = FileSystemService::new();
}
