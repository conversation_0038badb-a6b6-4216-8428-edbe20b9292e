use crate::models::project::{Project, ProjectPagination, ProjectQuery};
use dotenv::dotenv;
use futures::stream::TryStreamExt;
use log::{error, info};
use mongodb::{
    bson::{doc, oid::ObjectId, DateTime, Document},
    options::{ClientOptions, FindOptions},
    Client, Collection,
};
use std::env;
use std::error::Error;

/// 项目服务
pub struct ProjectService {
    collection: Collection<Document>,
}

impl ProjectService {
    /// 创建新的项目服务实例
    pub async fn new() -> Result<Self, Box<dyn Error>> {
        dotenv().ok();

        let mongo_uri =
            env::var("MONGO_URI").unwrap_or_else(|_| "mongodb://127.0.0.1:27017/".to_string());

        let db_name = env::var("DB_NAME").unwrap_or_else(|_| "project_manage".to_string());

        let collection_name =
            env::var("PROJECT_COLLECTION").unwrap_or_else(|_| "project_info".to_string());

        let client_options = ClientOptions::parse(&mongo_uri).await?;
        let client = Client::with_options(client_options)?;
        let db = client.database(&db_name);
        let collection = db.collection::<Document>(&collection_name);

        Ok(Self { collection })
    }

    /// 获取所有项目
    pub async fn get_all_projects(&self) -> Result<Vec<Project>, Box<dyn Error>> {
        let cursor = self.collection.find(None, None).await?;
        let documents: Vec<Document> = cursor.try_collect().await?;

        let mut projects = Vec::new();
        for doc in documents {
            match Project::from_document(doc) {
                Ok(project) => projects.push(project),
                Err(e) => error!("解析项目文档失败: {}", e),
            }
        }

        info!("获取了 {} 个项目", projects.len());
        Ok(projects)
    }

    /// 分页查询项目
    pub async fn get_projects_paginated(
        &self,
        query: ProjectQuery,
        page: u64,
        page_size: u64,
    ) -> Result<ProjectPagination, Box<dyn Error>> {
        // 构建查询条件
        let mut filter = Document::new();

        if let Some(project_name) = query.project_name {
            filter.insert(
                "project_name",
                doc! { "$regex": project_name, "$options": "i" },
            );
        }

        if let Some(project_short_name) = query.project_short_name {
            filter.insert(
                "project_short_name",
                doc! { "$regex": project_short_name, "$options": "i" },
            );
        }

        if let Some(project_number) = query.project_number {
            filter.insert(
                "project_number",
                doc! { "$regex": project_number, "$options": "i" },
            );
        }

        if let Some(sponsor) = query.sponsor {
            filter.insert("sponsor", doc! { "$in": [sponsor] });
        }

        if let Some(disease) = query.disease {
            filter.insert("disease", doc! { "$in": [disease] });
        }

        if let Some(project_stage) = query.project_stage {
            filter.insert("project_stage", project_stage);
        }

        if let Some(project_status) = query.project_status {
            filter.insert("project_status", project_status);
        }

        if let Some(recruitment_status) = query.recruitment_status {
            filter.insert("recruitment_status", recruitment_status);
        }

        if let Some(tag) = query.tag {
            filter.insert("tags", doc! { "$in": [tag] });
        }

        // 计算总数
        let total = self
            .collection
            .count_documents(filter.clone(), None)
            .await?;

        // 设置分页选项
        let skip = (page - 1) * page_size;
        let options = FindOptions::builder()
            .skip(skip as u64)
            .limit(page_size as i64)
            .sort(doc! { "updated_at": -1 })
            .build();

        // 执行查询
        let cursor = self.collection.find(filter, options).await?;
        let documents: Vec<Document> = cursor.try_collect().await?;

        // 转换为项目对象
        let mut items = Vec::new();
        for doc in documents {
            match Project::from_document(doc) {
                Ok(project) => items.push(project),
                Err(e) => error!("解析项目文档失败: {}", e),
            }
        }

        Ok(ProjectPagination {
            items,
            total,
            page,
            page_size,
        })
    }

    /// 根据 ID 获取项目
    pub async fn get_project_by_id(&self, id: &str) -> Result<Option<Project>, Box<dyn Error>> {
        let object_id = ObjectId::parse_str(id)?;
        let filter = doc! { "_id": object_id };

        let result = self.collection.find_one(filter, None).await?;

        match result {
            Some(doc) => {
                let project = Project::from_document(doc)?;
                Ok(Some(project))
            }
            None => Ok(None),
        }
    }

    /// 创建项目
    pub async fn create_project(&self, project: Project) -> Result<String, Box<dyn Error>> {
        let mut doc = project.to_document()?;

        // 确保创建和更新时间存在
        let now = DateTime::now();
        doc.insert("created_at", now);
        doc.insert("updated_at", now);

        // 插入文档
        let result = self.collection.insert_one(doc, None).await?;
        let id = result
            .inserted_id
            .as_object_id()
            .ok_or_else(|| "无法获取插入的ID".to_string())?;

        info!("创建了新项目，ID: {}", id);
        Ok(id.to_string())
    }

    /// 更新项目
    pub async fn update_project(&self, id: &str, project: Project) -> Result<bool, Box<dyn Error>> {
        let object_id = ObjectId::parse_str(id)?;
        let filter = doc! { "_id": object_id };

        let mut doc = project.to_document()?;

        // 移除 _id 字段，避免更新时冲突
        doc.remove("_id");

        // 更新时间
        doc.insert("updated_at", DateTime::now());

        // 执行更新
        let update = doc! { "$set": doc };
        let result = self.collection.update_one(filter, update, None).await?;

        info!(
            "更新项目 {}, 匹配数: {}, 修改数: {}",
            id, result.matched_count, result.modified_count
        );

        Ok(result.modified_count > 0)
    }

    /// 删除项目
    pub async fn delete_project(&self, id: &str) -> Result<bool, Box<dyn Error>> {
        let object_id = ObjectId::parse_str(id)?;
        let filter = doc! { "_id": object_id };

        let result = self.collection.delete_one(filter, None).await?;

        info!("删除项目 {}, 删除数: {}", id, result.deleted_count);
        Ok(result.deleted_count > 0)
    }
}

// 创建单例实例
lazy_static::lazy_static! {
    pub static ref PROJECT_SERVICE: tokio::sync::OnceCell<ProjectService> = tokio::sync::OnceCell::new();
}
