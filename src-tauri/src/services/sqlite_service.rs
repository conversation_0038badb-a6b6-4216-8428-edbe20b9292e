use r2d2::Pool;
use r2d2_sqlite::SqliteConnectionManager;
use std::error::Error;
use std::path::Path;
use std::sync::Arc;
// 导入
use log::info;
use std::fs;

/// SQLite 数据库服务
pub struct SqliteService {
    pool: Arc<Pool<SqliteConnectionManager>>,
}

impl SqliteService {
    /// 创建新的 SQLite 数据库服务实例
    pub fn new(db_path: &str) -> Result<Self, Box<dyn Error>> {
        // 确保数据库目录存在
        if let Some(parent) = Path::new(db_path).parent() {
            if !parent.exists() {
                fs::create_dir_all(parent)?;
            }
        }

        // 创建连接池
        let manager = SqliteConnectionManager::file(db_path);
        let pool = Pool::new(manager)?;

        // 初始化数据库
        let service = Self {
            pool: Arc::new(pool),
        };

        service.init_database()?;

        Ok(service)
    }

    /// 初始化数据库
    fn init_database(&self) -> Result<(), Box<dyn Error>> {
        let conn = self.pool.get()?;

        // 启用外键约束
        conn.execute("PRAGMA foreign_keys = ON", [])?;

        // 创建字典表
        conn.execute(
            "CREATE TABLE IF NOT EXISTS dictionaries (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                mongo_oid TEXT UNIQUE,
                name TEXT NOT NULL UNIQUE,
                description TEXT,
                type TEXT NOT NULL DEFAULT 'list',
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL,
                version INTEGER NOT NULL DEFAULT 1
            )",
            [],
        )?;

        // 创建字典项表
        conn.execute(
            "CREATE TABLE IF NOT EXISTS dictionary_items (
                item_id INTEGER PRIMARY KEY AUTOINCREMENT,
                dictionary_id INTEGER NOT NULL,
                item_key TEXT NOT NULL,
                item_value TEXT NOT NULL,
                item_description TEXT,
                status TEXT NOT NULL DEFAULT 'active',
                FOREIGN KEY (dictionary_id) REFERENCES dictionaries(id) ON DELETE CASCADE,
                UNIQUE (dictionary_id, item_key)
            )",
            [],
        )?;

        // 创建标签表
        conn.execute(
            "CREATE TABLE IF NOT EXISTS tags (
                tag_id INTEGER PRIMARY KEY AUTOINCREMENT,
                tag_name TEXT NOT NULL UNIQUE
            )",
            [],
        )?;

        // 创建字典标签关联表
        conn.execute(
            "CREATE TABLE IF NOT EXISTS dictionary_tags (
                dictionary_id INTEGER NOT NULL,
                tag_id INTEGER NOT NULL,
                PRIMARY KEY (dictionary_id, tag_id),
                FOREIGN KEY (dictionary_id) REFERENCES dictionaries(id) ON DELETE CASCADE,
                FOREIGN KEY (tag_id) REFERENCES tags(tag_id) ON DELETE CASCADE
            )",
            [],
        )?;

        // 创建索引
        conn.execute(
            "CREATE INDEX IF NOT EXISTS idx_dict_name ON dictionaries(name)",
            [],
        )?;
        conn.execute(
            "CREATE INDEX IF NOT EXISTS idx_item_dict_id ON dictionary_items(dictionary_id)",
            [],
        )?;
        conn.execute(
            "CREATE INDEX IF NOT EXISTS idx_item_key ON dictionary_items(item_key)",
            [],
        )?;
        conn.execute(
            "CREATE INDEX IF NOT EXISTS idx_tag_name ON tags(tag_name)",
            [],
        )?;
        conn.execute(
            "CREATE INDEX IF NOT EXISTS idx_dict_tags_tag_id ON dictionary_tags(tag_id)",
            [],
        )?;

        info!("数据库初始化完成");
        Ok(())
    }

    /// 获取数据库连接
    pub fn get_connection(
        &self,
    ) -> Result<r2d2::PooledConnection<SqliteConnectionManager>, Box<dyn Error>> {
        Ok(self.pool.get()?)
    }
}

// 创建单例实例
lazy_static::lazy_static! {
    pub static ref SQLITE_SERVICE: std::sync::Mutex<Option<SqliteService>> = std::sync::Mutex::new(None);
}

/// 初始化 SQLite 服务
pub fn init_sqlite_service(db_path: &str) -> Result<(), Box<dyn Error>> {
    let service = SqliteService::new(db_path)?;
    let mut sqlite_service = SQLITE_SERVICE.lock().unwrap();
    *sqlite_service = Some(service);
    Ok(())
}

/// 获取 SQLite 服务实例
pub fn get_sqlite_service() -> Result<Arc<SqliteService>, Box<dyn Error>> {
    let sqlite_service = SQLITE_SERVICE.lock().unwrap();
    match &*sqlite_service {
        Some(service) => Ok(Arc::new(service.clone())),
        None => Err("SQLite 服务未初始化".into()),
    }
}

// 为 SqliteService 实现 Clone trait
impl Clone for SqliteService {
    fn clone(&self) -> Self {
        Self {
            pool: Arc::clone(&self.pool),
        }
    }
}
