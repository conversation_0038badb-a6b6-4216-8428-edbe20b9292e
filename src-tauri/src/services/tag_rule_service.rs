use crate::models::file_system::{TagColor, TagRule};
use crate::services::config_service::{ConfigService, CONFIG_SERVICE};
// 导入
use serde_json::Value;
use std::error::Error;

/// 标签规则服务
pub struct TagRuleService;

impl TagRuleService {
    /// 创建新的标签规则服务实例
    pub fn new() -> Self {
        Self {}
    }

    /// 获取所有标签规则
    pub async fn get_tag_rules(&self) -> Result<Vec<TagRule>, Box<dyn Error>> {
        let config_service = match CONFIG_SERVICE.get() {
            Some(service) => service,
            None => {
                let service = ConfigService::new().await?;
                CONFIG_SERVICE
                    .set(service)
                    .map_err(|_| "无法设置配置服务实例".to_string())?;
                CONFIG_SERVICE.get().unwrap()
            }
        };

        let config = config_service.get_config("file_label_rules").await?;

        match config {
            Some(config) => {
                if let Some(config_value) = config.config {
                    if let Value::Array(rules) = config_value {
                        let tag_rules: Vec<TagRule> = rules
                            .into_iter()
                            .filter_map(|rule| {
                                let label = rule.get("label")?.as_str()?.to_string();
                                let keywords = rule
                                    .get("keywords")?
                                    .as_array()?
                                    .iter()
                                    .filter_map(|k| k.as_str().map(|s| s.to_string()))
                                    .collect();

                                let tag_color = match rule.get("tag_color").and_then(|c| c.as_str())
                                {
                                    Some("blue") => TagColor::Blue,
                                    Some("purple") => TagColor::Purple,
                                    Some("cyan") => TagColor::Cyan,
                                    Some("green") => TagColor::Green,
                                    Some("magenta") => TagColor::Magenta,
                                    Some("pink") => TagColor::Pink,
                                    Some("red") => TagColor::Red,
                                    Some("orange") => TagColor::Orange,
                                    Some("yellow") => TagColor::Yellow,
                                    Some("volcano") => TagColor::Volcano,
                                    Some("geekblue") => TagColor::Geekblue,
                                    Some("lime") => TagColor::Lime,
                                    Some("gold") => TagColor::Gold,
                                    _ => TagColor::Default,
                                };

                                Some(TagRule {
                                    label,
                                    keywords,
                                    tag_color,
                                })
                            })
                            .collect();

                        return Ok(tag_rules);
                    }
                }

                Ok(Vec::new())
            }
            None => Ok(Vec::new()),
        }
    }

    /// 添加标签规则
    pub async fn add_tag_rule(
        &self,
        label: &str,
        keywords: Vec<String>,
        tag_color: TagColor,
    ) -> Result<bool, Box<dyn Error>> {
        let config_service = match CONFIG_SERVICE.get() {
            Some(service) => service,
            None => {
                let service = ConfigService::new().await?;
                CONFIG_SERVICE
                    .set(service)
                    .map_err(|_| "无法设置配置服务实例".to_string())?;
                CONFIG_SERVICE.get().unwrap()
            }
        };

        // 获取现有规则
        let existing_rules = self.get_tag_rules().await?;

        // 检查是否已存在相同标签
        if existing_rules.iter().any(|rule| rule.label == label) {
            return Err("标签已存在".into());
        }

        // 添加新规则
        let mut rules = existing_rules;
        rules.push(TagRule {
            label: label.to_string(),
            keywords,
            tag_color,
        });

        // 转换为 JSON
        let rules_json = serde_json::to_value(rules)?;

        // 创建配置对象
        let config =
            crate::models::config::Config::new("file_label_rules".to_string(), Some(rules_json));

        // 保存配置
        let result = config_service.save_config(config).await?;

        Ok(result)
    }

    /// 删除标签规则
    pub async fn delete_tag_rule(&self, label: &str) -> Result<bool, Box<dyn Error>> {
        let config_service = match CONFIG_SERVICE.get() {
            Some(service) => service,
            None => {
                let service = ConfigService::new().await?;
                CONFIG_SERVICE
                    .set(service)
                    .map_err(|_| "无法设置配置服务实例".to_string())?;
                CONFIG_SERVICE.get().unwrap()
            }
        };

        // 获取现有规则
        let existing_rules = self.get_tag_rules().await?;

        // 过滤掉要删除的规则
        let new_rules: Vec<TagRule> = existing_rules
            .into_iter()
            .filter(|rule| rule.label != label)
            .collect();

        // 转换为 JSON
        let rules_json = serde_json::to_value(new_rules)?;

        // 创建配置对象
        let config =
            crate::models::config::Config::new("file_label_rules".to_string(), Some(rules_json));

        // 保存配置
        let result = config_service.save_config(config).await?;

        Ok(result)
    }

    /// 更新标签规则
    pub async fn update_tag_rule(
        &self,
        old_label: &str,
        new_label: &str,
        keywords: Vec<String>,
        tag_color: TagColor,
    ) -> Result<bool, Box<dyn Error>> {
        let config_service = match CONFIG_SERVICE.get() {
            Some(service) => service,
            None => {
                let service = ConfigService::new().await?;
                CONFIG_SERVICE
                    .set(service)
                    .map_err(|_| "无法设置配置服务实例".to_string())?;
                CONFIG_SERVICE.get().unwrap()
            }
        };

        // 获取现有规则
        let existing_rules = self.get_tag_rules().await?;

        // 检查新标签名是否已存在
        if old_label != new_label && existing_rules.iter().any(|rule| rule.label == new_label) {
            return Err("新标签名已存在".into());
        }

        // 更新规则
        let mut updated = false;
        let new_rules: Vec<TagRule> = existing_rules
            .into_iter()
            .map(|rule| {
                if rule.label == old_label {
                    updated = true;
                    TagRule {
                        label: new_label.to_string(),
                        keywords: keywords.clone(),
                        tag_color: tag_color.clone(),
                    }
                } else {
                    rule
                }
            })
            .collect();

        if !updated {
            return Err("标签不存在".into());
        }

        // 转换为 JSON
        let rules_json = serde_json::to_value(new_rules)?;

        // 创建配置对象
        let config =
            crate::models::config::Config::new("file_label_rules".to_string(), Some(rules_json));

        // 保存配置
        let result = config_service.save_config(config).await?;

        Ok(result)
    }
}

// 创建单例实例
lazy_static::lazy_static! {
    pub static ref TAG_RULE_SERVICE: TagRuleService = TagRuleService::new();
}
