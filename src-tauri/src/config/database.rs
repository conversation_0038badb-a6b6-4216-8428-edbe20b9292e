use crate::error::AppError;
use dotenv::dotenv;
use mongodb::{options::ClientOptions, Client};
use rusqlite::Connection;
use std::env;
use std::path::PathBuf;
use std::sync::{Arc, Mutex};

/// Database configuration for the application
pub struct DatabaseConfig {
    /// SQLite database path
    pub sqlite_path: PathBuf,
    /// MongoDB connection URI
    pub mongo_uri: String,
    /// MongoDB database name
    pub mongo_db_name: String,
}

impl DatabaseConfig {
    /// Create a new database configuration
    pub fn new() -> Self {
        dotenv().ok();

        // Get MongoDB configuration from environment variables
        let mongo_uri =
            env::var("MONGO_URI").unwrap_or_else(|_| "mongodb://127.0.0.1:27017/".to_string());

        let mongo_db_name = env::var("DB_NAME").unwrap_or_else(|_| "project_manage".to_string());

        // Get SQLite configuration
        let sqlite_path = PathBuf::from(
            env::var("SQLITE_PATH")
                .unwrap_or_else(|_| "/Users/<USER>/我的文档/sqlite/peckbyte.db".to_string()),
        );

        Self {
            sqlite_path,
            mongo_uri,
            mongo_db_name,
        }
    }

    /// Initialize SQLite connection
    pub fn init_sqlite_connection(&self) -> Result<Connection, AppError> {
        // Ensure directory exists
        if let Some(parent) = self.sqlite_path.parent() {
            if !parent.exists() {
                std::fs::create_dir_all(parent).map_err(|e| {
                    AppError::DatabaseError(format!("Failed to create database directory: {}", e))
                })?;
            }
        }

        // Open connection
        Connection::open(&self.sqlite_path).map_err(|e| {
            AppError::DatabaseError(format!("Failed to open SQLite connection: {}", e))
        })
    }

    /// Initialize MongoDB client
    pub async fn init_mongo_client(&self) -> Result<Client, AppError> {
        let client_options = ClientOptions::parse(&self.mongo_uri)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to parse MongoDB URI: {}", e)))?;

        Client::with_options(client_options)
            .map_err(|e| AppError::DatabaseError(format!("Failed to create MongoDB client: {}", e)))
    }
}

/// Thread-safe SQLite connection wrapper
pub struct SqliteConnection {
    conn: Arc<Mutex<Connection>>,
}

impl SqliteConnection {
    /// Create a new SQLite connection wrapper
    pub fn new(conn: Connection) -> Self {
        Self {
            conn: Arc::new(Mutex::new(conn)),
        }
    }

    /// Get the connection
    pub fn get(&self) -> Result<std::sync::MutexGuard<'_, Connection>, AppError> {
        self.conn.lock().map_err(|e| {
            AppError::DatabaseError(format!("Failed to acquire database connection lock: {}", e))
        })
    }
}

// Implement Send and Sync for SqliteConnection
unsafe impl Send for SqliteConnection {}
unsafe impl Sync for SqliteConnection {}
