use crate::error::AppError;
use serde::{Deserialize, Serialize};
use std::fs;
use std::io::{Read, Write};
use std::path::PathBuf;

/// Database type
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum DatabaseType {
    /// SQLite database
    Sqlite,
    /// MongoDB database
    MongoDB,
}

impl Default for DatabaseType {
    fn default() -> Self {
        DatabaseType::Sqlite
    }
}

/// Application configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppConfig {
    /// Database type to use
    #[serde(default)]
    pub database_type: DatabaseType,

    /// SQLite database path
    #[serde(default = "default_sqlite_path")]
    pub sqlite_path: String,

    /// MongoDB connection URI
    #[serde(default = "default_mongo_uri")]
    pub mongo_uri: String,

    /// MongoDB database name
    #[serde(default = "default_mongo_db_name")]
    pub mongo_db_name: String,
}

fn default_sqlite_path() -> String {
    "/Users/<USER>/我的文档/sqlite/peckbyte.db".to_string()
}

fn default_mongo_uri() -> String {
    "mongodb://127.0.0.1:27017/".to_string()
}

fn default_mongo_db_name() -> String {
    "project_manage".to_string()
}

impl Default for AppConfig {
    fn default() -> Self {
        Self {
            database_type: DatabaseType::default(),
            sqlite_path: default_sqlite_path(),
            mongo_uri: default_mongo_uri(),
            mongo_db_name: default_mongo_db_name(),
        }
    }
}

impl AppConfig {
    /// Load configuration from file
    pub fn load(path: &PathBuf) -> Result<Self, AppError> {
        // If file doesn't exist, create default config
        if !path.exists() {
            let config = Self::default();
            config.save(path)?;
            return Ok(config);
        }

        // Read file
        let mut file = fs::File::open(path)
            .map_err(|e| AppError::ConfigError(format!("Failed to open config file: {}", e)))?;

        let mut contents = String::new();
        file.read_to_string(&mut contents)
            .map_err(|e| AppError::ConfigError(format!("Failed to read config file: {}", e)))?;

        // Parse JSON
        serde_json::from_str(&contents)
            .map_err(|e| AppError::ConfigError(format!("Failed to parse config file: {}", e)))
    }

    /// Save configuration to file
    pub fn save(&self, path: &PathBuf) -> Result<(), AppError> {
        // Create parent directory if it doesn't exist
        if let Some(parent) = path.parent() {
            if !parent.exists() {
                fs::create_dir_all(parent).map_err(|e| {
                    AppError::ConfigError(format!("Failed to create config directory: {}", e))
                })?;
            }
        }

        // Serialize to JSON
        let json = serde_json::to_string_pretty(self)
            .map_err(|e| AppError::ConfigError(format!("Failed to serialize config: {}", e)))?;

        // Write to file
        let mut file = fs::File::create(path)
            .map_err(|e| AppError::ConfigError(format!("Failed to create config file: {}", e)))?;

        file.write_all(json.as_bytes())
            .map_err(|e| AppError::ConfigError(format!("Failed to write config file: {}", e)))?;

        Ok(())
    }

    /// Get the default config path
    pub fn default_path() -> PathBuf {
        let home = dirs::home_dir().unwrap_or_else(|| PathBuf::from("."));
        home.join(".config").join("tauri-app").join("config.json")
    }
}
