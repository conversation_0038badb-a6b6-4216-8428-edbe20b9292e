[package]
name = "tauri-app"
version = "0.1.0"
description = "A Tauri App"
authors = ["you"]
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "tauri_app_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
tauri = { version = "2", features = [] }
tauri-plugin-opener = "2"
serde = { version = "1", features = ["derive"] }
serde_json = "1"
reqwest = { version = "0.11", features = ["json"] }
tokio = { version = "1", features = ["full", "sync"] }
log = "0.4"
env_logger = "0.10"
dotenv = "0.15"
lazy_static = "1.4"
mongodb = { version = "2.8", features = ["tokio-runtime"] }
futures = "0.3"
chrono = { version = "0.4", features = ["serde"] }
rusqlite = { version = "0.30", features = ["bundled"] }
r2d2 = "0.8"
r2d2_sqlite = "0.23"
regex = "1.10"
uuid = { version = "1.6", features = ["v4", "serde"] }
async-trait = "0.1"
dirs = "5.0"
tauri-plugin-clipboard-manager = "2.2.2"
tauri-plugin-dialog = "2"
tauri-plugin-log = "2"
tauri-plugin-fs = "2"
tauri-plugin-http = "2"
urlencoding = "2.1.3"

