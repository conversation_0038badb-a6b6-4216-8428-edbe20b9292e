// 打开参数编辑对话框 - 完全重写
function openEditParamsDialog(match) {
  try {
    console.log('Opening parameter edit dialog for:', match.rule.rule_name);

    // 清除之前的错误和成功消息
    updateError = '';
    updateSuccess = '';

    // 设置当前匹配项
    selectedMatchToEditParams = {
      segment: match.segment,
      rule: { ...match.rule },
      criterion: { ...match.criterion }
    };

    // 初始化编辑参数值为空对象
    editedParamValues = {};

    // 处理参数值
    if (!match.criterion.parameter_values) {
      console.warn('参数值不存在，使用空对象');
    } else {
      console.log('参数值类型:', typeof match.criterion.parameter_values);
      console.log('参数值内容:', match.criterion.parameter_values);

      // 如果是对象类型，直接使用
      if (typeof match.criterion.parameter_values === 'object' && match.criterion.parameter_values !== null) {
        editedParamValues = { ...match.criterion.parameter_values };
        console.log('直接使用对象参数值:', editedParamValues);
      } 
      // 如果是字符串类型，尝试解析
      else if (typeof match.criterion.parameter_values === 'string') {
        const paramStr = match.criterion.parameter_values.trim();
        
        // 尝试直接解析JSON
        try {
          editedParamValues = JSON.parse(paramStr);
          console.log('成功解析参数值字符串:', editedParamValues);
        } catch (parseError) {
          console.error('解析参数值字符串失败:', parseError);
          
          // 特殊处理 - 尝试提取 Max_age 和 Min_age
          if (paramStr.includes('Max_age') || paramStr.includes('Min_age')) {
            const simpleObj = {};
            
            // 尝试匹配 "Max_age": 78 或 Max_age": 78 格式
            const maxAgeMatch = paramStr.match(/(?:")?Max_age(?:")?:\s*(\d+)/);
            const minAgeMatch = paramStr.match(/(?:")?Min_age(?:")?:\s*(\d+)/);
            
            if (maxAgeMatch && maxAgeMatch[1]) {
              simpleObj['Max_age'] = parseInt(maxAgeMatch[1], 10);
            }
            
            if (minAgeMatch && minAgeMatch[1]) {
              simpleObj['Min_age'] = parseInt(minAgeMatch[1], 10);
            }
            
            if (Object.keys(simpleObj).length > 0) {
              editedParamValues = simpleObj;
              console.log('成功提取年龄参数:', editedParamValues);
              updateError = '参数值格式有问题，但已自动修复';
            } else {
              // 尝试提取数字
              const numbers = paramStr.match(/\d+/g);
              if (numbers && numbers.length >= 2) {
                const num1 = parseInt(numbers[0], 10);
                const num2 = parseInt(numbers[1], 10);
                
                if (!isNaN(num1) && !isNaN(num2)) {
                  if (num1 >= num2) {
                    simpleObj['Max_age'] = num1;
                    simpleObj['Min_age'] = num2;
                  } else {
                    simpleObj['Min_age'] = num1;
                    simpleObj['Max_age'] = num2;
                  }
                  
                  editedParamValues = simpleObj;
                  console.log('从数字提取年龄参数:', editedParamValues);
                  updateError = '参数值格式有问题，但已自动修复';
                }
              }
            }
          }
          
          // 如果上述方法都失败，尝试清理字符串并解析
          if (Object.keys(editedParamValues).length === 0) {
            try {
              const cleanedStr = paramStr
                .replace(/'/g, '"')  // 替换单引号为双引号
                .replace(/(\w+):/g, '"$1":')  // 给键名添加双引号
                .replace(/,\s*}/g, '}');  // 移除尾部逗号
              
              editedParamValues = JSON.parse(cleanedStr);
              console.log('成功通过清理后解析参数值:', editedParamValues);
              updateError = '参数值格式有问题，但已自动修复';
            } catch (cleanError) {
              console.error('清理后仍然无法解析:', cleanError);
              // 保持空对象
            }
          }
        }
      } else {
        console.warn('参数值类型不支持:', typeof match.criterion.parameter_values);
      }
    }

    // 如果匹配规则定义的参数模式存在且非空，确保所有需要的参数都有初始值
    if (match.rule.parameter_schema && match.rule.parameter_schema.trim() !== '') {
      try {
        // 解析参数模式
        const schema = ruleDesignerService.parseParameterSchema(match.rule.parameter_schema);

        // 验证schema结构和parameters数组
        if (schema && schema.parameters && Array.isArray(schema.parameters)) {
          // 为每个定义的参数设置默认值（如果尚未设置）
          for (const param of schema.parameters) {
            // 确保param是有效的参数定义
            if (param && param.name) {
              if (!(param.name in editedParamValues)) {
                // 根据参数类型设置默认值
                if (param.type === 'boolean') {
                  editedParamValues[param.name] = param.default !== undefined ? param.default : false;
                } else if (param.type === 'number' || param.type === 'integer') {
                  editedParamValues[param.name] = param.default !== undefined ? param.default : null;
                } else if (param.type === 'enum' && param.options && param.options.length > 0) {
                  editedParamValues[param.name] = param.default !== undefined ? param.default : param.options[0];
                } else {
                  editedParamValues[param.name] = param.default !== undefined ? param.default : '';
                }
              }
            } else {
              console.warn('参数定义缺少name属性:', param);
            }
          }
        } else {
          console.warn('参数模式结构无效或parameters数组不存在:', schema);
        }
      } catch (e) {
        console.error('解析参数模式失败:', e);
        updateError = '解析参数模式失败: ' + (e instanceof Error ? e.message : String(e));
      }
    } else {
      console.log('参数模式为空或不存在，将根据规则名称生成默认参数');

      // 根据规则名称生成默认参数
      try {
        const generatedSchema = generateDefaultParametersFromRuleName(match.rule.rule_name);

        // 为生成的参数设置默认值
        for (const param of generatedSchema.parameters) {
          if (!(param.name in editedParamValues)) {
            // 根据参数类型设置默认值
            if (param.type === 'boolean') {
              editedParamValues[param.name] = false;
            } else if (param.type === 'integer' || param.type === 'number') {
              editedParamValues[param.name] = null;
            } else if (param.type === 'enum' && param.options && param.options.length > 0) {
              editedParamValues[param.name] = param.options[0];
            } else {
              editedParamValues[param.name] = '';
            }
          }
        }

        console.log('已根据规则名称生成默认参数:', editedParamValues);
      } catch (genError) {
        console.error('根据规则名称生成默认参数失败:', genError);
      }
    }

    // 打开对话框
    showEditParamsDialog = true;
    console.log('Dialog opened with parameters:', editedParamValues);
  } catch (error) {
    console.error('Error opening parameter edit dialog:', error);
    updateError = error instanceof Error ? error.message : '打开参数编辑对话框失败';
  }
}
