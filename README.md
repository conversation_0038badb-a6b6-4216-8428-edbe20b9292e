# 临床研究项目管理系统

本项目是一个基于 Tauri、SvelteKit 和 TypeScript 构建的现代化临床研究项目管理桌面应用程序。系统专为临床研究机构设计，提供完整的项目生命周期管理、智能化的入排标准配置、人员管理、数据分析等功能，并集成了 AI 能力来提升工作效率。

## ✨ 系统特色

- **专业化设计**: 专门针对临床研究项目管理需求设计，涵盖项目全生命周期
- **现代化技术栈**: Tauri (Rust), SvelteKit, TypeScript, Tailwind CSS，确保高性能和优秀的用户体验
- **智能化功能**: 集成 Langchain.js 和大型语言模型，提供智能笔记分类、自动化入排标准生成等功能
- **灵活的数据管理**: 支持 SQLite 本地存储和 MongoDB 云端配置，满足不同场景需求
- **丰富的集成能力**: 支持 Notion、Lighthouse 等外部系统集成，实现数据互通
- **可视化分析**: 内置仪表盘和图表分析，提供项目进度和数据洞察
- **模块化架构**: 清晰的前后端分层设计，易于维护和功能扩展

## 📚 目录

- [技术栈](#-技术栈)
- [系统架构](#-系统架构)
  - [前端架构 (SvelteKit)](#前端架构-sveltekit)
  - [后端架构 (Rust + Tauri)](#后端架构-rust--tauri)
  - [数据存储](#数据存储)
- [核心功能模块](#-核心功能模块)
  - [项目管理](#项目管理)
  - [入排标准规则引擎](#入排标准规则引擎)
  - [人员管理](#人员管理)
  - [数据字典管理](#数据字典管理)
  - [智能笔记系统](#智能笔记系统)
  - [数据分析仪表盘](#数据分析仪表盘)
  - [系统配置管理](#系统配置管理)
- [外部系统集成](#-外部系统集成)
- [AI 功能集成](#-ai-功能集成)
- [开发环境设置](#-开发环境设置)
- [安装与部署](#-安装与部署)
- [项目结构说明](#-项目结构说明)
- [开发指南](#-开发指南)

## 🛠️ 技术栈

### 核心框架
- **[Tauri](https://tauri.app/)**: 跨平台桌面应用开发框架，提供原生性能和安全性
- **[SvelteKit](https://kit.svelte.dev/)**: 现代化的全栈 Web 应用框架

### 前端技术
- **[Svelte](https://svelte.dev/)**: 编译时优化的响应式 UI 框架
- **[TypeScript](https://www.typescriptlang.org/)**: 类型安全的 JavaScript 超集
- **[Tailwind CSS](https://tailwindcss.com/)**: 实用优先的 CSS 框架
- **[ECharts](https://echarts.apache.org/)**: 强大的数据可视化图表库
- **[Lucide Icons](https://lucide.dev/)**: 现代化的 SVG 图标库

### 后端技术
- **[Rust](https://www.rust-lang.org/)**: 高性能、内存安全的系统编程语言
- **[rusqlite](https://github.com/rusqlite/rusqlite)**: SQLite 数据库 Rust 绑定
- **[reqwest](https://github.com/seanmonstar/reqwest)**: HTTP 客户端库
- **[serde](https://serde.rs/)**: 序列化/反序列化框架

### 数据存储
- **[SQLite](https://www.sqlite.org/)**: 主要的本地数据库，存储项目核心数据
- **[MongoDB](https://www.mongodb.com/)**: 云端配置存储和数据同步

### AI 与集成
- **[Langchain.js](https://js.langchain.com/)**: 大型语言模型应用开发框架
- **[OpenAI API](https://openai.com/api/)**: GPT 模型 API 集成
- **[Notion API](https://developers.notion.com/)**: Notion 数据同步集成

## 🏗️ 系统架构

系统采用现代化的分层架构设计，前后端分离，确保代码的组织性、可维护性和可扩展性。

### 前端架构 (SvelteKit)

**技术特点**: 基于 SvelteKit 的现代化前端架构，采用文件系统路由和组件化设计

**核心目录结构**:
- **`src/routes/`**: 页面路由和组件
  - `projects/`: 项目管理相关页面
  - `staff/`: 人员管理页面
  - `notes/`: 智能笔记功能
  - `dashboard/`: 数据分析仪表盘
  - `sqlite-dictionaries/`: 数据字典管理
- **`src/lib/`**: 共享代码库
  - `components/`: 可复用 UI 组件
  - `services/`: 前端服务层，封装后端 API 调用
  - `stores/`: Svelte 状态管理
  - `utils/`: 工具函数和类型定义

**设计原则**:
- 组件化开发，提高代码复用性
- 响应式设计，适配不同窗口尺寸
- TypeScript 类型安全
- Tailwind CSS 实用优先的样式系统

### 后端架构 (Rust + Tauri)

**技术特点**: 基于 Rust 的高性能后端，采用分层架构和仓储模式

**分层架构设计**:
- **命令层 (Commands)**: `src-tauri/src/commands/`
  - 定义 Tauri 命令，作为前端调用的 API 端点
  - 处理请求参数验证和响应格式化
- **服务层 (Services)**: `src-tauri/src/services/`
  - 实现核心业务逻辑
  - 协调多个仓储层操作
  - 处理事务管理
- **仓储层 (Repositories)**: `src-tauri/src/repositories/`
  - 封装数据库访问逻辑
  - 提供统一的数据操作接口
  - 支持 SQLite 和 MongoDB 双后端
- **模型层 (Models)**: `src-tauri/src/models/`
  - 定义数据结构和类型
  - 处理序列化/反序列化

**核心模块**:
- **错误处理**: 统一的错误类型和处理机制
- **配置管理**: 应用配置和数据库连接管理
- **数据库**: SQLite 本地存储 + MongoDB 云端配置

### 数据存储

**多层次存储策略**: 根据数据特性和使用场景，采用不同的存储方案

**SQLite 本地数据库**:
- **用途**: 存储核心业务数据，如项目信息、人员数据、入排标准规则等
- **位置**: `/Users/<USER>/我的文档/sqlite/peckbyte.db`
- **特点**: 高性能本地存储，支持复杂查询和事务
- **主要表结构**:
  - `projects`: 项目基本信息
  - `staff`: 人员信息
  - `rule_definitions`: 规则定义
  - `project_criteria`: 项目标准配置
  - `dictionaries`: 数据字典

**MongoDB 云端存储**:
- **用途**: 存储系统配置、API 密钥等敏感信息
- **特点**: 支持分布式部署，便于配置同步
- **集合**: `config` 集合存储各类配置信息

**浏览器本地存储**:
- **用途**: 存储用户界面偏好设置
- **实现**: 通过 Svelte stores 管理状态

## 🚀 核心功能模块

### 项目管理

**功能概述**: 提供完整的临床研究项目生命周期管理，包括项目创建、信息维护、人员分配、补贴管理等

**主要功能**:
- 📋 **项目信息管理**: 项目基本信息、研究阶段、状态跟踪
- 🏢 **申办方管理**: 支持多申办方项目，申办方信息维护
- 💊 **研究药物管理**: 研究药物信息和分组管理
- 👥 **人员角色分配**: 研究人员角色分配和权限管理
- 📤 **批量人员导入**: 支持CSV格式的人员授权批量导入和质量控制
- 💰 **补贴方案管理**: 补贴项目和方案配置
- 📁 **项目文件管理**: 项目文件夹结构和文档管理

**批量人员授权导入系统** ✨:
- **CSV格式支持**: 支持复杂的15列CSV格式，包含完整的项目和人员信息
- **智能解析**: 自动解析"序号：（人员名称：姓名，角色：角色名）"格式的授权人员字符串
- **多角色支持**: 支持一个人员担任多个角色，自动角色名称映射
- **质量控制**: 自动检查项目是否配置了关键角色（PI、CRC、CRA）
- **可视化指示器**: 在项目列表中显示人员配置状态和缺失角色警告
- **导入预览**: 提供详细的数据验证结果和导入预览
- **错误处理**: 完善的错误提示和数据修复指导

**技术实现**:
- **前端页面**: `src/routes/projects/` 目录下的完整页面体系
- **核心组件**: `src/lib/components/project/` 下的模块化组件
- **CSV导入组件**: `src/lib/components/csv-import/` 专门的导入组件
- **后端服务**: `src-tauri/src/commands/project_management_commands.rs`
- **CSV处理服务**: `src-tauri/src/services/csv_import_service.rs`
- **数据存储**: SQLite 多表关联存储

### 入排标准规则引擎

**功能概述**: 智能化的临床研究入排标准管理系统，支持规则模板定义和项目个性化配置

**主要功能**:
- 📝 **规则模板管理**: 预定义入排标准规则模板
- ⚙️ **参数化配置**: 支持 JSON Schema 参数验证
- 🔄 **批量导入导出**: JSON 格式的标准批量操作
- 🤖 **AI 自动生成**: 基于文本描述自动生成入排标准
- 🔗 **逻辑关系**: 支持 OR/AND 逻辑关系配置

**技术实现**:
- **规则设计器**: `src/lib/components/rule-designer/` 组件库
- **AI 集成**: Langchain.js 驱动的智能生成功能
- **后端验证**: JSON Schema 参数验证机制
- **数据模型**: `rule_definitions` 和 `project_criteria` 表

### 人员管理

**功能概述**: 全面的研究人员信息管理系统，支持人员档案维护和项目角色分配

**主要功能**:
- 👤 **人员档案管理**: 基本信息、联系方式、职位信息
- 🏥 **组织架构**: 部门和组织关系管理
- 🎯 **角色权限**: 项目角色分配和权限控制
- 🔍 **智能搜索**: 多条件人员搜索和筛选

**技术实现**:
- **前端界面**: `src/routes/staff/` 人员管理页面
- **后端服务**: `src-tauri/src/staff.rs` 直接命令实现
- **数据存储**: `staff` 表存储人员信息

### 数据字典管理

**功能概述**: 系统基础数据维护，为各功能模块提供标准化的选项数据

**主要功能**:
- 📚 **字典分类管理**: 按业务领域组织字典数据
- 🏷️ **标签系统**: 字典项标签化管理
- 🔄 **数据同步**: 支持字典数据的导入导出
- ⚡ **实时更新**: 字典变更实时生效

**技术实现**:
- **管理界面**: `src/routes/sqlite-dictionaries/` 字典管理页面
- **服务层**: `src/lib/services/sqliteDictionaryService.ts`
- **数据模型**: `dictionaries` 和 `dictionary_items` 表

### 智能笔记系统

**功能概述**: AI 驱动的智能笔记管理，支持自动分类和 Notion 同步

**主要功能**:
- ✍️ **笔记编辑**: 富文本笔记编辑和管理
- 🤖 **AI 分类**: 基于 Langchain.js 的智能内容分类
- 🔄 **Notion 同步**: 自动同步到 Notion 数据库
- 📊 **批量处理**: 支持批量笔记处理和分类

**技术实现**:
- **前端界面**: `src/routes/notes/` 笔记管理页面
- **AI 引擎**: `src/lib/utils/noteClassifier.ts` Langchain 集成
- **外部集成**: Notion API 数据同步

### 数据分析仪表盘

**功能概述**: 项目数据可视化分析，提供多维度的统计报表和图表展示

**主要功能**:
- 📊 **项目统计**: 项目状态、进度、分布统计
- 👥 **人员分析**: 人员分配和工作量分析
- 💰 **财务报表**: 补贴和费用统计分析
- 📈 **趋势分析**: 时间序列数据趋势展示

**技术实现**:
- **可视化**: ECharts 图表库
- **数据服务**: `src/lib/services/dashboardService.ts`
- **后端聚合**: `src-tauri/src/commands/dashboard_commands.rs`

### 系统配置管理

**功能概述**: 应用程序配置中心，管理各类系统设置和外部服务配置

**主要功能**:
- 🔑 **API 密钥管理**: OpenAI、Notion 等服务密钥配置
- 🌐 **服务器配置**: 外部服务连接参数设置
- 🎨 **界面偏好**: 用户界面个性化设置
- 🔒 **安全配置**: 敏感信息加密存储

**技术实现**:
- **配置界面**: `src/lib/components/SettingsDialog.svelte`
- **状态管理**: Svelte stores 配置状态管理
- **后端存储**: MongoDB 配置数据持久化

## 🔗 外部系统集成

### Notion 集成
- **功能**: 智能笔记同步、项目数据导出
- **API**: Notion Database API 和 Pages API
- **用途**: 笔记分类后自动同步到 Notion 工作区

### Lighthouse API 集成
- **功能**: 临床数据管理系统集成
- **用途**: 患者信息、医疗记录、访问数据管理
- **实现**: HTTP 代理模式，通过 Tauri 后端调用外部 API

### Referrer API 集成
- **功能**: 转诊管理系统集成
- **用途**: 转诊医生和患者关系管理
- **特点**: 支持转诊关系的创建、查询和更新

## 🤖 AI 功能集成

### Langchain.js 框架
**核心技术**: 基于 Langchain.js 的大型语言模型集成框架

**实现架构**:
- **模型管理**: 支持 OpenAI GPT 系列模型
- **提示工程**: 结构化的提示模板系统
- **链式处理**: LCEL (LangChain Expression Language) 模式
- **输出解析**: JSON 结构化输出处理

**应用场景**:
- 📝 **智能笔记分类**: 自动识别笔记内容类型和主题
- 🎯 **入排标准生成**: 基于文本描述自动生成结构化标准
- 📊 **内容摘要**: 长文本内容智能摘要提取
- 🔍 **语义搜索**: 基于语义的智能搜索功能

**配置管理**:
- API 密钥通过系统配置模块安全管理
- 支持多种模型和参数动态配置
- 实时模型切换和性能优化

## 🖥️ 开发环境设置

### 必要工具安装

**Node.js 环境**:
```bash
# 安装 Node.js (推荐 v18+)
# 使用 nvm 管理 Node.js 版本
nvm install 18
nvm use 18

# 安装包管理器 (推荐 pnpm)
npm install -g pnpm
```

**Rust 环境**:
```bash
# 安装 Rust
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh

# 安装 Tauri CLI
cargo install tauri-cli
```

**系统依赖** (根据操作系统):
- **macOS**: `xcode-select --install`
- **Windows**: Visual Studio C++ Build Tools
- **Linux**: `sudo apt install libwebkit2gtk-4.0-dev build-essential curl wget file`

### 推荐开发工具

**IDE 配置**:
- [Visual Studio Code](https://code.visualstudio.com/)
- [Svelte for VS Code](https://marketplace.visualstudio.com/items?itemName=svelte.svelte-vscode)
- [Tauri](https://marketplace.visualstudio.com/items?itemName=tauri-apps.tauri-vscode)
- [rust-analyzer](https://marketplace.visualstudio.com/items?itemName=rust-lang.rust-analyzer)
- [Tailwind CSS IntelliSense](https://marketplace.visualstudio.com/items?itemName=bradlc.vscode-tailwindcss)

## 🚀 安装与部署

### 快速开始

**1. 克隆项目**:
```bash
git clone <repository-url>
cd tauri-app
```

**2. 安装依赖**:
```bash
# 安装前端依赖
pnpm install

# 安装 Rust 依赖 (自动)
cd src-tauri
cargo build
```

**3. 配置数据库**:
```bash
# 创建数据库目录
mkdir -p "/Users/<USER>/我的文档/sqlite"

# 数据库将在首次运行时自动初始化
```

**4. 启动开发服务器**:
```bash
# 返回项目根目录
cd ..

# 启动开发模式
pnpm tauri dev
```

### 生产环境部署

**构建应用**:
```bash
# 构建生产版本
pnpm tauri build
```

**安装包位置**:
- **macOS**: `src-tauri/target/release/bundle/dmg/`
- **Windows**: `src-tauri/target/release/bundle/msi/`
- **Linux**: `src-tauri/target/release/bundle/deb/`

### 配置说明

**数据库配置**:
- SQLite 数据库位置: `/Users/<USER>/我的文档/sqlite/peckbyte.db`
- MongoDB 连接: 通过系统配置模块设置

**外部服务配置**:
- OpenAI API: 在系统设置中配置 API 密钥
- Notion API: 在系统设置中配置集成密钥
- Lighthouse API: 在系统设置中配置服务器地址

## 📂 项目结构说明

```
临床研究项目管理系统/
├── 📁 src/                          # 前端源码 (SvelteKit)
│   ├── 📄 app.html                  # 应用主模板
│   ├── 📄 app.css                   # 全局样式 (Tailwind CSS)
│   ├── 📁 lib/                      # 共享代码库
│   │   ├── 📁 components/           # UI 组件库
│   │   │   ├── 📁 project/          # 项目管理组件
│   │   │   ├── 📁 rule-designer/    # 规则设计器组件
│   │   │   ├── 📁 dashboard/        # 仪表盘组件
│   │   │   └── 📁 ui/               # 基础 UI 组件
│   │   ├── 📁 services/             # 前端服务层
│   │   │   ├── 📄 projectManagementService.ts
│   │   │   ├── 📄 ruleDesignerService.ts
│   │   │   ├── 📄 staffService.ts
│   │   │   └── 📄 configService.ts
│   │   ├── 📁 stores/               # 状态管理
│   │   ├── 📁 utils/                # 工具函数
│   │   └── 📁 examples/             # 示例数据
│   └── 📁 routes/                   # 页面路由
│       ├── 📁 projects/             # 项目管理页面
│       ├── 📁 staff/                # 人员管理页面
│       ├── 📁 notes/                # 智能笔记页面
│       ├── 📁 dashboard/            # 数据分析页面
│       └── 📁 sqlite-dictionaries/  # 字典管理页面
├── 📁 src-tauri/                    # 后端源码 (Rust)
│   ├── 📁 src/
│   │   ├── 📁 commands/             # Tauri 命令层
│   │   │   ├── 📄 project_management_commands.rs
│   │   │   ├── 📄 rule_designer_commands.rs
│   │   │   ├── 📄 dashboard_commands.rs
│   │   │   └── 📄 staff.rs
│   │   ├── 📁 services/             # 业务逻辑层
│   │   ├── 📁 repositories/         # 数据访问层
│   │   ├── 📁 models/               # 数据模型
│   │   ├── 📄 error.rs              # 错误处理
│   │   ├── 📄 db.rs                 # 数据库连接
│   │   └── 📄 lib.rs                # 库入口
│   ├── 📄 Cargo.toml                # Rust 依赖配置
│   └── 📄 tauri.conf.json           # Tauri 应用配置
├── 📁 开发文档/                      # 项目文档
│   ├── 📄 项目管理开发文档.md
│   ├── 📄 DASHBOARD_ENHANCEMENTS.md
│   └── 📄 api-documentation.md
├── 📄 README.md                     # 项目说明文档
├── 📄 FUNCTIONAL_MANUAL.md          # 功能使用手册
├── 📄 package.json                  # Node.js 依赖配置
├── 📄 tailwind.config.ts            # Tailwind CSS 配置
└── 📄 tsconfig.json                 # TypeScript 配置
```

## 📖 开发指南

### 前端开发规范

**组件开发**:
- 遵循原子设计原则，组件职责单一
- 使用 TypeScript 确保类型安全
- 采用 Tailwind CSS 进行样式设计
- 组件间通过 props 和 events 通信

**状态管理**:
- 使用 Svelte stores 管理全局状态
- 按功能模块组织 store 文件
- 异步操作通过 service 层处理

**代码质量**:
- 使用 ESLint 和 Prettier 保持代码风格一致
- 编写必要的 JSDoc 注释
- 遵循 SvelteKit 最佳实践

### 后端开发规范

**架构原则**:
- 严格遵循分层架构，保持层次清晰
- 使用仓储模式隔离数据访问逻辑
- 统一错误处理和响应格式

**代码规范**:
- 使用 `#[tauri::command]` 宏定义前端接口
- 所有数据库操作使用事务确保一致性
- 充分利用 Rust 类型系统保证安全性

**测试策略**:
- 为核心业务逻辑编写单元测试
- 使用集成测试验证 API 端点
- 数据库操作使用测试数据库

### 贡献指南

**开发流程**:
1. Fork 项目并创建功能分支
2. 遵循代码规范进行开发
3. 编写相应的测试用例
4. 提交 Pull Request 并描述变更内容

**提交规范**:
- 使用语义化的提交信息
- 每个提交专注于单一功能或修复
- 包含必要的测试和文档更新

---

## 📞 技术支持

如有问题或建议，请通过以下方式联系：

- 📧 **邮箱**: [技术支持邮箱]
- 📋 **Issues**: 在 GitHub 仓库提交 Issue
- 📖 **文档**: 查看项目 Wiki 和开发文档

---

**© 2024 临床研究项目管理系统. 保留所有权利.**
