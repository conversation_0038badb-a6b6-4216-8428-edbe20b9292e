#!/bin/bash

# 检查是否安装了必要的工具
check_command() {
    if ! command -v "$1" &> /dev/null; then
        echo "错误: 未找到 $1。请安装 $1 或使用其他工具手动转换图标。"
        return 1
    fi
    return 0
}

# 检查 ImageMagick
if ! check_command convert; then
    echo "请安装 ImageMagick: https://imagemagick.org/script/download.php"
    echo "Mac: brew install imagemagick"
    echo "Linux: sudo apt-get install imagemagick"
    echo "Windows: https://imagemagick.org/script/download.php#windows"
    exit 1
fi

# 检查是否在项目根目录
if [ ! -f "src-tauri/tauri.conf.json" ]; then
    echo "错误: 未找到 src-tauri/tauri.conf.json 文件"
    echo "请确保您在项目根目录中运行此脚本"
    exit 1
fi

echo "开始替换图标文件..."

# 备份原始图标文件
echo "备份原始图标文件..."
mkdir -p src-tauri/icons/original_backup
cp src-tauri/icons/32x32.png src-tauri/icons/original_backup/ 2>/dev/null || true
cp src-tauri/icons/128x128.png src-tauri/icons/original_backup/ 2>/dev/null || true
cp src-tauri/icons/<EMAIL> src-tauri/icons/original_backup/ 2>/dev/null || true
cp src-tauri/icons/icon.icns src-tauri/icons/original_backup/ 2>/dev/null || true
cp src-tauri/icons/icon.ico src-tauri/icons/original_backup/ 2>/dev/null || true
cp src-tauri/icons/icon.png src-tauri/icons/original_backup/ 2>/dev/null || true
cp static/favicon.png static/original_favicon.png 2>/dev/null || true

# 创建临时目录
TEMP_DIR=$(mktemp -d)
echo "创建临时目录: $TEMP_DIR"

# 生成基本 PNG 图标
echo "生成基本 PNG 图标..."
convert -background none -resize 32x32 src-tauri/icons/augment_logo.svg src-tauri/icons/32x32.png
convert -background none -resize 128x128 src-tauri/icons/augment_logo.svg src-tauri/icons/128x128.png
convert -background none -resize 256x256 src-tauri/icons/augment_logo.svg src-tauri/icons/<EMAIL>
convert -background none -resize 1024x1024 src-tauri/icons/augment_logo.svg src-tauri/icons/icon.png

# 生成 favicon
echo "生成 favicon..."
convert -background none -resize 32x32 src-tauri/icons/augment_logo.svg static/favicon.png

# 生成 Windows 图标 (.ico)
echo "生成 Windows 图标 (.ico)..."
# 创建多尺寸 ICO 文件所需的临时 PNG 文件
convert -background none -resize 16x16 src-tauri/icons/augment_logo.svg "$TEMP_DIR/icon_16.png"
convert -background none -resize 32x32 src-tauri/icons/augment_logo.svg "$TEMP_DIR/icon_32.png"
convert -background none -resize 48x48 src-tauri/icons/augment_logo.svg "$TEMP_DIR/icon_48.png"
convert -background none -resize 64x64 src-tauri/icons/augment_logo.svg "$TEMP_DIR/icon_64.png"
convert -background none -resize 128x128 src-tauri/icons/augment_logo.svg "$TEMP_DIR/icon_128.png"
convert -background none -resize 256x256 src-tauri/icons/augment_logo.svg "$TEMP_DIR/icon_256.png"

# 合并为 ICO 文件
convert "$TEMP_DIR/icon_16.png" "$TEMP_DIR/icon_32.png" "$TEMP_DIR/icon_48.png" \
        "$TEMP_DIR/icon_64.png" "$TEMP_DIR/icon_128.png" "$TEMP_DIR/icon_256.png" \
        src-tauri/icons/icon.ico

# 生成 macOS 图标 (.icns)
echo "生成 macOS 图标 (.icns)..."
if [[ "$OSTYPE" == "darwin"* ]]; then
    # 在 macOS 上使用 iconutil
    # 创建 iconset 目录
    mkdir -p "$TEMP_DIR/AppIcon.iconset"

    # 生成不同尺寸的图标
    convert -background none -resize 16x16 src-tauri/icons/augment_logo.svg "$TEMP_DIR/AppIcon.iconset/icon_16x16.png"
    convert -background none -resize 32x32 src-tauri/icons/augment_logo.svg "$TEMP_DIR/AppIcon.iconset/<EMAIL>"
    convert -background none -resize 32x32 src-tauri/icons/augment_logo.svg "$TEMP_DIR/AppIcon.iconset/icon_32x32.png"
    convert -background none -resize 64x64 src-tauri/icons/augment_logo.svg "$TEMP_DIR/AppIcon.iconset/<EMAIL>"
    convert -background none -resize 128x128 src-tauri/icons/augment_logo.svg "$TEMP_DIR/AppIcon.iconset/icon_128x128.png"
    convert -background none -resize 256x256 src-tauri/icons/augment_logo.svg "$TEMP_DIR/AppIcon.iconset/<EMAIL>"
    convert -background none -resize 256x256 src-tauri/icons/augment_logo.svg "$TEMP_DIR/AppIcon.iconset/icon_256x256.png"
    convert -background none -resize 512x512 src-tauri/icons/augment_logo.svg "$TEMP_DIR/AppIcon.iconset/<EMAIL>"
    convert -background none -resize 512x512 src-tauri/icons/augment_logo.svg "$TEMP_DIR/AppIcon.iconset/icon_512x512.png"
    convert -background none -resize 1024x1024 src-tauri/icons/augment_logo.svg "$TEMP_DIR/AppIcon.iconset/<EMAIL>"

    # 使用 iconutil 创建 .icns 文件
    if command -v iconutil &> /dev/null; then
        iconutil -c icns "$TEMP_DIR/AppIcon.iconset" -o src-tauri/icons/icon.icns
    else
        echo "警告: 未找到 iconutil 命令，无法生成 .icns 文件"
        echo "请手动生成 .icns 文件或使用在线转换工具"
    fi
else
    # 在非 macOS 系统上，提供警告
    echo "警告: 不在 macOS 系统上，无法生成 .icns 文件"
    echo "请在 macOS 系统上运行此脚本或使用在线转换工具生成 .icns 文件"
fi

# 生成 Android 图标
echo "生成 Android 图标..."
mkdir -p src-tauri/icons/android/mipmap-mdpi
mkdir -p src-tauri/icons/android/mipmap-hdpi
mkdir -p src-tauri/icons/android/mipmap-xhdpi
mkdir -p src-tauri/icons/android/mipmap-xxhdpi
mkdir -p src-tauri/icons/android/mipmap-xxxhdpi

# 生成不同尺寸的 Android 图标
convert -background none -resize 48x48 src-tauri/icons/augment_logo.svg src-tauri/icons/android/mipmap-mdpi/ic_launcher.png
convert -background none -resize 72x72 src-tauri/icons/augment_logo.svg src-tauri/icons/android/mipmap-hdpi/ic_launcher.png
convert -background none -resize 96x96 src-tauri/icons/augment_logo.svg src-tauri/icons/android/mipmap-xhdpi/ic_launcher.png
convert -background none -resize 144x144 src-tauri/icons/augment_logo.svg src-tauri/icons/android/mipmap-xxhdpi/ic_launcher.png
convert -background none -resize 192x192 src-tauri/icons/augment_logo.svg src-tauri/icons/android/mipmap-xxxhdpi/ic_launcher.png

# 复制为圆形图标
cp src-tauri/icons/android/mipmap-mdpi/ic_launcher.png src-tauri/icons/android/mipmap-mdpi/ic_launcher_round.png
cp src-tauri/icons/android/mipmap-hdpi/ic_launcher.png src-tauri/icons/android/mipmap-hdpi/ic_launcher_round.png
cp src-tauri/icons/android/mipmap-xhdpi/ic_launcher.png src-tauri/icons/android/mipmap-xhdpi/ic_launcher_round.png
cp src-tauri/icons/android/mipmap-xxhdpi/ic_launcher.png src-tauri/icons/android/mipmap-xxhdpi/ic_launcher_round.png
cp src-tauri/icons/android/mipmap-xxxhdpi/ic_launcher.png src-tauri/icons/android/mipmap-xxxhdpi/ic_launcher_round.png

# 复制为前景图标
cp src-tauri/icons/android/mipmap-mdpi/ic_launcher.png src-tauri/icons/android/mipmap-mdpi/ic_launcher_foreground.png
cp src-tauri/icons/android/mipmap-hdpi/ic_launcher.png src-tauri/icons/android/mipmap-hdpi/ic_launcher_foreground.png
cp src-tauri/icons/android/mipmap-xhdpi/ic_launcher.png src-tauri/icons/android/mipmap-xhdpi/ic_launcher_foreground.png
cp src-tauri/icons/android/mipmap-xxhdpi/ic_launcher.png src-tauri/icons/android/mipmap-xxhdpi/ic_launcher_foreground.png
cp src-tauri/icons/android/mipmap-xxxhdpi/ic_launcher.png src-tauri/icons/android/mipmap-xxxhdpi/ic_launcher_foreground.png

# 生成 Windows 应用商店图标
echo "生成 Windows 应用商店图标..."
convert -background none -resize 30x30 src-tauri/icons/augment_logo.svg src-tauri/icons/Square30x30Logo.png
convert -background none -resize 44x44 src-tauri/icons/augment_logo.svg src-tauri/icons/Square44x44Logo.png
convert -background none -resize 71x71 src-tauri/icons/augment_logo.svg src-tauri/icons/Square71x71Logo.png
convert -background none -resize 89x89 src-tauri/icons/augment_logo.svg src-tauri/icons/Square89x89Logo.png
convert -background none -resize 107x107 src-tauri/icons/augment_logo.svg src-tauri/icons/Square107x107Logo.png
convert -background none -resize 142x142 src-tauri/icons/augment_logo.svg src-tauri/icons/Square142x142Logo.png
convert -background none -resize 150x150 src-tauri/icons/augment_logo.svg src-tauri/icons/Square150x150Logo.png
convert -background none -resize 284x284 src-tauri/icons/augment_logo.svg src-tauri/icons/Square284x284Logo.png
convert -background none -resize 310x310 src-tauri/icons/augment_logo.svg src-tauri/icons/Square310x310Logo.png
convert -background none -resize 50x50 src-tauri/icons/augment_logo.svg src-tauri/icons/StoreLogo.png

# 生成 iOS 图标
echo "生成 iOS 图标..."
mkdir -p src-tauri/icons/ios

# 生成不同尺寸的 iOS 图标
convert -background none -resize 20x20 src-tauri/icons/augment_logo.svg src-tauri/icons/ios/<EMAIL>
convert -background none -resize 40x40 src-tauri/icons/augment_logo.svg src-tauri/icons/ios/<EMAIL>
convert -background none -resize 60x60 src-tauri/icons/augment_logo.svg src-tauri/icons/ios/<EMAIL>
convert -background none -resize 29x29 src-tauri/icons/augment_logo.svg src-tauri/icons/ios/<EMAIL>
convert -background none -resize 58x58 src-tauri/icons/augment_logo.svg src-tauri/icons/ios/<EMAIL>
convert -background none -resize 87x87 src-tauri/icons/augment_logo.svg src-tauri/icons/ios/<EMAIL>
convert -background none -resize 40x40 src-tauri/icons/augment_logo.svg src-tauri/icons/ios/<EMAIL>
convert -background none -resize 80x80 src-tauri/icons/augment_logo.svg src-tauri/icons/ios/<EMAIL>
convert -background none -resize 120x120 src-tauri/icons/augment_logo.svg src-tauri/icons/ios/<EMAIL>
convert -background none -resize 120x120 src-tauri/icons/augment_logo.svg src-tauri/icons/ios/<EMAIL>
convert -background none -resize 180x180 src-tauri/icons/augment_logo.svg src-tauri/icons/ios/<EMAIL>
convert -background none -resize 76x76 src-tauri/icons/augment_logo.svg src-tauri/icons/ios/<EMAIL>
convert -background none -resize 152x152 src-tauri/icons/augment_logo.svg src-tauri/icons/ios/<EMAIL>
convert -background none -resize 167x167 src-tauri/icons/augment_logo.svg src-tauri/icons/ios/<EMAIL>
convert -background none -resize 1024x1024 src-tauri/icons/augment_logo.svg src-tauri/icons/ios/<EMAIL>

# 复制一些重复的图标（Tauri 需要）
cp src-tauri/icons/ios/<EMAIL> src-tauri/icons/ios/<EMAIL>
cp src-tauri/icons/ios/<EMAIL> src-tauri/icons/ios/<EMAIL>
cp src-tauri/icons/ios/<EMAIL> src-tauri/icons/ios/<EMAIL>

# 清理临时文件
echo "清理临时文件..."
rm -rf "$TEMP_DIR"

echo "图标替换完成！"
echo ""
echo "注意: 原始图标已备份到 src-tauri/icons/original_backup/ 目录"
echo "      原始 favicon 已备份为 static/original_favicon.png"
echo ""
echo "提示: 您可能需要重新启动应用程序以查看新图标"
