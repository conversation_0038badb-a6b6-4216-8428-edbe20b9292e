#!/bin/bash

echo "设置 Git 忽略 tauri.conf.json 中的特定字段更改..."

# 首先，确保当前目录是项目根目录
if [ ! -f "src-tauri/tauri.conf.json" ]; then
    echo "错误: 未找到 src-tauri/tauri.conf.json 文件"
    echo "请确保您在项目根目录中运行此脚本"
    exit 1
fi

# 检查是否已经有未提交的更改
if ! git diff --quiet src-tauri/tauri.conf.json; then
    echo "警告: src-tauri/tauri.conf.json 有未提交的更改"
    echo "请先提交或恢复这些更改，然后再运行此脚本"
    exit 1
fi

# 创建一个临时文件来保存当前的 tauri.conf.json
cp src-tauri/tauri.conf.json src-tauri/tauri.conf.json.temp

# 修改 productName 和 title 字段
echo "修改 productName 和 title 字段为您的分支特定值..."
sed -i '' 's/"productName": ".*"/"productName": "超星助手-augment"/' src-tauri/tauri.conf.json
sed -i '' 's/"title": ".*"/"title": "超星助手-augment"/' src-tauri/tauri.conf.json

# 提交这个更改
echo "提交更改..."
git add src-tauri/tauri.conf.json
git commit -m "Update productName and title for branch"

# 告诉 Git 忽略这些特定字段的更改
echo "设置 Git 忽略这些字段的更改..."
git update-index --assume-unchanged src-tauri/tauri.conf.json

# 恢复原始文件
echo "恢复原始文件..."
mv src-tauri/tauri.conf.json.temp src-tauri/tauri.conf.json

echo "完成！Git 现在将忽略 tauri.conf.json 中的 productName 和 title 字段的更改"
echo "您可以自由修改这些字段，而不会影响 master 分支"
echo ""
echo "如果您需要让 Git 再次跟踪这些更改，请运行:"
echo "git update-index --no-assume-unchanged src-tauri/tauri.conf.json"
