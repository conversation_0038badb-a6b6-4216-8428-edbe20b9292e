{"permissions": {"allow": ["Bash(ls:*)", "Bash(git add:*)", "Bash(git push:*)", "Bash(find:*)", "Bash(grep:*)", "Bash(npm run build:*)", "Bash(npm run tauri:*)", "Bash(npm install:*)", "<PERSON><PERSON>(pkill:*)", "Bash(git commit:*)", "Bash(npm run check:*)", "<PERSON><PERSON>(claude config list)", "<PERSON><PERSON>(uv:*)", "Bash(claude mcp add:*)", "<PERSON><PERSON>(claude mcp:*)", "mcp__sqlite-explorer__list_tables", "mcp__sqlite-explorer__describe_table", "Bash(rg:*)", "mcp__sqlite-explorer__read_query", "<PERSON><PERSON>(curl:*)", "Bash(cargo clean:*)", "Bash(cargo:*)"], "deny": []}}