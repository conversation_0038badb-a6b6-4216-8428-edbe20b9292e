---
noteId: "f96b5f00441911f0ab6f679a54a69364"
tags: []

---

# 开发文档目录

本目录包含临床研究项目管理系统的详细技术开发文档。这些文档主要面向开发人员，提供系统架构、实现细节和技术规范。

## 文档结构

### 核心功能开发文档

#### 📋 [项目管理开发文档](./项目管理开发文档.md)
- **描述**: 项目管理模块的技术实现细节
- **内容**: 前端组件架构、后端服务、数据库结构、API 接口
- **面向**: 开发人员
- **关联**: [功能手册 - 项目管理](../FUNCTIONAL_MANUAL.md#1-项目管理)

#### 🎯 [入排标准规则开发文档](./入组排除标准配置功能开发文档.md)
- **描述**: 入排标准规则引擎的技术架构和实现
- **内容**: 规则定义、参数验证、前后端交互、数据模型
- **面向**: 开发人员
- **关联**: [功能手册 - 入排标准规则引擎](../FUNCTIONAL_MANUAL.md#2-入排标准规则引擎)

#### 🤖 [自动化生成入排标准开发文档](./自动化生成入排标准JSON文件.md)
- **描述**: AI 驱动的入排标准自动生成功能
- **内容**: Langchain.js 集成、LLM 处理流程、JSON 生成逻辑
- **面向**: 开发人员
- **关联**: [功能手册 - AI 功能集成](../FUNCTIONAL_MANUAL.md#12-ai-功能集成)

#### 📊 [项目管理仪表盘开发文档](./项目管理仪表盘.md)
- **描述**: 数据分析仪表盘的技术设计和实现
- **内容**: 图表组件、数据聚合、可视化技术、增强功能
- **面向**: 开发人员
- **关联**: [功能手册 - 数据分析仪表盘](../FUNCTIONAL_MANUAL.md#6-数据分析仪表盘)

### 数据库与 API 文档

#### 🗄️ [数据库结构文档](./database.md)
- **描述**: SQLite 数据库的完整结构说明
- **内容**: 表结构、字段定义、关系约束、索引信息
- **面向**: 开发人员、数据库管理员
- **注意**: 此信息也包含在工作区规则中

#### 🌐 [Lighthouse API 文档](./api-documentation.md)
- **描述**: Lighthouse 后端 API 的详细接口规范
- **内容**: 认证、用户管理、患者管理、数据模型、部署指南
- **面向**: 前端开发人员、API 集成开发人员
- **关联**: [功能手册 - Lighthouse API 集成](../FUNCTIONAL_MANUAL.md#9-lighthouse-api-集成)

#### 🔗 [转诊管理 API 文档](./referral-management-api.md)
- **描述**: 转诊管理系统的 API 接口规范
- **内容**: 转诊医生管理、患者关联、查询接口
- **面向**: API 集成开发人员
- **关联**: [功能手册 - 转诊管理 API 集成](../FUNCTIONAL_MANUAL.md#10-转诊管理-api-集成)

#### 📄 [GCPM API 调用实例](./GCPM%20API调用实例.md)
- **描述**: GCPM 系统的 API 调用示例和规范
- **内容**: API 调用方法、参数说明、响应格式
- **面向**: API 集成开发人员

#### 📋 [GCPM OpenAPI 规范](./gcpm-openapi-spec.json)
- **描述**: GCPM 系统的 OpenAPI 3.0 规范文件
- **内容**: 完整的 API 接口定义（JSON 格式）
- **面向**: API 开发人员、自动化工具

#### 🏥 [Lighthouse 后端用户管理逻辑](./lighthouse-后端用户管理逻辑.md)
- **描述**: Lighthouse 系统的用户管理业务逻辑
- **内容**: 用户认证、权限管理、业务流程
- **面向**: 后端开发人员

## 文档使用指南

### 开发人员快速导航

1. **新功能开发**: 
   - 先查看 [README.md](../README.md) 了解系统架构
   - 参考相关功能的开发文档了解实现模式
   - 查看数据库文档了解数据结构

2. **API 集成**:
   - 查看对应的 API 文档了解接口规范
   - 参考功能手册了解业务逻辑
   - 查看系统配置文档了解连接设置

3. **问题排查**:
   - 查看相关功能的开发文档了解技术细节
   - 参考数据库文档检查数据结构
   - 查看 API 文档确认接口调用

### 文档维护原则

1. **避免重复**: 技术实现细节在开发文档中，用户功能说明在功能手册中
2. **交叉引用**: 文档间通过链接相互引用，保持信息一致性
3. **及时更新**: 代码变更时同步更新相关文档
4. **清晰分类**: 按功能模块和文档类型进行分类组织

## 相关文档

- [项目主文档 (README.md)](../README.md)
- [功能使用手册 (FUNCTIONAL_MANUAL.md)](../FUNCTIONAL_MANUAL.md)
- [工作区规则](../workspace_rules.md) - 包含数据库结构等基础信息

## 贡献指南

如需更新开发文档，请遵循以下原则：

1. **保持一致性**: 使用统一的文档格式和风格
2. **添加交叉引用**: 在相关文档间添加链接
3. **更新索引**: 新增文档后更新本 README.md
4. **技术导向**: 开发文档专注于技术实现，避免重复功能说明

---

**最后更新**: 2024年
**维护者**: 开发团队
