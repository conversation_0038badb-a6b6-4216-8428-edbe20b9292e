---
noteId: "bf0be44030ca11f0b3d9b99446086531"
tags: []

---

BASE_URL=https://api.e3e4.club

## 登录获取token

请求:
```
curl -X 'POST' \
  'https://api.e3e4.club/auth/login' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "username": "<PERSON><PERSON><PERSON><PERSON>",
  "password": "changethepassword"
}'
```

返回示例:
```
{"access_token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************.m7vJfDJuXxlmiAB1D05eZw3msNk_ABw-1TrxYnE4wdU","user":{"id":3,"username":"<PERSON><PERSON><PERSON><PERSON>","email":"<EMAIL>","phone":"","userTypeId":0,"userProfile":null}
```

## /referral-management/referrer 分页查询推荐人

```
 curl -X 'GET' \
        'https://api.e3e4.club/auth/login/referral-management/referrer?limit=10&sortBy=createdAt&sortOrder=desc' \
        -H 'accept: application/json' \
        -H 'Authorization: Bearer <从POST /auth/login获取的token>'
```

```
{
  "items": [
    {
      "id": "string",
      "name": "string",
      "phone": "string",
      "createdAt": "2025-05-14T07:41:38.091Z",
      "updatedAt": "2025-05-14T07:41:38.091Z"
    }
  ],
  "nextCursor": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx 或 12345",
  "previousCursor": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx 或 12345",
  "sortBy": "createdAt",
  "sortOrder": "desc"
}
```
