---
noteId: "d4d8e220441911f0ab6f679a54a69364"
tags: []
---

以下是本项目的sqlite数据库表单结构，数据库地址是：`/Users/<USER>/我的文档/sqlite/peckbyte.db`

# Database Documentation

Documentation for database: `peckbyte.db`

## Tables

### Table: `dictionaries`

#### Columns

| Column Name | Data Type | NOT NULL | Primary Key | Default Value |
|-------------|-----------|----------|-------------|---------------|
| id | INTEGER | NO | YES | NULL |
| mongo_oid | TEXT | NO | NO | NULL |
| name | TEXT | YES | NO | NULL |
| description | TEXT | NO | NO | NULL |
| type | TEXT | YES | NO | 'list' |
| created_at | TEXT | YES | NO | NULL |
| updated_at | TEXT | YES | NO | NULL |
| version | INTEGER | YES | NO | 1 |

This table has no foreign key constraints.

---

### Table: `sqlite_sequence`

#### Columns

| Column Name | Data Type | NOT NULL | Primary Key | Default Value |
|-------------|-----------|----------|-------------|---------------|
| name |  | NO | NO | NULL |
| seq |  | NO | NO | NULL |

This table has no foreign key constraints.

---

### Table: `dictionary_items`

#### Columns

| Column Name | Data Type | NOT NULL | Primary Key | Default Value |
|-------------|-----------|----------|-------------|---------------|
| item_id | INTEGER | NO | YES | NULL |
| dictionary_id | INTEGER | YES | NO | NULL |
| item_key | TEXT | YES | NO | NULL |
| item_value | TEXT | YES | NO | NULL |
| item_description | TEXT | NO | NO | NULL |
| status | TEXT | YES | NO | 'active' |

#### Foreign Keys (Relationships)

| ID | Sequence | To Table | From Column | To Column | On Update | On Delete | Match |
|----|----------|----------|-------------|-----------|-----------|-----------|-------|
| 0 | 0 | `dictionaries` | `dictionary_id` | `id` | NO ACTION | CASCADE | NONE |

---

### Table: `tags`

#### Columns

| Column Name | Data Type | NOT NULL | Primary Key | Default Value |
|-------------|-----------|----------|-------------|---------------|
| tag_id | INTEGER | NO | YES | NULL |
| tag_name | TEXT | YES | NO | NULL |

This table has no foreign key constraints.

---

### Table: `dictionary_tags`

#### Columns

| Column Name | Data Type | NOT NULL | Primary Key | Default Value |
|-------------|-----------|----------|-------------|---------------|
| dictionary_id | INTEGER | YES | YES | NULL |
| tag_id | INTEGER | YES | YES | NULL |

#### Foreign Keys (Relationships)

| ID | Sequence | To Table | From Column | To Column | On Update | On Delete | Match |
|----|----------|----------|-------------|-----------|-----------|-----------|-------|
| 0 | 0 | `tags` | `tag_id` | `tag_id` | NO ACTION | CASCADE | NONE |
| 1 | 0 | `dictionaries` | `dictionary_id` | `id` | NO ACTION | CASCADE | NONE |

---

### Table: `staff`

#### Columns

| Column Name | Data Type | NOT NULL | Primary Key | Default Value |
|-------------|-----------|----------|-------------|---------------|
| id | INTEGER | NO | YES | NULL |
| name | TEXT | YES | NO | NULL |
| gender | TEXT | NO | NO | NULL |
| birthday | TEXT | NO | NO | NULL |
| phone | TEXT | NO | NO | NULL |
| email | TEXT | NO | NO | NULL |
| position_item_id | INTEGER | NO | NO | NULL |
| isPI | INTEGER | YES | NO | 0 |
| organization | TEXT | NO | NO | NULL |
| created_at | TEXT | NO | NO | CURRENT_TIMESTAMP |
| updated_at | TEXT | NO | NO | NULL |

#### Foreign Keys (Relationships)

| ID | Sequence | To Table | From Column | To Column | On Update | On Delete | Match |
|----|----------|----------|-------------|-----------|-----------|-----------|-------|
| 0 | 0 | `dictionary_items` | `position_item_id` | `item_id` | NO ACTION | NO ACTION | NONE |

---

### Table: `projects`

#### Columns

| Column Name | Data Type | NOT NULL | Primary Key | Default Value |
|-------------|-----------|----------|-------------|---------------|
| project_id | TEXT | NO | YES | NULL |
| project_name | TEXT | YES | NO | NULL |
| project_short_name | TEXT | YES | NO | NULL |
| project_path | TEXT | NO | NO | NULL |
| disease_item_id | INTEGER | NO | NO | NULL |
| project_stage_item_id | INTEGER | NO | NO | NULL |
| project_status_item_id | INTEGER | NO | NO | NULL |
| recruitment_status_item_id | INTEGER | NO | NO | NULL |
| contract_case_center | INTEGER | NO | NO | NULL |
| contract_case_total | INTEGER | NO | NO | NULL |
| project_start_date | TEXT | NO | NO | NULL |
| last_updated | TEXT | NO | NO | NULL |

#### Foreign Keys (Relationships)

| ID | Sequence | To Table | From Column | To Column | On Update | On Delete | Match |
|----|----------|----------|-------------|-----------|-----------|-----------|-------|
| 0 | 0 | `dictionary_items` | `recruitment_status_item_id` | `item_id` | NO ACTION | NO ACTION | NONE |
| 1 | 0 | `dictionary_items` | `project_status_item_id` | `item_id` | NO ACTION | NO ACTION | NONE |
| 2 | 0 | `dictionary_items` | `project_stage_item_id` | `item_id` | NO ACTION | NO ACTION | NONE |
| 3 | 0 | `dictionary_items` | `disease_item_id` | `item_id` | NO ACTION | NO ACTION | NONE |

---

### Table: `project_sponsors`

#### Columns

| Column Name | Data Type | NOT NULL | Primary Key | Default Value |
|-------------|-----------|----------|-------------|---------------|
| id | INTEGER | NO | YES | NULL |
| project_id | TEXT | YES | NO | NULL |
| sponsor_item_id | INTEGER | YES | NO | NULL |

#### Foreign Keys (Relationships)

| ID | Sequence | To Table | From Column | To Column | On Update | On Delete | Match |
|----|----------|----------|-------------|-----------|-----------|-----------|-------|
| 0 | 0 | `dictionary_items` | `sponsor_item_id` | `item_id` | NO ACTION | NO ACTION | NONE |
| 1 | 0 | `projects` | `project_id` | `project_id` | NO ACTION | NO ACTION | NONE |

---

### Table: `research_drugs`

#### Columns

| Column Name | Data Type | NOT NULL | Primary Key | Default Value |
|-------------|-----------|----------|-------------|---------------|
| drug_info_id | INTEGER | NO | YES | NULL |
| project_id | TEXT | YES | NO | NULL |
| research_drug | TEXT | YES | NO | NULL |

#### Foreign Keys (Relationships)

| ID | Sequence | To Table | From Column | To Column | On Update | On Delete | Match |
|----|----------|----------|-------------|-----------|-----------|-----------|-------|
| 0 | 0 | `projects` | `project_id` | `project_id` | NO ACTION | NO ACTION | NONE |

---

### Table: `drug_groups`

#### Columns

| Column Name | Data Type | NOT NULL | Primary Key | Default Value |
|-------------|-----------|----------|-------------|---------------|
| group_id | INTEGER | NO | YES | NULL |
| project_id | TEXT | YES | NO | NULL |
| drug_name | TEXT | YES | NO | NULL |
| share | INTEGER | YES | NO | NULL |

#### Foreign Keys (Relationships)

| ID | Sequence | To Table | From Column | To Column | On Update | On Delete | Match |
|----|----------|----------|-------------|-----------|-----------|-----------|-------|
| 0 | 0 | `projects` | `project_id` | `project_id` | NO ACTION | NO ACTION | NONE |

---

### Table: `project_personnel_roles`

#### Columns

| Column Name | Data Type | NOT NULL | Primary Key | Default Value |
|-------------|-----------|----------|-------------|---------------|
| assignment_id | INTEGER | NO | YES | NULL |
| project_id | TEXT | YES | NO | NULL |
| personnel_id | INTEGER | YES | NO | NULL |
| role_item_id | INTEGER | YES | NO | NULL |

#### Foreign Keys (Relationships)

| ID | Sequence | To Table | From Column | To Column | On Update | On Delete | Match |
|----|----------|----------|-------------|-----------|-----------|-----------|-------|
| 0 | 0 | `dictionary_items` | `role_item_id` | `item_id` | NO ACTION | NO ACTION | NONE |
| 1 | 0 | `staff` | `personnel_id` | `id` | NO ACTION | NO ACTION | NONE |
| 2 | 0 | `projects` | `project_id` | `project_id` | NO ACTION | NO ACTION | NONE |

---

### Table: `subsidies`

#### Columns

| Column Name | Data Type | NOT NULL | Primary Key | Default Value |
|-------------|-----------|----------|-------------|---------------|
| subsidy_item_id | INTEGER | NO | YES | NULL |
| project_id | TEXT | YES | NO | NULL |
| subsidy_type_item_id | INTEGER | YES | NO | NULL |
| unit_amount | REAL | YES | NO | NULL |
| total_units | INTEGER | YES | NO | NULL |
| unit_item_id | INTEGER | YES | NO | NULL |
| total_amount | REAL | YES | NO | NULL |
| last_updated | TEXT | NO | NO | NULL |

#### Foreign Keys (Relationships)

| ID | Sequence | To Table | From Column | To Column | On Update | On Delete | Match |
|----|----------|----------|-------------|-----------|-----------|-----------|-------|
| 0 | 0 | `dictionary_items` | `unit_item_id` | `item_id` | NO ACTION | NO ACTION | NONE |
| 1 | 0 | `dictionary_items` | `subsidy_type_item_id` | `item_id` | NO ACTION | NO ACTION | NONE |
| 2 | 0 | `projects` | `project_id` | `project_id` | NO ACTION | NO ACTION | NONE |

---

### Table: `subsidy_schemes`

#### Columns

| Column Name | Data Type | NOT NULL | Primary Key | Default Value |
|-------------|-----------|----------|-------------|---------------|
| scheme_id | INTEGER | NO | YES | NULL |
| project_id | TEXT | YES | NO | NULL |
| scheme_name | TEXT | YES | NO | NULL |
| total_amount | REAL | YES | NO | NULL |

#### Foreign Keys (Relationships)

| ID | Sequence | To Table | From Column | To Column | On Update | On Delete | Match |
|----|----------|----------|-------------|-----------|-----------|-----------|-------|
| 0 | 0 | `projects` | `project_id` | `project_id` | NO ACTION | NO ACTION | NONE |

---

### Table: `scheme_included_subsidies`

#### Columns

| Column Name | Data Type | NOT NULL | Primary Key | Default Value |
|-------------|-----------|----------|-------------|---------------|
| id | INTEGER | NO | YES | NULL |
| scheme_id | INTEGER | YES | NO | NULL |
| subsidy_item_id | INTEGER | YES | NO | NULL |

#### Foreign Keys (Relationships)

| ID | Sequence | To Table | From Column | To Column | On Update | On Delete | Match |
|----|----------|----------|-------------|-----------|-----------|-----------|-------|
| 0 | 0 | `subsidies` | `subsidy_item_id` | `subsidy_item_id` | NO ACTION | NO ACTION | NONE |
| 1 | 0 | `subsidy_schemes` | `scheme_id` | `scheme_id` | NO ACTION | NO ACTION | NONE |

---

### Table: `rule_definitions`

#### Columns

| Column Name | Data Type | NOT NULL | Primary Key | Default Value |
|-------------|-----------|----------|-------------|---------------|
| rule_definition_id | INTEGER | NO | YES | NULL |
| rule_name | TEXT | YES | NO | NULL |
| rule_description | TEXT | NO | NO | NULL |
| category | TEXT | NO | NO | NULL |
| parameter_schema | TEXT | YES | NO | NULL |
| created_at | TEXT | YES | NO | NULL |
| updated_at | TEXT | YES | NO | NULL |

This table has no foreign key constraints.

---

### Table: `project_criteria`

#### Columns

| Column Name | Data Type | NOT NULL | Primary Key | Default Value |
|-------------|-----------|----------|-------------|---------------|
| project_criterion_id | INTEGER | NO | YES | NULL |
| project_id | TEXT | YES | NO | NULL |
| rule_definition_id | INTEGER | YES | NO | NULL |
| criterion_type | TEXT | YES | NO | NULL |
| parameter_values | TEXT | YES | NO | NULL |
| is_active | BOOLEAN | YES | NO | 1 |
| display_order | INTEGER | NO | NO | NULL |
| created_at | TEXT | YES | NO | NULL |
| updated_at | TEXT | YES | NO | NULL |

#### Foreign Keys (Relationships)

| ID | Sequence | To Table | From Column | To Column | On Update | On Delete | Match |
|----|----------|----------|-------------|-----------|-----------|-----------|-------|
| 0 | 0 | `projects` | `project_id` | `project_id` | NO ACTION | NO ACTION | NONE |
| 1 | 0 | `rule_definitions` | `rule_definition_id` | `rule_definition_id` | NO ACTION | NO ACTION | NONE |

---

### Table: `rule_packages`

#### Columns

| Column Name | Data Type | NOT NULL | Primary Key | Default Value |
|-------------|-----------|----------|-------------|---------------|
| rule_package_id | INTEGER | NO | YES | NULL |
| package_name | TEXT | YES | NO | NULL |
| package_description | TEXT | NO | NO | NULL |
| category | TEXT | NO | NO | NULL |
| is_active | INTEGER | YES | NO | 1 |
| created_at | TEXT | YES | NO | CURRENT_TIMESTAMP |
| updated_at | TEXT | YES | NO | CURRENT_TIMESTAMP |

This table has no foreign key constraints.

---

### Table: `rule_package_items`

#### Columns

| Column Name | Data Type | NOT NULL | Primary Key | Default Value |
|-------------|-----------|----------|-------------|---------------|
| rule_package_item_id | INTEGER | NO | YES | NULL |
| rule_package_id | INTEGER | YES | NO | NULL |
| rule_definition_id | INTEGER | YES | NO | NULL |
| display_order | INTEGER | YES | NO | 0 |
| created_at | TEXT | YES | NO | CURRENT_TIMESTAMP |

#### Foreign Keys (Relationships)

| ID | Sequence | To Table | From Column | To Column | On Update | On Delete | Match |
|----|----------|----------|-------------|-----------|-----------|-----------|-------|
| 0 | 0 | `rule_definitions` | `rule_definition_id` | `rule_definition_id` | NO ACTION | CASCADE | NONE |
| 1 | 0 | `rule_packages` | `rule_package_id` | `rule_package_id` | NO ACTION | CASCADE | NONE |

---

### Table: `subsidy_scheme_items`

#### Columns

| Column Name | Data Type | NOT NULL | Primary Key | Default Value |
|-------------|-----------|----------|-------------|---------------|
| id | INTEGER | NO | YES | NULL |
| scheme_id | INTEGER | YES | NO | NULL |
| subsidy_item_id | INTEGER | YES | NO | NULL |

#### Foreign Keys (Relationships)

| ID | Sequence | To Table | From Column | To Column | On Update | On Delete | Match |
|----|----------|----------|-------------|-----------|-----------|-----------|-------|
| 0 | 0 | `subsidies` | `subsidy_item_id` | `subsidy_item_id` | NO ACTION | CASCADE | NONE |
| 1 | 0 | `subsidy_schemes` | `scheme_id` | `scheme_id` | NO ACTION | CASCADE | NONE |

