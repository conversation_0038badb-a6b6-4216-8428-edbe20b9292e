

我有一个后端程序，API调用地址是“http://**************:3001”
## 后端技术栈如下：
```
### Features

- **TypeScript & NestJS**: Modern, type-safe backend development
- **Prisma ORM**: Type-safe database access with MySQL
- **JWT Authentication**: Secure user authentication and authorization
- **Role-based Access Control**: Admin and User role management
- **RESTful API**: Well-structured API endpoints
- **Docker Support**: Containerized deployment for Tencent Cloud
- **Validation**: Request validation using class-validator
- **Security Features**:
  - Helmet for HTTP security headers
  - Rate limiting to prevent abuse
  - CORS protection
  - Environment variable validation
- **API Documentation**:
  - Swagger/OpenAPI interactive documentation
  - Compodoc for comprehensive code documentation
```

#  API 文档

## 项目概述

Lighthouse Backend 是一个基于 NestJS 框架的后端 API 服务，使用 Prisma ORM 连接 MySQL 数据库。该项目实现了用户认证、角色管理、书籍管理、文章管理和患者管理等功能。患者管理模块包括患者基本信息管理、就诊/随访记录管理和患者疾病记录管理，适用于医疗机构和临床研究场景。

## 模块结构

项目包含以下主要模块：

1. **AppModule**: 应用程序的根模块
2. **AuthModule**: 处理认证和授权
3. **UsersModule**: 用户管理
4. **BooksModule**: 书籍管理
5. **PostsModule**: 文章管理
6. **PatientsModule**: 患者管理
7. **VisitsModule**: 就诊/随访记录管理
8. **PatientConditionsModule**: 患者疾病记录管理
9. **PrismaModule**: 数据库连接
10. **ConfigModule**: 配置管理

## API 端点

### 认证 API

| 方法 | 路径 | 描述 | 认证 |
|------|------|------|------|
| POST | /api/auth/login | 用户登录 | 否 |

### 用户 API

| 方法 | 路径 | 描述 | 认证 | 权限 |
|------|------|------|------|------|
| POST | /api/users | 创建用户 | 否 | 所有人 |
| GET | /api/users | 获取所有用户 | JWT | ADMIN |
| GET | /api/users/:id | 获取单个用户 | JWT | 所有认证用户 |
| PATCH | /api/users/:id | 更新用户 | JWT | 所有认证用户 |
| DELETE | /api/users/:id | 删除用户 | JWT | ADMIN |

#### 用户 API 详细说明

##### 创建用户 (Create User)

- **URL**: `/api/users`
- **方法**: `POST`
- **认证**: 不需要
- **描述**: 创建一个新的用户账户
- **请求体**:
  ```json
  {
    "username": "johndoe",
    "email": "<EMAIL>",
    "password": "password123",
    "fullName": "John Doe",
    "role": "USER",
    "phone": "+8613800138000",
    "address": "北京市海淀区中关村大街1号",
    "city": "北京",
    "country": "中国",
    "postalCode": "100080",
    "birthDate": "1990-01-01",
    "avatar": "https://example.com/avatar.jpg",
    "bio": "热爱阅读和编程的软件工程师"
  }
  ```
- **响应**:
  - `201 Created`: 用户创建成功
  - `400 Bad Request`: 请求数据无效
  - `409 Conflict`: 用户名或邮箱已存在

##### 获取所有用户 (Get All Users)

- **URL**: `/api/users`
- **方法**: `GET`
- **认证**: JWT
- **权限**: ADMIN
- **描述**: 获取系统中的所有用户（仅管理员可用）
- **响应**:
  - `200 OK`: 返回所有用户列表
  - `401 Unauthorized`: 未授权
  - `403 Forbidden`: 权限不足

##### 获取单个用户 (Get User)

- **URL**: `/api/users/:id`
- **方法**: `GET`
- **认证**: JWT
- **描述**: 根据ID获取特定用户信息
- **参数**:
  - `id`: 用户ID
- **响应**:
  - `200 OK`: 返回用户信息
  - `401 Unauthorized`: 未授权
  - `404 Not Found`: 用户不存在

##### 更新用户 (Update User)

- **URL**: `/api/users/:id`
- **方法**: `PATCH`
- **认证**: JWT
- **描述**: 更新特定用户的信息
- **参数**:
  - `id`: 用户ID
- **请求体**: 包含要更新的字段（所有字段都是可选的）
  ```json
  {
    "username": "johndoe_updated",
    "email": "<EMAIL>",
    "password": "newpassword123",
    "fullName": "John Doe Updated",
    "phone": "+8613800138001",
    "address": "上海市浦东新区张江高科技园区",
    "city": "上海",
    "country": "中国",
    "postalCode": "201203",
    "birthDate": "1990-01-02",
    "avatar": "https://example.com/avatar_updated.jpg",
    "bio": "更新后的个人简介"
  }
  ```
- **响应**:
  - `200 OK`: 用户信息更新成功
  - `400 Bad Request`: 请求数据无效
  - `401 Unauthorized`: 未授权
  - `404 Not Found`: 用户不存在

##### 删除用户 (Delete User)

- **URL**: `/api/users/:id`
- **方法**: `DELETE`
- **认证**: JWT
- **权限**: ADMIN
- **描述**: 删除特定用户（仅管理员可用）
- **参数**:
  - `id`: 用户ID
- **响应**:
  - `200 OK`: 用户删除成功
  - `401 Unauthorized`: 未授权
  - `403 Forbidden`: 权限不足
  - `404 Not Found`: 用户不存在

### 书籍 API

| 方法 | 路径 | 描述 | 认证 |
|------|------|------|------|
| POST | /api/books | 创建书籍 | JWT |
| GET | /api/books | 获取所有书籍 | 否 |
| GET | /api/books/:id | 获取单本书籍 | 否 |
| PUT | /api/books/:id | 更新书籍 | JWT |
| DELETE | /api/books/:id | 删除书籍 | JWT |

### 文章 API

| 方法 | 路径 | 描述 | 认证 |
|------|------|------|------|
| POST | /api/posts | 创建文章 | JWT |
| GET | /api/posts | 获取所有文章 | 否 |
| GET | /api/posts/:id | 获取单篇文章 | 否 |
| PATCH | /api/posts/:id | 更新文章 | JWT |
| DELETE | /api/posts/:id | 删除文章 | JWT |

### 患者 API

| 方法 | 路径 | 描述 | 认证 | 权限 |
|------|------|------|------|------|
| POST | /api/patients | 创建患者记录 | JWT | 所有认证用户 |
| GET | /api/patients | 获取患者列表 | JWT | 所有认证用户 |
| GET | /api/patients/:id | 获取单个患者详情 | JWT | 所有认证用户 |
| PATCH | /api/patients/:id | 更新患者信息 | JWT | 所有认证用户 |
| DELETE | /api/patients/:id | 删除患者记录 | JWT | ADMIN |

#### 患者 API 详细说明

##### 创建患者记录 (Create Patient)

- **URL**: `/api/patients`
- **方法**: `POST`
- **认证**: JWT
- **描述**: 创建一个新的患者记录
- **请求体**:
  ```json
  {
    "assignedUserId": 1,
    "name": "张三",
    "gender": "男",
    "dateOfBirth": "1980-01-01",
    "nationalIdNumber": "110101198001010011",
    "ethnicity": "汉族",
    "occupation": "工程师",
    "educationLevel": "本科",
    "maritalStatus": "已婚",
    "registrationDate": "2023-01-01",
    "notesGeneral": "患者一般情况良好",
    "primaryPhoneNumber": "+8613800138000",
    "secondaryPhoneNumber": "+8613900139000",
    "addressLine1": "北京市海淀区中关村大街1号",
    "addressLine2": "科技园3号楼5单元501室",
    "city": "北京",
    "province": "北京",
    "postalCode": "100080",
    "emergencyContactName": "李四",
    "emergencyContactPhone": "+8613700137000",
    "emergencyContactRelationship": "配偶",
    "primaryDiagnosis": "高血压",
    "diagnosisCodeIcd": "I10",
    "diagnosisDate": "2022-12-01",
    "diseaseStage": "早期",
    "comorbidities": "糖尿病",
    "treatmentProtocolId": "HT-001",
    "treatmentProtocolDetails": "标准降压治疗方案",
    "notesDisease": "血压控制稳定"
  }
  ```
- **响应**:
  - `201 Created`: 患者记录创建成功
  - `400 Bad Request`: 请求数据无效
  - `401 Unauthorized`: 未授权

##### 获取患者列表 (Get All Patients)

- **URL**: `/api/patients`
- **方法**: `GET`
- **认证**: JWT
- **描述**: 获取患者列表，支持分页和筛选
- **查询参数**:
  - `name`: 按姓名搜索
  - `primaryDiagnosis`: 按主要诊断搜索
  - `nationalIdNumber`: 按身份证号搜索
  - `phoneNumber`: 按电话号码搜索
  - `assignedUserId`: 按管理人员ID搜索
  - `registrationDateStart`: 按登记日期范围搜索（开始日期）
  - `registrationDateEnd`: 按登记日期范围搜索（结束日期）
  - `skip`: 分页 - 跳过记录数（默认: 0）
  - `take`: 分页 - 获取记录数（默认: 10）
- **响应**:
  - `200 OK`: 返回患者列表和总数
    ```json
    {
      "data": [
        {
          "id": 1,
          "name": "张三",
          "primaryDiagnosis": "高血压",
          "primaryPhoneNumber": "+8613800138000",
          "assignedUser": {
            "id": 1,
            "username": "admin",
            "fullName": "Admin User"
          }
        }
      ],
      "total": 1
    }
    ```
  - `401 Unauthorized`: 未授权

##### 获取单个患者详情 (Get Patient)

- **URL**: `/api/patients/:id`
- **方法**: `GET`
- **认证**: JWT
- **描述**: 获取单个患者的详细信息
- **参数**:
  - `id`: 患者ID
- **响应**:
  - `200 OK`: 返回患者详情
  - `401 Unauthorized`: 未授权
  - `404 Not Found`: 患者不存在

##### 更新患者信息 (Update Patient)

- **URL**: `/api/patients/:id`
- **方法**: `PATCH`
- **认证**: JWT
- **描述**: 更新患者信息
- **参数**:
  - `id`: 患者ID
- **请求体**: 包含要更新的字段（所有字段都是可选的）
- **响应**:
  - `200 OK`: 患者信息更新成功
  - `400 Bad Request`: 请求数据无效
  - `401 Unauthorized`: 未授权
  - `404 Not Found`: 患者不存在

##### 删除患者记录 (Delete Patient)

- **URL**: `/api/patients/:id`
- **方法**: `DELETE`
- **认证**: JWT
- **权限**: ADMIN
- **描述**: 删除患者记录（仅管理员可用）
- **参数**:
  - `id`: 患者ID
- **响应**:
  - `200 OK`: 患者记录删除成功
  - `401 Unauthorized`: 未授权
  - `403 Forbidden`: 权限不足
  - `404 Not Found`: 患者不存在

### 就诊/随访记录 API

| 方法 | 路径 | 描述 | 认证 | 权限 |
|------|------|------|------|------|
| POST | /api/visits | 创建就诊/随访记录 | JWT | 所有认证用户 |
| GET | /api/visits | 获取就诊/随访记录列表 | JWT | 所有认证用户 |
| GET | /api/visits/:id | 获取单个就诊/随访记录详情 | JWT | 所有认证用户 |
| PATCH | /api/visits/:id | 更新就诊/随访记录信息 | JWT | 所有认证用户 |
| DELETE | /api/visits/:id | 删除就诊/随访记录 | JWT | ADMIN |

#### 就诊/随访记录 API 详细说明

##### 创建就诊/随访记录 (Create Visit)

- **URL**: `/api/visits`
- **方法**: `POST`
- **认证**: JWT
- **描述**: 创建一个新的就诊/随访记录
- **请求体**:
  ```json
  {
    "patientId": 1,
    "visitDate": "2023-06-15T09:30:00Z",
    "visitType": "常规随访",
    "visitLocation": "门诊部",
    "systolicBp": 135,
    "diastolicBp": 85,
    "heartRate": 72,
    "bodyWeightKg": 70.5,
    "bodyHeightCm": 175.0,
    "currentSymptoms": "偶尔头痛，无其他不适",
    "medicationAdherence": "良好",
    "labResultsSummary": "血糖正常，肝肾功能正常",
    "imagingResultsSummary": "心脏超声未见异常",
    "nextVisitDate": "2023-09-15",
    "notes": "患者血压控制良好，继续当前治疗方案",
    "recordedByUserId": 1
  }
  ```
- **响应**:
  - `201 Created`: 就诊/随访记录创建成功
  - `400 Bad Request`: 请求数据无效
  - `401 Unauthorized`: 未授权

##### 获取就诊/随访记录列表 (Get All Visits)

- **URL**: `/api/visits`
- **方法**: `GET`
- **认证**: JWT
- **描述**: 获取就诊/随访记录列表，支持分页和筛选
- **查询参数**:
  - `patientId`: 按患者ID搜索
  - `recordedByUserId`: 按记录人员ID搜索
  - `visitType`: 按就诊/随访类型搜索
  - `visitDateStart`: 按就诊/随访日期范围搜索（开始日期）
  - `visitDateEnd`: 按就诊/随访日期范围搜索（结束日期）
  - `skip`: 分页 - 跳过记录数（默认: 0）
  - `take`: 分页 - 获取记录数（默认: 10）
- **响应**:
  - `200 OK`: 返回就诊/随访记录列表和总数
    ```json
    {
      "data": [
        {
          "id": 1,
          "visitDate": "2023-06-15T09:30:00Z",
          "visitType": "常规随访",
          "patient": {
            "id": 1,
            "name": "张三",
            "primaryDiagnosis": "高血压"
          },
          "recordedByUser": {
            "id": 1,
            "username": "admin",
            "fullName": "Admin User"
          }
        }
      ],
      "total": 1
    }
    ```
  - `401 Unauthorized`: 未授权

##### 获取单个就诊/随访记录详情 (Get Visit)

- **URL**: `/api/visits/:id`
- **方法**: `GET`
- **认证**: JWT
- **描述**: 获取单个就诊/随访记录的详细信息
- **参数**:
  - `id`: 就诊/随访记录ID
- **响应**:
  - `200 OK`: 返回就诊/随访记录详情
  - `401 Unauthorized`: 未授权
  - `404 Not Found`: 就诊/随访记录不存在

##### 更新就诊/随访记录信息 (Update Visit)

- **URL**: `/api/visits/:id`
- **方法**: `PATCH`
- **认证**: JWT
- **描述**: 更新就诊/随访记录信息
- **参数**:
  - `id`: 就诊/随访记录ID
- **请求体**: 包含要更新的字段（所有字段都是可选的）
- **响应**:
  - `200 OK`: 就诊/随访记录信息更新成功
  - `400 Bad Request`: 请求数据无效
  - `401 Unauthorized`: 未授权
  - `404 Not Found`: 就诊/随访记录不存在

##### 删除就诊/随访记录 (Delete Visit)

- **URL**: `/api/visits/:id`
- **方法**: `DELETE`
- **认证**: JWT
- **权限**: ADMIN
- **描述**: 删除就诊/随访记录（仅管理员可用）
- **参数**:
  - `id`: 就诊/随访记录ID
- **响应**:
  - `200 OK`: 就诊/随访记录删除成功
  - `401 Unauthorized`: 未授权
  - `403 Forbidden`: 权限不足
  - `404 Not Found`: 就诊/随访记录不存在

### 患者疾病记录 API

| 方法 | 路径 | 描述 | 认证 | 权限 |
|------|------|------|------|------|
| POST | /api/patient-conditions | 创建患者疾病记录 | JWT | 所有认证用户 |
| GET | /api/patient-conditions | 获取患者疾病记录列表 | JWT | 所有认证用户 |
| GET | /api/patient-conditions/:id | 获取单个患者疾病记录详情 | JWT | 所有认证用户 |
| PATCH | /api/patient-conditions/:id | 更新患者疾病记录信息 | JWT | 所有认证用户 |
| DELETE | /api/patient-conditions/:id | 删除患者疾病记录 | JWT | ADMIN |

#### 患者疾病记录 API 详细说明

##### 创建患者疾病记录 (Create Patient Condition)

- **URL**: `/api/patient-conditions`
- **方法**: `POST`
- **认证**: JWT
- **描述**: 创建一个新的患者疾病记录
- **请求体**:
  ```json
  {
    "patientId": 1,
    "conditionName": "糖尿病",
    "conditionCodeIcd": "E11",
    "diagnosisDate": "2022-10-15",
    "isPrimary": false,
    "status": "活动期",
    "onsetDate": "2022-09-01",
    "severity": "中度",
    "treatmentHistory": "口服降糖药物治疗",
    "notes": "血糖控制一般，需要定期监测"
  }
  ```
- **响应**:
  - `201 Created`: 患者疾病记录创建成功
  - `400 Bad Request`: 请求数据无效
  - `401 Unauthorized`: 未授权

##### 获取患者疾病记录列表 (Get All Patient Conditions)

- **URL**: `/api/patient-conditions`
- **方法**: `GET`
- **认证**: JWT
- **描述**: 获取患者疾病记录列表，支持分页和筛选
- **查询参数**:
  - `patientId`: 按患者ID搜索
  - `conditionName`: 按疾病/状况名称搜索
  - `conditionCodeIcd`: 按疾病诊断编码搜索
  - `isPrimary`: 按是否为主要诊断搜索
  - `status`: 按疾病状态搜索
  - `skip`: 分页 - 跳过记录数（默认: 0）
  - `take`: 分页 - 获取记录数（默认: 10）
- **响应**:
  - `200 OK`: 返回患者疾病记录列表和总数
    ```json
    {
      "data": [
        {
          "id": 1,
          "conditionName": "糖尿病",
          "conditionCodeIcd": "E11",
          "isPrimary": false,
          "status": "活动期",
          "patient": {
            "id": 1,
            "name": "张三"
          }
        }
      ],
      "total": 1
    }
    ```
  - `401 Unauthorized`: 未授权

##### 获取单个患者疾病记录详情 (Get Patient Condition)

- **URL**: `/api/patient-conditions/:id`
- **方法**: `GET`
- **认证**: JWT
- **描述**: 获取单个患者疾病记录的详细信息
- **参数**:
  - `id`: 患者疾病记录ID
- **响应**:
  - `200 OK`: 返回患者疾病记录详情
  - `401 Unauthorized`: 未授权
  - `404 Not Found`: 患者疾病记录不存在

##### 更新患者疾病记录信息 (Update Patient Condition)

- **URL**: `/api/patient-conditions/:id`
- **方法**: `PATCH`
- **认证**: JWT
- **描述**: 更新患者疾病记录信息
- **参数**:
  - `id`: 患者疾病记录ID
- **请求体**: 包含要更新的字段（所有字段都是可选的）
- **响应**:
  - `200 OK`: 患者疾病记录信息更新成功
  - `400 Bad Request`: 请求数据无效
  - `401 Unauthorized`: 未授权
  - `404 Not Found`: 患者疾病记录不存在

##### 删除患者疾病记录 (Delete Patient Condition)

- **URL**: `/api/patient-conditions/:id`
- **方法**: `DELETE`
- **认证**: JWT
- **权限**: ADMIN
- **描述**: 删除患者疾病记录（仅管理员可用）
- **参数**:
  - `id`: 患者疾病记录ID
- **响应**:
  - `200 OK`: 患者疾病记录删除成功
  - `401 Unauthorized`: 未授权
  - `403 Forbidden`: 权限不足
  - `404 Not Found`: 患者疾病记录不存在

## 数据模型

### 用户 (User)

```typescript
model User {
  id         Int       @id @default(autoincrement())
  email      String    @unique
  username   String    @unique
  password   String
  fullName   String?
  role       String
  phone      String?   @db.VarChar(20)
  address    String?   @db.VarChar(255)
  city       String?   @db.VarChar(100)
  country    String?   @db.VarChar(100)
  postalCode String?   @db.VarChar(20)
  birthDate  DateTime?
  avatar     String?   @db.VarChar(255)
  bio        String?   @db.Text
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @default(now()) @updatedAt
  posts      Post[]
}
```

#### 用户字段说明

| 字段名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| id | Int | 用户ID，自动递增 | 自动生成 |
| email | String | 电子邮箱，唯一 | 是 |
| username | String | 用户名，唯一 | 是 |
| password | String | 密码（存储为哈希值） | 是 |
| fullName | String | 用户全名 | 否 |
| role | String | 用户角色（USER 或 ADMIN） | 是 |
| phone | String | 电话号码 | 否 |
| address | String | 地址 | 否 |
| city | String | 城市 | 否 |
| country | String | 国家 | 否 |
| postalCode | String | 邮政编码 | 否 |
| birthDate | DateTime | 出生日期 | 否 |
| avatar | String | 头像URL | 否 |
| bio | String | 个人简介 | 否 |
| createdAt | DateTime | 创建时间 | 自动生成 |
| updatedAt | DateTime | 更新时间 | 自动更新 |

### 文章 (Post)

```typescript
model Post {
  id        Int      @id @default(autoincrement())
  title     String
  content   String
  published Boolean  @default(false)
  author    User     @relation(fields: [authorId], references: [id])
  authorId  Int
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}
```

### 书籍 (Book)

```typescript
model Book {
  book_id          Int      @id @default(autoincrement())
  title            String
  publication_year Int
  author_id        Int
  category_id      Int
  isbn             String   @unique
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt
}
```

### 患者 (Patient)

```typescript
model Patient {
  id                          Int                @id @default(autoincrement()) @map("patient_id")
  assignedUserId              Int                @map("assigned_user_id")
  name                        String             @db.VarChar(255)
  gender                      Gender?
  dateOfBirth                 DateTime?          @map("date_of_birth") @db.Date
  nationalIdNumber            String?            @unique @map("national_id_number") @db.VarChar(18)
  ethnicity                   String?            @db.VarChar(50)
  occupation                  String?            @db.VarChar(255)
  educationLevel              String?            @map("education_level") @db.VarChar(100)
  maritalStatus               MaritalStatus?     @map("marital_status")
  registrationDate            DateTime           @map("registration_date") @db.Date
  notesGeneral                String?            @map("notes_general") @db.Text
  primaryPhoneNumber          String             @map("primary_phone_number") @db.VarChar(20)
  secondaryPhoneNumber        String?            @map("secondary_phone_number") @db.VarChar(20)
  addressLine1                String?            @map("address_line1") @db.VarChar(255)
  addressLine2                String?            @map("address_line2") @db.VarChar(255)
  city                        String?            @db.VarChar(100)
  province                    String?            @db.VarChar(100)
  postalCode                  String?            @map("postal_code") @db.VarChar(10)
  emergencyContactName        String?            @map("emergency_contact_name") @db.VarChar(255)
  emergencyContactPhone       String?            @map("emergency_contact_phone") @db.VarChar(20)
  emergencyContactRelationship String?           @map("emergency_contact_relationship") @db.VarChar(100)
  primaryDiagnosis            String             @map("primary_diagnosis") @db.VarChar(255)
  diagnosisCodeIcd            String?            @map("diagnosis_code_icd") @db.VarChar(50)
  diagnosisDate               DateTime?          @map("diagnosis_date") @db.Date
  diseaseStage                String?            @map("disease_stage") @db.VarChar(100)
  comorbidities               String?            @db.Text
  treatmentProtocolId         String?            @map("treatment_protocol_id") @db.VarChar(100)
  treatmentProtocolDetails    String?            @map("treatment_protocol_details") @db.Text
  notesDisease                String?            @map("notes_disease") @db.Text
  createdAt                   DateTime           @default(now()) @map("created_at")
  updatedAt                   DateTime           @updatedAt @map("updated_at")

  // Relations
  assignedUser                User               @relation("AssignedPatients", fields: [assignedUserId], references: [id])
  visits                      Visit[]
  conditions                  PatientCondition[]
}
```

### 就诊/随访记录 (Visit)

```typescript
model Visit {
  id                    Int       @id @default(autoincrement()) @map("visit_id")
  patientId             Int       @map("patient_id")
  visitDate             DateTime  @map("visit_date")
  visitType             String?   @map("visit_type") @db.VarChar(100)
  visitLocation         String?   @map("visit_location") @db.VarChar(255)
  systolicBp            Int?      @map("systolic_bp")
  diastolicBp           Int?      @map("diastolic_bp")
  heartRate             Int?      @map("heart_rate")
  bodyWeightKg          Decimal?  @map("body_weight_kg") @db.Decimal(5, 2)
  bodyHeightCm          Decimal?  @map("body_height_cm") @db.Decimal(5, 1)
  currentSymptoms       String?   @map("current_symptoms") @db.Text
  medicationAdherence   String?   @map("medication_adherence") @db.VarChar(100)
  labResultsSummary     String?   @map("lab_results_summary") @db.Text
  imagingResultsSummary String?   @map("imaging_results_summary") @db.Text
  nextVisitDate         DateTime? @map("next_visit_date") @db.Date
  notes                 String?   @db.Text
  recordedByUserId      Int       @map("recorded_by_user_id")
  createdAt             DateTime  @default(now()) @map("created_at")
  updatedAt             DateTime  @updatedAt @map("updated_at")

  // Relations
  patient               Patient   @relation(fields: [patientId], references: [id])
  recordedByUser        User      @relation("RecordedVisits", fields: [recordedByUserId], references: [id])
}
```

### 患者疾病记录 (PatientCondition)

```typescript
model PatientCondition {
  id                Int       @id @default(autoincrement()) @map("condition_id")
  patientId         Int       @map("patient_id")
  conditionName     String    @map("condition_name") @db.VarChar(255)
  conditionCodeIcd  String?   @map("condition_code_icd") @db.VarChar(50)
  diagnosisDate     DateTime? @map("diagnosis_date") @db.Date
  isPrimary         Boolean   @default(false) @map("is_primary")
  status            String?   @db.VarChar(100)
  onsetDate         DateTime? @map("onset_date") @db.Date
  severity          String?   @db.VarChar(100)
  treatmentHistory  String?   @map("treatment_history") @db.Text
  notes             String?   @db.Text
  createdAt         DateTime  @default(now()) @map("created_at")
  updatedAt         DateTime  @updatedAt @map("updated_at")

  // Relations
  patient           Patient   @relation(fields: [patientId], references: [id])
}
```

## 认证与授权

项目使用 JWT (JSON Web Token) 进行认证，并实现了基于角色的访问控制 (RBAC)。

### 角色

```typescript
enum Role {
  USER = 'USER',
  ADMIN = 'ADMIN'
}
```

### 认证 API 详细说明

#### 用户登录 (Login)

- **URL**: `/api/auth/login`
- **方法**: `POST`
- **认证**: 不需要
- **描述**: 用户登录并获取JWT令牌
- **请求体**:
  ```json
  {
    "username": "johndoe",
    "password": "password123"
  }
  ```
- **响应**:
  - `200 OK`: 登录成功，返回JWT令牌和用户信息
    ```json
    {
      "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "user": {
        "id": 1,
        "username": "johndoe",
        "email": "<EMAIL>",
        "role": "USER"
      }
    }
    ```
  - `401 Unauthorized`: 用户名或密码错误

### JWT 认证流程

1. 用户通过 `/api/auth/login` 端点提交用户名和密码
2. 服务器验证凭据并返回 JWT 令牌
3. 客户端在后续请求中使用 Bearer 认证头包含该令牌
4. 受保护的端点使用 `JwtAuthGuard` 验证令牌

#### JWT 令牌格式

JWT令牌包含以下信息：

- **sub**: 用户ID
- **username**: 用户名
- **role**: 用户角色
- **iat**: 令牌签发时间
- **exp**: 令牌过期时间

#### 在请求中使用JWT令牌

在需要认证的API请求中，需要在HTTP头部添加Authorization字段：

```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 角色授权

使用 `@Roles()` 装饰器和 `RolesGuard` 实现基于角色的访问控制：

```typescript
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(Role.ADMIN)
@Get()
findAll() {
  // 只有 ADMIN 角色可以访问
}
```

#### 角色权限说明

| 角色 | 描述 | 权限 |
|------|------|------|
| USER | 普通用户 | 可以访问自己的用户信息，查看公开内容 |
| ADMIN | 管理员 | 可以管理所有用户、文章和书籍，拥有完全访问权限 |

## 安全特性

### 1. JWT 认证

使用 JSON Web Token (JWT) 保护敏感端点，确保只有经过认证的用户才能访问受保护的资源。

- JWT 密钥长度至少为32个字符，确保足够的安全性
- 令牌有效期默认为1天，可通过环境变量配置
- 使用 Bearer 认证方案在请求头中传递令牌

### 2. 角色授权

基于用户角色的访问控制 (RBAC)，限制不同角色用户的访问权限。

- 使用 `@Roles()` 装饰器和 `RolesGuard` 实现
- 支持 USER 和 ADMIN 两种角色
- 敏感操作（如删除用户、获取所有用户列表）仅限 ADMIN 角色访问

### 3. 密码安全

- **密码哈希**: 使用 bcrypt 算法安全存储密码，不存储明文密码
- **密码强度**: 要求密码至少6个字符
- **密码重置**: 支持安全的密码重置流程

### 4. 数据验证

- 使用 class-validator 验证所有请求数据
- 强制验证电子邮箱格式
- 验证电话号码格式（使用 @IsPhoneNumber 装饰器）
- 白名单验证，过滤掉未定义的属性

### 5. HTTP 安全头

使用 Helmet 中间件添加安全相关的 HTTP 头，防止常见的 Web 攻击：

- Content-Security-Policy
- X-XSS-Protection
- X-Content-Type-Options
- X-Frame-Options
- Strict-Transport-Security

### 6. CORS 保护

配置跨域资源共享 (CORS) 策略，限制哪些域可以访问 API：

- 在生产环境中，仅允许特定来源访问
- 可通过环境变量 ALLOWED_ORIGINS 配置允许的来源
- 限制允许的 HTTP 方法和头部

### 7. 速率限制

实现 API 速率限制，防止暴力破解和滥用：

- 使用 ThrottlerModule 实现
- 默认限制：每分钟10个请求
- 可通过环境变量 THROTTLE_TTL 和 THROTTLE_LIMIT 配置
- 全局应用于所有端点

## 环境变量

项目使用环境变量进行配置，以下是所有支持的环境变量及其说明：

```
# 数据库连接
DATABASE_URL="mysql://username:password@your-tencent-mysql-host:3306/lighthouse_db"

# JWT 认证
JWT_SECRET="VRbG4z3yT2j-9F!eVHpVKb_FuXMNvRoo"  # 至少32个字符
JWT_EXPIRES_IN="1d"  # 令牌有效期，例如：60s, 2m, 10h, 7d

# 服务器
PORT=3001  # API服务器端口
NODE_ENV="development"  # 环境：development, production, test

# CORS
ALLOWED_ORIGINS="*"  # 允许的来源，多个来源用逗号分隔，例如：https://example.com,https://api.example.com

# 速率限制
THROTTLE_TTL=60  # 速率限制窗口时间（秒）
THROTTLE_LIMIT=10  # 在TTL时间窗口内允许的最大请求数
```

### 环境变量说明

#### 数据库连接

- **DATABASE_URL**: 数据库连接字符串，格式为 `mysql://username:password@host:port/database`
  - 在腾讯云轻量服务器上部署时，请使用正确的数据库凭据
  - 注意：在腾讯云轻量数据库上，应使用 `npx prisma db push` 而不是 `prisma migrate dev`，因为数据库用户可能没有创建影子数据库的权限

#### JWT 认证

- **JWT_SECRET**: JWT签名密钥，必须至少32个字符长
  - 生产环境中应使用强随机生成的密钥
  - 不要在代码库中存储实际的密钥值
- **JWT_EXPIRES_IN**: JWT令牌的有效期
  - 默认为"1d"（1天）
  - 可以使用不同的时间单位：s（秒）, m（分钟）, h（小时）, d（天）

#### 服务器配置

- **PORT**: API服务器监听的端口
  - 默认为3001
- **NODE_ENV**: 运行环境
  - development: 开发环境，提供详细错误信息
  - production: 生产环境，隐藏敏感错误信息
  - test: 测试环境

#### CORS 配置

- **ALLOWED_ORIGINS**: 允许访问API的来源
  - "*": 允许所有来源（不推荐用于生产环境）
  - 多个来源用逗号分隔，例如：`https://example.com,https://api.example.com`

#### 速率限制

- **THROTTLE_TTL**: 速率限制的时间窗口（秒）
  - 默认为60秒
- **THROTTLE_LIMIT**: 在TTL时间窗口内允许的最大请求数
  - 默认为10个请求

## 部署信息

项目可以部署在腾讯云轻量服务器上，支持 Docker 容器化部署。详细的部署指南可以在 `deployment` 目录中找到，包括：

1. **部署指南**：详细的部署步骤和配置说明
2. **腾讯云快速部署**：针对腾讯云轻量服务器的简明部署指南
3. **Docker 部署指南**：使用 Docker 容器化部署的详细说明
4. **安全指南**：部署安全加固和最佳实践
5. **问题排查**：常见部署问题和解决方案

部署文档入口：[部署文档](deployment/README.md)

### Docker 部署

项目支持使用 Docker 进行容器化部署，以下是基本的 Docker 部署步骤：

1. **构建 Docker 镜像**:
   ```bash
   docker build -t lighthouse-backend .
   ```

2. **运行容器**:
   ```bash
   docker run -d -p 3001:3001 \
     --name lighthouse-api \
     -e DATABASE_URL="mysql://username:password@host:3306/lighthouse_db" \
     -e JWT_SECRET="your-jwt-secret-key-here-make-it-at-least-32-chars-long" \
     -e NODE_ENV="production" \
     lighthouse-backend
   ```

3. **使用 Docker Compose**:

   创建 `docker-compose.yml` 文件：
   ```yaml
   version: '3'
   services:
     api:
       build: .
       ports:
         - "3001:3001"
       environment:
         - DATABASE_URL=mysql://username:password@db:3306/lighthouse_db
         - JWT_SECRET=your-jwt-secret-key-here-make-it-at-least-32-chars-long
         - NODE_ENV=production
       depends_on:
         - db
     db:
       image: mysql:8.0
       environment:
         - MYSQL_ROOT_PASSWORD=root_password
         - MYSQL_DATABASE=lighthouse_db
         - MYSQL_USER=username
         - MYSQL_PASSWORD=password
       volumes:
         - mysql_data:/var/lib/mysql
   volumes:
     mysql_data:
   ```

   启动服务：
   ```bash
   docker-compose up -d
   ```

### 腾讯云轻量服务器部署

在腾讯云轻量服务器上部署时，需要注意以下几点：

1. **数据库连接**：使用腾讯云轻量数据库的连接信息
2. **防火墙配置**：确保开放API服务器使用的端口（默认3001）
3. **使用 Nginx 反向代理**：建议使用 Nginx 作为反向代理，并配置 HTTPS

详细的腾讯云部署指南请参考 [腾讯云部署指南](deployment/guides/tencent-cloud-guide.md)。

## 相关文档

- [功能使用手册 - Lighthouse API 集成](../FUNCTIONAL_MANUAL.md#9-lighthouse-api-集成)
- [系统配置管理](../FUNCTIONAL_MANUAL.md#7-系统配置管理)

## API 测试

### Postman 测试

可以使用 Postman 集合 `lighthouse_api.postman_collection.json` 测试 API 端点。该集合包含了所有 API 端点的示例请求，并支持 JWT 认证。

1. **导入 Postman 集合**：
   - 打开 Postman
   - 点击 "Import" 按钮
   - 选择 `lighthouse_api.postman_collection.json` 文件

2. **设置环境变量**：
   - 创建一个新的环境
   - 添加 `baseUrl` 变量（例如：`http://localhost:3001/api`）
   - 添加 `token` 变量（登录后会自动设置）

3. **执行测试**：
   - 首先执行 "Login" 请求获取令牌
   - 然后可以测试其他需要认证的端点

### Swagger UI 测试

项目集成了 Swagger UI，可以通过浏览器直接测试 API：

1. 启动应用程序：
   ```bash
   npm run start:dev
   ```

2. 在浏览器中访问：
   ```
   http://localhost:3001/api/docs
   ```

3. 使用 Swagger UI 界面测试各个端点
