# CSV批量导入补贴信息 - 功能文档

## 📋 功能概述

CSV批量导入功能允许用户通过上传CSV文件，一次性导入多个补贴项目到研究项目中，大大提高数据录入的效率。

## 🎯 设计目标

- **提高效率**: 减少重复的手动录入工作
- **数据准确性**: 统一的模板格式，减少输入错误
- **用户友好**: 直观的导入流程和清晰的错误提示
- **数据验证**: 自动验证数据格式和字典映射

## 🔧 功能特点

### 1. 智能数据验证
- ✅ CSV格式检查
- ✅ 必需字段验证
- ✅ 数值类型验证
- ✅ 字典项自动映射
- ✅ 重复数据检测

### 2. 用户友好界面
- 📄 模板文件下载
- 👀 数据预览确认
- 📊 导入进度显示
- ❗ 详细错误提示
- ✅ 导入结果反馈

### 3. 数据集成
- 🔄 与现有补贴数据合并
- 🧮 自动计算总金额
- 📈 更新补贴方案总金额
- 💾 保存到项目数据

## 📁 CSV文件格式

### 必需列

| 列名 | 数据类型 | 描述 | 示例 |
|------|----------|------|------|
| 补贴类型 | 文本 | 必须匹配系统字典中的补贴类型 | 交通补贴 |
| 单位金额 | 数值 | 每单位的金额，支持小数 | 50.00 |
| 总单位数 | 整数 | 总的单位数量，必须为正整数 | 20 |
| 单位 | 文本 | 必须匹配系统字典中的单位 | 次 |

### 可选列

| 列名 | 数据类型 | 描述 |
|------|----------|------|
| 备注 | 文本 | 补贴项目的说明信息 |

### 示例CSV格式

```csv
补贴类型,单位金额,总单位数,单位,备注
交通补贴,50.00,20,次,每次往返交通费
餐饮补贴,30.00,15,餐,工作餐补贴
住宿补贴,200.00,5,晚,出差住宿费
材料费,100.00,10,份,研究材料费用
```

## 🚀 使用流程

### 步骤1: 准备CSV文件
1. 点击"下载CSV模板"按钮获取标准模板
2. 按照模板格式填写补贴数据
3. 保存为CSV格式文件

### 步骤2: 导入数据
1. 在项目详情页面的"补贴项"部分，点击"CSV批量导入"按钮
2. 选择准备好的CSV文件
3. 点击"解析文件"进行数据验证

### 步骤3: 预览确认
1. 查看解析后的数据预览
2. 检查数据是否正确
3. 如有错误，根据提示修改CSV文件后重新导入

### 步骤4: 确认导入
1. 确认数据无误后，点击"确认导入"
2. 系统显示导入进度
3. 导入完成后显示成功数量

### 步骤5: 验证结果
1. 查看补贴项列表，确认数据已正确导入
2. 如需要，可以继续编辑导入的数据
3. 保存项目以持久化数据

## ⚠️ 注意事项

### 数据要求
- **补贴类型**和**单位**必须在系统字典中存在
- **单位金额**必须是正数
- **总单位数**必须是正整数
- CSV文件需要包含标题行

### 常见问题

#### 1. 字典映射失败
**问题**: 提示"未找到补贴类型"或"未找到单位"
**解决**: 
- 检查CSV中的补贴类型是否与系统字典完全匹配
- 在系统管理中查看可用的补贴类型和单位
- 确保没有多余的空格或特殊字符

#### 2. 数值格式错误
**问题**: 提示"单位金额必须是正数"
**解决**:
- 确保金额使用数字格式，如 50.00 而不是 "50元"
- 不要包含货币符号或其他文字
- 使用英文小数点，不要使用逗号

#### 3. 文件格式问题
**问题**: 文件解析失败
**解决**:
- 确保文件保存为CSV格式
- 使用UTF-8编码保存文件
- 检查是否有缺失的列

## 🔧 技术实现

### 前端组件
- `SubsidyCsvImport.svelte`: 主要的CSV导入组件
- `ProjectSubsidies.svelte`: 集成导入功能的补贴管理组件

### 数据流程
1. **文件上传**: 使用浏览器File API读取CSV文件
2. **数据解析**: 前端JavaScript解析CSV内容
3. **字典映射**: 调用字典服务将文本映射为ID
4. **数据验证**: 检查数据格式和完整性
5. **批量导入**: 合并到现有补贴数据中

### 错误处理
- 文件格式验证
- 数据类型检查
- 字典映射验证
- 重复数据检测
- 详细错误消息

## 📊 数据库影响

### 涉及的表
- `subsidies`: 补贴项明细表
- `dictionary_items`: 字典条目表（补贴类型、单位）
- `subsidy_schemes`: 补贴方案表（金额自动更新）

### 数据完整性
- 自动分配临时ID，避免冲突
- 保持与现有数据的关联关系
- 自动更新相关的金额计算

## 🚀 扩展功能

### 未来可能的改进
1. **导出功能**: 将现有补贴数据导出为CSV
2. **批量编辑**: 支持CSV文件的批量修改功能
3. **模板管理**: 保存和管理多个导入模板
4. **数据校验**: 更复杂的业务规则验证
5. **导入历史**: 记录导入操作的历史记录

## 🛠️ 维护说明

### 定期检查
- 验证字典数据的完整性
- 检查导入功能的性能
- 更新模板文件（如有新的字典项）

### 故障排除
- 查看浏览器控制台的错误日志
- 检查字典服务的可用性
- 验证CSV文件的编码格式

---

> **最后更新**: 2024年12月
> **版本**: 1.0.0
> **作者**: Claude Code Assistant