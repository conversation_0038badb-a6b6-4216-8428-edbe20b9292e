# 数据库结构文档

> **注意**: 本文档提供数据库结构的详细信息。此信息也已包含在工作区规则中，本文档作为独立参考保留。

## 概述

本文档详细描述了项目使用的 SQLite 数据库结构，包括所有表的字段定义、数据类型、约束条件和关系。

**数据库位置**: `src-tauri/.sqlite/peckbyte.db` (相对于工作区根目录)

## 数据库概览

### 基本信息
- **数据库类型**: SQLite
- **表总数**: 17个主要业务表 + 1个系统表 (`sqlite_sequence`)
- **核心功能**: 临床研究项目管理系统
- **当前数据规模**: 46个项目，17个规则定义，10个字典类型

### 功能模块分类

#### 🏗️ **项目管理核心模块**
- `projects` - 项目主表
- `project_sponsors` - 项目申办方关系
- `research_drugs` - 研究药物信息
- `drug_groups` - 药物分组信息
- `project_personnel_roles` - 项目人员角色分配

#### 💰 **财务补贴模块**
- `subsidies` - 补贴项目明细
- `subsidy_schemes` - 补贴方案
- `scheme_included_subsidies` - 方案与补贴关联

#### 📋 **入排标准规则模块**
- `rule_definitions` - 规则定义
- `project_criteria` - 项目入排标准
- `rule_packages` - 规则包
- `rule_package_items` - 规则包条目

#### 📚 **字典数据模块**
- `dictionaries` - 字典主表
- `dictionary_items` - 字典条目
- `tags` - 标签
- `dictionary_tags` - 字典标签关联

##### 字典数据概览
- **招募公司** (6个条目) - 临床试验招募公司
- **招募状态** (3个条目) - 招募中、暂停招募、结束招募
- **用户角色** (5个条目) - 系统用户角色分类
- **申办方** (31个条目) - 临床试验申办方/制药公司
- **疾病** (12个条目) - 研究疾病分类（哮喘、慢阻肺病、肺癌等）
- **研究分期** (8个条目) - I期、II期、III期、IV期等临床试验期次
- **研究角色** (15个条目) - 项目团队成员角色
- **研究阶段** (4个条目) - 未启动、在研、已结束、暂停中等项目状态
- **补贴的单位** (11个条目) - 补贴计算单位
- **补贴类型** (14个条目) - 各种补贴项目类型

#### 👥 **人员管理模块**
- `staff` - 员工信息

## 表关系图谱

### 项目中心关系网
```
projects (核心表)
├── project_sponsors (申办方关系) → dictionary_items
├── research_drugs (研究药物)
├── drug_groups (药物分组)
├── project_personnel_roles (人员角色) → staff + dictionary_items
├── subsidies (补贴项目) → dictionary_items
├── subsidy_schemes (补贴方案)
└── project_criteria (入排标准) → rule_definitions
```

### 字典系统关系网
```
dictionaries (字典主表)
├── dictionary_items (字典条目) ← 被多个表引用
└── dictionary_tags (标签关系) → tags
```

### 规则系统关系网
```
rule_definitions (规则定义)
├── project_criteria (项目标准) → projects
└── rule_package_items → rule_packages
```

### 补贴关系网络
```
subsidy_schemes (补贴方案) → projects
└── scheme_included_subsidies (方案包含的补贴) → subsidies → projects
```

## 详细表结构

### Table: `dictionaries`

#### 用途说明
字典主表，用于定义各种分类数据的容器，如疾病类型、项目阶段、人员角色等。每个字典可以包含多个字典条目。

#### Columns

| Column Name | Data Type | NOT NULL | Primary Key | Default Value | Constraints |
|-------------|-----------|----------|-------------|---------------|-------------|
| id | INTEGER | NO | YES | AUTOINCREMENT | PRIMARY KEY |
| mongo_oid | TEXT | NO | NO | NULL | UNIQUE |
| name | TEXT | YES | NO | NULL | NOT NULL UNIQUE |
| description | TEXT | NO | NO | NULL | |
| type | TEXT | YES | NO | 'list' | NOT NULL DEFAULT 'list' |
| created_at | TEXT | YES | NO | NULL | NOT NULL |
| updated_at | TEXT | YES | NO | NULL | NOT NULL |
| version | INTEGER | YES | NO | 1 | NOT NULL DEFAULT 1 |

#### Foreign Keys (Relationships)
This table has no foreign key constraints.

#### Unique Constraints
- `mongo_oid` - 保证MongoDB对象ID的唯一性
- `name` - 保证字典名称的唯一性

#### 关联说明
- **被引用关系**: `dictionary_items` 表通过 `dictionary_id` 字段引用此表
- **标签关系**: 通过 `dictionary_tags` 表与 `tags` 表建立多对多关系
- **业务作用**: 作为整个字典系统的根节点，为系统中的下拉选项、分类数据提供组织结构

---

### Table: `sqlite_sequence`

#### 用途说明
SQLite 系统表，用于管理 AUTOINCREMENT 字段的序列值。

#### Columns

| Column Name | Data Type | NOT NULL | Primary Key | Default Value |
|-------------|-----------|----------|-------------|---------------|
| name |  | NO | NO | NULL |
| seq |  | NO | NO | NULL |

#### Foreign Keys (Relationships)
This table has no foreign key constraints.

#### 关联说明
- **系统表**: SQLite 自动维护，无需手动操作
- **业务作用**: 确保自增主键的唯一性和连续性

---

### Table: `dictionary_items`

#### 用途说明
字典条目表，存储具体的字典数据项，如具体的疾病名称、项目阶段名称等。是字典系统的核心数据表。

#### Columns

| Column Name | Data Type | NOT NULL | Primary Key | Default Value | Constraints |
|-------------|-----------|----------|-------------|---------------|-------------|
| item_id | INTEGER | NO | YES | AUTOINCREMENT | PRIMARY KEY |
| dictionary_id | INTEGER | YES | NO | NULL | NOT NULL |
| item_key | TEXT | YES | NO | NULL | NOT NULL |
| item_value | TEXT | YES | NO | NULL | NOT NULL |
| item_description | TEXT | NO | NO | NULL | |
| status | TEXT | YES | NO | 'active' | NOT NULL DEFAULT 'active' |

#### Foreign Keys (Relationships)

| From Column | To Table | To Column | On Update | On Delete | Description |
|-------------|----------|-----------|-----------|-----------|-------------|
| `dictionary_id` | `dictionaries` | `id` | NO ACTION | CASCADE | 字典条目必须属于某个字典分类 |

#### Unique Constraints
- `(dictionary_id, item_key)` - 同一字典内键值必须唯一

#### 关联说明
- **父表关系**: 通过 `dictionary_id` 属于某个字典分类
- **被广泛引用**: 被多个业务表引用，如 `projects`、`staff`、`subsidies` 等
- **业务作用**: 提供系统中所有下拉选项的具体数据，是数据标准化的基础

---

### Table: `tags`

#### 用途说明
标签表，用于为字典进行分类和标记，支持字典的多维度组织和检索。

#### Columns

| Column Name | Data Type | NOT NULL | Primary Key | Default Value | Constraints |
|-------------|-----------|----------|-------------|---------------|-------------|
| tag_id | INTEGER | NO | YES | AUTOINCREMENT | PRIMARY KEY |
| tag_name | TEXT | YES | NO | NULL | NOT NULL UNIQUE |

#### Foreign Keys (Relationships)
This table has no foreign key constraints.

#### Unique Constraints
- `tag_name` - 保证标签名称的唯一性

#### 关联说明
- **多对多关系**: 通过 `dictionary_tags` 表与 `dictionaries` 表建立多对多关系
- **业务作用**: 为字典提供灵活的分类和标记功能，便于管理和检索

---

### Table: `dictionary_tags`

#### 用途说明
字典标签关联表，建立字典与标签之间的多对多关系，一个字典可以有多个标签，一个标签可以应用于多个字典。

#### Columns

| Column Name | Data Type | NOT NULL | Primary Key | Default Value | Constraints |
|-------------|-----------|----------|-------------|---------------|-------------|
| dictionary_id | INTEGER | YES | YES | NULL | PRIMARY KEY, NOT NULL |
| tag_id | INTEGER | YES | YES | NULL | PRIMARY KEY, NOT NULL |

#### Foreign Keys (Relationships)

| From Column | To Table | To Column | On Update | On Delete | Description |
|-------------|----------|-----------|-----------|-----------|-------------|
| `dictionary_id` | `dictionaries` | `id` | NO ACTION | CASCADE | 关联到字典主表 |
| `tag_id` | `tags` | `tag_id` | NO ACTION | CASCADE | 关联到标签表 |

#### Composite Primary Key
- `(dictionary_id, tag_id)` - 复合主键，保证同一个字典-标签对只能存在一条记录

#### 关联说明
- **关联表**: 实现 `dictionaries` 和 `tags` 的多对多关系
- **复合主键**: 由 `dictionary_id` 和 `tag_id` 组成，确保关系的唯一性
- **业务作用**: 支持字典的多维度分类，提高数据组织的灵活性

---

### Table: `staff`

#### 用途说明
员工信息表，存储系统中所有人员的基本信息，包括研究人员、PI等。支持项目人员分配和角色管理。

#### Columns

| Column Name | Data Type | NOT NULL | Primary Key | Default Value | Constraints |
|-------------|-----------|----------|-------------|---------------|-------------|
| id | INTEGER | NO | YES | AUTOINCREMENT | PRIMARY KEY |
| name | TEXT | YES | NO | NULL | NOT NULL |
| gender | TEXT | NO | NO | NULL | CHECK(gender IN ('男', '女', '未知')) |
| birthday | TEXT | NO | NO | NULL | |
| phone | TEXT | NO | NO | NULL | |
| email | TEXT | NO | NO | NULL | UNIQUE |
| position_item_id | INTEGER | NO | NO | NULL | |
| isPI | INTEGER | YES | NO | 0 | NOT NULL DEFAULT 0 |
| organization | TEXT | NO | NO | NULL | |
| created_at | TEXT | NO | NO | CURRENT_TIMESTAMP | DEFAULT CURRENT_TIMESTAMP |
| updated_at | TEXT | NO | NO | NULL | |

#### Foreign Keys (Relationships)

| From Column | To Table | To Column | On Update | On Delete | Description |
|-------------|----------|-----------|-----------|-----------|-------------|
| `position_item_id` | `dictionary_items` | `item_id` | NO ACTION | NO ACTION | 关联员工职位信息 |

#### Check Constraints
- `gender` - 性别只能是 '男'、'女' 或 '未知'

#### Unique Constraints
- `email` - 保证邮箱地址的唯一性

#### 关联说明
- **职位关系**: 通过 `position_item_id` 引用字典条目中的职位信息
- **项目关联**: 通过 `project_personnel_roles` 表与项目建立多对多关系
- **PI标识**: `isPI` 字段标识是否为主要研究者（Principal Investigator）
- **业务作用**: 人员管理的核心表，支持项目团队组建和角色分配

---

### Table: `projects`

#### 用途说明
项目主表，存储临床研究项目的核心信息。是整个项目管理系统的中心表，其他相关表都通过项目ID与此表关联。

#### Columns

| Column Name | Data Type | NOT NULL | Primary Key | Default Value | Constraints |
|-------------|-----------|----------|-------------|---------------|-------------|
| project_id | TEXT | NO | YES | NULL | PRIMARY KEY |
| project_name | TEXT | YES | NO | NULL | NOT NULL |
| project_short_name | TEXT | YES | NO | NULL | NOT NULL |
| project_path | TEXT | NO | NO | NULL | |
| disease_item_id | INTEGER | NO | NO | NULL | |
| project_stage_item_id | INTEGER | NO | NO | NULL | |
| project_status_item_id | INTEGER | NO | NO | NULL | |
| recruitment_status_item_id | INTEGER | NO | NO | NULL | |
| contract_case_center | INTEGER | NO | NO | NULL | |
| contract_case_total | INTEGER | NO | NO | NULL | |
| project_start_date | TEXT | NO | NO | NULL | |
| last_updated | TEXT | NO | NO | NULL | |

#### Foreign Keys (Relationships)

| From Column | To Table | To Column | On Update | On Delete | Description |
|-------------|----------|-----------|-----------|-----------|-------------|
| `disease_item_id` | `dictionary_items` | `item_id` | NO ACTION | NO ACTION | 关联疾病分类字典 |
| `project_stage_item_id` | `dictionary_items` | `item_id` | NO ACTION | NO ACTION | 关联研究分期字典 |
| `project_status_item_id` | `dictionary_items` | `item_id` | NO ACTION | NO ACTION | 关联研究阶段字典 |
| `recruitment_status_item_id` | `dictionary_items` | `item_id` | NO ACTION | NO ACTION | 关联招募状态字典 |

#### 关联说明
- **字典关联**: 通过多个 `*_item_id` 字段引用字典条目，实现数据标准化
- **一对多关系**: 与申办方、研究药物、人员角色、补贴等表建立一对多关系
- **核心地位**: 作为项目管理系统的中心，所有项目相关数据都围绕此表组织
- **业务作用**: 存储项目的基本信息和状态，支持项目全生命周期管理

---

### Table: `project_sponsors`

#### 用途说明
项目申办方关联表，存储项目与申办方（赞助商）的关系。一个项目可以有多个申办方。

#### Columns

| Column Name | Data Type | NOT NULL | Primary Key | Default Value | Constraints |
|-------------|-----------|----------|-------------|---------------|-------------|
| id | INTEGER | NO | YES | AUTOINCREMENT | PRIMARY KEY |
| project_id | TEXT | YES | NO | NULL | NOT NULL |
| sponsor_item_id | INTEGER | YES | NO | NULL | NOT NULL |

#### Foreign Keys (Relationships)

| From Column | To Table | To Column | On Update | On Delete | Description |
|-------------|----------|-----------|-----------|-----------|-------------|
| `project_id` | `projects` | `project_id` | NO ACTION | NO ACTION | 关联到项目主表 |
| `sponsor_item_id` | `dictionary_items` | `item_id` | NO ACTION | NO ACTION | 关联申办方字典条目 |

#### Unique Constraints
- `(project_id, sponsor_item_id)` - 防止同一项目重复添加相同申办方

#### 关联说明
- **项目关联**: 通过 `project_id` 关联到具体项目
- **申办方信息**: 通过 `sponsor_item_id` 引用字典中的申办方信息
- **一对多关系**: 支持一个项目有多个申办方的业务场景
- **业务作用**: 管理项目的资金来源和合作伙伴关系

---

### Table: `research_drugs`

#### 用途说明
研究药物表，存储项目中使用的研究药物信息。记录每个项目涉及的具体药物名称。

#### Columns

| Column Name | Data Type | NOT NULL | Primary Key | Default Value |
|-------------|-----------|----------|-------------|---------------|
| drug_info_id | INTEGER | NO | YES | NULL |
| project_id | TEXT | YES | NO | NULL |
| research_drug | TEXT | YES | NO | NULL |

#### Foreign Keys (Relationships)

| ID | Sequence | To Table | From Column | To Column | On Update | On Delete | Match |
|----|----------|----------|-------------|-----------|-----------|-----------|-------|
| 0 | 0 | `projects` | `project_id` | `project_id` | NO ACTION | NO ACTION | NONE |

#### 关联说明
- **项目关联**: 属于特定项目的研究药物
- **药物信息**: 存储药物的名称和基本信息
- **业务作用**: 支持多药物临床试验的管理，记录试验中使用的所有药物

---

### Table: `drug_groups`

#### 用途说明
药物分组表，用于对项目中的药物进行分组管理，记录每个分组的药物名称和分配比例。

#### Columns

| Column Name | Data Type | NOT NULL | Primary Key | Default Value |
|-------------|-----------|----------|-------------|---------------|
| group_id | INTEGER | NO | YES | NULL |
| project_id | TEXT | YES | NO | NULL |
| drug_name | TEXT | YES | NO | NULL |
| share | INTEGER | YES | NO | NULL |

#### Foreign Keys (Relationships)

| ID | Sequence | To Table | From Column | To Column | On Update | On Delete | Match |
|----|----------|----------|-------------|-----------|-----------|-----------|-------|
| 0 | 0 | `projects` | `project_id` | `project_id` | NO ACTION | NO ACTION | NONE |

#### 关联说明
- **项目关联**: 属于特定项目的药物分组
- **分组管理**: 支持将药物按照试验设计进行分组
- **比例分配**: `share` 字段记录该分组在试验中的分配比例
- **业务作用**: 支持随机对照试验中的药物分组和比例管理

---

### Table: `project_personnel_roles`

#### 用途说明
项目人员角色分配表，建立项目、人员和角色之间的三元关系。记录每个人员在特定项目中担任的角色。

#### Columns

| Column Name | Data Type | NOT NULL | Primary Key | Default Value | Constraints |
|-------------|-----------|----------|-------------|---------------|-------------|
| assignment_id | INTEGER | NO | YES | AUTOINCREMENT | PRIMARY KEY |
| project_id | TEXT | YES | NO | NULL | NOT NULL |
| personnel_id | INTEGER | YES | NO | NULL | NOT NULL |
| role_item_id | INTEGER | YES | NO | NULL | NOT NULL |

#### Foreign Keys (Relationships)

| From Column | To Table | To Column | On Update | On Delete | Description |
|-------------|----------|-----------|-----------|-----------|-------------|
| `project_id` | `projects` | `project_id` | NO ACTION | NO ACTION | 关联到项目主表 |
| `personnel_id` | `staff` | `id` | NO ACTION | NO ACTION | 关联到员工表 |
| `role_item_id` | `dictionary_items` | `item_id` | NO ACTION | NO ACTION | 关联研究角色字典条目 |

#### Unique Constraints
- `(project_id, personnel_id, role_item_id)` - 防止同一人员在同一项目中重复分配相同角色

#### 关联说明
- **三元关系**: 连接项目、人员和角色三个维度
- **项目团队**: 定义项目团队的组成和每个成员的职责
- **角色管理**: 通过字典系统标准化角色定义（如PI、研究护士、数据管理员等）
- **多项目支持**: 同一人员可以在不同项目中担任不同角色
- **业务作用**: 支持项目团队管理和权限控制的基础

---

### Table: `subsidies`

#### 用途说明
补贴项目明细表，存储项目中各种补贴项目的详细信息，包括补贴类型、单价、数量和总金额等。

#### Columns

| Column Name | Data Type | NOT NULL | Primary Key | Default Value |
|-------------|-----------|----------|-------------|---------------|
| subsidy_item_id | INTEGER | NO | YES | NULL |
| project_id | TEXT | YES | NO | NULL |
| subsidy_type_item_id | INTEGER | YES | NO | NULL |
| unit_amount | REAL | YES | NO | NULL |
| total_units | INTEGER | YES | NO | NULL |
| unit_item_id | INTEGER | YES | NO | NULL |
| total_amount | REAL | YES | NO | NULL |
| last_updated | TEXT | NO | NO | NULL |

#### Foreign Keys (Relationships)

| ID | Sequence | To Table | From Column | To Column | On Update | On Delete | Match |
|----|----------|----------|-------------|-----------|-----------|-----------|-------|
| 0 | 0 | `dictionary_items` | `unit_item_id` | `item_id` | NO ACTION | NO ACTION | NONE |
| 1 | 0 | `dictionary_items` | `subsidy_type_item_id` | `item_id` | NO ACTION | NO ACTION | NONE |
| 2 | 0 | `projects` | `project_id` | `project_id` | NO ACTION | NO ACTION | NONE |

#### 关联说明
- **项目关联**: 属于特定项目的补贴项目
- **类型标准化**: 通过 `subsidy_type_item_id` 引用字典中的补贴类型
- **单位标准化**: 通过 `unit_item_id` 引用字典中的计量单位
- **金额计算**: `total_amount = unit_amount × total_units`
- **业务作用**: 支持项目财务管理和成本核算

---

### Table: `subsidy_schemes`

#### 用途说明
补贴方案表，用于将多个补贴项目组合成一个完整的补贴方案，便于批量管理和应用。

#### Columns

| Column Name | Data Type | NOT NULL | Primary Key | Default Value |
|-------------|-----------|----------|-------------|---------------|
| scheme_id | INTEGER | NO | YES | NULL |
| project_id | TEXT | YES | NO | NULL |
| scheme_name | TEXT | YES | NO | NULL |
| total_amount | REAL | YES | NO | NULL |

#### Foreign Keys (Relationships)

| ID | Sequence | To Table | From Column | To Column | On Update | On Delete | Match |
|----|----------|----------|-------------|-----------|-----------|-----------|-------|
| 0 | 0 | `projects` | `project_id` | `project_id` | NO ACTION | NO ACTION | NONE |

#### 关联说明
- **项目关联**: 属于特定项目的补贴方案
- **方案管理**: 将相关的补贴项目打包成方案
- **总金额**: 记录整个方案的总金额
- **业务作用**: 支持补贴的批量管理和标准化应用

---

### Table: `scheme_included_subsidies`

#### 用途说明
补贴方案包含项目关联表，建立补贴方案与具体补贴项目之间的多对多关系。

#### Columns

| Column Name | Data Type | NOT NULL | Primary Key | Default Value | Constraints |
|-------------|-----------|----------|-------------|---------------|-------------|
| id | INTEGER | NO | YES | AUTOINCREMENT | PRIMARY KEY |
| scheme_id | INTEGER | YES | NO | NULL | NOT NULL |
| subsidy_item_id | INTEGER | YES | NO | NULL | NOT NULL |

#### Foreign Keys (Relationships)

| From Column | To Table | To Column | On Update | On Delete | Description |
|-------------|----------|-----------|-----------|-----------|-------------|
| `scheme_id` | `subsidy_schemes` | `scheme_id` | NO ACTION | NO ACTION | 关联到补贴方案表 |
| `subsidy_item_id` | `subsidies` | `subsidy_item_id` | NO ACTION | NO ACTION | 关联到补贴项目表 |

#### Unique Constraints
- `(scheme_id, subsidy_item_id)` - 防止同一方案重复包含相同补贴项目

#### 关联说明
- **多对多关系**: 一个方案可以包含多个补贴项目，一个补贴项目可以属于多个方案
- **灵活组合**: 支持补贴项目的灵活组合和重用
- **业务作用**: 实现补贴方案的灵活配置和管理

---

### Table: `rule_definitions`

#### 用途说明
规则定义表，存储入排标准规则的定义信息，包括规则名称、描述、参数模式等。是入排标准系统的基础。

#### Columns

| Column Name | Data Type | NOT NULL | Primary Key | Default Value | Constraints |
|-------------|-----------|----------|-------------|---------------|-------------|
| rule_definition_id | INTEGER | NO | YES | AUTOINCREMENT | PRIMARY KEY |
| rule_name | TEXT | YES | NO | NULL | NOT NULL UNIQUE |
| rule_description | TEXT | NO | NO | NULL | |
| category | TEXT | NO | NO | NULL | |
| parameter_schema | TEXT | YES | NO | NULL | NOT NULL |
| created_at | TEXT | YES | NO | NULL | NOT NULL |
| updated_at | TEXT | YES | NO | NULL | NOT NULL |

#### Foreign Keys (Relationships)
This table has no foreign key constraints.

#### Unique Constraints
- `rule_name` - 规则名称必须唯一

#### 关联说明
- **规则模板**: 定义可重用的入排标准规则模板
- **参数模式**: `parameter_schema` 字段存储JSON格式的参数定义
- **分类管理**: 通过 `category` 字段对规则进行分类
- **被引用**: 被 `project_criteria` 和 `rule_package_items` 表引用
- **业务作用**: 提供标准化的入排标准规则，支持规则的重用和管理

---

### Table: `project_criteria`

#### 用途说明
项目入排标准表，存储每个项目的具体入选和排除标准。支持标准分组和OR关系的复杂逻辑。

#### Columns

| Column Name | Data Type | NOT NULL | Primary Key | Default Value |
|-------------|-----------|----------|-------------|---------------|
| project_criterion_id | INTEGER | NO | YES | NULL |
| project_id | TEXT | YES | NO | NULL |
| rule_definition_id | INTEGER | YES | NO | NULL |
| criterion_type | TEXT | YES | NO | NULL |
| parameter_values | TEXT | YES | NO | NULL |
| is_active | BOOLEAN | YES | NO | 1 |
| display_order | INTEGER | NO | NO | NULL |
| created_at | TEXT | YES | NO | NULL |
| updated_at | TEXT | YES | NO | NULL |
| group_operator | TEXT | NO | NO | NULL |
| criteria_group_id | TEXT | NO | NO | NULL |

#### Foreign Keys (Relationships)

| ID | Sequence | To Table | From Column | To Column | On Update | On Delete | Match |
|----|----------|----------|-------------|-----------|-----------|-----------|-------|
| 0 | 0 | `projects` | `project_id` | `project_id` | NO ACTION | NO ACTION | NONE |
| 1 | 0 | `rule_definitions` | `rule_definition_id` | `rule_definition_id` | NO ACTION | NO ACTION | NONE |

#### 关联说明
- **项目关联**: 属于特定项目的入排标准
- **规则引用**: 通过 `rule_definition_id` 引用规则定义
- **类型区分**: `criterion_type` 区分入选标准（inclusion）和排除标准（exclusion）
- **参数配置**: `parameter_values` 存储该规则的具体参数值（JSON格式）
- **分组支持**: `criteria_group_id` 和 `group_operator` 支持标准的分组和OR关系
- **排序显示**: `display_order` 控制标准的显示顺序
- **业务作用**: 实现项目入排标准的灵活配置和复杂逻辑支持

---

### Table: `rule_packages`

#### 用途说明
规则包表，用于将多个相关的规则定义组合成一个规则包，便于批量应用和管理常用的规则组合。

#### Columns

| Column Name | Data Type | NOT NULL | Primary Key | Default Value | Constraints |
|-------------|-----------|----------|-------------|---------------|-------------|
| rule_package_id | INTEGER | NO | YES | AUTOINCREMENT | PRIMARY KEY |
| package_name | TEXT | YES | NO | NULL | NOT NULL UNIQUE |
| package_description | TEXT | NO | NO | NULL | |
| category | TEXT | NO | NO | NULL | |
| is_active | INTEGER | YES | NO | 1 | NOT NULL DEFAULT 1 CHECK(is_active IN (0, 1)) |
| created_at | TEXT | YES | NO | CURRENT_TIMESTAMP | NOT NULL DEFAULT CURRENT_TIMESTAMP |
| updated_at | TEXT | YES | NO | CURRENT_TIMESTAMP | NOT NULL DEFAULT CURRENT_TIMESTAMP |

#### Foreign Keys (Relationships)
This table has no foreign key constraints.

#### Check Constraints
- `is_active` - 状态值只能是 0（禁用）或 1（启用）

#### Unique Constraints
- `package_name` - 规则包名称必须唯一

#### 关联说明
- **规则组合**: 将相关的规则定义打包成可重用的规则包
- **分类管理**: 通过 `category` 字段对规则包进行分类
- **状态控制**: `is_active` 字段控制规则包的启用状态
- **业务作用**: 提供规则的批量管理和快速应用功能

---

### Table: `rule_package_items`

#### 用途说明
规则包条目表，建立规则包与具体规则定义之间的多对多关系，定义规则包的具体内容。

#### Columns

| Column Name | Data Type | NOT NULL | Primary Key | Default Value | Constraints |
|-------------|-----------|----------|-------------|---------------|-------------|
| rule_package_item_id | INTEGER | NO | YES | AUTOINCREMENT | PRIMARY KEY |
| rule_package_id | INTEGER | YES | NO | NULL | NOT NULL |
| rule_definition_id | INTEGER | YES | NO | NULL | NOT NULL |
| display_order | INTEGER | YES | NO | 0 | NOT NULL DEFAULT 0 |
| created_at | TEXT | YES | NO | CURRENT_TIMESTAMP | NOT NULL DEFAULT CURRENT_TIMESTAMP |

#### Foreign Keys (Relationships)

| From Column | To Table | To Column | On Update | On Delete | Description |
|-------------|----------|-----------|-----------|-----------|-------------|
| `rule_package_id` | `rule_packages` | `rule_package_id` | NO ACTION | CASCADE | 关联到规则包表，级联删除 |
| `rule_definition_id` | `rule_definitions` | `rule_definition_id` | NO ACTION | CASCADE | 关联到规则定义表，级联删除 |

#### Unique Constraints
- `(rule_package_id, rule_definition_id)` - 同一个规则包内不能重复添加同一个规则

#### 关联说明
- **多对多关系**: 一个规则包可以包含多个规则，一个规则可以属于多个规则包
- **顺序控制**: `display_order` 字段控制规则在包中的显示顺序
- **级联删除**: 当规则包或规则定义被删除时，相关条目自动删除
- **业务作用**: 实现规则包的灵活配置和内容管理

---

## 常用查询模式

### 项目完整信息查询
```sql
-- 获取项目的完整信息，包括所有关联数据
SELECT
    p.*,
    d1.item_value as disease_name,
    d2.item_value as project_stage,
    d3.item_value as project_status,
    d4.item_value as recruitment_status
FROM projects p
LEFT JOIN dictionary_items d1 ON p.disease_item_id = d1.item_id
LEFT JOIN dictionary_items d2 ON p.project_stage_item_id = d2.item_id
LEFT JOIN dictionary_items d3 ON p.project_status_item_id = d3.item_id
LEFT JOIN dictionary_items d4 ON p.recruitment_status_item_id = d4.item_id
WHERE p.project_id = ?;
```

### 项目团队查询
```sql
-- 获取项目的团队成员和角色信息
SELECT
    s.name as staff_name,
    s.email,
    s.organization,
    di.item_value as role_name,
    ppr.assignment_id
FROM project_personnel_roles ppr
JOIN staff s ON ppr.personnel_id = s.id
JOIN dictionary_items di ON ppr.role_item_id = di.item_id
WHERE ppr.project_id = ?
ORDER BY di.item_value;
```

### 项目入排标准查询
```sql
-- 获取项目的入排标准，包括规则信息
SELECT
    pc.*,
    rd.rule_name,
    rd.rule_description,
    rd.parameter_schema
FROM project_criteria pc
JOIN rule_definitions rd ON pc.rule_definition_id = rd.rule_definition_id
WHERE pc.project_id = ? AND pc.is_active = 1
ORDER BY pc.criterion_type, pc.display_order;
```

### 项目财务信息查询
```sql
-- 获取项目的补贴信息
SELECT
    s.*,
    dt.item_value as subsidy_type,
    du.item_value as unit_name
FROM subsidies s
JOIN dictionary_items dt ON s.subsidy_type_item_id = dt.item_id
JOIN dictionary_items du ON s.unit_item_id = du.item_id
WHERE s.project_id = ?;
```

### 字典数据查询
```sql
-- 获取特定字典的所有条目
SELECT di.*
FROM dictionary_items di
JOIN dictionaries d ON di.dictionary_id = d.id
WHERE d.name = ? AND di.status = 'active'
ORDER BY di.item_key;
```

### 规则包内容查询
```sql
-- 获取规则包的所有规则
SELECT
    rp.package_name,
    rd.rule_name,
    rd.rule_description,
    rpi.display_order
FROM rule_packages rp
JOIN rule_package_items rpi ON rp.rule_package_id = rpi.rule_package_id
JOIN rule_definitions rd ON rpi.rule_definition_id = rd.rule_definition_id
WHERE rp.rule_package_id = ? AND rp.is_active = 1
ORDER BY rpi.display_order;
```

## 数据完整性约束总结

### 数据库级约束概览

#### 主键约束 (Primary Key Constraints)
所有表都使用 `INTEGER PRIMARY KEY AUTOINCREMENT`，确保记录的唯一标识：
- 字典系统：`dictionaries.id`, `dictionary_items.item_id`, `tags.tag_id`
- 项目系统：`projects.project_id` (文本主键), `project_sponsors.id`, `research_drugs.drug_info_id`
- 人员系统：`staff.id`, `project_personnel_roles.assignment_id`
- 规则系统：`rule_definitions.rule_definition_id`, `rule_packages.rule_package_id`
- 财务系统：`subsidies.subsidy_item_id`, `subsidy_schemes.scheme_id`

#### 外键约束 (Foreign Key Constraints)
严格的引用完整性，支持级联删除：
- **字典关联**：多数业务表通过 `*_item_id` 关联到 `dictionary_items`
- **项目关联**：所有项目相关表通过 `project_id` 关联到 `projects`
- **级联删除**：`dictionary_tags`, `rule_package_items` 等关联表支持级联删除

#### 唯一性约束 (Unique Constraints)
防止重复数据，确保业务逻辑正确：
- **全局唯一**：`dictionaries.name`, `staff.email`, `rule_definitions.rule_name`
- **组合唯一**：`(dictionary_id, item_key)`, `(project_id, sponsor_item_id)`, `(project_id, personnel_id, role_item_id)`
- **复合主键**：`dictionary_tags(dictionary_id, tag_id)`

#### 检查约束 (Check Constraints)
业务规则验证：
- **性别限制**：`staff.gender` 只能是 '男'、'女'、'未知'
- **状态限制**：`rule_packages.is_active` 只能是 0 或 1

#### 默认值约束 (Default Value Constraints)
自动填充常用值：
- **时间戳**：`staff.created_at`, `rule_packages.created_at` 默认为 `CURRENT_TIMESTAMP`
- **状态值**：`dictionary_items.status` 默认为 'active'
- **版本号**：`dictionaries.version` 默认为 1

### 业务规则约束

#### 项目相关约束
1. **项目ID唯一性**: `projects.project_id` 必须全局唯一
2. **项目名称**: `project_name` 和 `project_short_name` 不能为空
3. **日期格式**: `project_start_date` 应使用 ISO 8601 格式 (YYYY-MM-DD)
4. **合同数量**: `contract_case_center` ≤ `contract_case_total`

#### 人员相关约束
1. **员工姓名**: `staff.name` 不能为空
2. **PI标识**: `staff.isPI` 只能为 0 或 1
3. **角色分配**: 每个项目-人员组合的角色应该唯一
4. **邮箱唯一**: 员工邮箱必须全局唯一

#### 财务相关约束
1. **金额计算**: `subsidies.total_amount` = `unit_amount` × `total_units`
2. **金额非负**: 所有金额字段应 ≥ 0
3. **数量非负**: `total_units` 应 > 0

#### 规则相关约束
1. **参数验证**: `project_criteria.parameter_values` 必须符合对应 `rule_definitions.parameter_schema` 的JSON Schema
2. **类型限制**: `criterion_type` 只能为 'inclusion' 或 'exclusion'
3. **分组逻辑**: 同一 `criteria_group_id` 的标准应有相同的 `group_operator`
4. **规则名称唯一**: 规则定义和规则包名称必须全局唯一

#### 字典相关约束
1. **状态值**: `dictionary_items.status` 只能为 'active' 或 'inactive'
2. **键值唯一**: 同一字典内的 `item_key` 必须唯一
3. **引用完整性**: 所有 `*_item_id` 字段引用的字典条目必须存在且状态为 'active'
4. **字典名称唯一**: 字典名称和标签名称必须全局唯一

### 数据一致性检查

#### 定期检查项目
1. **孤立记录检查**: 检查是否存在引用不存在记录的外键
2. **金额一致性**: 验证补贴方案总金额与包含的补贴项目总和是否一致
3. **规则参数验证**: 定期验证项目标准的参数值是否符合规则定义的模式
4. **状态一致性**: 检查项目状态与相关子表数据的一致性

#### 推荐的维护操作
1. **定期清理**: 清理无效的字典条目和过期的项目数据
2. **索引优化**: 为常用查询字段创建适当的索引
3. **数据备份**: 定期备份重要的项目和配置数据
4. **性能监控**: 监控复杂查询的性能，必要时进行优化

## 数据库现状总结

### 数据统计（截至本次更新）
- **项目总数**: 46个临床研究项目
- **规则定义**: 17个入排标准规则
- **字典分类**: 10个字典类型，共计95个字典条目
- **数据质量**: 所有表都有完整的约束定义，确保数据完整性

### 系统架构特点
1. **高度规范化**: 使用字典系统标准化所有枚举值
2. **完善约束**: 主键、外键、唯一性、检查约束齐全
3. **支持级联**: 关联表支持级联删除，维护数据一致性
4. **灵活扩展**: 规则系统支持复杂的入排标准配置
5. **数据完整性**: 外键关系确保引用完整性

### 关键改进点
本次文档修订基于实际数据库结构，主要改进：
1. **准确的约束信息**: 添加了所有 UNIQUE、CHECK、DEFAULT 约束
2. **外键关系描述**: 详细描述了所有外键关系和级联行为
3. **字典数据概览**: 添加了实际字典内容的统计信息
4. **数据库位置**: 更正了数据库文件的实际路径
5. **约束分类总结**: 系统化地总结了所有数据库约束类型

### 维护建议
1. **定期检查**: 使用 `sqlite-explorer` 定期验证数据库结构
2. **文档同步**: 数据库结构变更时及时更新此文档
3. **约束验证**: 定期检查约束是否按预期工作
4. **性能优化**: 为常用查询字段添加适当的索引

---

> **文档更新记录**: 本文档于2024年12月基于实际数据库结构全面修订，确保与当前系统状态完全一致。
